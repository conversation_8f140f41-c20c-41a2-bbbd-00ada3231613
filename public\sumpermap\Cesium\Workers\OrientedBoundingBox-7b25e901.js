/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Plane-2bcb9154","./EllipsoidTangentPlane-9c25b2da"],function(a,W,X,j,Z,G,N,Y,F){function A(a,e){this.center=Z.Cartesian3.clone(W.defaultValue(a,Z.Cartesian3.ZERO)),this.halfAxes=N.Matrix3.clone(W.defaultValue(e,N.Matrix3.ZERO))}A.packedLength=Z.Cartesian3.packedLength+N.Matrix3.packedLength,A.pack=function(a,e,t){return X.Check.typeOf.object("value",a),X.Check.defined("array",e),t=W.defaultValue(t,0),Z.Cartesian3.pack(a.center,e,t),N.Matrix3.pack(a.halfAxes,e,t+Z.Cartesian3.packedLength),e},A.unpack=function(a,e,t){return X.Check.defined("array",a),e=W.defaultValue(e,0),W.defined(t)||(t=new A),Z.Cartesian3.unpack(a,e,t.center),N.Matrix3.unpack(a,e+Z.Cartesian3.packedLength,t.halfAxes),t};var R=new Z.Cartesian3,T=new Z.Cartesian3,I=new Z.Cartesian3,D=new Z.Cartesian3,L=new Z.Cartesian3,S=new Z.Cartesian3,z=new N.Matrix3,q={unitary:new N.Matrix3,diagonal:new N.Matrix3};A.fromPoints=function(a,e){if(W.defined(e)||(e=new A),!W.defined(a)||0===a.length)return e.halfAxes=N.Matrix3.ZERO,e.center=Z.Cartesian3.ZERO,e;var t,r=a.length,n=Z.Cartesian3.clone(a[0],R);for(t=1;t<r;t++)Z.Cartesian3.add(n,a[t],n);var i=1/r;Z.Cartesian3.multiplyByScalar(n,i,n);var s,o=0,d=0,C=0,u=0,c=0,l=0;for(t=0;t<r;t++)o+=(s=Z.Cartesian3.subtract(a[t],n,T)).x*s.x,d+=s.x*s.y,C+=s.x*s.z,u+=s.y*s.y,c+=s.y*s.z,l+=s.z*s.z;o*=i,d*=i,C*=i,u*=i,c*=i,l*=i;var h=z;h[0]=o,h[1]=d,h[2]=C,h[3]=d,h[4]=u,h[5]=c,h[6]=C,h[7]=c,h[8]=l;var f=N.Matrix3.computeEigenDecomposition(h,q),m=N.Matrix3.clone(f.unitary,e.halfAxes),x=N.Matrix3.getColumn(m,0,D),p=N.Matrix3.getColumn(m,1,L),M=N.Matrix3.getColumn(m,2,S),w=-Number.MAX_VALUE,g=-Number.MAX_VALUE,b=-Number.MAX_VALUE,v=Number.MAX_VALUE,y=Number.MAX_VALUE,O=Number.MAX_VALUE;for(t=0;t<r;t++)s=a[t],w=Math.max(Z.Cartesian3.dot(x,s),w),g=Math.max(Z.Cartesian3.dot(p,s),g),b=Math.max(Z.Cartesian3.dot(M,s),b),v=Math.min(Z.Cartesian3.dot(x,s),v),y=Math.min(Z.Cartesian3.dot(p,s),y),O=Math.min(Z.Cartesian3.dot(M,s),O);x=Z.Cartesian3.multiplyByScalar(x,.5*(v+w),x),p=Z.Cartesian3.multiplyByScalar(p,.5*(y+g),p),M=Z.Cartesian3.multiplyByScalar(M,.5*(O+b),M);var E=Z.Cartesian3.add(x,p,e.center);Z.Cartesian3.add(E,M,E);var P=I;return P.x=w-v,P.y=g-y,P.z=b-O,Z.Cartesian3.multiplyByScalar(P,.5,P),N.Matrix3.multiplyByScale(e.halfAxes,P,e.halfAxes),e};var m=new Z.Cartesian3,x=new Z.Cartesian3;function H(a,e,t,r,n,i,s,o,d,C,u){if(!(W.defined(n)&&W.defined(i)&&W.defined(s)&&W.defined(o)&&W.defined(d)&&W.defined(C)))throw new X.DeveloperError("all extents (minimum/maximum X/Y/Z) are required.");W.defined(u)||(u=new A);var c=u.halfAxes;N.Matrix3.setColumn(c,0,e,c),N.Matrix3.setColumn(c,1,t,c),N.Matrix3.setColumn(c,2,r,c);var l=m;l.x=(n+i)/2,l.y=(s+o)/2,l.z=(d+C)/2;var h=x;h.x=(i-n)/2,h.y=(o-s)/2,h.z=(C-d)/2;var f=u.center;return l=N.Matrix3.multiplyByVector(c,l,l),Z.Cartesian3.add(a,l,f),N.Matrix3.multiplyByScale(c,h,c),u}var J=new Z.Cartographic,K=new Z.Cartesian3,Q=new Z.Cartographic,$=new Z.Cartographic,aa=new Z.Cartographic,ea=new Z.Cartographic,ta=new Z.Cartographic,ra=new Z.Cartesian3,na=new Z.Cartesian3,ia=new Z.Cartesian3,sa=new Z.Cartesian3,oa=new Z.Cartesian3,da=new G.Cartesian2,Ca=new G.Cartesian2,ua=new G.Cartesian2,ca=new G.Cartesian2,la=new G.Cartesian2,ha=new Z.Cartesian3,fa=new Z.Cartesian3,ma=new Z.Cartesian3,xa=new Z.Cartesian3,pa=new G.Cartesian2,Ma=new Z.Cartesian3,wa=new Z.Cartesian3,ga=new Z.Cartesian3,ba=new Y.Plane(Z.Cartesian3.UNIT_X,0);A.fromRectangle=function(a,e,t,r,n){if(!W.defined(a))throw new X.DeveloperError("rectangle is required");if(a.width<0||a.width>j.CesiumMath.TWO_PI)throw new X.DeveloperError("Rectangle width must be between 0 and 2*pi");if(a.height<0||a.height>j.CesiumMath.PI)throw new X.DeveloperError("Rectangle height must be between 0 and pi");if(W.defined(r)&&!j.CesiumMath.equalsEpsilon(r.radii.x,r.radii.y,j.CesiumMath.EPSILON15))throw new X.DeveloperError("Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)");var i,s,o,d,C,u,c;if(e=W.defaultValue(e,0),t=W.defaultValue(t,0),r=W.defaultValue(r,G.Ellipsoid.WGS84),a.width<=j.CesiumMath.PI){var l=G.Rectangle.center(a,J),h=r.cartographicToCartesian(l,K),f=new F.EllipsoidTangentPlane(h,r);c=f.plane;var m=l.longitude,x=a.south<0&&0<a.north?0:l.latitude,p=Z.Cartographic.fromRadians(m,a.north,t,Q),M=Z.Cartographic.fromRadians(a.west,a.north,t,$),w=Z.Cartographic.fromRadians(a.west,x,t,aa),g=Z.Cartographic.fromRadians(a.west,a.south,t,ea),b=Z.Cartographic.fromRadians(m,a.south,t,ta),v=r.cartographicToCartesian(p,ra),y=r.cartographicToCartesian(M,na),O=r.cartographicToCartesian(w,ia),E=r.cartographicToCartesian(g,sa),P=r.cartographicToCartesian(b,oa),N=f.projectPointToNearestOnPlane(v,da),A=f.projectPointToNearestOnPlane(y,Ca),R=f.projectPointToNearestOnPlane(O,ua),T=f.projectPointToNearestOnPlane(E,ca),I=f.projectPointToNearestOnPlane(P,la);return s=-(i=Math.min(A.x,R.x,T.x)),d=Math.max(A.y,N.y),o=Math.min(T.y,I.y),M.height=g.height=e,y=r.cartographicToCartesian(M,na),E=r.cartographicToCartesian(g,sa),C=Math.min(Y.Plane.getPointDistance(c,y),Y.Plane.getPointDistance(c,E)),u=t,H(f.origin,f.xAxis,f.yAxis,f.zAxis,i,s,o,d,C,u,n)}var D=0<a.south,L=a.north<0,S=D?a.south:L?a.north:0,z=G.Rectangle.center(a,J).longitude,q=Z.Cartesian3.fromRadians(z,S,t,r,ha);q.z=0;var U=Math.abs(q.x)<j.CesiumMath.EPSILON10&&Math.abs(q.y)<j.CesiumMath.EPSILON10?Z.Cartesian3.UNIT_X:Z.Cartesian3.normalize(q,fa),V=Z.Cartesian3.UNIT_Z,B=Z.Cartesian3.cross(U,V,ma);c=Y.Plane.fromPointNormal(q,U,ba);var k=Z.Cartesian3.fromRadians(z+j.CesiumMath.PI_OVER_TWO,S,t,r,xa);i=-(s=Z.Cartesian3.dot(Y.Plane.projectPointOntoPlane(c,k,pa),B)),d=Z.Cartesian3.fromRadians(0,a.north,L?e:t,r,Ma).z,o=Z.Cartesian3.fromRadians(0,a.south,D?e:t,r,wa).z;var _=Z.Cartesian3.fromRadians(a.east,S,t,r,ga);return H(q,B,V,U,i,s,o,d,C=Y.Plane.getPointDistance(c,_),u=0,n)},A.clone=function(a,e){if(W.defined(a))return W.defined(e)?(Z.Cartesian3.clone(a.center,e.center),N.Matrix3.clone(a.halfAxes,e.halfAxes),e):new A(a.center,a.halfAxes)},A.intersectPlane=function(a,e){if(!W.defined(a))throw new X.DeveloperError("box is required.");if(!W.defined(e))throw new X.DeveloperError("plane is required.");var t=a.center,r=e.normal,n=a.halfAxes,i=r.x,s=r.y,o=r.z,d=Math.abs(i*n[N.Matrix3.COLUMN0ROW0]+s*n[N.Matrix3.COLUMN0ROW1]+o*n[N.Matrix3.COLUMN0ROW2])+Math.abs(i*n[N.Matrix3.COLUMN1ROW0]+s*n[N.Matrix3.COLUMN1ROW1]+o*n[N.Matrix3.COLUMN1ROW2])+Math.abs(i*n[N.Matrix3.COLUMN2ROW0]+s*n[N.Matrix3.COLUMN2ROW1]+o*n[N.Matrix3.COLUMN2ROW2]),C=Z.Cartesian3.dot(r,t)+e.distance;return C<=-d?N.Intersect.OUTSIDE:d<=C?N.Intersect.INSIDE:N.Intersect.INTERSECTING};var f=new Z.Cartesian3,p=new Z.Cartesian3,M=new Z.Cartesian3,h=new Z.Cartesian3;A.distanceSquaredTo=function(a,e){if(!W.defined(a))throw new X.DeveloperError("box is required.");if(!W.defined(e))throw new X.DeveloperError("cartesian is required.");var t=Z.Cartesian3.subtract(e,a.center,m),r=a.halfAxes,n=N.Matrix3.getColumn(r,0,f),i=N.Matrix3.getColumn(r,1,p),s=N.Matrix3.getColumn(r,2,M),o=Z.Cartesian3.magnitude(n),d=Z.Cartesian3.magnitude(i),C=Z.Cartesian3.magnitude(s);Z.Cartesian3.normalize(n,n),Z.Cartesian3.normalize(i,i),Z.Cartesian3.normalize(s,s);var u=h;u.x=Z.Cartesian3.dot(t,n),u.y=Z.Cartesian3.dot(t,i),u.z=Z.Cartesian3.dot(t,s);var c,l=0;return u.x<-o?l+=(c=u.x+o)*c:u.x>o&&(l+=(c=u.x-o)*c),u.y<-d?l+=(c=u.y+d)*c:u.y>d&&(l+=(c=u.y-d)*c),u.z<-C?l+=(c=u.z+C)*c:u.z>C&&(l+=(c=u.z-C)*c),l};var w=new Z.Cartesian3,g=new Z.Cartesian3;A.computePlaneDistances=function(a,e,t,r){if(!W.defined(a))throw new X.DeveloperError("box is required.");if(!W.defined(e))throw new X.DeveloperError("position is required.");if(!W.defined(t))throw new X.DeveloperError("direction is required.");W.defined(r)||(r=new N.Interval);var n=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY,s=a.center,o=a.halfAxes,d=N.Matrix3.getColumn(o,0,f),C=N.Matrix3.getColumn(o,1,p),u=N.Matrix3.getColumn(o,2,M),c=Z.Cartesian3.add(d,C,w);Z.Cartesian3.add(c,u,c),Z.Cartesian3.add(c,s,c);var l=Z.Cartesian3.subtract(c,e,g),h=Z.Cartesian3.dot(t,l);return n=Math.min(h,n),i=Math.max(h,i),Z.Cartesian3.add(s,d,c),Z.Cartesian3.add(c,C,c),Z.Cartesian3.subtract(c,u,c),Z.Cartesian3.subtract(c,e,l),h=Z.Cartesian3.dot(t,l),n=Math.min(h,n),i=Math.max(h,i),Z.Cartesian3.add(s,d,c),Z.Cartesian3.subtract(c,C,c),Z.Cartesian3.add(c,u,c),Z.Cartesian3.subtract(c,e,l),h=Z.Cartesian3.dot(t,l),n=Math.min(h,n),i=Math.max(h,i),Z.Cartesian3.add(s,d,c),Z.Cartesian3.subtract(c,C,c),Z.Cartesian3.subtract(c,u,c),Z.Cartesian3.subtract(c,e,l),h=Z.Cartesian3.dot(t,l),n=Math.min(h,n),i=Math.max(h,i),Z.Cartesian3.subtract(s,d,c),Z.Cartesian3.add(c,C,c),Z.Cartesian3.add(c,u,c),Z.Cartesian3.subtract(c,e,l),h=Z.Cartesian3.dot(t,l),n=Math.min(h,n),i=Math.max(h,i),Z.Cartesian3.subtract(s,d,c),Z.Cartesian3.add(c,C,c),Z.Cartesian3.subtract(c,u,c),Z.Cartesian3.subtract(c,e,l),h=Z.Cartesian3.dot(t,l),n=Math.min(h,n),i=Math.max(h,i),Z.Cartesian3.subtract(s,d,c),Z.Cartesian3.subtract(c,C,c),Z.Cartesian3.add(c,u,c),Z.Cartesian3.subtract(c,e,l),h=Z.Cartesian3.dot(t,l),n=Math.min(h,n),i=Math.max(h,i),Z.Cartesian3.subtract(s,d,c),Z.Cartesian3.subtract(c,C,c),Z.Cartesian3.subtract(c,u,c),Z.Cartesian3.subtract(c,e,l),h=Z.Cartesian3.dot(t,l),n=Math.min(h,n),i=Math.max(h,i),r.start=n,r.stop=i,r};var r=new N.BoundingSphere;A.isOccluded=function(a,e){if(!W.defined(a))throw new X.DeveloperError("box is required.");if(!W.defined(e))throw new X.DeveloperError("occluder is required.");var t=N.BoundingSphere.fromOrientedBoundingBox(a,r);return!e.isBoundingSphereVisible(t)},A.prototype.intersectPlane=function(a){return A.intersectPlane(this,a)},A.prototype.distanceSquaredTo=function(a){return A.distanceSquaredTo(this,a)},A.prototype.computePlaneDistances=function(a,e,t){return A.computePlaneDistances(this,a,e,t)},A.prototype.isOccluded=function(a){return A.isOccluded(this,a)},A.equals=function(a,e){return a===e||W.defined(a)&&W.defined(e)&&Z.Cartesian3.equals(a.center,e.center)&&N.Matrix3.equals(a.halfAxes,e.halfAxes)},A.prototype.clone=function(a){return A.clone(this,a)},A.prototype.equals=function(a){return A.equals(this,a)},a.OrientedBoundingBox=A});