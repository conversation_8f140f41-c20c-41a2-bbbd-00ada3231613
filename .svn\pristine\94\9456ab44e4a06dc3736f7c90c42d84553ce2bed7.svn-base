<template>
    <div id="Dcrgl">
        <Card :title="'电池热管理'" class="card-main">
            <div class="info">
                <InfoCard>
                    <template #title>
                    <h2 v-if="showTitle">{{ infoTitle }}</h2>
                    </template>
                    <template #content>
                    <div v-for="item in totalInfos" :key="item.id">
                        {{ item.key + ":" + item.text }}
                    </div>
                    </template>
                </InfoCard> 
            </div>
            <div class="content" >
                <Content>
                    <template #label v-if="switchValue">
                        <el-tab-pane  :label="'电池总览'" :name="'1'">
                            <TemperBlock :batteries="batteryTemperData" :isclickblock="true" @clickblock="(index)=>handleClickBlock(index)"></TemperBlock>
                        </el-tab-pane>
                    </template>
                    <template #label v-else>
                        <el-tab-pane :label="'电池电芯总览'" :name="'1'"><TemperBlock :batteries="genTemperData(cellTemperData[0], cellTemperData[1], cellTemperData[2], cellTemperData[3], cellTemperData[4])" :maxBlocks="50" @back="handleReturn()"></TemperBlock></el-tab-pane>
                        <el-tab-pane :label="'电池详情'" :name="'2'"></el-tab-pane>
                    </template>
                </Content>
            </div>
        </Card>
    </div>
</template>

<script>
import Card from './components/Card/Card.vue';
import InfoCard from './components/InfoCard/InfoCard.vue';
import Content from './components/Content/Content.vue';
import TemperBlock from './components/TemperBlock/TemperBlock.vue';
export default {
    name: 'Dcrgl',
    components: { Card, InfoCard, Content, TemperBlock },
    data() {
    return {
        showTitle:true,
        infoTitle:'电池热管理信息',
        items:[  
            { id: 1, text: "项目1" },
            { id: 2, text: "项目2" },
            { id: 3, text: "项目3" },
            { id: 4, text: "项目4" },
            { id: 5, text: "项目5" },
            
        ],
        totalInfos:[
            {
                key:"电池总数",
                text:100
            },
            {
                key:"温度异常电池数",
                text:10
            },
            {
                key:"温度异常电池占比",
                text:10
            },
        ],
        switchValue: true,
        testBatterys:[
        ],
        batteryData:[],
        batteryTemperData:[],
        cellTemperData:[]

    }
    },
    mounted() {
        this.getBatteryData()
    },
    watch: {
        "$store.state.tree.sectionId": {
            handler: function () {
                this.getBatteryData();
            },
            deep: true,
            immediate: true,
        },
    },

    methods: {
        handleClick(){
            console.log("点击了");
            // this.switchValue = !this.switchValue;
            // this.items = [ ...this.items, { id: 3, text: "项目3"}]
        },
        handleClickBlock(index){
            // console.log(index);
            this.cellTemperData = [this.batteryData[index].cellNum, this.batteryData[index].cellMaxTempValue, this.batteryData[index].cellMaxTempNum, this.batteryData[index].cellMinTempValue, this.batteryData[index].cellMinTempNum];
            console.log(this.cellTemperData);
            
            this.switchValue = false;
        },
        handleReturn(){
            this.switchValue = true;
        },
        genTemperData(bgNums, maxTemper, maxIndex, minTemper, minIndex){
            if (bgNums === undefined || maxTemper === undefined || maxIndex === undefined || minTemper === undefined || minIndex === undefined){
                return;
            }
            this.testBatterys = [];
            for(let i = 0; i < bgNums; i++){
                if(i == maxIndex){
                    this.testBatterys.push({ temperature: maxTemper});
                }else if(i == minIndex){
                    this.testBatterys.push({ temperature: minTemper});
                }else{
                    this.testBatterys.push({ temperature : Math.floor(Math.random() * (maxTemper - minTemper)) + minTemper + 1});
                }
            }
            return this.testBatterys;
        },
        async getBatteryData(){
            let params = {
                sectionId: this.$store.state.tree.sectionId||'',
            };
            let res = await this.$http.post(
                `/equip/battery/listByQuery?keyword=0&page=1&limit=999`,
                params
            );
            let res2 = await this.$http.post(
                `/equip/battery/listByQuery?keyword=1&page=1&limit=999`,
                params
            );
            this.batteryData = res.result.records.concat(res2.result.records);
            this.batteryTemperData = this.batteryData.map((item,index) => {
                return {
                    temperature: item.cellMaxTempValue,
                }
                

            })
            // this.batteryTemperData = batteryTemperData;
            this.totalInfos[0].text = this.batteryData.length;
            this.totalInfos[1].text = this.batteryData.filter(item => item.cellMaxTempValue > 60).length;
            this.totalInfos[2].text = Math.round((this.batteryData.filter(item => item.cellMaxTempValue > 60).length / this.batteryData.length) * 100) + '%';
            // return this.batteryData;
            // self.batteryData.push(...batteryTemperData);
            // console.log(self.batteryData);
        }
    }
}
</script>

<style lang="scss" scoped>
#Dcrgl {
    display: flex;

    // width: 100vw;
    // height: 86vh;
    .card-main{
        width: 50%;
        height: 87vh;
        flex: 1;
        .info{
            width: 100%;
            height: 20%;
            // background: #000000;
            // margin-top: 10px;
            // border-radius: 10px;
        }
        .content{
            width: 100%;
            height: 78%;
            margin: 10px;
            // background: #000000;
            
            // border-radius: 10px;
            // border: 1px solid #000000;
        }
    }
}
</style>
  