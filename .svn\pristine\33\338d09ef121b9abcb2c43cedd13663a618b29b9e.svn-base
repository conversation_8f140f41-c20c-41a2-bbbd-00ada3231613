/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./GeometryAttributes-aacecde6","./IndexDatatype-9435b55f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./VertexFormat-fe4db402","./arrayRemoveDuplicates-2869246d","./EllipsoidRhumbLine-6ca4b1e6","./EllipsoidGeodesic-db2069b3","./PolylinePipeline-65700d85","./WallGeometryLibrary-80ce00e5"],function(Q,u,X,$,c,ee,e,t,i,te,ie,ae,a,re,r,ne,oe,n,o,f,s,l,m,p,se){var le=new $.Cartesian3,me=new $.Cartesian3,pe=new $.Cartesian3,de=new $.Cartesian3,ue=new $.Cartesian3,ce=new $.Cartesian3,fe=new $.Cartesian3,he=new $.Cartesian3;function h(e){var t=(e=Q.defaultValue(e,Q.defaultValue.EMPTY_OBJECT)).positions,i=e.maximumHeights,a=e.minimumHeights;if(!Q.defined(t))throw new u.DeveloperError("options.positions is required.");if(Q.defined(i)&&i.length!==t.length)throw new u.DeveloperError("options.positions and options.maximumHeights must have the same length.");if(Q.defined(a)&&a.length!==t.length)throw new u.DeveloperError("options.positions and options.minimumHeights must have the same length.");var r=Q.defaultValue(e.vertexFormat,f.VertexFormat.DEFAULT),n=Q.defaultValue(e.granularity,X.CesiumMath.RADIANS_PER_DEGREE),o=Q.defaultValue(e.ellipsoid,c.Ellipsoid.WGS84);this._positions=t,this._minimumHeights=a,this._maximumHeights=i,this._vertexFormat=f.VertexFormat.clone(r),this._granularity=n,this._ellipsoid=c.Ellipsoid.clone(o),this._enuCenter=e.enuCenter,this._workerName="createWallGeometry";var s=1+t.length*$.Cartesian3.packedLength+2;Q.defined(a)&&(s+=a.length),Q.defined(i)&&(s+=i.length),this.packedLength=s+c.Ellipsoid.packedLength+f.VertexFormat.packedLength+1,this.packedLength+=$.Cartesian3.packedLength}h.pack=function(e,t,i){if(!Q.defined(e))throw new u.DeveloperError("value is required");if(!Q.defined(t))throw new u.DeveloperError("array is required");var a;i=Q.defaultValue(i,0);var r=e._positions,n=r.length;for(t[i++]=n,a=0;a<n;++a,i+=$.Cartesian3.packedLength)$.Cartesian3.pack(r[a],t,i);var o=e._minimumHeights;if(n=Q.defined(o)?o.length:0,t[i++]=n,Q.defined(o))for(a=0;a<n;++a)t[i++]=o[a];var s=e._maximumHeights;if(n=Q.defined(s)?s.length:0,t[i++]=n,Q.defined(s))for(a=0;a<n;++a)t[i++]=s[a];return c.Ellipsoid.pack(e._ellipsoid,t,i),i+=c.Ellipsoid.packedLength,f.VertexFormat.pack(e._vertexFormat,t,i),i+=f.VertexFormat.packedLength,t[i++]=e._granularity,Q.defined(e._enuCenter)?$.Cartesian3.pack(e._enuCenter,t,i):$.Cartesian3.pack($.Cartesian3.ZERO,t,i),t};var v=c.Ellipsoid.clone(c.Ellipsoid.UNIT_SPHERE),y=new f.VertexFormat,g={positions:void 0,minimumHeights:void 0,maximumHeights:void 0,ellipsoid:v,vertexFormat:y,granularity:void 0,enuCenter:void 0};return h.unpack=function(e,t,i){if(!Q.defined(e))throw new u.DeveloperError("array is required");var a;t=Q.defaultValue(t,0);var r,n,o=e[t++],s=new Array(o);for(a=0;a<o;++a,t+=$.Cartesian3.packedLength)s[a]=$.Cartesian3.unpack(e,t);if(0<(o=e[t++]))for(r=new Array(o),a=0;a<o;++a)r[a]=e[t++];if(0<(o=e[t++]))for(n=new Array(o),a=0;a<o;++a)n[a]=e[t++];var l=c.Ellipsoid.unpack(e,t,v);t+=c.Ellipsoid.packedLength;var m=f.VertexFormat.unpack(e,t,y);t+=f.VertexFormat.packedLength;var p=e[t++],d=$.Cartesian3.unpack(e,t);return $.Cartesian3.equals(d,$.Cartesian3.ZERO)&&(d=void 0),Q.defined(i)?(i._positions=s,i._minimumHeights=r,i._maximumHeights=n,i._ellipsoid=c.Ellipsoid.clone(l,i._ellipsoid),i._vertexFormat=f.VertexFormat.clone(m,i._vertexFormat),i._granularity=p,i._enuCenter=d,i):(g.positions=s,g.minimumHeights=r,g.maximumHeights=n,g.granularity=p,g.enuCenter=d,new h(g))},h.fromConstantHeights=function(e){var t,i,a=(e=Q.defaultValue(e,Q.defaultValue.EMPTY_OBJECT)).positions;if(!Q.defined(a))throw new u.DeveloperError("options.positions is required.");var r=e.minimumHeight,n=e.maximumHeight,o=Q.defined(r),s=Q.defined(n);if(o||s){var l=a.length;t=o?new Array(l):void 0,i=s?new Array(l):void 0;for(var m=0;m<l;++m)o&&(t[m]=r),s&&(i[m]=n)}return new h({positions:a,maximumHeights:i,minimumHeights:t,ellipsoid:e.ellipsoid,vertexFormat:e.vertexFormat})},h.createGeometry=function(e){var t=e._positions,i=e._minimumHeights,a=e._maximumHeights,r=e._vertexFormat,n=e._granularity,o=e._ellipsoid,s=e._enuCenter,l=se.WallGeometryLibrary.computePositions(o,t,a,i,n,!0,s);if(Q.defined(l.pos)){var m;Q.defined(s)&&(m=re.Transforms.eastNorthUpToFixedFrame(s));var p,d=l.pos.bottomPositions,u=l.pos.topPositions,c=l.pos.numCorners,f=u.length,h=2*f,v=r.position?new Float64Array(h):void 0,y=r.normal?new Float32Array(h):void 0,g=r.tangent?new Float32Array(h):void 0,C=r.bitangent?new Float32Array(h):void 0,b=r.st?new Float32Array(h/3*2):void 0,w=0,x=0,E=0,A=0,_=0,F=he,D=fe,k=ce,L=!0,P=0,H=1/((f/=3)-t.length+1);for(p=0;p<f;++p){var T=3*p,G=$.Cartesian3.fromArray(u,T,le),V=$.Cartesian3.fromArray(d,T,me);if(r.position&&(v[w++]=V.x,v[w++]=V.y,v[w++]=V.z,v[w++]=G.x,v[w++]=G.y,v[w++]=G.z),r.st&&(b[_++]=P,b[_++]=0,b[_++]=P,b[_++]=1),r.normal||r.tangent||r.bitangent){var O,z=$.Cartesian3.clone($.Cartesian3.ZERO,ue),R=o.scaleToGeodeticSurface($.Cartesian3.fromArray(u,T,me),me);if(p+1<f&&(O=o.scaleToGeodeticSurface($.Cartesian3.fromArray(u,T+3,pe),pe),z=$.Cartesian3.fromArray(u,T+3,ue)),L){var S=$.Cartesian3.subtract(z,G,de),q=$.Cartesian3.subtract(R,G,le);F=$.Cartesian3.normalize($.Cartesian3.cross(q,S,F),F),L=!1}$.Cartesian3.equalsEpsilon(O,R,X.CesiumMath.EPSILON10)?L=!0:(P+=H,r.tangent&&(D=$.Cartesian3.normalize($.Cartesian3.subtract(O,R,D),D)),r.bitangent&&(k=$.Cartesian3.normalize($.Cartesian3.cross(F,D,k),k))),r.normal&&(Q.defined(s)&&(ee.Matrix4.multiplyByPoint(m,F,F),$.Cartesian3.normalize(F,F)),y[x++]=F.x,y[x++]=F.y,y[x++]=F.z,y[x++]=F.x,y[x++]=F.y,y[x++]=F.z),r.tangent&&(g[A++]=D.x,g[A++]=D.y,g[A++]=D.z,g[A++]=D.x,g[A++]=D.y,g[A++]=D.z),r.bitangent&&(C[E++]=k.x,C[E++]=k.y,C[E++]=k.z,C[E++]=k.x,C[E++]=k.y,C[E++]=k.z)}}var I=new ne.GeometryAttributes;r.position&&(I.position=new ie.GeometryAttribute({componentDatatype:te.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:v})),r.normal&&(I.normal=new ie.GeometryAttribute({componentDatatype:te.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:y})),r.tangent&&(I.tangent=new ie.GeometryAttribute({componentDatatype:te.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:g})),r.bitangent&&(I.bitangent=new ie.GeometryAttribute({componentDatatype:te.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:C})),r.st&&(I.st=new ie.GeometryAttribute({componentDatatype:te.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:b}));var M=h/3;h-=6*(c+1);var N=oe.IndexDatatype.createTypedArray(M,h),B=0;for(p=0;p<M-2;p+=2){var U=p,W=p+2,Z=$.Cartesian3.fromArray(v,3*U,le),J=$.Cartesian3.fromArray(v,3*W,me);if(!$.Cartesian3.equalsEpsilon(Z,J,X.CesiumMath.EPSILON10)){var Y=p+1,j=p+3;N[B++]=Y,N[B++]=U,N[B++]=j,N[B++]=j,N[B++]=U,N[B++]=W}}var K=new ie.Geometry({attributes:I,indices:N,primitiveType:ae.PrimitiveType.TRIANGLES,boundingSphere:new ee.BoundingSphere.fromVertices(v)});return Q.defined(e._enuCenter)&&(K.attributes.position.values.set(l.localPos.topPositions,0),K.attributes.position.values.set(l.localPos.bottomPositions,K.attributes.position.values.length/2),K.attributes.position.componentDatatype=te.ComponentDatatype.FLOAT),K}},function(e,t){return Q.defined(t)&&(e=h.unpack(e,t)),e._ellipsoid=c.Ellipsoid.clone(e._ellipsoid),h.createGeometry(e)}});