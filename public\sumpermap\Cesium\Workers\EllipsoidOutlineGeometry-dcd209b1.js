/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./GeometryAttributes-aacecde6","./IndexDatatype-9435b55f","./arrayFill-9766fb2e","./GeometryOffsetAttribute-999fc023"],function(e,R,c,N,p,B,S,q,F,U,W,Y,J,j){var f=new p.Cartesian3(1,1,1),H=Math.cos,K=Math.sin;function C(e){e=R.defaultValue(e,R.defaultValue.EMPTY_OBJECT);var i=R.defaultValue(e.radii,f),t=R.defaultValue(e.innerRadii,i),r=R.defaultValue(e.minimumClock,0),a=R.defaultValue(e.maximumClock,N.CesiumMath.TWO_PI),o=R.defaultValue(e.minimumCone,0),n=R.defaultValue(e.maximumCone,N.CesiumMath.PI),s=Math.round(R.defaultValue(e.stackPartitions,10)),u=Math.round(R.defaultValue(e.slicePartitions,8)),m=Math.round(R.defaultValue(e.subdivisions,128));if(s<1)throw new c.DeveloperError("options.stackPartitions cannot be less than 1");if(u<0)throw new c.DeveloperError("options.slicePartitions cannot be less than 0");if(m<0)throw new c.DeveloperError("options.subdivisions must be greater than or equal to zero.");if(R.defined(e.offsetAttribute)&&e.offsetAttribute===j.GeometryOffsetAttribute.TOP)throw new c.DeveloperError("GeometryOffsetAttribute.TOP is not a supported options.offsetAttribute for this geometry.");this._radii=p.Cartesian3.clone(i),this._innerRadii=p.Cartesian3.clone(t),this._minimumClock=r,this._maximumClock=a,this._minimumCone=o,this._maximumCone=n,this._stackPartitions=s,this._slicePartitions=u,this._subdivisions=m,this._offsetAttribute=e.offsetAttribute,this._workerName="createEllipsoidOutlineGeometry"}C.packedLength=2*p.Cartesian3.packedLength+8,C.pack=function(e,i,t){if(!R.defined(e))throw new c.DeveloperError("value is required");if(!R.defined(i))throw new c.DeveloperError("array is required");return t=R.defaultValue(t,0),p.Cartesian3.pack(e._radii,i,t),t+=p.Cartesian3.packedLength,p.Cartesian3.pack(e._innerRadii,i,t),t+=p.Cartesian3.packedLength,i[t++]=e._minimumClock,i[t++]=e._maximumClock,i[t++]=e._minimumCone,i[t++]=e._maximumCone,i[t++]=e._stackPartitions,i[t++]=e._slicePartitions,i[t++]=e._subdivisions,i[t]=R.defaultValue(e._offsetAttribute,-1),i};var h=new p.Cartesian3,_=new p.Cartesian3,v={radii:h,innerRadii:_,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,subdivisions:void 0,offsetAttribute:void 0};C.unpack=function(e,i,t){if(!R.defined(e))throw new c.DeveloperError("array is required");i=R.defaultValue(i,0);var r=p.Cartesian3.unpack(e,i,h);i+=p.Cartesian3.packedLength;var a=p.Cartesian3.unpack(e,i,_);i+=p.Cartesian3.packedLength;var o=e[i++],n=e[i++],s=e[i++],u=e[i++],m=e[i++],f=e[i++],d=e[i++],l=e[i];return R.defined(t)?(t._radii=p.Cartesian3.clone(r,t._radii),t._innerRadii=p.Cartesian3.clone(a,t._innerRadii),t._minimumClock=o,t._maximumClock=n,t._minimumCone=s,t._maximumCone=u,t._stackPartitions=m,t._slicePartitions=f,t._subdivisions=d,t._offsetAttribute=-1===l?void 0:l,t):(v.minimumClock=o,v.maximumClock=n,v.minimumCone=s,v.maximumCone=u,v.stackPartitions=m,v.slicePartitions=f,v.subdivisions=d,v.offsetAttribute=-1===l?void 0:l,new C(v))},C.createGeometry=function(e){var i=e._radii;if(!(i.x<=0||i.y<=0||i.z<=0)){var t=e._innerRadii;if(!(t.x<=0||t.y<=0||t.z<=0)){var r=e._minimumClock,a=e._maximumClock,o=e._minimumCone,n=e._maximumCone,s=e._subdivisions,u=B.Ellipsoid.fromCartesian3(i),m=e._slicePartitions+1,f=e._stackPartitions+1;(m=Math.round(m*Math.abs(a-r)/N.CesiumMath.TWO_PI))<2&&(m=2),(f=Math.round(f*Math.abs(n-o)/N.CesiumMath.PI))<2&&(f=2);var d=0,l=1,c=t.x!==i.x||t.y!==i.y||t.z!==i.z,p=!1,C=!1;c&&(l=2,0<o&&(p=!0,d+=m),n<Math.PI&&(C=!0,d+=m));var h,_,v,b,y=s*l*(f+m),k=new Float64Array(3*y),A=2*(y+d-(m+f)*l),w=Y.IndexDatatype.createTypedArray(y,A),P=0,x=new Array(f),E=new Array(f);for(h=0;h<f;h++)b=o+h*(n-o)/(f-1),x[h]=K(b),E[h]=H(b);var g=new Array(s),D=new Array(s);for(h=0;h<s;h++)v=r+h*(a-r)/(s-1),g[h]=K(v),D[h]=H(v);for(h=0;h<f;h++)for(_=0;_<s;_++)k[P++]=i.x*x[h]*D[_],k[P++]=i.y*x[h]*g[_],k[P++]=i.z*E[h];if(c)for(h=0;h<f;h++)for(_=0;_<s;_++)k[P++]=t.x*x[h]*D[_],k[P++]=t.y*x[h]*g[_],k[P++]=t.z*E[h];for(x.length=s,E.length=s,h=0;h<s;h++)b=o+h*(n-o)/(s-1),x[h]=K(b),E[h]=H(b);for(g.length=m,D.length=m,h=0;h<m;h++)v=r+h*(a-r)/(m-1),g[h]=K(v),D[h]=H(v);for(h=0;h<s;h++)for(_=0;_<m;_++)k[P++]=i.x*x[h]*D[_],k[P++]=i.y*x[h]*g[_],k[P++]=i.z*E[h];if(c)for(h=0;h<s;h++)for(_=0;_<m;_++)k[P++]=t.x*x[h]*D[_],k[P++]=t.y*x[h]*g[_],k[P++]=t.z*E[h];for(h=P=0;h<f*l;h++){var M=h*s;for(_=0;_<s-1;_++)w[P++]=M+_,w[P++]=M+_+1}var G=f*s*l;for(h=0;h<m;h++)for(_=0;_<s-1;_++)w[P++]=G+h+_*m,w[P++]=G+h+(_+1)*m;if(c)for(G=f*s*l+m*s,h=0;h<m;h++)for(_=0;_<s-1;_++)w[P++]=G+h+_*m,w[P++]=G+h+(_+1)*m;if(c){var O=f*s*l,V=O+s*m;if(p)for(h=0;h<m;h++)w[P++]=O+h,w[P++]=V+h;if(C)for(O+=s*m-m,V+=s*m-m,h=0;h<m;h++)w[P++]=O+h,w[P++]=V+h}var T=new W.GeometryAttributes({position:new F.GeometryAttribute({componentDatatype:q.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:k})});if(R.defined(e._offsetAttribute)){var z=k.length,I=new Uint8Array(z/3),L=e._offsetAttribute===j.GeometryOffsetAttribute.NONE?0:1;J.arrayFill(I,L),T.applyOffset=new F.GeometryAttribute({componentDatatype:q.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:I})}return new F.Geometry({attributes:T,indices:w,primitiveType:U.PrimitiveType.LINES,boundingSphere:S.BoundingSphere.fromEllipsoid(u),offsetAttribute:e._offsetAttribute})}}},e.EllipsoidOutlineGeometry=C});