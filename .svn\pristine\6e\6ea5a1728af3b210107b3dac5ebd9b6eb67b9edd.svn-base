<template>
    <div id="Wbgl">
      <div class="container-left">
        <Card :title="'设备维修管理'" class="card-preview">
          <div class="card-content">
            <ECharts :options="chartOptions"/>
            <div class="text-content">
              测试文本
            </div>
          </div>

          
        </Card>
        <Card :title="'设备故障警报'" class="card-preview">
          <div class="card-content">
            <ECharts :options="pieChartOptions"/>
          </div>
        </Card>
      </div>

      <div class="container-right">
        <Card :title="'设备保养与检测'" class="card-preview">
          <div class="card-content">
            <ECharts :options="ringChartOptions"/>
          </div>
        </Card>
        <Card :title="'维保知识库'" class="card-preview">
          <div class="card-content">
            <ECharts :options="lineChartOptions"/>
          </div>
        </Card>
      </div>
    </div>
  </template>
  
  <script>
  import Card from './components/Card/Card.vue';
  import ECharts from './components/Chart/Chart.vue';
  export default {
    name: 'Wbgl',
    components: { Card, ECharts },
    data() {
      return {
        chartOptions: {
        title: {
          text: 'ECharts 柱状图示例'
        },
        tooltip: {},
        xAxis: {
          data: ["衬衫","羊毛衫","雪纺衫","裤子","高跟鞋","袜子"]
        },
        yAxis: {},
        series: [{
          name: '销量',
          type: 'bar',
          data: [5, 20, 36, 10, 10, 20]
        }]
      },
      pieChartOptions: {
        title: {
          text: 'ECharts 饼图示例',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '访问来源',
            type: 'pie',
            radius: '50%',
            data: [
              { value: 1048, name: '搜索引擎' },
              { value: 735, name: '直接访问' },
              { value: 580, name: '邮件营销' },
              { value: 484, name: '联盟广告' },
              { value: 300, name: '视频广告' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      },
      ringChartOptions: {
        title: {
          text: '环状图示例',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '访问来源',
            type: 'pie',
            radius: ['40%', '70%'], // 修改这里，创建一个环形图
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '30',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 1048, name: '搜索引擎' },
              { value: 735, name: '直接访问' },
              { value: 580, name: '邮件营销' },
              { value: 484, name: '联盟广告' },
              { value: 300, name: '视频广告' }
            ]
          }
        ]
      },
      lineChartOptions: {
        title: {
          text: '折线图示例',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['销售额', '目标额']
        },
        xAxis: {
          type: 'category',
          data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '销售额',
            type: 'line',
            data: [120, 200, 150, 80, 70, 110, 130]
          },
          {
            name: '目标额',
            type: 'line',
            data: [100, 150, 160, 130, 120, 140, 150]
          }
        ]
      }
      }
    },
    mounted() {

    },
    watch: {

    },
  
    methods: {
    }
  }
  </script>
  
  <style lang="scss" scoped>
  #Wbgl {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
  
    .container-left {
      width: 49%;
      margin-right: 1%;
  
      .card-preview {
        height: 43vh;
        margin-bottom: 1%;
        .card-content{
          width:100%;
          height:100%;
          display: flex;
          flex-direction: row;
          .text-content{
            width: 40%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 60px;

          }
        }
      }
  
    }
  
  
    .container-right {
      width: 49%;
      margin-right: 1%;
      .card-preview {
        height: 43vh;
        margin-bottom: 1%;
        .card-content{
          width:60%;
          height:100%;
        }
      }
  
    }
  
    .card-item {
      margin-top: 20px;
    }
  }
  </style>
  