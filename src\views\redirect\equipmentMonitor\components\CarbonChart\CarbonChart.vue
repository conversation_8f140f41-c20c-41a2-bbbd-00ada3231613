<template>
  <div ref="chart" style="width: 100%; height: 98px;"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'CarbonChart',
  props: {
    chartData: {
      type: Object,
      required: true,
      default: () => ({
        categories: [],
        carbonFootprint: [],
        carbonReduction: [],
      }),
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.initChart();
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.initChart();
        this.updateChart();
      },
    },
  },
  
  methods: {
    initChart() {
      const { categories, carbonFootprint, carbonReduction } = this.chartData;

      // 计算最大值，用于控制柱子高度显示范围
      const maxValue = Math.max(...carbonFootprint.concat(carbonReduction)) * 1.2; // 多出20%留白

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.7)',
          borderColor: 'rgba(0,0,0,0.7)',
          textStyle: {
            fontSize: 10,
          },
        },
        legend: {
          show: false,
        },
        grid: {
          top: 10,
          bottom: 20,
          left: 45,
          right: 10,
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            rotate: 0,
            fontSize: 9,
          },
          axisTick: {
            alignWithLabel: true,
          },
        },
        yAxis: {
          type: 'value',
          name: '', // 确保单位清晰，碳排放量 (kgCO₂)
          nameLocation: 'center',   // 名称居中显示
          nameGap: 30,              // 名称与轴线距离
          axisLabel: {
            width: 140,             // 设置足够宽度防止截断
            overflow: 'break',      // 文字过长自动换行
            fontSize: 9,            // 可选：减小字体
            formatter: function(value) {
              // 使用科学计数法格式化 y 轴标签
              return value.toExponential(2); // 保留两位小数，例如 1.23e+5
            }
          }
          // axisLabel: {
          //   fontSize: 7, // 更小字体
          //   overflow: 'break',
          //   width: 80, // 减小宽度，让内容更紧凑
          //   formatter: function(value) {
          //     if (value >= 1e5) {
          //       return value.toExponential(1); // 更简洁的科学计数法
          //     } else {
          //       return value.toLocaleString(); // 小数值保留原样
          //     }
          //   }
          // }
        },

        series: [
          {
            name: '碳足迹',
            type: 'bar',
            barWidth: 10,
            itemStyle: { color: '#409EFF' },
            data: carbonFootprint,
            barGap: '0%', // 柱子并列显示
            categoryGap: '10%', // 控制柱子之间的间距，数值越大柱子越窄
          },
          {
            name: '碳减排量',
            type: 'bar',
            barWidth: 10,
            itemStyle: { color: '#67C23A' },
            data: carbonReduction,
          },
        ],
      };

      this.chart = echarts.init(this.$refs.chart);
      this.chart.setOption(option);

      window.addEventListener("resize", () => {
        this.chart.resize();
      });
      // this.adjustYAxisLabelFontSize();
      // window.addEventListener('resize', () => {
      //   this.chart.resize();
      //   this.adjustYAxisLabelFontSize();
      // });
    },

    adjustYAxisLabelFontSize() {
      const containerWidth = this.$refs.chart.clientWidth;
      const fontSize = containerWidth < 300 ? 6 : 8; // 根据宽度调整字体大小
      this.chart.setOption({
        yAxis: {
          axisLabel: {
            fontSize: fontSize
          }
        }
      });
    },

    formatter: function(value) {
      if (value >= 1e5) {
        return value.toExponential(2); // 大于等于1e5时用科学计数法
      } else {
        return value; // 否则正常显示
      }
    },

    updateChart() {
      if (this.chart) {
        this.chart.setOption({
          xAxis: {
            data: this.chartData.categories,
          },
          series: [
            {
              data: this.chartData.carbonFootprint,
            },
            {
              data: this.chartData.carbonReduction,
            },
          ],
        });
      }
    },
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
};
</script>