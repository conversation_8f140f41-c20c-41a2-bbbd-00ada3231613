<template>
  <div id="Gzsbjrlb">
    <el-card class="card-content">
      <div class="content-option">
        <div class="content-title">感知设备接入列表</div>
        <el-form
          :inline="true"
          :model="formInline"
          ref="formInline"
          class="form-view"
        >
          <el-form-item label="车牌号码">
            <el-input
              placeholder="请输入关键词"
              v-model="formInline.carNum"
              clearable
              prefix-icon="el-icon-search"
            ></el-input>
          </el-form-item>
          <el-form-item label="管理编号">
            <el-input
              placeholder="请输入关键词"
              v-model="formInline.managementNumber"
              clearable
              prefix-icon="el-icon-search"
            ></el-input>
          </el-form-item>
          <el-form-item label="机械类型">
            <el-select
              v-model="formInline.deviceType"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(item, index) in deviceTypeData"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery"
              >查询</el-button
            >
            <el-button
              icon="el-icon-download"
              @click="handleExport"
              :loading="loading"
              >导出数据</el-button
            >
            <el-button
              icon="el-icon-refresh-left"
              type="primary"
              @click="handleReset"
              >返回</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div class="content-table">
        <el-table
          ref="table"
          :data="tableData"
          style="width: 100%"
          border
          :header-cell-style="headerCellStyle"
          :cell-style="cellStyle"
        >
          <el-table-column
            type="index"
            width="50"
            label="序号"
            align="center"
          />
          <el-table-column
            prop="carNum"
            label="车牌号码"
            align="center"
            min-width="130"
          />
          <el-table-column label="管理编号" align="center" min-width="180">
            <template slot-scope="scope">
              <span class="device-code" @click="handleView(scope.row)">{{
                scope.row.managementNumber
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="机械类型" align="center" min-width="130">
            <template slot-scope="scope">
              <span v-if="scope.row.deviceType == '0'">充电挖掘机</span>
              <span v-else-if="scope.row.deviceType == '1'">换电挖掘机</span>
              <span v-else-if="scope.row.deviceType == '2'">充电装载机</span>
              <span v-else-if="scope.row.deviceType == '3'">换电装载机</span>
              <span v-else-if="scope.row.deviceType == '4'">充电自卸车</span>
              <span v-else-if="scope.row.deviceType == '5'">换电自卸车</span>
              <span v-else-if="scope.row.deviceType == '6'">充电混泥土车</span>
              <span v-else-if="scope.row.deviceType == '7'">换电混泥土车</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="walkStatus"
            label="车辆状态"
            align="center"
            min-width="130"
          />
          <el-table-column label="当前SOC" align="center" min-width="130">
            <template slot-scope="scope">{{
              scope.row.soc ? scope.row.soc + "%" : ""
            }}</template>
          </el-table-column>
          <el-table-column label="当前SOH" align="center" min-width="130">
            <template slot-scope="scope">{{
              scope.row.soc ? scope.row.soc + "%" : ""
            }}</template>
          </el-table-column>
          <el-table-column
            prop="totalWorkHours"
            label="总工作时长 / h"
            align="center"
            min-width="130"
          />
          <el-table-column
            label="在线状态"
            align="center"
            min-width="130"
            prop="carStatus"
          >
          </el-table-column>
          <el-table-column
            prop="manufacturer"
            label="制造商"
            align="center"
            min-width="150"
          />
          <el-table-column
            prop="energyType"
            label="补能类型"
            align="center"
            min-width="130"
          />
          <el-table-column
            prop="faultLevel"
            label="故障等级"
            align="center"
            min-width="130"
          >
          </el-table-column>
        </el-table>
        <el-pagination
          class="pagination-part"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageParams.page"
          :page-sizes="pageSizes"
          :page-size="pageParams.limit"
          :layout="tableLayout"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import tableUtils from "@/mixins/tableUtils";
import { exportExcel } from "@/api/baseApi/common";
export default {
  name: "Gzsbjrlb",
  mixins: [tableUtils],
  data() {
    return {
      formInline: {},
      type: "",
      tableData: [],
      deviceTypeData: [],
      keyword: null,
      loading: false,
      timer: null,
    };
  },

  beforeMount() {
    this.type = this.$route.query.type;
    this.keyword = this.type === "in" ? 2 : this.type === "out" ? 3 : 0;
  },

  mounted() {
    this.getDeviceType();
  },

  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.timer = setTimeout(() => {
          this.requestList();
        }, 500);
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    async getDeviceType() {
      let device = [];
      let res = await this.$http.get(`/equip/dictData/type?dictName=机械类型`);
      res?.result.map((item) => {
        device.push({
          label: item.dictLabel,
          value: item.dictValue,
        });
      });
      this.deviceTypeData = device;
    },

    async requestList() {
      let params = {
        ...this.formInline,
        sectionId: this.$store.state.tree.sectionId||'',
      };
      let res = await this.$http.post(
        `/equip/battery/listByQuery?keyword=${this.keyword}&page=${this.pageParams.page}&limit=${this.pageParams.limit}`,
        params
      );
      this.tableData = res.result?.records || [];
      this.total = res.result?.total;
      clearTimeout(this.timer);
    },

    async handleExport() {
      this.loading = true;
      let params = {
        ...this.formInline,
        keyword: this.keyword,
        sectionId: this.$store.state.tree.sectionId||'',
      };
      let res = await exportExcel(params);
      var list = new Blob([res], {
        type: "application/vnd.ms-excel;charset=utf-8",
      });
      var downloadUrl = window.URL.createObjectURL(list);
      var anchor = document.createElement("a");
      anchor.href = downloadUrl;
      anchor.download = "感知设备接入列表.xlsx";
      anchor.click();
      window.URL.revokeObjectURL(list);
      this.loading = false;
    },

    handleView(row) {
      this.$router.push({
        path: "/devices/details",
        query: {
          id: row.id,
        },
      });
    },

    handleReset() {
      window.history.back();
    },
  },
};
</script>

<style lang="scss" scoped>
#Gzsbjrlb {
  .content-option {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .content-title {
      line-height: 40px;
      font-family: "微软雅黑 Bold", "微软雅黑 Regular", "微软雅黑", sans-serif;
      font-weight: 700;
      font-style: normal;
      font-size: 18px;
      color: #3a3f5d;
      margin-right: 5%;
    }

    .form-view {
      flex: 1;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }

    ::v-deep .el-input {
      width: 175px;
    }
  }

  .pagination-part {
    margin-top: 20px;
  }

  .device-code {
    color: #4575e7;
  }

  .device-in {
    color: #60bf79;
  }

  .device-out {
    color: #f58a69;
  }
}
</style>