
.tags-breadcrumb{
  display: flex;
  align-items: center;
  padding: 0 16px;
  height: 50px;
}
.tags-breadcrumb-list{
  padding: 0 15px;
}
.tag-collapse{
  font-size: 36px !important;
}
.tag-collapse.tag-collapse_right {
  transform: rotate(90deg);
}
.tags-container {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  .tags-box{
    position: relative;
    box-sizing: border-box;
    padding-right: 106px;
    width: 100%;
    height: 40px;
    background: #fff;
    
  }
  .tags-list{
    position: absolute;
    padding: 2px 10px;
    overflow: visible;
    white-space: nowrap;
    transition: left .3s ease;
  }
  .tag-item-icon{
    color: #eee;
    font-size: 11px !important;
  }
  .tag-item {
      position: relative;
      display: inline-block;
      height: 30px;
      line-height: 30px;
      margin: 2px 4px 2px 0;
      padding: 0 10px;
      border: 1px solid #eee;
      border-radius: 3px;
      background: #fff;
      color: #495060!important;
      font-size: 12px;
      vertical-align: middle;
      opacity: 1;
      overflow: hidden;
      cursor: pointer;
      &.is-active{
        .tag-item-icon{
          color: $mainBg;
        }
       border-color:  $mainBg;
      }
  }
  .tag-text{
    margin-left: 8px;
  }
  .tag-close{
    margin-left: 8px;
  }
  .tag-item:hover {
    opacity: .85;
  }
  .tags-menu{
   position: absolute;
   top:0;
   right: 0;
   display: flex;
   align-items: center;
   padding: 0 15px;
   height: 96%;
   box-sizing: border-box;
   background-color: #fff;
   box-shadow: -3px 0 15px 3px rgba(0, 0, 0, 0.1);
  }
}
.contextmenu {
  margin: 0;
  background: #fff;
  top:64px;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
  li {
    margin: 0;
    height: 32px;
    line-height: 32px;
    padding: 0 15px;
    cursor: pointer;
    &:hover {
      background: #eee;
    }
  }
}