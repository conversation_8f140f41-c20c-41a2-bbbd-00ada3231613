const user = {
  state: {
    userInfo: {},
    token: null,
    permissions: []
  },
  mutations: {
    SET_USER_INFO(state, view) {
      state.userInfo = view;
    },
    SET_USER_TOKEN(state, view) {
      state.token = view;
    },
    SET_USER_PERMISSIONS(state, permissions) {//permissions
      state.permissions = permissions;
    },
  },

  actions: {
    setUserInfo({
      commit
    }, view) {
      commit('SET_USER_INFO', view);
    },
    setUserToken({
      commit
    }, view) {
      commit('SET_USER_TOKEN', view);
    },
    setUserPermissions({ commit }, permissions) {
      commit('SET_USER_PERMISSIONS', permissions);
    }
  },

  getters: {
    userInfo: state => state.userInfo,
    userToken: state => state.token,
    permissions: state => state.permissions,
  },
}

export default user;
