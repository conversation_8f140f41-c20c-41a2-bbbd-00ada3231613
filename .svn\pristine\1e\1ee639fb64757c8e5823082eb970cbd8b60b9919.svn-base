<template>
    <div>
        <div class="title">

            <div class="legend">
                <div class="legend-item">
                    <div class="color-block min-temp"></div>
                    <div class="text">
                    低温 {{ minTemp }}°C
                    </div>
                </div>
                <div class="legend-item">
                    <div class="color-block mid-temp"></div>
                    <div class="text">
                    中温 {{ Math.round((minTemp + maxTemp) / 2) }}°C
                    </div>
                </div>
                <div class="legend-item">
                    <div class="color-block max-temp"></div>
                    <div class="text">
                    高温 {{ maxTemp }}°C
                    </div>
                </div>
                <div class="legend-item">
                    <div class="color-block empty"></div>
                    <div class="text">
                    空置
                    </div>
                </div>
            </div>
            <div class="back-button" v-if="!isclickblock" @click="$emit('back')"><div class="button-text">返回</div></div>
            <div v-else></div>
        </div>
        
        <div class="battery-chart-container">
            <div 
                v-for="(block, index) in formattedBlocks" 
                :key="index" 
                class="battery-block" 
                :class="{ 'empty': block.status === 'empty' }"
                :style="{ 
                    width: blockWidth,
                    background: block.temperature !== undefined ? getTemperatureColor(block.temperature) : '',
                    cursor: block.status === 'empty' || !isclickblock ? 'default' : 'pointer'
                }"
                @click="(block.status === 'empty' || !isclickblock)?1:$emit('clickblock', index)"

            >
                <!-- 显示电池序号和温度 -->
                <div class="block-info">
                <span v-if="block.temperature !== undefined" class="number">{{ index + 1 }}</span>
                <span v-if="block.temperature !== undefined" class="temp">{{ block.temperature }}°C</span>
                </div>
        

                <div v-if="(block.temperature !== undefined && isclickblock)" class="tooltip">
                    <div>温度：{{ block.temperature }}°C</div>
                    <div></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    emits: ['clickblock', 'back'],
    name: 'TemperBlock',
    props: {
        batteries: {
            type: Array,
            required: true,
            default: () => []
        },
        minBlocks: {
            type: Number,
            default: 8 // 最小分块数
        },
        maxBlocks: {
            type: Number,
            default: 30 // 最大分块数
        },
        minTemp: {
            type: Number,
            default: 20 // 最低温度值
        },
        maxTemp: {
            type: Number,
            default: 50 // 最高温度值
        },
        isclickblock:{
            type:Boolean,
            default:false
        }
    },
    computed: {
        totalBlocks() {
            return Math.max(this.batteries.length, this.minBlocks);
        },
        blockWidth() {
            return `calc(100% / ${Math.min(this.totalBlocks, this.maxBlocks)} - 7px)`;
        },
        formattedBlocks() {
            return Array.from({ length: Math.ceil(this.totalBlocks/this.maxBlocks)*this.maxBlocks }, (_, index) => {
                const battery = this.batteries[index];
                if (battery && battery.temperature !== undefined) {
                    const temp = battery.temperature;
                    return { temperature: temp, status: 'active' };
                } else {
                    return { temperature: undefined, status: 'empty' };
                }
            });
        }
    },
    methods: {
        getTemperatureColor(temp) {
            // 将温度映射到0-1的范围
            const normalizedTemp = Math.min(Math.max((temp - this.minTemp) / (this.maxTemp - this.minTemp), 0), 1);
            
            // 计算颜色分量 - 改进的版本
            let red, green, blue;
            
            if (normalizedTemp < 0.5) {
                // 从绿色(0,200,0)到黄色(230,200,0)
                red = Math.round(510 * normalizedTemp);
                green = 200;
            } else {
                // 从黄色(230,200,0)到红色(230,0,0)
                red = 230;
                green = Math.round(510 * (1 - normalizedTemp));
            }
            blue = 0; // 纯黄色不需要蓝色分量
            
            // 创建更鲜明的渐变颜色
            const baseColor = `rgb(${red}, ${green}, ${blue})`;
            const darkerColor = `rgb(${Math.round(red*0.8)}, ${Math.round(green*0.8)}, ${blue})`;
            const lighterColor = `rgb(${Math.min(255, red + 40)}, ${Math.min(255, green + 40)}, ${blue})`;
            
            return `linear-gradient(135deg, ${lighterColor} 0%, ${baseColor} 50%, ${darkerColor} 100%)`;
        },
    }
};
</script>

<style lang="scss" scoped>
.battery-chart-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    position: relative; 
    .battery-block {
        cursor: pointer;
        aspect-ratio: 1 / 1;
        background-color: #eee;
        transition: background-color 0.3s, z-index 0s;
        position: relative;
        z-index: 1; 
        border-radius: 10%;
        box-shadow: 
            0 2px 4px rgba(0,0,0,0.1),
            inset 0 -1px 3px rgba(0,0,0,0.2);
        &:hover {
            z-index: 100; // 悬停时提升层级
        }
        /* 显示在块内的电池序号和温度 */
        .block-info {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }
        .number {
            display: block;
            font-weight: bold;
        }
        
        .temp {
            font-size: 0.8em;
            display: block;
        }
        

        .tooltip {
            display: none;
            position: absolute;
            top: 100%; 
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            white-space: nowrap;
            z-index: 1000; 
        }
        
        &:hover .tooltip {
            display: flex;
            flex-direction: column;


        }
    }
    
    .empty {
        background-image: linear-gradient(
            135deg, 
            #E0E0E0 0%,
            #9E9E9E 50%,
            #616161 100%
        );
        cursor:default;
    }
}

// 定义颜色变量（可选）
$white-bg: rgba(255, 255, 255, 0.9);
$box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
.title{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0% 0.5% 0.5% 0.5%;
    .back-button{
        cursor: pointer;
        width: 3%;
        height: 35px;
        font-size: 15px;
        // font-weight: bold;
        border: #409EFF solid 1px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        &:hover{
            color: #fff;
            background-color: #409EFF;
        }
    }

    .legend {
        display: flex;
        gap: 15px;
        padding: 8px;
        background: $white-bg;
        border-radius: 4px;
        box-shadow: $box-shadow;

        // 图例项
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;

            // 颜色块
            .color-block {
                width: 20px;
                height: 20px;
                aspect-ratio: 1 / 1;
                border-radius: 4px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.2);
                
                &.min-temp {
                    background-image: linear-gradient(
                        135deg,
                        rgb(100, 255, 100) 0%,
                        rgb(0, 200, 0) 50%,
                        rgb(0, 150, 0) 100%
                    );
                }
                
                &.mid-temp {
                    background-image: linear-gradient(
                        135deg,
                        rgb(255, 255, 100) 0%,
                        rgb(255, 200, 0) 50%,
                        rgb(200, 150, 0) 100%
                    );
                }
                
                &.max-temp {
                    background-image: linear-gradient(
                        135deg,
                        rgb(255, 100, 100) 0%,
                        rgb(255, 0, 0) 50%,
                        rgb(200, 0, 0) 100%
                    );
                }
                
                &.empty {
                    background-image: linear-gradient(
                        135deg, 
                        #E0E0E0 0%,
                        #9E9E9E 50%,
                        #616161 100%
                    );
                }
            }

            // 文字说明
            .text {
                font-size: 14px;
                line-height: 1.4;
            }
        }
    }
}
// 图例容器

</style>