/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./ComponentDatatype-5862616f","./AttributeCompression-75ce15eb"],function(e,S,d,f,b,C,g,s,x){function t(e,t){d.Check.typeOf.object("ellipsoid",e),this._ellipsoid=e,this._cameraPosition=new b.Cartesian3,this._cameraPositionInScaledSpace=new b.Cartesian3,this._distanceToLimbInScaledSpaceSquared=0,S.defined(t)&&(this.cameraPosition=t)}Object.defineProperties(t.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},cameraPosition:{get:function(){return this._cameraPosition},set:function(e){var t=this._ellipsoid.transformPositionToScaledSpace(e,this._cameraPositionInScaledSpace),i=b.Cartesian3.magnitudeSquared(t)-1;b.Cartesian3.clone(e,this._cameraPosition),this._cameraPositionInScaledSpace=t,this._distanceToLimbInScaledSpaceSquared=i}}});var c=new b.Cartesian3;t.prototype.isPointVisible=function(e){return y(this._ellipsoid.transformPositionToScaledSpace(e,c),this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared)},t.prototype.isScaledSpacePointVisible=function(e){return y(e,this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared)};var a=new b.Cartesian3;t.prototype.isScaledSpacePointVisiblePossiblyUnderEllipsoid=function(e,t){var i,r=this._ellipsoid;return y(e,i,S.defined(t)&&t<0&&r.minimumRadius>-t?((i=a).x=this._cameraPosition.x/(r.radii.x+t),i.y=this._cameraPosition.y/(r.radii.y+t),i.z=this._cameraPosition.z/(r.radii.z+t),i.x*i.x+i.y*i.y+i.z*i.z-1):(i=this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared))},t.prototype.computeHorizonCullingPoint=function(e,t,i){return l(this._ellipsoid,e,t,i)};var o=C.Ellipsoid.clone(C.Ellipsoid.UNIT_SPHERE);t.prototype.computeHorizonCullingPointPossiblyUnderEllipsoid=function(e,t,i,r){return l(u(this._ellipsoid,i,o),e,t,r)},t.prototype.computeHorizonCullingPointFromVertices=function(e,t,i,r,a){return h(this._ellipsoid,e,t,i,r,a)},t.prototype.computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid=function(e,t,i,r,a,n){return h(u(this._ellipsoid,a,o),e,t,i,r,n)};var n=[];t.prototype.computeHorizonCullingPointFromRectangle=function(e,t,i){d.Check.typeOf.object("rectangle",e);var r=C.Rectangle.subsample(e,t,0,n),a=g.BoundingSphere.fromPoints(r);if(!(b.Cartesian3.magnitude(a.center)<.1*t.minimumRadius))return this.computeHorizonCullingPoint(a.center,r,i)};var m=new b.Cartesian3;function u(e,t,i){if(S.defined(t)&&t<0&&e.minimumRadius>-t){var r=b.Cartesian3.fromElements(e.radii.x+t,e.radii.y+t,e.radii.z+t,m);e=C.Ellipsoid.fromCartesian3(r,i)}return e}function l(e,t,i,r){d.Check.typeOf.object("directionToPoint",t),d.Check.defined("positions",i),S.defined(r)||(r=new b.Cartesian3);for(var a=E(e,t),n=0,o=0,s=i.length;o<s;++o){var c=T(e,i[o],a);if(c<0)return;n=Math.max(n,c)}return P(a,n,r)}var p=new b.Cartesian3;function h(e,t,i,r,a,n){d.Check.typeOf.object("directionToPoint",t),d.Check.defined("vertices",i),d.Check.typeOf.number("stride",r),S.defined(n)||(n=new b.Cartesian3),r=S.defaultValue(r,3),a=S.defaultValue(a,b.Cartesian3.ZERO);for(var o=E(e,t),s=0,c=0,m=i.length;c<m;c+=r){p.x=i[c]+a.x,p.y=i[c+1]+a.y,p.z=i[c+2]+a.z;var u=T(e,p,o);if(u<0)return;s=Math.max(s,u)}return P(o,s,n)}function y(e,t,i){var r=t,a=i,n=b.Cartesian3.subtract(e,r,c),o=-b.Cartesian3.dot(n,r);return!(a<0?0<o:a<o&&o*o/b.Cartesian3.magnitudeSquared(n)>a)}var v=new b.Cartesian3,M=new b.Cartesian3;function T(e,t,i){var r=e.transformPositionToScaledSpace(t,v),a=b.Cartesian3.magnitudeSquared(r),n=Math.sqrt(a),o=b.Cartesian3.divideByScalar(r,n,M);a=Math.max(1,a);var s=1/(n=Math.max(1,n));return 1/(b.Cartesian3.dot(o,i)*s-b.Cartesian3.magnitude(b.Cartesian3.cross(o,i,o))*(Math.sqrt(a-1)*s))}function P(e,t,i){if(!(t<=0||t===1/0||t!=t))return b.Cartesian3.multiplyByScalar(e,t,i)}var i=new b.Cartesian3;function E(e,t){return b.Cartesian3.equals(t,b.Cartesian3.ZERO)?t:(e.transformPositionToScaledSpace(t,i),b.Cartesian3.normalize(i,i))}var z=Object.freeze({NONE:0,BITS12:1}),N=new b.Cartesian3,I=new b.Cartesian3,B=new C.Cartesian2,_=new g.Matrix4,w=new g.Matrix4,A=Math.pow(2,12);function r(e,t,i,r,a,n){var o,s,c,m=z.NONE;if(S.defined(e)&&S.defined(t)&&S.defined(i)&&S.defined(r)){var u=e.minimum,d=e.maximum,l=b.Cartesian3.subtract(d,u,I),p=i-t;m=Math.max(b.Cartesian3.maximumComponent(l),p)<A-1?z.BITS12:z.NONE,m=z.NONE,o=e.center,s=g.Matrix4.inverseTransformation(r,new g.Matrix4);var h=b.Cartesian3.negate(u,N);g.Matrix4.multiply(g.Matrix4.fromTranslation(h,_),s,s);var f=N;f.x=1/l.x,f.y=1/l.y,f.z=1/l.z,g.Matrix4.multiply(g.Matrix4.fromScale(f,_),s,s),c=g.Matrix4.clone(r),g.Matrix4.setTranslation(c,b.Cartesian3.ZERO,c),r=g.Matrix4.clone(r,new g.Matrix4);var C=g.Matrix4.fromTranslation(u,_),x=g.Matrix4.fromScale(l,w),y=g.Matrix4.multiply(C,x,_);g.Matrix4.multiply(r,y,r),g.Matrix4.multiply(c,y,c)}this.quantization=m,this.minimumHeight=t,this.maximumHeight=i,this.center=o,this.toScaledENU=s,this.fromScaledENU=r,this.matrix=c,this.hasVertexNormals=a,this.hasWebMercatorT=S.defaultValue(n,!1)}r.prototype.encode=function(e,t,i,r,a,n,o){var s=r.x,c=r.y;if(this.quantization===z.BITS12){(i=g.Matrix4.multiplyByPoint(this.toScaledENU,i,N)).x=f.CesiumMath.clamp(i.x,0,1),i.y=f.CesiumMath.clamp(i.y,0,1),i.z=f.CesiumMath.clamp(i.z,0,1);var m=this.maximumHeight-this.minimumHeight,u=f.CesiumMath.clamp((a-this.minimumHeight)/m,0,1);C.Cartesian2.fromElements(i.x,i.y,B);var d=x.AttributeCompression.compressTextureCoordinates(B);C.Cartesian2.fromElements(i.z,u,B);var l=x.AttributeCompression.compressTextureCoordinates(B);C.Cartesian2.fromElements(s,c,B);var p=x.AttributeCompression.compressTextureCoordinates(B);if(e[t++]=d,e[t++]=l,e[t++]=p,this.hasWebMercatorT){C.Cartesian2.fromElements(o,0,B);var h=x.AttributeCompression.compressTextureCoordinates(B);e[t++]=h}}else b.Cartesian3.subtract(i,this.center,N),e[t++]=N.x,e[t++]=N.y,e[t++]=N.z,e[t++]=a,e[t++]=s,e[t++]=c,this.hasWebMercatorT&&(e[t++]=o);return this.hasVertexNormals&&(e[t++]=x.AttributeCompression.octPackFloat(n)),t},r.prototype.decodePosition=function(e,t,i){if(S.defined(i)||(i=new b.Cartesian3),t*=this.getStride(),this.quantization!==z.BITS12)return i.x=e[t],i.y=e[t+1],i.z=e[t+2],b.Cartesian3.add(i,this.center,i);var r=x.AttributeCompression.decompressTextureCoordinates(e[t],B);i.x=r.x,i.y=r.y;var a=x.AttributeCompression.decompressTextureCoordinates(e[t+1],B);return i.z=a.x,g.Matrix4.multiplyByPoint(this.fromScaledENU,i,i)},r.prototype.decodeTextureCoordinates=function(e,t,i){return S.defined(i)||(i=new C.Cartesian2),t*=this.getStride(),this.quantization===z.BITS12?x.AttributeCompression.decompressTextureCoordinates(e[t+2],i):C.Cartesian2.fromElements(e[t+4],e[t+5],i)},r.prototype.decodeHeight=function(e,t){return t*=this.getStride(),this.quantization!==z.BITS12?e[t+3]:x.AttributeCompression.decompressTextureCoordinates(e[t+1],B).y*(this.maximumHeight-this.minimumHeight)+this.minimumHeight},r.prototype.decodeWebMercatorT=function(e,t){return t*=this.getStride(),this.quantization===z.BITS12?x.AttributeCompression.decompressTextureCoordinates(e[t+3],B).x:e[t+6]},r.prototype.getOctEncodedNormal=function(e,t,i){var r=e[t=(t+1)*this.getStride()-1]/256,a=Math.floor(r),n=256*(r-a);return C.Cartesian2.fromElements(a,n,i)},r.prototype.getStride=function(){var e;switch(this.quantization){case z.BITS12:e=3;break;default:e=6}return this.hasWebMercatorT&&++e,this.hasVertexNormals&&++e,e};var q={position3DAndHeight:0,textureCoordAndEncodedNormals:1},H={compressed0:0,compressed1:1};r.prototype.getAttributes=function(e){var t,i=s.ComponentDatatype.FLOAT,r=s.ComponentDatatype.getSizeInBytes(i);if(this.quantization===z.NONE){var a=2;return this.hasWebMercatorT&&++a,this.hasVertexNormals&&++a,[{index:q.position3DAndHeight,vertexBuffer:e,componentDatatype:i,componentsPerAttribute:4,offsetInBytes:0,strideInBytes:t=(4+a)*r},{index:q.textureCoordAndEncodedNormals,vertexBuffer:e,componentDatatype:i,componentsPerAttribute:a,offsetInBytes:4*r,strideInBytes:t}]}var n=3,o=0;return(this.hasWebMercatorT||this.hasVertexNormals)&&++n,this.hasWebMercatorT&&this.hasVertexNormals?[{index:H.compressed0,vertexBuffer:e,componentDatatype:i,componentsPerAttribute:n,offsetInBytes:0,strideInBytes:t=(n+ ++o)*r},{index:H.compressed1,vertexBuffer:e,componentDatatype:i,componentsPerAttribute:o,offsetInBytes:n*r,strideInBytes:t}]:[{index:H.compressed0,vertexBuffer:e,componentDatatype:i,componentsPerAttribute:n}]},r.prototype.getAttributeLocations=function(){return this.quantization===z.NONE?q:H},r.clone=function(e,t){return S.defined(t)||(t=new r),t.quantization=e.quantization,t.minimumHeight=e.minimumHeight,t.maximumHeight=e.maximumHeight,t.center=b.Cartesian3.clone(e.center),t.toScaledENU=g.Matrix4.clone(e.toScaledENU),t.fromScaledENU=g.Matrix4.clone(e.fromScaledENU),t.matrix=g.Matrix4.clone(e.matrix),t.hasVertexNormals=e.hasVertexNormals,t.hasWebMercatorT=e.hasWebMercatorT,t},e.EllipsoidalOccluder=t,e.TerrainEncoding=r});