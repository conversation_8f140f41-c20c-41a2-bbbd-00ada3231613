/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./Transforms-1509c877","./GeometryAttributes-aacecde6","./GeometryPipeline-8e55e413","./IndexDatatype-9435b55f","./arrayRemoveDuplicates-2869246d","./ArcType-66bc286a","./EllipsoidRhumbLine-6ca4b1e6","./PolygonPipeline-cc78b34e"],function(e,P,x,_,f,y,I,E,A,g,L,d,M,S,G,v,D){function N(){this._array=[],this._offset=0,this._length=0}Object.defineProperties(N.prototype,{length:{get:function(){return this._length}}}),N.prototype.enqueue=function(e){this._array.push(e),this._length++},N.prototype.dequeue=function(){if(0!==this._length){var e=this._array,t=this._offset,r=e[t];return e[t]=void 0,10<++t&&2*t>e.length&&(this._array=e.slice(t),t=0),this._offset=t,this._length--,r}},N.prototype.peek=function(){if(0!==this._length)return this._array[this._offset]},N.prototype.contains=function(e){return-1!==this._array.indexOf(e)},N.prototype.clear=function(){this._array.length=this._offset=this._length=0},N.prototype.sort=function(e){0<this._offset&&(this._array=this._array.slice(this._offset),this._offset=0),this._array.sort(e)};var R={computeHierarchyPackedLength:function(e){for(var t=0,r=[e];0<r.length;){var i=r.pop();if(P.defined(i)){t+=2;var a=i.positions,n=i.holes;if(P.defined(a)&&(t+=a.length*_.Cartesian3.packedLength),P.defined(n))for(var o=n.length,s=0;s<o;++s)r.push(n[s])}}return t},packPolygonHierarchy:function(e,t,r){for(var i=[e];0<i.length;){var a=i.pop();if(P.defined(a)){var n=a.positions,o=a.holes;if(t[r++]=P.defined(n)?n.length:0,t[r++]=P.defined(o)?o.length:0,P.defined(n))for(var s=n.length,u=0;u<s;++u,r+=3)_.Cartesian3.pack(n[u],t,r);if(P.defined(o))for(var l=o.length,h=0;h<l;++h)i.push(o[h])}}return r},unpackPolygonHierarchy:function(e,t){for(var r=e[t++],i=e[t++],a=new Array(r),n=0<i?new Array(i):void 0,o=0;o<r;++o,t+=_.Cartesian3.packedLength)a[o]=_.Cartesian3.unpack(e,t);for(var s=0;s<i;++s)n[s]=R.unpackPolygonHierarchy(e,t),t=n[s].startingIndex,delete n[s].startingIndex;return{positions:a,holes:n,startingIndex:t}}},m=new _.Cartesian3;R.subdivideLineCount=function(e,t,r){var i=_.Cartesian3.distance(e,t)/r,a=Math.max(0,Math.ceil(x.CesiumMath.log2(i)));return Math.pow(2,a)};var C=new _.Cartographic,b=new _.Cartographic,w=new _.Cartographic,T=new _.Cartesian3;R.subdivideRhumbLineCount=function(e,t,r,i){var a=e.cartesianToCartographic(t,C),n=e.cartesianToCartographic(r,b),o=new v.EllipsoidRhumbLine(a,n,e).surfaceDistance/i,s=Math.max(0,Math.ceil(x.CesiumMath.log2(o)));return Math.pow(2,s)},R.subdivideLine=function(e,t,r,i){var a=R.subdivideLineCount(e,t,r),n=_.Cartesian3.distance(e,t),o=n/a;P.defined(i)||(i=[]);var s=i;s.length=3*a;for(var u,l,h,c,f=0,p=0;p<a;p++){var d=(u=e,l=t,h=p*o,c=n,_.Cartesian3.subtract(l,u,m),_.Cartesian3.multiplyByScalar(m,h/c,m),_.Cartesian3.add(u,m,m),[m.x,m.y,m.z]);s[f++]=d[0],s[f++]=d[1],s[f++]=d[2]}return s},R.subdivideRhumbLine=function(e,t,r,i,a){var n=e.cartesianToCartographic(t,C),o=e.cartesianToCartographic(r,b),s=new v.EllipsoidRhumbLine(n,o,e),u=s.surfaceDistance/i,l=Math.max(0,Math.ceil(x.CesiumMath.log2(u))),h=Math.pow(2,l),c=s.surfaceDistance/h;P.defined(a)||(a=[]);var f=a;f.length=3*h;for(var p=0,d=0;d<h;d++){var y=s.interpolateUsingSurfaceDistance(d*c,w),g=e.cartographicToCartesian(y,T);f[p++]=g.x,f[p++]=g.y,f[p++]=g.z}return f};var p=new _.Cartesian3,O=new _.Cartesian3,q=new _.Cartesian3,B=new _.Cartesian3;R.scaleToGeodeticHeightExtruded=function(e,t,r,i,a){i=P.defaultValue(i,f.Ellipsoid.WGS84);var n=p,o=O,s=q,u=B;if(P.defined(e)&&P.defined(e.attributes)&&P.defined(e.attributes.position))for(var l=e.attributes.position.values,h=l.length/2,c=0;c<h;c+=3)_.Cartesian3.fromArray(l,c,s),i.geodeticSurfaceNormal(s,n),u=i.scaleToGeodeticSurface(s,u),o=_.Cartesian3.multiplyByScalar(n,r,o),o=_.Cartesian3.add(u,o,o),l[c+h]=o.x,l[c+1+h]=o.y,l[c+2+h]=o.z,a&&(u=_.Cartesian3.clone(s,u)),o=_.Cartesian3.multiplyByScalar(n,t,o),o=_.Cartesian3.add(u,o,o),l[c]=o.x,l[c+1]=o.y,l[c+2]=o.z;return e},R.polygonOutlinesFromHierarchy=function(e,t,r){var i,a,n,o=[],s=new N;for(s.enqueue(e);0!==s.length;){var u=s.dequeue(),l=u.positions;if(t)for(n=l.length,i=0;i<n;i++)r.scaleToGeodeticSurface(l[i],l[i]);if(!((l=S.arrayRemoveDuplicates(l,_.Cartesian3.equalsEpsilon,!0)).length<3)){var h=u.holes?u.holes.length:0;for(i=0;i<h;i++){var c=u.holes[i],f=c.positions;if(t)for(n=f.length,a=0;a<n;++a)r.scaleToGeodeticSurface(f[a],f[a]);if(!((f=S.arrayRemoveDuplicates(f,_.Cartesian3.equalsEpsilon,!0)).length<3)){o.push(f);var p=0;for(P.defined(c.holes)&&(p=c.holes.length),a=0;a<p;a++)s.enqueue(c.holes[a])}}o.push(l)}}return o};var H=new _.Cartesian3(6378137,6378137,6378137);R.polygonsFromHierarchy=function(e,t,r,i){var a=[],n=[],o=new N;for(o.enqueue(e);0!==o.length;){var s,u,l,h=o.dequeue(),c=h.positions,f=h.holes,p=c.slice();if(r)for(u=c.length,s=0;s<u;s++)i.scaleToGeodeticSurface(c[s],p[s]);if(P.defined(i)&&!_.Cartesian3.equals(i._radii,H)&&(l=x.CesiumMath.EPSILON7),!((c=S.arrayRemoveDuplicates(p,_.Cartesian3.equalsEpsilon,!0,l)).length<3)){var d=t(c);if(P.defined(d)){var y=[],g=D.PolygonPipeline.computeWindingOrder2D(d);g===D.WindingOrder.CLOCKWISE&&(d.reverse(),c=c.slice().reverse());var v,m=c.slice(),C=P.defined(f)?f.length:0,b=[];for(s=0;s<C;s++){var w=f[s],T=w.positions;if(r)for(u=T.length,v=0;v<u;++v)i.scaleToGeodeticSurface(T[v],T[v]);if(!((T=S.arrayRemoveDuplicates(T,_.Cartesian3.equalsEpsilon,!0,x.CesiumMath.EPSILON7)).length<3)){var I=t(T);if(P.defined(I)){(g=D.PolygonPipeline.computeWindingOrder2D(I))===D.WindingOrder.CLOCKWISE&&(I.reverse(),T=T.slice().reverse()),b.push(T),y.push(m.length),m=m.concat(T),d=d.concat(I);var E=0;for(P.defined(w.holes)&&(E=w.holes.length),v=0;v<E;v++)o.enqueue(w.holes[v])}}}a.push({outerRing:c,holes:b}),n.push({positions:m,positions2D:d,holes:y})}}}return{hierarchy:a,polygons:n}};var k=new f.Cartesian2,z=new _.Cartesian3,W=new g.Quaternion,F=new y.Matrix3;R.computeBoundingRectangle=function(e,t,r,i,a){for(var n=g.Quaternion.fromAxisAngle(e,i,W),o=y.Matrix3.fromQuaternion(n,F),s=Number.POSITIVE_INFINITY,u=Number.NEGATIVE_INFINITY,l=Number.POSITIVE_INFINITY,h=Number.NEGATIVE_INFINITY,c=r.length,f=0;f<c;++f){var p=_.Cartesian3.clone(r[f],z);y.Matrix3.multiplyByVector(o,p,p);var d=t(p,k);P.defined(d)&&(s=Math.min(s,d.x),u=Math.max(u,d.x),l=Math.min(l,d.y),h=Math.max(h,d.y))}return a.x=s,a.y=l,a.width=u-s,a.height=h-l,a},R.createGeometryFromPositions=function(e,t,r,i,a,n){var o=D.PolygonPipeline.triangulate(t.positions2D,t.holes);o.length<3&&(o=[0,1,2]);var s=t.positions;if(i){for(var u=s.length,l=new Array(3*u),h=0,c=0;c<u;c++){var f=s[c];l[h++]=f.x,l[h++]=f.y,l[h++]=f.z}var p=new E.Geometry({attributes:{position:new E.GeometryAttribute({componentDatatype:I.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:l})},indices:o,primitiveType:A.PrimitiveType.TRIANGLES});return a.normal?d.GeometryPipeline.computeNormal(p):p}return n===G.ArcType.GEODESIC?D.PolygonPipeline.computeSubdivision(e,s,o,r):n===G.ArcType.RHUMB?D.PolygonPipeline.computeRhumbLineSubdivision(e,s,o,r):void 0};var U=[],V=new _.Cartesian3,Y=new _.Cartesian3;R.computeWallGeometry=function(e,t,r,i,a){var n,o,s,u,l,h=e.length,c=0;if(i)for(o=3*h*2,n=new Array(2*o),s=0;s<h;s++)u=e[s],l=e[(s+1)%h],n[c]=n[c+o]=u.x,n[++c]=n[c+o]=u.y,n[++c]=n[c+o]=u.z,n[++c]=n[c+o]=l.x,n[++c]=n[c+o]=l.y,n[++c]=n[c+o]=l.z,++c;else{var f=x.CesiumMath.chordLength(r,t.maximumRadius),p=0;if(a===G.ArcType.GEODESIC)for(s=0;s<h;s++)p+=R.subdivideLineCount(e[s],e[(s+1)%h],f);else if(a===G.ArcType.RHUMB)for(s=0;s<h;s++)p+=R.subdivideRhumbLineCount(t,e[s],e[(s+1)%h],f);for(o=3*(p+h),n=new Array(2*o),s=0;s<h;s++){var d;u=e[s],l=e[(s+1)%h],a===G.ArcType.GEODESIC?d=R.subdivideLine(u,l,f,U):a===G.ArcType.RHUMB&&(d=R.subdivideRhumbLine(t,u,l,f,U));for(var y=d.length,g=0;g<y;++g,++c)n[c]=d[g],n[c+o]=d[g];n[c]=l.x,n[c+o]=l.x,n[++c]=l.y,n[c+o]=l.y,n[++c]=l.z,n[c+o]=l.z,++c}}h=n.length;var v=M.IndexDatatype.createTypedArray(h/3,h-6*e.length),m=0;for(h/=6,s=0;s<h;s++){var C=s,b=C+1,w=C+h,T=w+1;u=_.Cartesian3.fromArray(n,3*C,V),l=_.Cartesian3.fromArray(n,3*b,Y),_.Cartesian3.equalsEpsilon(u,l,x.CesiumMath.EPSILON10,x.CesiumMath.EPSILON10)||(v[m++]=C,v[m++]=w,v[m++]=b,v[m++]=b,v[m++]=w,v[m++]=T)}return new E.Geometry({attributes:new L.GeometryAttributes({position:new E.GeometryAttribute({componentDatatype:I.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:n})}),indices:v,primitiveType:A.PrimitiveType.TRIANGLES})},e.PolygonGeometryLibrary=R});