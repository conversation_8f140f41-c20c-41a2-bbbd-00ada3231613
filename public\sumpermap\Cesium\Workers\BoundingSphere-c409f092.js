/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e"],function(e,V,J,H,I,l,Q,X){function t(e){this._ellipsoid=V.defaultValue(e,l.Ellipsoid.WGS84),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(t.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}}),t.prototype.project=function(e,t){var a=this._semimajorAxis,n=e.longitude*a,r=e.latitude*a,i=e.height;return V.defined(t)?(t.x=n,t.y=r,t.z=i,t):new I.Cartesian3(n,r,i)},t.prototype.unproject=function(e,t){if(!V.defined(e))throw new J.DeveloperError("cartesian is required");var a=this._oneOverSemimajorAxis,n=e.x*a,r=e.y*a,i=e.z;return V.defined(t)?(t.longitude=n,t.latitude=r,t.height=i,t):new I.Cartographic(n,r,i)};var c=Object.freeze({OUTSIDE:-1,INTERSECTING:0,INSIDE:1});function o(e,t){this.start=V.defaultValue(e,0),this.stop=V.defaultValue(t,0)}function F(e,t,a,n,r,i,c,o,u){this[0]=V.defaultValue(e,0),this[1]=V.defaultValue(n,0),this[2]=V.defaultValue(c,0),this[3]=V.defaultValue(t,0),this[4]=V.defaultValue(r,0),this[5]=V.defaultValue(o,0),this[6]=V.defaultValue(a,0),this[7]=V.defaultValue(i,0),this[8]=V.defaultValue(u,0)}F.packedLength=9,F.pack=function(e,t,a){return J.Check.typeOf.object("value",e),J.Check.defined("array",t),a=V.defaultValue(a,0),t[a++]=e[0],t[a++]=e[1],t[a++]=e[2],t[a++]=e[3],t[a++]=e[4],t[a++]=e[5],t[a++]=e[6],t[a++]=e[7],t[a++]=e[8],t},F.unpack=function(e,t,a){return J.Check.defined("array",e),t=V.defaultValue(t,0),V.defined(a)||(a=new F),a[0]=e[t++],a[1]=e[t++],a[2]=e[t++],a[3]=e[t++],a[4]=e[t++],a[5]=e[t++],a[6]=e[t++],a[7]=e[t++],a[8]=e[t++],a},F.clone=function(e,t){if(V.defined(e))return V.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t):new F(e[0],e[3],e[6],e[1],e[4],e[7],e[2],e[5],e[8])},F.fromArray=function(e,t,a){return J.Check.defined("array",e),t=V.defaultValue(t,0),V.defined(a)||(a=new F),a[0]=e[t],a[1]=e[t+1],a[2]=e[t+2],a[3]=e[t+3],a[4]=e[t+4],a[5]=e[t+5],a[6]=e[t+6],a[7]=e[t+7],a[8]=e[t+8],a},F.fromColumnMajorArray=function(e,t){return J.Check.defined("values",e),F.clone(e,t)},F.fromRowMajorArray=function(e,t){return J.Check.defined("values",e),V.defined(t)?(t[0]=e[0],t[1]=e[3],t[2]=e[6],t[3]=e[1],t[4]=e[4],t[5]=e[7],t[6]=e[2],t[7]=e[5],t[8]=e[8],t):new F(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8])},F.fromQuaternion=function(e,t){J.Check.typeOf.object("quaternion",e);var a=e.x*e.x,n=e.x*e.y,r=e.x*e.z,i=e.x*e.w,c=e.y*e.y,o=e.y*e.z,u=e.y*e.w,s=e.z*e.z,f=e.z*e.w,l=e.w*e.w,C=a-c-s+l,h=2*(n-f),d=2*(r+u),y=2*(n+f),p=-a+c-s+l,O=2*(o-i),m=2*(r-u),b=2*(o+i),k=-a-c+s+l;return V.defined(t)?(t[0]=C,t[1]=y,t[2]=m,t[3]=h,t[4]=p,t[5]=b,t[6]=d,t[7]=O,t[8]=k,t):new F(C,h,d,y,p,O,m,b,k)},F.fromHeadingPitchRoll=function(e,t){J.Check.typeOf.object("headingPitchRoll",e);var a=Math.cos(-e.pitch),n=Math.cos(-e.heading),r=Math.cos(e.roll),i=Math.sin(-e.pitch),c=Math.sin(-e.heading),o=Math.sin(e.roll),u=a*n,s=-r*c+o*i*n,f=o*c+r*i*n,l=a*c,C=r*n+o*i*c,h=-o*n+r*i*c,d=-i,y=o*a,p=r*a;return V.defined(t)?(t[0]=u,t[1]=l,t[2]=d,t[3]=s,t[4]=C,t[5]=y,t[6]=f,t[7]=h,t[8]=p,t):new F(u,s,f,l,C,h,d,y,p)},F.fromScale=function(e,t){return J.Check.typeOf.object("scale",e),V.defined(t)?(t[0]=e.x,t[1]=0,t[2]=0,t[3]=0,t[4]=e.y,t[5]=0,t[6]=0,t[7]=0,t[8]=e.z,t):new F(e.x,0,0,0,e.y,0,0,0,e.z)},F.fromUniformScale=function(e,t){return J.Check.typeOf.number("scale",e),V.defined(t)?(t[0]=e,t[1]=0,t[2]=0,t[3]=0,t[4]=e,t[5]=0,t[6]=0,t[7]=0,t[8]=e,t):new F(e,0,0,0,e,0,0,0,e)},F.fromCrossProduct=function(e,t){return J.Check.typeOf.object("vector",e),V.defined(t)?(t[0]=0,t[1]=e.z,t[2]=-e.y,t[3]=-e.z,t[4]=0,t[5]=e.x,t[6]=e.y,t[7]=-e.x,t[8]=0,t):new F(0,-e.z,e.y,e.z,0,-e.x,-e.y,e.x,0)},F.fromRotationX=function(e,t){J.Check.typeOf.number("angle",e);var a=Math.cos(e),n=Math.sin(e);return V.defined(t)?(t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=a,t[5]=n,t[6]=0,t[7]=-n,t[8]=a,t):new F(1,0,0,0,a,-n,0,n,a)},F.fromRotationY=function(e,t){J.Check.typeOf.number("angle",e);var a=Math.cos(e),n=Math.sin(e);return V.defined(t)?(t[0]=a,t[1]=0,t[2]=-n,t[3]=0,t[4]=1,t[5]=0,t[6]=n,t[7]=0,t[8]=a,t):new F(a,0,n,0,1,0,-n,0,a)},F.fromRotationZ=function(e,t){J.Check.typeOf.number("angle",e);var a=Math.cos(e),n=Math.sin(e);return V.defined(t)?(t[0]=a,t[1]=n,t[2]=0,t[3]=-n,t[4]=a,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t):new F(a,-n,0,n,a,0,0,0,1)},F.toArray=function(e,t){return J.Check.typeOf.object("matrix",e),V.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t):[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8]]},F.getElementIndex=function(e,t){return J.Check.typeOf.number.greaterThanOrEquals("row",t,0),J.Check.typeOf.number.lessThanOrEquals("row",t,2),J.Check.typeOf.number.greaterThanOrEquals("column",e,0),J.Check.typeOf.number.lessThanOrEquals("column",e,2),3*e+t},F.getColumn=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.number.greaterThanOrEquals("index",t,0),J.Check.typeOf.number.lessThanOrEquals("index",t,2),J.Check.typeOf.object("result",a);var n=3*t,r=e[n],i=e[n+1],c=e[n+2];return a.x=r,a.y=i,a.z=c,a},F.setColumn=function(e,t,a,n){J.Check.typeOf.object("matrix",e),J.Check.typeOf.number.greaterThanOrEquals("index",t,0),J.Check.typeOf.number.lessThanOrEquals("index",t,2),J.Check.typeOf.object("cartesian",a),J.Check.typeOf.object("result",n);var r=3*t;return(n=F.clone(e,n))[r]=a.x,n[r+1]=a.y,n[r+2]=a.z,n},F.getRow=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.number.greaterThanOrEquals("index",t,0),J.Check.typeOf.number.lessThanOrEquals("index",t,2),J.Check.typeOf.object("result",a);var n=e[t],r=e[t+3],i=e[t+6];return a.x=n,a.y=r,a.z=i,a},F.setRow=function(e,t,a,n){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.number.greaterThanOrEquals("index",t,0),J.Check.typeOf.number.lessThanOrEquals("index",t,2),J.Check.typeOf.object("cartesian",a),J.Check.typeOf.object("result",n),(n=F.clone(e,n))[t]=a.x,n[t+3]=a.y,n[t+6]=a.z,n};var a=new I.Cartesian3;F.getScale=function(e,t){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t),t.x=I.Cartesian3.magnitude(I.Cartesian3.fromElements(e[0],e[1],e[2],a)),t.y=I.Cartesian3.magnitude(I.Cartesian3.fromElements(e[3],e[4],e[5],a)),t.z=I.Cartesian3.magnitude(I.Cartesian3.fromElements(e[6],e[7],e[8],a)),t};var n=new I.Cartesian3;F.getMaximumScale=function(e){return F.getScale(e,n),I.Cartesian3.maximumComponent(n)},F.multiply=function(e,t,a){J.Check.typeOf.object("left",e),J.Check.typeOf.object("right",t),J.Check.typeOf.object("result",a);var n=e[0]*t[0]+e[3]*t[1]+e[6]*t[2],r=e[1]*t[0]+e[4]*t[1]+e[7]*t[2],i=e[2]*t[0]+e[5]*t[1]+e[8]*t[2],c=e[0]*t[3]+e[3]*t[4]+e[6]*t[5],o=e[1]*t[3]+e[4]*t[4]+e[7]*t[5],u=e[2]*t[3]+e[5]*t[4]+e[8]*t[5],s=e[0]*t[6]+e[3]*t[7]+e[6]*t[8],f=e[1]*t[6]+e[4]*t[7]+e[7]*t[8],l=e[2]*t[6]+e[5]*t[7]+e[8]*t[8];return a[0]=n,a[1]=r,a[2]=i,a[3]=c,a[4]=o,a[5]=u,a[6]=s,a[7]=f,a[8]=l,a},F.add=function(e,t,a){return J.Check.typeOf.object("left",e),J.Check.typeOf.object("right",t),J.Check.typeOf.object("result",a),a[0]=e[0]+t[0],a[1]=e[1]+t[1],a[2]=e[2]+t[2],a[3]=e[3]+t[3],a[4]=e[4]+t[4],a[5]=e[5]+t[5],a[6]=e[6]+t[6],a[7]=e[7]+t[7],a[8]=e[8]+t[8],a},F.subtract=function(e,t,a){return J.Check.typeOf.object("left",e),J.Check.typeOf.object("right",t),J.Check.typeOf.object("result",a),a[0]=e[0]-t[0],a[1]=e[1]-t[1],a[2]=e[2]-t[2],a[3]=e[3]-t[3],a[4]=e[4]-t[4],a[5]=e[5]-t[5],a[6]=e[6]-t[6],a[7]=e[7]-t[7],a[8]=e[8]-t[8],a},F.multiplyByVector=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("cartesian",t),J.Check.typeOf.object("result",a);var n=t.x,r=t.y,i=t.z,c=e[0]*n+e[3]*r+e[6]*i,o=e[1]*n+e[4]*r+e[7]*i,u=e[2]*n+e[5]*r+e[8]*i;return a.x=c,a.y=o,a.z=u,a},F.multiplyByScalar=function(e,t,a){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.number("scalar",t),J.Check.typeOf.object("result",a),a[0]=e[0]*t,a[1]=e[1]*t,a[2]=e[2]*t,a[3]=e[3]*t,a[4]=e[4]*t,a[5]=e[5]*t,a[6]=e[6]*t,a[7]=e[7]*t,a[8]=e[8]*t,a},F.multiplyByScale=function(e,t,a){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("scale",t),J.Check.typeOf.object("result",a),a[0]=e[0]*t.x,a[1]=e[1]*t.x,a[2]=e[2]*t.x,a[3]=e[3]*t.y,a[4]=e[4]*t.y,a[5]=e[5]*t.y,a[6]=e[6]*t.z,a[7]=e[7]*t.z,a[8]=e[8]*t.z,a},F.negate=function(e,t){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t),t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t[4]=-e[4],t[5]=-e[5],t[6]=-e[6],t[7]=-e[7],t[8]=-e[8],t},F.transpose=function(e,t){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t);var a=e[0],n=e[3],r=e[6],i=e[1],c=e[4],o=e[7],u=e[2],s=e[5],f=e[8];return t[0]=a,t[1]=n,t[2]=r,t[3]=i,t[4]=c,t[5]=o,t[6]=u,t[7]=s,t[8]=f,t};var r=new I.Cartesian3(1,1,1);F.getRotation=function(e,t){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t);var a=I.Cartesian3.divideComponents(r,F.getScale(e,n),n);return t=F.multiplyByScale(e,a,t)};var h=[1,0,0],d=[2,2,1];function u(e){for(var t=0,a=0;a<3;++a){var n=e[F.getElementIndex(d[a],h[a])];t+=2*n*n}return Math.sqrt(t)}function s(e,t){for(var a=H.CesiumMath.EPSILON15,n=0,r=1,i=0;i<3;++i){var c=Math.abs(e[F.getElementIndex(d[i],h[i])]);n<c&&(r=i,n=c)}var o=1,u=0,s=h[r],f=d[r];if(Math.abs(e[F.getElementIndex(f,s)])>a){var l,C=(e[F.getElementIndex(f,f)]-e[F.getElementIndex(s,s)])/2/e[F.getElementIndex(f,s)];u=(l=C<0?-1/(-C+Math.sqrt(1+C*C)):1/(C+Math.sqrt(1+C*C)))*(o=1/Math.sqrt(1+l*l))}return(t=F.clone(F.IDENTITY,t))[F.getElementIndex(s,s)]=t[F.getElementIndex(f,f)]=o,t[F.getElementIndex(f,s)]=u,t[F.getElementIndex(s,f)]=-u,t}var f=new F,C=new F;function K(e,t,a,n,r,i,c,o,u,s,f,l,C,h,d,y){this[0]=V.defaultValue(e,0),this[1]=V.defaultValue(r,0),this[2]=V.defaultValue(u,0),this[3]=V.defaultValue(C,0),this[4]=V.defaultValue(t,0),this[5]=V.defaultValue(i,0),this[6]=V.defaultValue(s,0),this[7]=V.defaultValue(h,0),this[8]=V.defaultValue(a,0),this[9]=V.defaultValue(c,0),this[10]=V.defaultValue(f,0),this[11]=V.defaultValue(d,0),this[12]=V.defaultValue(n,0),this[13]=V.defaultValue(o,0),this[14]=V.defaultValue(l,0),this[15]=V.defaultValue(y,0)}F.computeEigenDecomposition=function(e,t){J.Check.typeOf.object("matrix",e);var a=H.CesiumMath.EPSILON20,n=0,r=0;V.defined(t)||(t={});for(var i=t.unitary=F.clone(F.IDENTITY,t.unitary),c=t.diagonal=F.clone(e,t.diagonal),o=a*function(e){for(var t=0,a=0;a<9;++a){var n=e[a];t+=n*n}return Math.sqrt(t)}(c);r<10&&u(c)>o;)s(c,f),F.transpose(f,C),F.multiply(c,f,c),F.multiply(C,c,c),F.multiply(i,f,i),2<++n&&(++r,n=0);return t},F.abs=function(e,t){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t),t[0]=Math.abs(e[0]),t[1]=Math.abs(e[1]),t[2]=Math.abs(e[2]),t[3]=Math.abs(e[3]),t[4]=Math.abs(e[4]),t[5]=Math.abs(e[5]),t[6]=Math.abs(e[6]),t[7]=Math.abs(e[7]),t[8]=Math.abs(e[8]),t},F.determinant=function(e){J.Check.typeOf.object("matrix",e);var t=e[0],a=e[3],n=e[6],r=e[1],i=e[4],c=e[7],o=e[2],u=e[5],s=e[8];return t*(i*s-u*c)+r*(u*n-a*s)+o*(a*c-i*n)},F.inverse=function(e,t){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t);var a=e[0],n=e[1],r=e[2],i=e[3],c=e[4],o=e[5],u=e[6],s=e[7],f=e[8],l=F.determinant(e);if(Math.abs(l)<=H.CesiumMath.EPSILON15)throw new J.DeveloperError("matrix is not invertible");return t[0]=c*f-s*o,t[1]=s*r-n*f,t[2]=n*o-c*r,t[3]=u*o-i*f,t[4]=a*f-u*r,t[5]=i*r-a*o,t[6]=i*s-u*c,t[7]=u*n-a*s,t[8]=a*c-i*n,F.multiplyByScalar(t,1/l,t)},F.equals=function(e,t){return e===t||V.defined(e)&&V.defined(t)&&e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]&&e[4]===t[4]&&e[5]===t[5]&&e[6]===t[6]&&e[7]===t[7]&&e[8]===t[8]},F.equalsEpsilon=function(e,t,a){return J.Check.typeOf.number("epsilon",a),e===t||V.defined(e)&&V.defined(t)&&Math.abs(e[0]-t[0])<=a&&Math.abs(e[1]-t[1])<=a&&Math.abs(e[2]-t[2])<=a&&Math.abs(e[3]-t[3])<=a&&Math.abs(e[4]-t[4])<=a&&Math.abs(e[5]-t[5])<=a&&Math.abs(e[6]-t[6])<=a&&Math.abs(e[7]-t[7])<=a&&Math.abs(e[8]-t[8])<=a},F.IDENTITY=Object.freeze(new F(1,0,0,0,1,0,0,0,1)),F.ZERO=Object.freeze(new F(0,0,0,0,0,0,0,0,0)),F.COLUMN0ROW0=0,F.COLUMN0ROW1=1,F.COLUMN0ROW2=2,F.COLUMN1ROW0=3,F.COLUMN1ROW1=4,F.COLUMN1ROW2=5,F.COLUMN2ROW0=6,F.COLUMN2ROW1=7,F.COLUMN2ROW2=8,Object.defineProperties(F.prototype,{length:{get:function(){return F.packedLength}}}),F.prototype.clone=function(e){return F.clone(this,e)},F.prototype.equals=function(e){return F.equals(this,e)},F.equalsArray=function(e,t,a){return e[0]===t[a]&&e[1]===t[a+1]&&e[2]===t[a+2]&&e[3]===t[a+3]&&e[4]===t[a+4]&&e[5]===t[a+5]&&e[6]===t[a+6]&&e[7]===t[a+7]&&e[8]===t[a+8]},F.prototype.equalsEpsilon=function(e,t){return F.equalsEpsilon(this,e,t)},F.prototype.toString=function(){return"("+this[0]+", "+this[3]+", "+this[6]+")\n("+this[1]+", "+this[4]+", "+this[7]+")\n("+this[2]+", "+this[5]+", "+this[8]+")"},K.packedLength=16,K.pack=function(e,t,a){return J.Check.typeOf.object("value",e),J.Check.defined("array",t),a=V.defaultValue(a,0),t[a++]=e[0],t[a++]=e[1],t[a++]=e[2],t[a++]=e[3],t[a++]=e[4],t[a++]=e[5],t[a++]=e[6],t[a++]=e[7],t[a++]=e[8],t[a++]=e[9],t[a++]=e[10],t[a++]=e[11],t[a++]=e[12],t[a++]=e[13],t[a++]=e[14],t[a]=e[15],t},K.unpack=function(e,t,a){return J.Check.defined("array",e),t=V.defaultValue(t,0),V.defined(a)||(a=new K),a[0]=e[t++],a[1]=e[t++],a[2]=e[t++],a[3]=e[t++],a[4]=e[t++],a[5]=e[t++],a[6]=e[t++],a[7]=e[t++],a[8]=e[t++],a[9]=e[t++],a[10]=e[t++],a[11]=e[t++],a[12]=e[t++],a[13]=e[t++],a[14]=e[t++],a[15]=e[t],a},K.clone=function(e,t){if(V.defined(e))return V.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t):new K(e[0],e[4],e[8],e[12],e[1],e[5],e[9],e[13],e[2],e[6],e[10],e[14],e[3],e[7],e[11],e[15])},K.fromArray=K.unpack,K.fromColumnMajorArray=function(e,t){return J.Check.defined("values",e),K.clone(e,t)},K.fromRowMajorArray=function(e,t){return J.Check.defined("values",e),V.defined(t)?(t[0]=e[0],t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=e[1],t[5]=e[5],t[6]=e[9],t[7]=e[13],t[8]=e[2],t[9]=e[6],t[10]=e[10],t[11]=e[14],t[12]=e[3],t[13]=e[7],t[14]=e[11],t[15]=e[15],t):new K(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])},K.fromRotationTranslation=function(e,t,a){return J.Check.typeOf.object("rotation",e),t=V.defaultValue(t,I.Cartesian3.ZERO),V.defined(a)?(a[0]=e[0],a[1]=e[1],a[2]=e[2],a[3]=0,a[4]=e[3],a[5]=e[4],a[6]=e[5],a[7]=0,a[8]=e[6],a[9]=e[7],a[10]=e[8],a[11]=0,a[12]=t.x,a[13]=t.y,a[14]=t.z,a[15]=1,a):new K(e[0],e[3],e[6],t.x,e[1],e[4],e[7],t.y,e[2],e[5],e[8],t.z,0,0,0,1)},K.fromTranslationQuaternionRotationScale=function(e,t,a,n){J.Check.typeOf.object("translation",e),J.Check.typeOf.object("rotation",t),J.Check.typeOf.object("scale",a),V.defined(n)||(n=new K);var r=a.x,i=a.y,c=a.z,o=t.x*t.x,u=t.x*t.y,s=t.x*t.z,f=t.x*t.w,l=t.y*t.y,C=t.y*t.z,h=t.y*t.w,d=t.z*t.z,y=t.z*t.w,p=t.w*t.w,O=o-l-d+p,m=2*(u-y),b=2*(s+h),k=2*(u+y),x=-o+l-d+p,j=2*(C-f),v=2*(s-h),g=2*(C+f),w=-o-l+d+p;return n[0]=O*r,n[1]=k*r,n[2]=v*r,n[3]=0,n[4]=m*i,n[5]=x*i,n[6]=g*i,n[7]=0,n[8]=b*c,n[9]=j*c,n[10]=w*c,n[11]=0,n[12]=e.x,n[13]=e.y,n[14]=e.z,n[15]=1,n},K.fromTranslationRotationScale=function(e,t){return J.Check.typeOf.object("translationRotationScale",e),K.fromTranslationQuaternionRotationScale(e.translation,e.rotation,e.scale,t)},K.fromTranslation=function(e,t){return J.Check.typeOf.object("translation",e),K.fromRotationTranslation(F.IDENTITY,e,t)},K.fromScale=function(e,t){return J.Check.typeOf.object("scale",e),V.defined(t)?(t[0]=e.x,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=e.y,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=e.z,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t):new K(e.x,0,0,0,0,e.y,0,0,0,0,e.z,0,0,0,0,1)},K.fromUniformScale=function(e,t){return J.Check.typeOf.number("scale",e),V.defined(t)?(t[0]=e,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=e,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=e,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t):new K(e,0,0,0,0,e,0,0,0,0,e,0,0,0,0,1)};var k=new I.Cartesian3,x=new I.Cartesian3,j=new I.Cartesian3;K.fromCamera=function(e,t){J.Check.typeOf.object("camera",e);var a=e.position,n=e.direction,r=e.up;J.Check.typeOf.object("camera.position",a),J.Check.typeOf.object("camera.direction",n),J.Check.typeOf.object("camera.up",r),I.Cartesian3.normalize(n,k),I.Cartesian3.normalize(I.Cartesian3.cross(k,r,x),x),I.Cartesian3.normalize(I.Cartesian3.cross(x,k,j),j);var i=x.x,c=x.y,o=x.z,u=k.x,s=k.y,f=k.z,l=j.x,C=j.y,h=j.z,d=a.x,y=a.y,p=a.z,O=i*-d+c*-y+o*-p,m=l*-d+C*-y+h*-p,b=u*d+s*y+f*p;return V.defined(t)?(t[0]=i,t[1]=l,t[2]=-u,t[3]=0,t[4]=c,t[5]=C,t[6]=-s,t[7]=0,t[8]=o,t[9]=h,t[10]=-f,t[11]=0,t[12]=O,t[13]=m,t[14]=b,t[15]=1,t):new K(i,c,o,O,l,C,h,m,-u,-s,-f,b,0,0,0,1)},K.computePerspectiveFieldOfView=function(e,t,a,n,r){J.Check.typeOf.number.greaterThan("fovY",e,0),J.Check.typeOf.number.lessThan("fovY",e,Math.PI),J.Check.typeOf.number.greaterThan("near",a,0),J.Check.typeOf.number.greaterThan("far",n,0),J.Check.typeOf.object("result",r);var i=1/Math.tan(.5*e),c=i/t,o=(n+a)/(a-n),u=2*n*a/(a-n);return r[0]=c,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=i,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=o,r[11]=-1,r[12]=0,r[13]=0,r[14]=u,r[15]=0,r},K.computeOrthographicOffCenter=function(e,t,a,n,r,i,c){J.Check.typeOf.number("left",e),J.Check.typeOf.number("right",t),J.Check.typeOf.number("bottom",a),J.Check.typeOf.number("top",n),J.Check.typeOf.number("near",r),J.Check.typeOf.number("far",i),J.Check.typeOf.object("result",c);var o=1/(t-e),u=1/(n-a),s=1/(i-r),f=-(t+e)*o,l=-(n+a)*u,C=-(i+r)*s;return o*=2,u*=2,s*=-2,c[0]=o,c[1]=0,c[2]=0,c[3]=0,c[4]=0,c[5]=u,c[6]=0,c[7]=0,c[8]=0,c[9]=0,c[10]=s,c[11]=0,c[12]=f,c[13]=l,c[14]=C,c[15]=1,c},K.computePerspectiveOffCenter=function(e,t,a,n,r,i,c){J.Check.typeOf.number("left",e),J.Check.typeOf.number("right",t),J.Check.typeOf.number("bottom",a),J.Check.typeOf.number("top",n),J.Check.typeOf.number("near",r),J.Check.typeOf.number("far",i),J.Check.typeOf.object("result",c);var o=2*r/(t-e),u=2*r/(n-a),s=(t+e)/(t-e),f=(n+a)/(n-a),l=-(i+r)/(i-r),C=-2*i*r/(i-r);return c[0]=o,c[1]=0,c[2]=0,c[3]=0,c[4]=0,c[5]=u,c[6]=0,c[7]=0,c[8]=s,c[9]=f,c[10]=l,c[11]=-1,c[12]=0,c[13]=0,c[14]=C,c[15]=0,c},K.computeInfinitePerspectiveOffCenter=function(e,t,a,n,r,i){J.Check.typeOf.number("left",e),J.Check.typeOf.number("right",t),J.Check.typeOf.number("bottom",a),J.Check.typeOf.number("top",n),J.Check.typeOf.number("near",r),J.Check.typeOf.object("result",i);var c=2*r/(t-e),o=2*r/(n-a),u=(t+e)/(t-e),s=(n+a)/(n-a),f=-2*r;return i[0]=c,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=o,i[6]=0,i[7]=0,i[8]=u,i[9]=s,i[10]=-1,i[11]=-1,i[12]=0,i[13]=0,i[14]=f,i[15]=0,i},K.computeViewportTransformation=function(e,t,a,n){J.Check.typeOf.object("result",n),e=V.defaultValue(e,V.defaultValue.EMPTY_OBJECT);var r=V.defaultValue(e.x,0),i=V.defaultValue(e.y,0),c=V.defaultValue(e.width,0),o=V.defaultValue(e.height,0);t=V.defaultValue(t,0);var u=.5*c,s=.5*o,f=.5*((a=V.defaultValue(a,1))-t),l=u,C=s,h=f,d=r+u,y=i+s,p=t+f;return n[0]=l,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=C,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=h,n[11]=0,n[12]=d,n[13]=y,n[14]=p,n[15]=1,n},K.computeView=function(e,t,a,n,r){return J.Check.typeOf.object("position",e),J.Check.typeOf.object("direction",t),J.Check.typeOf.object("up",a),J.Check.typeOf.object("right",n),J.Check.typeOf.object("result",r),r[0]=n.x,r[1]=a.x,r[2]=-t.x,r[3]=0,r[4]=n.y,r[5]=a.y,r[6]=-t.y,r[7]=0,r[8]=n.z,r[9]=a.z,r[10]=-t.z,r[11]=0,r[12]=-I.Cartesian3.dot(n,e),r[13]=-I.Cartesian3.dot(a,e),r[14]=I.Cartesian3.dot(t,e),r[15]=1,r},K.toArray=function(e,t){return J.Check.typeOf.object("matrix",e),V.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t):[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15]]},K.getElementIndex=function(e,t){return J.Check.typeOf.number.greaterThanOrEquals("row",t,0),J.Check.typeOf.number.lessThanOrEquals("row",t,3),J.Check.typeOf.number.greaterThanOrEquals("column",e,0),J.Check.typeOf.number.lessThanOrEquals("column",e,3),4*e+t},K.getColumn=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.number.greaterThanOrEquals("index",t,0),J.Check.typeOf.number.lessThanOrEquals("index",t,3),J.Check.typeOf.object("result",a);var n=4*t,r=e[n],i=e[n+1],c=e[n+2],o=e[n+3];return a.x=r,a.y=i,a.z=c,a.w=o,a},K.setColumn=function(e,t,a,n){J.Check.typeOf.object("matrix",e),J.Check.typeOf.number.greaterThanOrEquals("index",t,0),J.Check.typeOf.number.lessThanOrEquals("index",t,3),J.Check.typeOf.object("cartesian",a),J.Check.typeOf.object("result",n);var r=4*t;return(n=K.clone(e,n))[r]=a.x,n[r+1]=a.y,n[r+2]=a.z,n[r+3]=a.w,n},K.setTranslation=function(e,t,a){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("translation",t),J.Check.typeOf.object("result",a),a[0]=e[0],a[1]=e[1],a[2]=e[2],a[3]=e[3],a[4]=e[4],a[5]=e[5],a[6]=e[6],a[7]=e[7],a[8]=e[8],a[9]=e[9],a[10]=e[10],a[11]=e[11],a[12]=t.x,a[13]=t.y,a[14]=t.z,a[15]=e[15],a};var i=new I.Cartesian3;K.setScale=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("scale",t),J.Check.typeOf.object("result",a);var n=K.getScale(e,i),r=I.Cartesian3.divideComponents(t,n,i);return K.multiplyByScale(e,r,a)},K.getRow=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.number.greaterThanOrEquals("index",t,0),J.Check.typeOf.number.lessThanOrEquals("index",t,3),J.Check.typeOf.object("result",a);var n=e[t],r=e[t+4],i=e[t+8],c=e[t+12];return a.x=n,a.y=r,a.z=i,a.w=c,a},K.setRow=function(e,t,a,n){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.number.greaterThanOrEquals("index",t,0),J.Check.typeOf.number.lessThanOrEquals("index",t,3),J.Check.typeOf.object("cartesian",a),J.Check.typeOf.object("result",n),(n=K.clone(e,n))[t]=a.x,n[t+4]=a.y,n[t+8]=a.z,n[t+12]=a.w,n};var y=new I.Cartesian3;K.getScale=function(e,t){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t),t.x=I.Cartesian3.magnitude(I.Cartesian3.fromElements(e[0],e[1],e[2],y)),t.y=I.Cartesian3.magnitude(I.Cartesian3.fromElements(e[4],e[5],e[6],y)),t.z=I.Cartesian3.magnitude(I.Cartesian3.fromElements(e[8],e[9],e[10],y)),t};var p=new I.Cartesian3;K.getMaximumScale=function(e){return K.getScale(e,p),I.Cartesian3.maximumComponent(p)},K.multiply=function(e,t,a){J.Check.typeOf.object("left",e),J.Check.typeOf.object("right",t),J.Check.typeOf.object("result",a);var n=e[0],r=e[1],i=e[2],c=e[3],o=e[4],u=e[5],s=e[6],f=e[7],l=e[8],C=e[9],h=e[10],d=e[11],y=e[12],p=e[13],O=e[14],m=e[15],b=t[0],k=t[1],x=t[2],j=t[3],v=t[4],g=t[5],w=t[6],z=t[7],M=t[8],E=t[9],q=t[10],R=t[11],S=t[12],T=t[13],V=t[14],I=t[15],N=n*b+o*k+l*x+y*j,L=r*b+u*k+C*x+p*j,P=i*b+s*k+h*x+O*j,U=c*b+f*k+d*x+m*j,B=n*v+o*g+l*w+y*z,W=r*v+u*g+C*w+p*z,D=i*v+s*g+h*w+O*z,A=c*v+f*g+d*w+m*z,Z=n*M+o*E+l*q+y*R,_=r*M+u*E+C*q+p*R,Y=i*M+s*E+h*q+O*R,G=c*M+f*E+d*q+m*R,H=n*S+o*T+l*V+y*I,Q=r*S+u*T+C*V+p*I,X=i*S+s*T+h*V+O*I,F=c*S+f*T+d*V+m*I;return a[0]=N,a[1]=L,a[2]=P,a[3]=U,a[4]=B,a[5]=W,a[6]=D,a[7]=A,a[8]=Z,a[9]=_,a[10]=Y,a[11]=G,a[12]=H,a[13]=Q,a[14]=X,a[15]=F,a},K.add=function(e,t,a){return J.Check.typeOf.object("left",e),J.Check.typeOf.object("right",t),J.Check.typeOf.object("result",a),a[0]=e[0]+t[0],a[1]=e[1]+t[1],a[2]=e[2]+t[2],a[3]=e[3]+t[3],a[4]=e[4]+t[4],a[5]=e[5]+t[5],a[6]=e[6]+t[6],a[7]=e[7]+t[7],a[8]=e[8]+t[8],a[9]=e[9]+t[9],a[10]=e[10]+t[10],a[11]=e[11]+t[11],a[12]=e[12]+t[12],a[13]=e[13]+t[13],a[14]=e[14]+t[14],a[15]=e[15]+t[15],a},K.subtract=function(e,t,a){return J.Check.typeOf.object("left",e),J.Check.typeOf.object("right",t),J.Check.typeOf.object("result",a),a[0]=e[0]-t[0],a[1]=e[1]-t[1],a[2]=e[2]-t[2],a[3]=e[3]-t[3],a[4]=e[4]-t[4],a[5]=e[5]-t[5],a[6]=e[6]-t[6],a[7]=e[7]-t[7],a[8]=e[8]-t[8],a[9]=e[9]-t[9],a[10]=e[10]-t[10],a[11]=e[11]-t[11],a[12]=e[12]-t[12],a[13]=e[13]-t[13],a[14]=e[14]-t[14],a[15]=e[15]-t[15],a},K.multiplyTransformation=function(e,t,a){J.Check.typeOf.object("left",e),J.Check.typeOf.object("right",t),J.Check.typeOf.object("result",a);var n=e[0],r=e[1],i=e[2],c=e[4],o=e[5],u=e[6],s=e[8],f=e[9],l=e[10],C=e[12],h=e[13],d=e[14],y=t[0],p=t[1],O=t[2],m=t[4],b=t[5],k=t[6],x=t[8],j=t[9],v=t[10],g=t[12],w=t[13],z=t[14],M=n*y+c*p+s*O,E=r*y+o*p+f*O,q=i*y+u*p+l*O,R=n*m+c*b+s*k,S=r*m+o*b+f*k,T=i*m+u*b+l*k,V=n*x+c*j+s*v,I=r*x+o*j+f*v,N=i*x+u*j+l*v,L=n*g+c*w+s*z+C,P=r*g+o*w+f*z+h,U=i*g+u*w+l*z+d;return a[0]=M,a[1]=E,a[2]=q,a[3]=0,a[4]=R,a[5]=S,a[6]=T,a[7]=0,a[8]=V,a[9]=I,a[10]=N,a[11]=0,a[12]=L,a[13]=P,a[14]=U,a[15]=1,a},K.multiplyByMatrix3=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("rotation",t),J.Check.typeOf.object("result",a);var n=e[0],r=e[1],i=e[2],c=e[4],o=e[5],u=e[6],s=e[8],f=e[9],l=e[10],C=t[0],h=t[1],d=t[2],y=t[3],p=t[4],O=t[5],m=t[6],b=t[7],k=t[8],x=n*C+c*h+s*d,j=r*C+o*h+f*d,v=i*C+u*h+l*d,g=n*y+c*p+s*O,w=r*y+o*p+f*O,z=i*y+u*p+l*O,M=n*m+c*b+s*k,E=r*m+o*b+f*k,q=i*m+u*b+l*k;return a[0]=x,a[1]=j,a[2]=v,a[3]=0,a[4]=g,a[5]=w,a[6]=z,a[7]=0,a[8]=M,a[9]=E,a[10]=q,a[11]=0,a[12]=e[12],a[13]=e[13],a[14]=e[14],a[15]=e[15],a},K.multiplyByTranslation=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("translation",t),J.Check.typeOf.object("result",a);var n=t.x,r=t.y,i=t.z,c=n*e[0]+r*e[4]+i*e[8]+e[12],o=n*e[1]+r*e[5]+i*e[9]+e[13],u=n*e[2]+r*e[6]+i*e[10]+e[14];return a[0]=e[0],a[1]=e[1],a[2]=e[2],a[3]=e[3],a[4]=e[4],a[5]=e[5],a[6]=e[6],a[7]=e[7],a[8]=e[8],a[9]=e[9],a[10]=e[10],a[11]=e[11],a[12]=c,a[13]=o,a[14]=u,a[15]=e[15],a};var O=new I.Cartesian3;K.multiplyByUniformScale=function(e,t,a){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.number("scale",t),J.Check.typeOf.object("result",a),O.x=t,O.y=t,O.z=t,K.multiplyByScale(e,O,a)},K.multiplyByScale=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("scale",t),J.Check.typeOf.object("result",a);var n=t.x,r=t.y,i=t.z;return 1===n&&1===r&&1===i?K.clone(e,a):(a[0]=n*e[0],a[1]=n*e[1],a[2]=n*e[2],a[3]=0,a[4]=r*e[4],a[5]=r*e[5],a[6]=r*e[6],a[7]=0,a[8]=i*e[8],a[9]=i*e[9],a[10]=i*e[10],a[11]=0,a[12]=e[12],a[13]=e[13],a[14]=e[14],a[15]=1,a)},K.multiplyByVector=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("cartesian",t),J.Check.typeOf.object("result",a);var n=t.x,r=t.y,i=t.z,c=t.w,o=e[0]*n+e[4]*r+e[8]*i+e[12]*c,u=e[1]*n+e[5]*r+e[9]*i+e[13]*c,s=e[2]*n+e[6]*r+e[10]*i+e[14]*c,f=e[3]*n+e[7]*r+e[11]*i+e[15]*c;return a.x=o,a.y=u,a.z=s,a.w=f,a},K.multiplyByPointAsVector=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("cartesian",t),J.Check.typeOf.object("result",a);var n=t.x,r=t.y,i=t.z,c=e[0]*n+e[4]*r+e[8]*i,o=e[1]*n+e[5]*r+e[9]*i,u=e[2]*n+e[6]*r+e[10]*i;return a.x=c,a.y=o,a.z=u,a},K.multiplyByPoint=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("cartesian",t),J.Check.typeOf.object("result",a);var n=t.x,r=t.y,i=t.z,c=e[0]*n+e[4]*r+e[8]*i+e[12],o=e[1]*n+e[5]*r+e[9]*i+e[13],u=e[2]*n+e[6]*r+e[10]*i+e[14];return a.x=c,a.y=o,a.z=u,a},K.multiplyByScalar=function(e,t,a){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.number("scalar",t),J.Check.typeOf.object("result",a),a[0]=e[0]*t,a[1]=e[1]*t,a[2]=e[2]*t,a[3]=e[3]*t,a[4]=e[4]*t,a[5]=e[5]*t,a[6]=e[6]*t,a[7]=e[7]*t,a[8]=e[8]*t,a[9]=e[9]*t,a[10]=e[10]*t,a[11]=e[11]*t,a[12]=e[12]*t,a[13]=e[13]*t,a[14]=e[14]*t,a[15]=e[15]*t,a},K.multiplyByPlane=function(e,t,a){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("plane",t),J.Check.typeOf.object("result",a);var n=new K,r=new K;K.inverse(e,n),K.transpose(n,r);var i=new Q.Cartesian4(t.normal.x,t.normal.y,t.normal.z,t.distance);K.multiplyByVector(r,i,i),a.normal.x=i.x,a.normal.y=i.y,a.normal.z=i.z;var c=I.Cartesian3.magnitude(a.normal);return I.Cartesian3.normalize(a.normal,a.normal),a.distance=i.w/c,a},K.negate=function(e,t){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t),t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t[4]=-e[4],t[5]=-e[5],t[6]=-e[6],t[7]=-e[7],t[8]=-e[8],t[9]=-e[9],t[10]=-e[10],t[11]=-e[11],t[12]=-e[12],t[13]=-e[13],t[14]=-e[14],t[15]=-e[15],t},K.transpose=function(e,t){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t);var a=e[1],n=e[2],r=e[3],i=e[6],c=e[7],o=e[11];return t[0]=e[0],t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=a,t[5]=e[5],t[6]=e[9],t[7]=e[13],t[8]=n,t[9]=i,t[10]=e[10],t[11]=e[14],t[12]=r,t[13]=c,t[14]=o,t[15]=e[15],t},K.abs=function(e,t){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t),t[0]=Math.abs(e[0]),t[1]=Math.abs(e[1]),t[2]=Math.abs(e[2]),t[3]=Math.abs(e[3]),t[4]=Math.abs(e[4]),t[5]=Math.abs(e[5]),t[6]=Math.abs(e[6]),t[7]=Math.abs(e[7]),t[8]=Math.abs(e[8]),t[9]=Math.abs(e[9]),t[10]=Math.abs(e[10]),t[11]=Math.abs(e[11]),t[12]=Math.abs(e[12]),t[13]=Math.abs(e[13]),t[14]=Math.abs(e[14]),t[15]=Math.abs(e[15]),t},K.equals=function(e,t){return e===t||V.defined(e)&&V.defined(t)&&e[12]===t[12]&&e[13]===t[13]&&e[14]===t[14]&&e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[4]===t[4]&&e[5]===t[5]&&e[6]===t[6]&&e[8]===t[8]&&e[9]===t[9]&&e[10]===t[10]&&e[3]===t[3]&&e[7]===t[7]&&e[11]===t[11]&&e[15]===t[15]},K.equalsEpsilon=function(e,t,a){return J.Check.typeOf.number("epsilon",a),e===t||V.defined(e)&&V.defined(t)&&Math.abs(e[0]-t[0])<=a&&Math.abs(e[1]-t[1])<=a&&Math.abs(e[2]-t[2])<=a&&Math.abs(e[3]-t[3])<=a&&Math.abs(e[4]-t[4])<=a&&Math.abs(e[5]-t[5])<=a&&Math.abs(e[6]-t[6])<=a&&Math.abs(e[7]-t[7])<=a&&Math.abs(e[8]-t[8])<=a&&Math.abs(e[9]-t[9])<=a&&Math.abs(e[10]-t[10])<=a&&Math.abs(e[11]-t[11])<=a&&Math.abs(e[12]-t[12])<=a&&Math.abs(e[13]-t[13])<=a&&Math.abs(e[14]-t[14])<=a&&Math.abs(e[15]-t[15])<=a},K.getTranslation=function(e,t){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t),t.x=e[12],t.y=e[13],t.z=e[14],t},K.getMatrix3=function(e,t){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t),t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[4],t[4]=e[5],t[5]=e[6],t[6]=e[8],t[7]=e[9],t[8]=e[10],t},K.getRotation=function(e,t){return J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t),t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[4],t[4]=e[5],t[5]=e[6],t[6]=e[8],t[7]=e[9],t[8]=e[10],t};var $=new F,ee=new F,te=new Q.Cartesian4,ae=new Q.Cartesian4(0,0,0,1);function N(e,t){this.center=I.Cartesian3.clone(V.defaultValue(e,I.Cartesian3.ZERO)),this.radius=V.defaultValue(t,0)}K.inverse=function(e,t){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t);var a=e[0],n=e[4],r=e[8],i=e[12],c=e[1],o=e[5],u=e[9],s=e[13],f=e[2],l=e[6],C=e[10],h=e[14],d=e[3],y=e[7],p=e[11],O=e[15],m=C*O,b=h*p,k=l*O,x=h*y,j=l*p,v=C*y,g=f*O,w=h*d,z=f*p,M=C*d,E=f*y,q=l*d,R=m*o+x*u+j*s-(b*o+k*u+v*s),S=b*c+g*u+M*s-(m*c+w*u+z*s),T=k*c+w*o+E*s-(x*c+g*o+q*s),V=v*c+z*o+q*u-(j*c+M*o+E*u),I=b*n+k*r+v*i-(m*n+x*r+j*i),N=m*a+w*r+z*i-(b*a+g*r+M*i),L=x*a+g*n+q*i-(k*a+w*n+E*i),P=j*a+M*n+E*r-(v*a+z*n+q*r),U=(m=r*s)*y+(x=i*o)*p+(j=n*u)*O-((b=i*u)*y+(k=n*s)*p+(v=r*o)*O),B=b*d+(g=a*s)*p+(M=r*c)*O-(m*d+(w=i*c)*p+(z=a*u)*O),W=k*d+w*y+(E=a*o)*O-(x*d+g*y+(q=n*c)*O),D=v*d+z*y+q*p-(j*d+M*y+E*p),A=k*C+v*h+b*l-(j*h+m*l+x*C),Z=z*h+m*f+w*C-(g*C+M*h+b*f),_=g*l+q*h+x*f-(E*h+k*f+w*l),Y=E*C+j*f+M*l-(z*l+q*C+v*f),G=a*R+n*S+r*T+i*V;if(Math.abs(G)<H.CesiumMath.EPSILON21){if(F.equalsEpsilon(K.getRotation(e,$),ee,H.CesiumMath.EPSILON7)&&Q.Cartesian4.equals(K.getRow(e,3,te),ae))return t[0]=0,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=0,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=0,t[11]=0,t[12]=-e[12],t[13]=-e[13],t[14]=-e[14],t[15]=1,t;throw new X.RuntimeError("matrix is not invertible because its determinate is zero.")}return G=1/G,t[0]=R*G,t[1]=S*G,t[2]=T*G,t[3]=V*G,t[4]=I*G,t[5]=N*G,t[6]=L*G,t[7]=P*G,t[8]=U*G,t[9]=B*G,t[10]=W*G,t[11]=D*G,t[12]=A*G,t[13]=Z*G,t[14]=_*G,t[15]=Y*G,t},K.inverseTransformation=function(e,t){J.Check.typeOf.object("matrix",e),J.Check.typeOf.object("result",t);var a=e[0],n=e[1],r=e[2],i=e[4],c=e[5],o=e[6],u=e[8],s=e[9],f=e[10],l=e[12],C=e[13],h=e[14],d=-a*l-n*C-r*h,y=-i*l-c*C-o*h,p=-u*l-s*C-f*h;return t[0]=a,t[1]=i,t[2]=u,t[3]=0,t[4]=n,t[5]=c,t[6]=s,t[7]=0,t[8]=r,t[9]=o,t[10]=f,t[11]=0,t[12]=d,t[13]=y,t[14]=p,t[15]=1,t},K.IDENTITY=Object.freeze(new K(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)),K.ZERO=Object.freeze(new K(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)),K.COLUMN0ROW0=0,K.COLUMN0ROW1=1,K.COLUMN0ROW2=2,K.COLUMN0ROW3=3,K.COLUMN1ROW0=4,K.COLUMN1ROW1=5,K.COLUMN1ROW2=6,K.COLUMN1ROW3=7,K.COLUMN2ROW0=8,K.COLUMN2ROW1=9,K.COLUMN2ROW2=10,K.COLUMN2ROW3=11,K.COLUMN3ROW0=12,K.COLUMN3ROW1=13,K.COLUMN3ROW2=14,K.COLUMN3ROW3=15,Object.defineProperties(K.prototype,{length:{get:function(){return K.packedLength}}}),K.prototype.clone=function(e){return K.clone(this,e)},K.prototype.equals=function(e){return K.equals(this,e)},K.equalsArray=function(e,t,a){return e[0]===t[a]&&e[1]===t[a+1]&&e[2]===t[a+2]&&e[3]===t[a+3]&&e[4]===t[a+4]&&e[5]===t[a+5]&&e[6]===t[a+6]&&e[7]===t[a+7]&&e[8]===t[a+8]&&e[9]===t[a+9]&&e[10]===t[a+10]&&e[11]===t[a+11]&&e[12]===t[a+12]&&e[13]===t[a+13]&&e[14]===t[a+14]&&e[15]===t[a+15]},K.prototype.equalsEpsilon=function(e,t){return K.equalsEpsilon(this,e,t)},K.prototype.toString=function(){return"("+this[0]+", "+this[4]+", "+this[8]+", "+this[12]+")\n("+this[1]+", "+this[5]+", "+this[9]+", "+this[13]+")\n("+this[2]+", "+this[6]+", "+this[10]+", "+this[14]+")\n("+this[3]+", "+this[7]+", "+this[11]+", "+this[15]+")"};var L=new I.Cartesian3,P=new I.Cartesian3,U=new I.Cartesian3,B=new I.Cartesian3,W=new I.Cartesian3,D=new I.Cartesian3,A=new I.Cartesian3,Z=new I.Cartesian3,_=new I.Cartesian3,Y=new I.Cartesian3,G=new I.Cartesian3,ne=new I.Cartesian3,m=4/3*H.CesiumMath.PI;N.fromPoints=function(e,t){if(V.defined(t)||(t=new N),!V.defined(e)||0===e.length)return t.center=I.Cartesian3.clone(I.Cartesian3.ZERO,t.center),t.radius=0,t;var a,n=I.Cartesian3.clone(e[0],A),r=I.Cartesian3.clone(n,L),i=I.Cartesian3.clone(n,P),c=I.Cartesian3.clone(n,U),o=I.Cartesian3.clone(n,B),u=I.Cartesian3.clone(n,W),s=I.Cartesian3.clone(n,D),f=e.length;for(a=1;a<f;a++){I.Cartesian3.clone(e[a],n);var l=n.x,C=n.y,h=n.z;l<r.x&&I.Cartesian3.clone(n,r),l>o.x&&I.Cartesian3.clone(n,o),C<i.y&&I.Cartesian3.clone(n,i),C>u.y&&I.Cartesian3.clone(n,u),h<c.z&&I.Cartesian3.clone(n,c),h>s.z&&I.Cartesian3.clone(n,s)}var d=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(o,r,Z)),y=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(u,i,Z)),p=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(s,c,Z)),O=r,m=o,b=d;b<y&&(b=y,O=i,m=u),b<p&&(b=p,O=c,m=s);var k=_;k.x=.5*(O.x+m.x),k.y=.5*(O.y+m.y),k.z=.5*(O.z+m.z);var x=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(m,k,Z)),j=Math.sqrt(x),v=Y;v.x=r.x,v.y=i.y,v.z=c.z;var g=G;g.x=o.x,g.y=u.y,g.z=s.z;var w=I.Cartesian3.midpoint(v,g,ne),z=0;for(a=0;a<f;a++){I.Cartesian3.clone(e[a],n);var M=I.Cartesian3.magnitude(I.Cartesian3.subtract(n,w,Z));z<M&&(z=M);var E=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(n,k,Z));if(x<E){var q=Math.sqrt(E);x=(j=.5*(j+q))*j;var R=q-j;k.x=(j*k.x+R*n.x)/q,k.y=(j*k.y+R*n.y)/q,k.z=(j*k.z+R*n.z)/q}}return t.radius=j<z?(I.Cartesian3.clone(k,t.center),j):(I.Cartesian3.clone(w,t.center),z),t};var b=new t,v=new I.Cartesian3,g=new I.Cartesian3,w=new I.Cartographic,z=new I.Cartographic;N.fromRectangle2D=function(e,t,a){return N.fromRectangleWithHeights2D(e,t,0,0,a)},N.fromRectangleWithHeights2D=function(e,t,a,n,r){if(V.defined(r)||(r=new N),!V.defined(e))return r.center=I.Cartesian3.clone(I.Cartesian3.ZERO,r.center),r.radius=0,r;t=V.defaultValue(t,b),l.Rectangle.southwest(e,w),w.height=a,l.Rectangle.northeast(e,z),z.height=n;var i=t.project(w,v),c=t.project(z,g),o=c.x-i.x,u=c.y-i.y,s=c.z-i.z;r.radius=.5*Math.sqrt(o*o+u*u+s*s);var f=r.center;return f.x=i.x+.5*o,f.y=i.y+.5*u,f.z=i.z+.5*s,r};var M=[];N.fromRectangle3D=function(e,t,a,n){if(t=V.defaultValue(t,l.Ellipsoid.WGS84),a=V.defaultValue(a,0),V.defined(n)||(n=new N),!V.defined(e))return n.center=I.Cartesian3.clone(I.Cartesian3.ZERO,n.center),n.radius=0,n;var r=l.Rectangle.subsample(e,t,a,M);return N.fromPoints(r,n)},N.fromVertices=function(e,t,a,n){if(V.defined(n)||(n=new N),!V.defined(e)||0===e.length)return n.center=I.Cartesian3.clone(I.Cartesian3.ZERO,n.center),n.radius=0,n;t=V.defaultValue(t,I.Cartesian3.ZERO),a=V.defaultValue(a,3),J.Check.typeOf.number.greaterThanOrEquals("stride",a,3);var r=A;r.x=e[0]+t.x,r.y=e[1]+t.y,r.z=e[2]+t.z;var i,c=I.Cartesian3.clone(r,L),o=I.Cartesian3.clone(r,P),u=I.Cartesian3.clone(r,U),s=I.Cartesian3.clone(r,B),f=I.Cartesian3.clone(r,W),l=I.Cartesian3.clone(r,D),C=e.length;for(i=0;i<C;i+=a){var h=e[i]+t.x,d=e[i+1]+t.y,y=e[i+2]+t.z;r.x=h,r.y=d,r.z=y,h<c.x&&I.Cartesian3.clone(r,c),h>s.x&&I.Cartesian3.clone(r,s),d<o.y&&I.Cartesian3.clone(r,o),d>f.y&&I.Cartesian3.clone(r,f),y<u.z&&I.Cartesian3.clone(r,u),y>l.z&&I.Cartesian3.clone(r,l)}var p=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(s,c,Z)),O=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(f,o,Z)),m=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(l,u,Z)),b=c,k=s,x=p;x<O&&(x=O,b=o,k=f),x<m&&(x=m,b=u,k=l);var j=_;j.x=.5*(b.x+k.x),j.y=.5*(b.y+k.y),j.z=.5*(b.z+k.z);var v=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(k,j,Z)),g=Math.sqrt(v),w=Y;w.x=c.x,w.y=o.y,w.z=u.z;var z=G;z.x=s.x,z.y=f.y,z.z=l.z;var M=I.Cartesian3.midpoint(w,z,ne),E=0;for(i=0;i<C;i+=a){r.x=e[i]+t.x,r.y=e[i+1]+t.y,r.z=e[i+2]+t.z;var q=I.Cartesian3.magnitude(I.Cartesian3.subtract(r,M,Z));E<q&&(E=q);var R=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(r,j,Z));if(v<R){var S=Math.sqrt(R);v=(g=.5*(g+S))*g;var T=S-g;j.x=(g*j.x+T*r.x)/S,j.y=(g*j.y+T*r.y)/S,j.z=(g*j.z+T*r.z)/S}}return n.radius=g<E?(I.Cartesian3.clone(j,n.center),g):(I.Cartesian3.clone(M,n.center),E),n},N.fromEncodedCartesianVertices=function(e,t,a){if(V.defined(a)||(a=new N),!V.defined(e)||!V.defined(t)||e.length!==t.length||0===e.length)return a.center=I.Cartesian3.clone(I.Cartesian3.ZERO,a.center),a.radius=0,a;var n=A;n.x=e[0]+t[0],n.y=e[1]+t[1],n.z=e[2]+t[2];var r,i=I.Cartesian3.clone(n,L),c=I.Cartesian3.clone(n,P),o=I.Cartesian3.clone(n,U),u=I.Cartesian3.clone(n,B),s=I.Cartesian3.clone(n,W),f=I.Cartesian3.clone(n,D),l=e.length;for(r=0;r<l;r+=3){var C=e[r]+t[r],h=e[r+1]+t[r+1],d=e[r+2]+t[r+2];n.x=C,n.y=h,n.z=d,C<i.x&&I.Cartesian3.clone(n,i),C>u.x&&I.Cartesian3.clone(n,u),h<c.y&&I.Cartesian3.clone(n,c),h>s.y&&I.Cartesian3.clone(n,s),d<o.z&&I.Cartesian3.clone(n,o),d>f.z&&I.Cartesian3.clone(n,f)}var y=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(u,i,Z)),p=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(s,c,Z)),O=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(f,o,Z)),m=i,b=u,k=y;k<p&&(k=p,m=c,b=s),k<O&&(k=O,m=o,b=f);var x=_;x.x=.5*(m.x+b.x),x.y=.5*(m.y+b.y),x.z=.5*(m.z+b.z);var j=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(b,x,Z)),v=Math.sqrt(j),g=Y;g.x=i.x,g.y=c.y,g.z=o.z;var w=G;w.x=u.x,w.y=s.y,w.z=f.z;var z=I.Cartesian3.midpoint(g,w,ne),M=0;for(r=0;r<l;r+=3){n.x=e[r]+t[r],n.y=e[r+1]+t[r+1],n.z=e[r+2]+t[r+2];var E=I.Cartesian3.magnitude(I.Cartesian3.subtract(n,z,Z));M<E&&(M=E);var q=I.Cartesian3.magnitudeSquared(I.Cartesian3.subtract(n,x,Z));if(j<q){var R=Math.sqrt(q);j=(v=.5*(v+R))*v;var S=R-v;x.x=(v*x.x+S*n.x)/R,x.y=(v*x.y+S*n.y)/R,x.z=(v*x.z+S*n.z)/R}}return a.radius=v<M?(I.Cartesian3.clone(x,a.center),v):(I.Cartesian3.clone(z,a.center),M),a},N.fromCornerPoints=function(e,t,a){J.Check.typeOf.object("corner",e),J.Check.typeOf.object("oppositeCorner",t),V.defined(a)||(a=new N);var n=I.Cartesian3.midpoint(e,t,a.center);return a.radius=I.Cartesian3.distance(n,t),a},N.fromEllipsoid=function(e,t){return J.Check.typeOf.object("ellipsoid",e),V.defined(t)||(t=new N),I.Cartesian3.clone(I.Cartesian3.ZERO,t.center),t.radius=e.maximumRadius,t};var E=new I.Cartesian3;N.fromBoundingSpheres=function(e,t){if(V.defined(t)||(t=new N),!V.defined(e)||0===e.length)return t.center=I.Cartesian3.clone(I.Cartesian3.ZERO,t.center),t.radius=0,t;var a=e.length;if(1===a)return N.clone(e[0],t);if(2===a)return N.union(e[0],e[1],t);var n,r=[];for(n=0;n<a;n++)r.push(e[n].center);var i=(t=N.fromPoints(r,t)).center,c=t.radius;for(n=0;n<a;n++){var o=e[n];c=Math.max(c,I.Cartesian3.distance(i,o.center,E)+o.radius)}return t.radius=c,t};var q=new I.Cartesian3,R=new I.Cartesian3,S=new I.Cartesian3;N.fromOrientedBoundingBox=function(e,t){J.Check.defined("orientedBoundingBox",e),V.defined(t)||(t=new N);var a=e.halfAxes,n=F.getColumn(a,0,q),r=F.getColumn(a,1,R),i=F.getColumn(a,2,S);return I.Cartesian3.add(n,r,n),I.Cartesian3.add(n,i,n),t.center=I.Cartesian3.clone(e.center,t.center),t.radius=I.Cartesian3.magnitude(n),t},N.clone=function(e,t){if(V.defined(e))return V.defined(t)?(t.center=I.Cartesian3.clone(e.center,t.center),t.radius=e.radius,t):new N(e.center,e.radius)},N.packedLength=4,N.pack=function(e,t,a){J.Check.typeOf.object("value",e),J.Check.defined("array",t),a=V.defaultValue(a,0);var n=e.center;return t[a++]=n.x,t[a++]=n.y,t[a++]=n.z,t[a]=e.radius,t},N.unpack=function(e,t,a){J.Check.defined("array",e),t=V.defaultValue(t,0),V.defined(a)||(a=new N);var n=a.center;return n.x=e[t++],n.y=e[t++],n.z=e[t++],a.radius=e[t],a};var T=new I.Cartesian3,re=new I.Cartesian3;N.union=function(e,t,a){J.Check.typeOf.object("left",e),J.Check.typeOf.object("right",t),V.defined(a)||(a=new N);var n=e.center,r=e.radius,i=t.center,c=t.radius,o=I.Cartesian3.subtract(i,n,T),u=I.Cartesian3.magnitude(o);if(u+c<=r)return e.clone(a),a;if(u+r<=c)return t.clone(a),a;var s=.5*(r+u+c),f=I.Cartesian3.multiplyByScalar(o,(-r+s)/u,re);return I.Cartesian3.add(f,n,f),I.Cartesian3.clone(f,a.center),a.radius=s,a};var ie=new I.Cartesian3;N.expand=function(e,t,a){J.Check.typeOf.object("sphere",e),J.Check.typeOf.object("point",t),a=N.clone(e,a);var n=I.Cartesian3.magnitude(I.Cartesian3.subtract(t,a.center,ie));return n>a.radius&&(a.radius=n),a},N.intersectPlane=function(e,t){J.Check.typeOf.object("sphere",e),J.Check.typeOf.object("plane",t);var a=e.center,n=e.radius,r=t.normal,i=I.Cartesian3.dot(r,a)+t.distance;return i<-n?c.OUTSIDE:i<n?c.INTERSECTING:c.INSIDE},N.transform=function(e,t,a){return J.Check.typeOf.object("sphere",e),J.Check.typeOf.object("transform",t),V.defined(a)||(a=new N),a.center=K.multiplyByPoint(t,e.center,a.center),a.radius=K.getMaximumScale(t)*e.radius,a};var ce=new I.Cartesian3;N.distanceSquaredTo=function(e,t){J.Check.typeOf.object("sphere",e),J.Check.typeOf.object("cartesian",t);var a=I.Cartesian3.subtract(e.center,t,ce);return I.Cartesian3.magnitudeSquared(a)-e.radius*e.radius},N.transformWithoutScale=function(e,t,a){return J.Check.typeOf.object("sphere",e),J.Check.typeOf.object("transform",t),V.defined(a)||(a=new N),a.center=K.multiplyByPoint(t,e.center,a.center),a.radius=e.radius,a};var oe=new I.Cartesian3;N.computePlaneDistances=function(e,t,a,n){J.Check.typeOf.object("sphere",e),J.Check.typeOf.object("position",t),J.Check.typeOf.object("direction",a),V.defined(n)||(n=new o);var r=I.Cartesian3.subtract(e.center,t,oe),i=I.Cartesian3.dot(a,r);return n.start=i-e.radius,n.stop=i+e.radius,n};for(var ue=new I.Cartesian3,se=new I.Cartesian3,fe=new I.Cartesian3,le=new I.Cartesian3,Ce=new I.Cartesian3,he=new I.Cartographic,de=new Array(8),ye=0;ye<8;++ye)de[ye]=new I.Cartesian3;var pe=new t;N.projectTo2D=function(e,t,a){J.Check.typeOf.object("sphere",e);var n,r=(t=V.defaultValue(t,pe)).ellipsoid,i=e.center,c=e.radius;n=I.Cartesian3.equals(i,I.Cartesian3.ZERO)?I.Cartesian3.clone(I.Cartesian3.UNIT_X,ue):r.geodeticSurfaceNormal(i,ue);var o=I.Cartesian3.cross(I.Cartesian3.UNIT_Z,n,se);I.Cartesian3.normalize(o,o);var u=I.Cartesian3.cross(n,o,fe);I.Cartesian3.normalize(u,u),I.Cartesian3.multiplyByScalar(n,c,n),I.Cartesian3.multiplyByScalar(u,c,u),I.Cartesian3.multiplyByScalar(o,c,o);var s=I.Cartesian3.negate(u,Ce),f=I.Cartesian3.negate(o,le),l=de,C=l[0];I.Cartesian3.add(n,u,C),I.Cartesian3.add(C,o,C),C=l[1],I.Cartesian3.add(n,u,C),I.Cartesian3.add(C,f,C),C=l[2],I.Cartesian3.add(n,s,C),I.Cartesian3.add(C,f,C),C=l[3],I.Cartesian3.add(n,s,C),I.Cartesian3.add(C,o,C),I.Cartesian3.negate(n,n),C=l[4],I.Cartesian3.add(n,u,C),I.Cartesian3.add(C,o,C),C=l[5],I.Cartesian3.add(n,u,C),I.Cartesian3.add(C,f,C),C=l[6],I.Cartesian3.add(n,s,C),I.Cartesian3.add(C,f,C),C=l[7],I.Cartesian3.add(n,s,C),I.Cartesian3.add(C,o,C);for(var h=l.length,d=0;d<h;++d){var y=l[d];I.Cartesian3.add(i,y,y);var p=r.cartesianToCartographic(y,he);t.project(p,y)}var O=(i=(a=N.fromPoints(l,a)).center).x,m=i.y,b=i.z;return i.x=b,i.y=O,i.z=m,a},N.isOccluded=function(e,t){return J.Check.typeOf.object("sphere",e),J.Check.typeOf.object("occluder",t),!t.isBoundingSphereVisible(e)},N.equals=function(e,t){return e===t||V.defined(e)&&V.defined(t)&&I.Cartesian3.equals(e.center,t.center)&&e.radius===t.radius},N.prototype.intersectPlane=function(e){return N.intersectPlane(this,e)},N.prototype.distanceSquaredTo=function(e){return N.distanceSquaredTo(this,e)},N.prototype.computePlaneDistances=function(e,t,a){return N.computePlaneDistances(this,e,t,a)},N.prototype.isOccluded=function(e){return N.isOccluded(this,e)},N.prototype.equals=function(e){return N.equals(this,e)},N.prototype.clone=function(e){return N.clone(this,e)},N.prototype.volume=function(){var e=this.radius;return m*e*e*e},e.BoundingSphere=N,e.GeographicProjection=t,e.Intersect=c,e.Interval=o,e.Matrix3=F,e.Matrix4=K});