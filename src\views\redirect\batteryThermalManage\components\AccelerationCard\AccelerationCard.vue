<template>
    <div class="vibration-monitor">
      <!-- 3D 电池模型 -->
      <div class="battery-3d" ref="battery3D"></div>
  
      <!-- 三轴仪表盘 -->
      <div class="gauges">
        <div class="gauge" v-for="(axis, index) in axes" :key="index">
          <e-charts :option="getGaugeOption(axis)" />
          <div class="axis-label">{{ axis.label }}</div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import ECharts from 'vue-echarts'
  import 'echarts-gl'
  
  export default {
    components: { ECharts },
    data() {
      return {
        axes: [
          { label: 'X', value: 0, color: '#4CAF50' },
          { label: 'Y', value: 0, color: '#2196F3' },
          { label: 'Z', value: 0, color: '#FF5722' }
        ],
        ws: null
      }
    },
    mounted() {
      this.init3DModel()
      this.connectWebSocket()
    },
    methods: {
      // 初始化3D模型
      init3DModel() {
        const chart = this.$refs.battery3D
        const option = {
          grid3D: {},
          xAxis3D: { type: 'value' },
          yAxis3D: { type: 'value' },
          zAxis3D: { type: 'value' },
          series: [{
            type: 'surface',
            parametric: true,
            wireframe: { show: false },
            itemStyle: { color: '#666', opacity: 0.8 },
            equation: {
              u: { min: -1, max: 1 },
              v: { min: -1, max: 1 },
              x: (u, v) => u * 2,
              y: (u, v) => v * 2,
              z: (u, v) => Math.sin(u * Math.PI) * Math.cos(v * Math.PI)
            }
          }]
        }
        const ec = new echarts.init(chart)
        ec.setOption(option)
      },
  
      // 获取仪表盘配置
      getGaugeOption(axis) {
        return {
          series: [{
            type: 'gauge',
            radius: '90%',
            axisLine: { lineStyle: { width: 8, color: [[1, axis.color]] } },
            splitLine: { show: false },
            axisTick: { show: false },
            detail: { formatter: '{value}g', fontSize: 20 },
            data: [{ value: axis.value }]
          }]
        }
      },
  
      // WebSocket实时数据
      connectWebSocket() {
        this.ws = new WebSocket('ws://your-websocket-endpoint')
        this.ws.onmessage = (e) => {
          const data = JSON.parse(e.data)
          this.axes.forEach(axis => {
            axis.value = data[axis.label.toLowerCase()]
            axis.color = this.getColorByValue(axis.value)
          })
        }
      },
  
      // 震动强度颜色映射
      getColorByValue(value) {
        return value < 2 ? '#4CAF50' 
             : value < 5 ? '#FFEB3B' 
             : '#F44336'
      }
    }
  }
  </script>
  
  <style scoped>
  .vibration-monitor {
    display: flex;
    gap: 30px;
    padding: 20px;
  }
  .battery-3d {
    width: 400px;
    height: 400px;
  }
  .gauges {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
  .gauge {
    width: 200px;
    height: 200px;
  }
  .axis-label {
    text-align: center;
    font-size: 16px;
    margin-top: 10px;
  }
  </style>