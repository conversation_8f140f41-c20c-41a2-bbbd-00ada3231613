
.header-cell-style {
  background: '#EBF4FE';
  color: '#606266';
  border-color: '#C9DBF4';
}

.cell-style {
  border-color: '#C9DBF4'
}


.link_a {
  text-decoration: underline;
  color: #409EFF;
  cursor: pointer;
}

.table-page {
  .card-content {
    margin: 10px 10px 0 10px;

    .content-option {
        width: calc(100% - 2px) !important;
        padding: 15px 20px 0px 20px;
        background-color: #F4F7FE;
        border: 1px solid #eee;
        border-bottom: none;

        .demo-form-inline {
            height: 55px;

            ::v-deep .el-select {
                width: 180px;
            }

            ::v-deep .el-input {
                width: 180px;
            }
        }
    }

    .content-table {
        max-height: calc(100vh - 200px);
        overflow: auto;

        .pagination-part {
            margin-top: 20px;
        }
    }
}
}