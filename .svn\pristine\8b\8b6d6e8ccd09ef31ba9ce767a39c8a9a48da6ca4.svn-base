.cesium-lighter .cesium-animation-themeNormal {
    color: #E5F2FE;
}

.cesium-lighter .cesium-animation-themeHover {
    color: #ABD6FF;
}

.cesium-lighter .cesium-animation-themeSelect {
    color: #E5F2FE;
}

.cesium-lighter .cesium-animation-themeDisabled {
    color: #EFEFEF;
}

.cesium-lighter .cesium-animation-themeKnob {
    color: #E1E2E3;
}

.cesium-lighter .cesium-animation-themePointer {
    color: #FA5;
}

.cesium-lighter .cesium-animation-themeSwoosh {
    color: #ACE;
}

.cesium-lighter .cesium-animation-themeSwooshHover {
    color: #BDF;
}

.cesium-lighter .cesium-animation-svgText {
    fill: #111;
}

.cesium-lighter .cesium-animation-rectButton .cesium-animation-buttonPath {
    fill: #111;
}

.cesium-lighter .cesium-animation-rectButton .cesium-animation-buttonMain {
    stroke: #759DC0;
}

.cesium-lighter .cesium-animation-buttonToggled .cesium-animation-buttonGlow {
    fill: #FFAA2A;
}

.cesium-lighter .cesium-animation-buttonToggled .cesium-animation-buttonMain {
    /* Widget will add: fill: url(#animation_buttonToggled); */
    stroke: #EA0;
}

.cesium-lighter .cesium-animation-rectButton:hover .cesium-animation-buttonMain {
    stroke: #759DC0;
}

.cesium-lighter .cesium-animation-buttonToggled:hover .cesium-animation-buttonGlow {
    fill: #fff;
}

.cesium-lighter .cesium-animation-buttonToggled:hover .cesium-animation-buttonMain {
    stroke: #EA0;
}

.cesium-lighter .cesium-animation-rectButton:active .cesium-animation-buttonMain {
    fill: #ABD6FF;
}

.cesium-lighter .cesium-animation-buttonDisabled .cesium-animation-buttonMain {
    stroke: #D3D3D3;
}

.cesium-lighter .cesium-animation-buttonDisabled .cesium-animation-buttonPath {
    fill: #818181;
}

.cesium-lighter .cesium-animation-shuttleRingBack {
    fill: #FAFAFA;
    fill-opacity: 1;
    stroke: #AEAEAE;
    stroke-width: 1.2;
}

.cesium-lighter .cesium-animation-shuttleRingSwoosh line {
    stroke: #8AC;
}

.cesium-lighter .cesium-animation-knobOuter {
    stroke: #A5A5A5;
}
