<template>
  <div class="dialog">
    <div class="dialog-main" :style="mainStyle">
      <div class="title">
        <p>{{ title }}</p>
      </div>
      <div class="dialog-wrap">
        <slot />
        <slot name="wrap" />
      </div>

      <div class="btn-div" v-if="!hiddenBtn">
        <el-button @click="closePopup()">{{ cancelStr }}</el-button>
        <el-button type="primary" @click.native.prevent="save()">{{ okStr }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["data", "title", "hiddenBtn", "isHiddenOk", "isHiddenCancel", "width", "okText", "cancelText", "height"],
  components: {
  },
  data() {
    return {}
  },
  computed: {
    okStr() {
      return this.okText ? this.okText : '保存'
    },
    cancelStr() {
      if (this.cancelText) {
        return this.cancelText
      }
      if (this.isHiddenOk) {
        return '返回'
      }
      return '取  消'
    },
    mainStyle() {
      let style = ''
      if (this.width) {
        style = `width: ${this.width};`
      }
      if (this.height) {
        style = `${style}height: ${this.height};`
      }
      return style
    },

  },

  methods: {
    closePopup() {
      this.$emit("closePopup")
    },
    save() {
      this.$emit("onSave")
    }
  }
}
</script>

<style scoped lang="scss">
.dialog {
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  justify-content: center;
  align-items: center;

  .dialog-main {
    background: white;
    width: 40vw;
    max-height: 85vh;
    min-height: 40vh;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    border: 12px solid white;

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 10px;

      p {
        font-size: 20px;
        font-weight: 500;
      }

      .close-img {
        width: 48px;
        padding: 12px;
        cursor: pointer;
      }


    }

    .dialog-wrap {
      margin: 0 12px;
      flex: 1;
      overflow: auto;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .btn-div {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 12px 0px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      font-weight: 400;

      &>div {
        margin: 0px 10px;
      }
    }
  }


  ::v-deep .el-select {
    width: 100%;
  }

  ::v-deep .el-upload--picture-card {
    width: 80px;
    height: 80px;
    line-height: 90px;
  }

  ::v-deep .el-upload-list--picture-card .el-upload-list__item {
    width: 80px;
    height: 80px;
  }

  ::v-deep .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 100%;
  }

}
</style>
