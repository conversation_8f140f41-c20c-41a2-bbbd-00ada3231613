<template>
  <div class="tree-content">
    <div class="tree-title">目录结构树</div>
    <el-tree
      :highlight-current="true"
      :data="treeData"
      :props="defaultProps"
      node-key="id"
      class="tree_form_left_tree"
      @node-click="handleNodeClick"
      v-loading="treeLoading"
      :default-expanded-keys="defaultShowNodes"
    >
      <div class="custom-tree-node_body" slot-scope="{ data }">
        <span class="custom-tree-node">
          <span class="node-item node-project" v-if="data.type === 3">项</span>
          <span class="node-item node-biao" v-else-if="data.type === 4"
            >标</span
          >
          <span class="node-item node-sui" v-else-if="data.type === 5">隧</span>
          <span
            class="node-item node-gong"
            v-else-if="data.type === 6"
            style="padding: 0 2px"
            >工</span
          >
          <span class="node-item node-qu" v-else-if="data.type === 8">部</span>
          <span class="node-item node-bu" v-else>区</span>
          <span
            class="label"
            :title="data.name || '-'"
            :class="{ 'label-title': data.type >= 6 }"
            >{{ (data.type == 4 || data.type == 8) ?data.name + "(" + (data.num||0) + ")" : data.name}}</span
          >
        </span>
      </div>
    </el-tree>
  </div>
</template>

<script>
import constDataSet from "@/utils/const";
export default {
  name: "Tree",
  props: [],
  data() {
    return {
      treeLoading: false,
      treeData: constDataSet.constData,
      defaultProps: {
        children: "children",
        label: "name",
      },
      defaultShowNodes: [11363], //默认展开节点
    };
  },
  beforeMount() {},
  mounted() {
    this.getAllNodeByUserId();
    // this.$store.dispatch("setSection", this.treeData[0].children[0].sectionId);
  },
  watch: {},
  methods: {
    // 根据用户Id查询树结构
    async getAllNodeByUserId() {
      this.treeLoading = true;
      let userId = this.$store.state.user.userInfo.id || null;
      if (userId) {
        let res = await this.$http.get(
          `/equip/tree/getAllNodeByUserId?userId=${userId}`
        );
        if (res.code === 0) {
          this.treeData = res?.result || constDataSet.constData;
          // 默认选中第一个标段
          this.$store.dispatch(
            "setSection",
            res?.result[0].sectionId
          );
          this.$store.dispatch(
            "setTreeInfo",
            res?.result[0]
          );
        } else {
          this.$message.error(res.msg);
        }
      } else {
        this.$message.error("获取用户id失败，请返回登录页面重新登录");
        // 返回登录页面
      }
      this.treeLoading = false;
    },

    handleNodeClick(data) {
      if (data.type >= 3) {
        this.$store.dispatch("setTreeInfo", data);
        this.$store.dispatch("setSection", data.sectionId);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.tree-content {
  width: 240px;
  background-color: #fff;
  // height: 100%;
  border-radius: 4px;
  margin: 0px 10px 0px;

  .tree-title {
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 600;
    color: #000;
    margin: 10px 0px 5px 10px !important;
  }
}
</style>

<style lang="scss">
.tree_form_left_tree {
  margin-top: 10px;
  font-size: 14px;
  .node-item {
    font-size: 12px;
    display: inline-block;
    border-radius: 8px;
    color: #fff;
    width: 16px;
    height: 16px;
    line-height: 16px;
    text-align: center;
  }

  .node-project {
    background-color: #224993;
  }

  .node-biao {
    background-color: #6098f9;
  }

  .node-sui {
    background-color: #fa846c;
  }

  .node-qu {
    background-color: #8489a1;
  }

  .node-gong {
    background-color: #e16367;
  }

  .node-bu {
    background-color: #6098f9;
  }

  .el-tree-node {
    position: relative;
    color: black;
    padding: 3px 0px;
    padding-left: 10px; // 缩进量
  }

  .el-tree-node__children {
    padding-left: 16px; // 缩进量
  }

  // 竖线
  .el-tree-node::before {
    content: "";
    height: 100%;
    width: 1px;
    position: absolute;
    left: -3px;
    top: -20px;
    border-width: 1px;
    border-left: 1px dashed #88909c;
  }

  // 当前层最后一个节点的竖线高度固定
  .el-tree-node:last-child::before {
    height: 38px; // 可以自己调节到合适数值
  }

  // 横线
  .el-tree-node::after {
    content: "";
    width: 24px;
    height: 20px;
    position: absolute;
    left: -3px;
    top: 19px;
    border-width: 1px;
    border-top: 1px dashed #88909c;
  }

  // 去掉最顶层的虚线，放最下面样式才不会被上面的覆盖了
  & > .el-tree-node::after {
    border-top: none;
  }

  & > .el-tree-node::before {
    border-left: none;
  }

  .el-tree-node.is-current > .el-tree-node__content {
    .custom-tree-node_body,
    .tree_form_left_tree_icon,
    .custom-tree-node .no_children {
      color: #006eff !important;
    }
  }

  .el-tree-node__content {
    padding-left: 0 !important;
    color: #444;
    line-height: 12px;

    // 树缩进样式
    .el-icon-caret-right::before {
      content: "";
      width: 14px;
      height: 14px;
      background: url("/src/assets/icons/plus-circle.png");
      background-size: 100% 100%;
      border-radius: 3px;
      display: inline-block;
    }

    .el-tree-node__expand-icon.expanded {
      transform: rotate(0);
    }

    // 树展开样式
    .el-tree-node__expand-icon.expanded::before {
      content: "";
      width: 14px;
      height: 14px;
      background: url("/src/assets/icons/delete.png");
      background-size: 100% 100%;
      border-radius: 3px;
      display: inline-block;
    }

    .el-tree-node__expand-icon.is-leaf::before {
      content: "";
    }

    .el-icon-caret-right.is-leaf::before {
      content: "";
      display: none;
    }

    .custom-tree-node .file_class {
      color: #ffb400;
    }

    .custom-tree-node .label {
      width: 140px;
      padding-left: 5px;
      display: inline-block;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      white-space: nowrap;
    }

    .label-title {
      width: 100px !important;
    }

    .custom-tree-node .no_children {
      color: #aaa;
    }
  }

  // 使用flex布局对custom-tree-node_body盒子进行排版
  .custom-tree-node_body {
    width: 100%;
    display: flex;
    justify-content: space-between;
    // margin-top:-3px;
  }

  .tree_form_left_tree_icon {
    padding: 0 3px;
    font-size: 16px;
    color: #666;

    i {
      margin: 0 4px;
    }
  }

  .tree_form_left_tree_icon:hover {
    cursor: pointer;
  }
}
</style>
