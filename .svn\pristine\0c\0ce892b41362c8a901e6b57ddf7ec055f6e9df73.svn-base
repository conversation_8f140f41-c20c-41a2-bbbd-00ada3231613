import postcss from '../../postcss.config';

/**
 * 该方法支持行内样式(EChart不支持Vw,vh)
 * @param {*} size 
 * @returns 
 */
export function pxToVw(size) {
  return 100 / postcss.plugins['postcss-px-to-viewport'].viewportWidth * size + 'vw'
};

/**
 * EChart fontSize 转换
 * @param {*} size 
 * @returns 
 */
export function pxToNum(size) {
  let screenWidth = window.screen.width;
  return 100 / postcss.plugins['postcss-px-to-viewport'].viewportWidth * size * screenWidth / 100
}
