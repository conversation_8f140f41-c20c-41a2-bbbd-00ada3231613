import axios from "axios";
import store from "@/store";


//axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 创建一个axios实例
const service = axios.create({
  baseURL: "/api",
  // baseURL: "http://***************:8071/",
  // baseURL: "http://************:8001/",
  // baseURL: "http://*************:8001/",
  // baseURL:'http://localhost:8001/',
  timeout: 100000,
  withCredentials: true, // 允许发送凭证
});


// 定义 getTokenNew 和 getLanguage 函数
function getTokenNew() {
  // 这里返回你的 token
  return store.getters.token || '';
}

function getLanguage() {
  // 这里返回你的语言设置
  return store.state.app.language || 'zh';
}


// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // if (store.getters.token) {
    //   config.headers["x-Token"] = getToken();
    // } 
    config.headers['content-Type']='application/json'
    // config.headers['Origin'] = 'http://*************:8001/'
   

    // const isToken = (config.headers || {}).isToken === false
    config.headers['crbimuid'] = store.state.user.userInfo.id
    return config
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use((response) => {
  //   if (response.headers.token) {
  //     sessionStorage.setItem("token", response.headers.token);
  //   }
  //自定义设置后台返回code对应的响应方式
  if (response.status === 200) {
    return response.data;
  } else {
    Promise.reject();
  }
});
export default service;
