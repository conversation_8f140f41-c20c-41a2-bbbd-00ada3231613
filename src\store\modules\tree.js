import Vue from "vue";

const tree = {
  state: {
    treeInfo: {},
    sectionId: null,
  },
  mutations: {
    SET_TREE_INFO(state, result) {
      Vue.set(state.treeInfo, 'sectionId', result)
    },
    SET_SECTION_ID(state, result) {
      state.sectionId = result;
    }
  },
  actions: {
    setTreeInfo({
      commit
    }, view) {
      commit('SET_TREE_INFO', view)
    },
    setSection({
      commit
    }, view) {
      commit('SET_SECTION_ID', view)
    }
  }
}
export default tree;
