/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./AttributeCompression-75ce15eb","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./WebMercatorProjection-bc9aa7fe","./createTaskProcessorWorker","./EllipsoidTangentPlane-9c25b2da","./OrientedBoundingBox-7b25e901","./TerrainEncoding-3dab0ca0"],function(We,e,Oe,Ue,Ye,ke,t,Ve,i,a,r,He,n,o,s,u,Le,h,De,Ge,je){var ze=Uint16Array.BYTES_PER_ELEMENT,qe=Int32Array.BYTES_PER_ELEMENT,Je=Uint32Array.BYTES_PER_ELEMENT,Ke=Float32Array.BYTES_PER_ELEMENT,Qe=Float64Array.BYTES_PER_ELEMENT;function Xe(e,t,i){i=We.defaultValue(i,Oe.CesiumMath);for(var a=e.length,r=0;r<a;++r)if(i.equalsEpsilon(e[r],t,Oe.CesiumMath.EPSILON12))return r;return-1}var Ze=new Ue.Cartographic,$e=new Ue.Cartesian3,et=new Ue.Cartesian3,tt=new Ue.Cartesian3,it=new ke.Matrix4;function at(e,t,i,a,r,n,o,s,u,h){for(var c=o.length,d=0;d<c;++d){var l=o[d],g=l.cartographic,m=l.index,p=e.length,v=g.longitude,I=g.latitude;I=Oe.CesiumMath.clamp(I,-Oe.CesiumMath.PI_OVER_TWO,Oe.CesiumMath.PI_OVER_TWO);var E=g.height-n.skirtHeight;n.hMin=Math.min(n.hMin,E),Ue.Cartographic.fromRadians(v,I,E,Ze),u&&(Ze.longitude+=s),u?d===c-1?Ze.latitude+=h:0===d&&(Ze.latitude-=h):Ze.latitude+=s;var f=n.ellipsoid.cartographicToCartesian(Ze);e.push(f),t.push(E),i.push(Ye.Cartesian2.clone(i[m])),0<a.length&&a.push(a[m]),ke.Matrix4.multiplyByPoint(n.toENU,f,$e);var C=n.minimum,T=n.maximum;Ue.Cartesian3.minimumByComponent($e,C,C),Ue.Cartesian3.maximumByComponent($e,T,T);var M=n.lastBorderPoint;if(We.defined(M)){var b=M.index;r.push(b,p-1,p,p,m,b)}n.lastBorderPoint=l}}return h(function(e,t){e.ellipsoid=Ye.Ellipsoid.clone(e.ellipsoid),e.rectangle=Ye.Rectangle.clone(e.rectangle);var i=function(e,t,i,a,r,n,o,s,u,h){var c,d,l,g,m,p;p=We.defined(a)?(c=a.west,d=a.south,l=a.east,g=a.north,m=a.width,a.height):(c=Oe.CesiumMath.toRadians(r.west),d=Oe.CesiumMath.toRadians(r.south),l=Oe.CesiumMath.toRadians(r.east),g=Oe.CesiumMath.toRadians(r.north),m=Oe.CesiumMath.toRadians(a.width),Oe.CesiumMath.toRadians(a.height));var v,I,E=[d,g],f=[c,l],C=He.Transforms.eastNorthUpToFixedFrame(t,i),T=ke.Matrix4.inverseTransformation(C,it);s&&(v=Le.WebMercatorProjection.geodeticLatitudeToMercatorAngle(d),I=1/(Le.WebMercatorProjection.geodeticLatitudeToMercatorAngle(g)-v));var M=new DataView(e),b=Number.POSITIVE_INFINITY,N=Number.NEGATIVE_INFINITY,x=et;x.x=Number.POSITIVE_INFINITY,x.y=Number.POSITIVE_INFINITY,x.z=Number.POSITIVE_INFINITY;var S=tt;S.x=Number.NEGATIVE_INFINITY,S.y=Number.NEGATIVE_INFINITY,S.z=Number.NEGATIVE_INFINITY;var w,P,B=0,y=0,A=0;for(P=0;P<4;++P){var R=B;w=M.getUint32(R,!0),R+=Je;var _=Oe.CesiumMath.toRadians(180*M.getFloat64(R,!0));R+=Qe,-1===Xe(f,_)&&f.push(_);var F=Oe.CesiumMath.toRadians(180*M.getFloat64(R,!0));R+=Qe,-1===Xe(E,F)&&E.push(F),R+=2*Qe;var W=M.getInt32(R,!0);R+=qe,y+=W,W=M.getInt32(R,!0),A+=3*W,B+=w+Je}var O=[],U=[],Y=new Array(y),k=new Array(y),V=new Array(y),H=s?new Array(y):[],L=new Array(A),D=[],G=[],j=[],z=[],q=0,J=0;for(P=B=0;P<4;++P){w=M.getUint32(B,!0);var K=B+=Je,Q=Oe.CesiumMath.toRadians(180*M.getFloat64(B,!0));B+=Qe;var X=Oe.CesiumMath.toRadians(180*M.getFloat64(B,!0));B+=Qe;var Z=Oe.CesiumMath.toRadians(180*M.getFloat64(B,!0)),$=.5*Z;B+=Qe;var ee=Oe.CesiumMath.toRadians(180*M.getFloat64(B,!0)),te=.5*ee;B+=Qe;var ie=M.getInt32(B,!0);B+=qe;var ae=M.getInt32(B,!0);B+=qe,B+=qe;for(var re=new Array(ie),ne=0;ne<ie;++ne){var oe=Q+M.getUint8(B++)*Z;Ze.longitude=oe;var se=X+M.getUint8(B++)*ee;Ze.latitude=se;var ue=M.getFloat32(B,!0);if(B+=Ke,0!==ue&&ue<h&&(ue*=-Math.pow(2,u)),ue*=6371010*n,Ze.height=ue,-1!==Xe(f,oe)||-1!==Xe(E,se)){var he=Xe(O,Ze,Ue.Cartographic);if(-1!==he){re[ne]=U[he];continue}O.push(Ue.Cartographic.clone(Ze)),U.push(q)}re[ne]=q,Math.abs(oe-c)<$?D.push({index:q,cartographic:Ue.Cartographic.clone(Ze)}):Math.abs(oe-l)<$?j.push({index:q,cartographic:Ue.Cartographic.clone(Ze)}):Math.abs(se-d)<te?G.push({index:q,cartographic:Ue.Cartographic.clone(Ze)}):Math.abs(se-g)<te&&z.push({index:q,cartographic:Ue.Cartographic.clone(Ze)}),b=Math.min(ue,b),N=Math.max(ue,N),V[q]=ue;var ce=i.cartographicToCartesian(Ze);Y[q]=ce,s&&(H[q]=(Le.WebMercatorProjection.geodeticLatitudeToMercatorAngle(se)-v)*I),ke.Matrix4.multiplyByPoint(T,ce,$e),Ue.Cartesian3.minimumByComponent($e,x,x),Ue.Cartesian3.maximumByComponent($e,S,S);var de=(oe-c)/(l-c);de=Oe.CesiumMath.clamp(de,0,1);var le=(se-d)/(g-d);le=Oe.CesiumMath.clamp(le,0,1),k[q]=new Ye.Cartesian2(de,le),++q}for(var ge=3*ae,me=0;me<ge;++me,++J)L[J]=re[M.getUint16(B,!0)],B+=ze;if(w!==B-K)throw new Ve.RuntimeError("Invalid terrain tile.")}Y.length=q,k.length=q,V.length=q,s&&(H.length=q);var pe=q,ve=J,Ie={hMin:b,lastBorderPoint:void 0,skirtHeight:o,toENU:T,ellipsoid:i,minimum:x,maximum:S};D.sort(function(e,t){return t.cartographic.latitude-e.cartographic.latitude}),G.sort(function(e,t){return e.cartographic.longitude-t.cartographic.longitude}),j.sort(function(e,t){return e.cartographic.latitude-t.cartographic.latitude}),z.sort(function(e,t){return t.cartographic.longitude-e.cartographic.longitude});var Ee=1e-5;if(at(Y,V,k,H,L,Ie,D,-Ee*m,!0,-Ee*p),at(Y,V,k,H,L,Ie,G,-Ee*p,!1),at(Y,V,k,H,L,Ie,j,Ee*m,!0,Ee*p),at(Y,V,k,H,L,Ie,z,Ee*p,!1),0<D.length&&0<z.length){var fe=D[0].index,Ce=pe,Te=z[z.length-1].index,Me=Y.length-1;L.push(Te,Me,Ce,Ce,fe,Te)}y=Y.length;var be,Ne=ke.BoundingSphere.fromPoints(Y);We.defined(a)&&(be=Ge.OrientedBoundingBox.fromRectangle(a,b,N,i));for(var xe=new je.EllipsoidalOccluder(i).computeHorizonCullingPointPossiblyUnderEllipsoid(t,Y,b),Se=new De.AxisAlignedBoundingBox(x,S,t),we=new je.TerrainEncoding(Se,Ie.hMin,N,C,!1,s),Pe=new Float32Array(y*we.getStride()),Be=0,ye=0;ye<y;++ye)Be=we.encode(Pe,Be,Y[ye],k[ye],V[ye],void 0,H[ye]);var Ae=D.map(function(e){return e.index}).reverse(),Re=G.map(function(e){return e.index}).reverse(),_e=j.map(function(e){return e.index}).reverse(),Fe=z.map(function(e){return e.index}).reverse();return Re.unshift(_e[_e.length-1]),Re.push(Ae[0]),Fe.unshift(Ae[Ae.length-1]),Fe.push(_e[0]),{vertices:Pe,indices:new Uint16Array(L),maximumHeight:N,minimumHeight:b,encoding:we,boundingSphere3D:Ne,orientedBoundingBox:be,occludeePointInScaledSpace:xe,vertexCountWithoutSkirts:pe,indexCountWithoutSkirts:ve,westIndicesSouthToNorth:Ae,southIndicesEastToWest:Re,eastIndicesNorthToSouth:_e,northIndicesWestToEast:Fe}}(e.buffer,e.relativeToCenter,e.ellipsoid,e.rectangle,e.nativeRectangle,e.exaggeration,e.skirtHeight,e.includeWebMercatorT,e.negativeAltitudeExponentBias,e.negativeElevationThreshold),a=i.vertices;t.push(a.buffer);var r=i.indices;return t.push(r.buffer),{vertices:a.buffer,indices:r.buffer,numberOfAttributes:i.encoding.getStride(),minimumHeight:i.minimumHeight,maximumHeight:i.maximumHeight,boundingSphere3D:i.boundingSphere3D,orientedBoundingBox:i.orientedBoundingBox,occludeePointInScaledSpace:i.occludeePointInScaledSpace,encoding:i.encoding,vertexCountWithoutSkirts:i.vertexCountWithoutSkirts,indexCountWithoutSkirts:i.indexCountWithoutSkirts,westIndicesSouthToNorth:i.westIndicesSouthToNorth,southIndicesEastToWest:i.southIndicesEastToWest,eastIndicesNorthToSouth:i.eastIndicesNorthToSouth,northIndicesWestToEast:i.northIndicesWestToEast}})});