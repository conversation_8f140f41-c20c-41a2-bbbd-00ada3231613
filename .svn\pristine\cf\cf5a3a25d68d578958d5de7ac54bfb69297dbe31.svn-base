/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports"],function(e){if("undefined"!=typeof WebAssembly&&"object"!=typeof window){var t,_=void 0!==_?_:{},n={};for(t in _)_.hasOwnProperty(t)&&(n[t]=_[t]);_.arguments=[],_.thisProgram="./this.program",_.quit=function(e,t){throw t},_.preRun=[];var r,i,s=!(_.postRun=[]),u=!1;if(s="object"==typeof window,u="function"==typeof importScripts,r="object"==typeof process&&"function"==typeof require&&!s&&!u,i=!s&&!r&&!u,_.ENVIRONMENT)throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)");var o,a,l="";if(r)l=__dirname+"/",_.read=function(e,t){var n;return o||(o=require("fs")),a||(a=require("path")),e=a.normalize(e),n=o.readFileSync(e),t?n:n.toString()},_.readBinary=function(e){var t=_.read(e,!0);return t.buffer||(t=new Uint8Array(t)),m(t.buffer),t},1<process.argv.length&&(_.thisProgram=process.argv[1].replace(/\\/g,"/")),_.arguments=process.argv.slice(2),"undefined"!=typeof module&&(module.exports=_),process.on("uncaughtException",function(e){if(!(e instanceof Re))throw e}),process.on("unhandledRejection",Ae),_.quit=function(e){process.exit(e)},_.inspect=function(){return"[Emscripten Module object]"};else if(i)"undefined"!=typeof read&&(_.read=function(e){return read(e)}),_.readBinary=function(e){var t;return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(m("object"==typeof(t=read(e,"binary"))),t)},"undefined"!=typeof scriptArgs?_.arguments=scriptArgs:void 0!==arguments&&(_.arguments=arguments),"function"==typeof quit&&(_.quit=function(e){quit(e)});else{if(!s&&!u)throw new Error("environment detection error");u?l=self.location.href:document.currentScript&&(l=document.currentScript.src),l=0!==l.indexOf("blob:")?l.substr(0,l.indexOf("/Workers")+1):"",_.read=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},u&&(_.readBinary=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),_.readAsync=function(e,t,n){var r=new XMLHttpRequest;r.open("GET",e,!0),r.responseType="arraybuffer",r.onload=function(){200==r.status||0==r.status&&r.response?t(r.response):n()},r.onerror=n,r.send(null)},_.setWindowTitle=function(e){document.title=e}}var d=_.print||("undefined"!=typeof console?console.log.bind(console):"undefined"!=typeof print?print:null),c=_.printErr||("undefined"!=typeof printErr?printErr:"undefined"!=typeof console&&console.warn.bind(console)||d);for(t in n)n.hasOwnProperty(t)&&(_[t]=n[t]);function p(e){p.shown||(p.shown={}),p.shown[e]||(p.shown[e]=1)}m((n=void 0)===_.memoryInitializerPrefixURL,"Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),m(void 0===_.pthreadMainPrefixURL,"Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),m(void 0===_.cdInitializerPrefixURL,"Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),m(void 0===_.filePackagePrefixURL,"Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),ye=me=he=function(){Ae("cannot use the stack before compiled code is ready to run, and has provided stack access")};var f,E={"f64-rem":function(e,t){return e%t},debugger:function(){}},T=(new Array(0),0);"object"!=typeof WebAssembly&&Ae("No WebAssembly support found. Build with -s WASM=0 to target JavaScript instead.");var h=!1;function m(e,t){e||Ae("Assertion failed: "+t)}function y(e,t,n,r,i){var o={string:function(e){var t,n,r,i=0;if(null!=e&&0!==e){var o=1+(e.length<<2);i=he(o),t=e,n=i,m("number"==typeof(r=o),"stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),function(e,t,n,r){if(!(0<r))return;for(var i=n,o=n+r-1,a=0;a<e.length;++a){var s=e.charCodeAt(a);if(55296<=s&&s<=57343){var u=e.charCodeAt(++a);s=65536+((1023&s)<<10)|1023&u}if(s<=127){if(o<=n)break;t[n++]=s}else if(s<=2047){if(o<=n+1)break;t[n++]=192|s>>6,t[n++]=128|63&s}else if(s<=65535){if(o<=n+2)break;t[n++]=224|s>>12,t[n++]=128|s>>6&63,t[n++]=128|63&s}else{if(o<=n+3)break;2097152<=s&&p("Invalid Unicode code point 0x"+s.toString(16)+" encountered when serializing a JS string to an UTF-8 string on the asm.js/wasm heap! (Valid unicode code points should be in range 0-0x1FFFFF)."),t[n++]=240|s>>18,t[n++]=128|s>>12&63,t[n++]=128|s>>6&63,t[n++]=128|63&s}}t[n]=0}(t,S,n,r)}return i},array:function(e){var t,n,r=he(e.length);return n=r,m(0<=(t=e).length,"writeArrayToMemory array must have a length (should be an array or typed array)"),M.set(t,n),r}};var a,s,u=(m(s=_["_"+(a=e)],"Cannot call unknown function "+a+", make sure it is exported"),s),l=[],d=0;if(m("array"!==t,'Return type should not be "array".'),r)for(var c=0;c<r.length;c++){var f=o[n[c]];l[c]=f?(0===d&&(d=ye()),f(r[c])):r[c]}var E,T=u.apply(null,l);return E=T,T="string"===t?g(E):"boolean"===t?Boolean(E):E,0!==d&&me(d),T}var R="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function w(e,t,n){for(var r=t+n,i=t;e[i]&&!(r<=i);)++i;if(16<i-t&&e.subarray&&R)return R.decode(e.subarray(t,i));for(var o="";t<i;){var a=e[t++];if(128&a){var s=63&e[t++];if(192!=(224&a)){var u=63&e[t++];if((a=224==(240&a)?(15&a)<<12|s<<6|u:(240!=(248&a)&&p("Invalid UTF-8 leading byte 0x"+a.toString(16)+" encountered when deserializing a UTF-8 string on the asm.js/wasm heap to a JS string!"),(7&a)<<18|s<<12|u<<6|63&e[t++]))<65536)o+=String.fromCharCode(a);else{var l=a-65536;o+=String.fromCharCode(55296|l>>10,56320|1023&l)}}else o+=String.fromCharCode((31&a)<<6|s)}else o+=String.fromCharCode(a)}return o}function g(e,t){return e?w(S,e,t):""}"undefined"!=typeof TextDecoder&&new TextDecoder("utf-16le");function A(){var e=function(){var t=new Error;if(!t.stack){try{throw new Error(0)}catch(e){t=e}if(!t.stack)return"(no stack trace available)"}return t.stack.toString()}();return _.extraStackTrace&&(e+="\n"+_.extraStackTrace()),e.replace(/__Z[\w\d_]+/g,function(e){var t=e;return e===t?e:t+" ["+e+"]"})}var O,M,S,b,F,v,I,N;function x(e,t){return 0<e%t&&(e+=t-e%t),e}function D(){_.HEAP8=M=new Int8Array(O),_.HEAP16=b=new Int16Array(O),_.HEAP32=F=new Int32Array(O),_.HEAPU8=S=new Uint8Array(O),_.HEAPU16=new Uint16Array(O),_.HEAPU32=v=new Uint32Array(O),_.HEAPF32=I=new Float32Array(O),_.HEAPF64=N=new Float64Array(O)}var U=5257984;m(!0,"stack must start aligned"),m(!0,"heap must start aligned");var X=5242880;_.TOTAL_STACK&&m(X===_.TOTAL_STACK,"the stack size can no longer be determined at runtime");var P=_.TOTAL_MEMORY||16777216;function L(){34821223==v[(U>>2)-1]&&2310721022==v[(U>>2)-2]||Ae("Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x02135467, but received 0x"+v[(U>>2)-2].toString(16)+" "+v[(U>>2)-1].toString(16)),1668509029!==F[0]&&Ae("Runtime error: The application has corrupted its heap memory area (address zero)!")}if(P<X&&c("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+P+"! (TOTAL_STACK="+X+")"),m("undefined"!=typeof Int32Array&&"undefined"!=typeof Float64Array&&void 0!==Int32Array.prototype.subarray&&void 0!==Int32Array.prototype.set,"JS engine does not provide full typed array support"),_.buffer?m((O=_.buffer).byteLength===P,"provided buffer should be "+P+" bytes, but it is "+O.byteLength):m((O="object"==typeof WebAssembly&&"function"==typeof WebAssembly.Memory?(m(P%65536==0),(f=new WebAssembly.Memory({initial:P/65536})).buffer):new ArrayBuffer(P)).byteLength===P),D(),F[3768]=5257984,F[0]=1668509029,b[1]=25459,115!==S[2]||99!==S[3])throw"Runtime error: expected the system to be little-endian!";function k(e){for(;0<e.length;){var t=e.shift();if("function"!=typeof t){var n=t.func;"number"==typeof n?void 0===t.arg?_.dynCall_v(n):_.dynCall_vi(n,t.arg):n(void 0===t.arg?null:t.arg)}else t()}}var H=[],C=[],Q=[],B=[],Y=!1;m(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),m(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),m(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),m(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var W=0,z=null,j=null,V={};_.preloadedImages={},_.preloadedAudios={};var G={error:function(){Ae("Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with  -s FORCE_FILESYSTEM=1")},init:function(){G.error()},createDataFile:function(){G.error()},createPreloadedFile:function(){G.error()},createLazyFile:function(){G.error()},open:function(){G.error()},mkdev:function(){G.error()},registerDevice:function(){G.error()},analyzePath:function(){G.error()},loadFilesFromDB:function(){G.error()},ErrnoError:function(){G.error()}};_.FS_createDataFile=G.createDataFile,_.FS_createPreloadedFile=G.createPreloadedFile;var q="data:application/octet-stream;base64,";function J(e){return String.prototype.startsWith?e.startsWith(q):0===e.indexOf(q)}var K="ThirdParty/unzip.wasm";function Z(){try{if(_.wasmBinary)return new Uint8Array(_.wasmBinary);if(_.readBinary)return _.readBinary(K);throw"both async and sync fetching of the wasm failed"}catch(e){Ae(e)}}function $(e){var t,n={env:e,global:{NaN:NaN,Infinity:1/0},"global.Math":Math,asm2wasm:E};function r(e,t){var n=e.exports;_.asm=n,function(e){if(W--,_.monitorRunDependencies&&_.monitorRunDependencies(W),e?(m(V[e]),delete V[e]):c("warning: run dependency removed without ID"),0==W&&(null!==z&&(clearInterval(z),z=null),j)){var t=j;j=null,t()}}("wasm-instantiate")}t="wasm-instantiate",W++,_.monitorRunDependencies&&_.monitorRunDependencies(W),t&&(m(!V[t]),V[t]=1,null===z&&"undefined"!=typeof setInterval&&(z=setInterval(function(){if(h)return clearInterval(z),void(z=null)},1e4)));var i=_;function o(e){m(_===i,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),i=null,r(e.instance)}function a(e){return(_.wasmBinary||!s&&!u||"function"!=typeof fetch?new Promise(function(e,t){e(Z())}):fetch(K,{credentials:"same-origin"}).then(function(e){if(!e.ok)throw"failed to load wasm binary file at '"+K+"'";return e.arrayBuffer()}).catch(function(){return Z()})).then(function(e){return WebAssembly.instantiate(e,n)}).then(e,function(e){})}if(_.instantiateWasm)try{return _.instantiateWasm(n,r)}catch(e){return!1}return function(){if(_.wasmBinary||"function"!=typeof WebAssembly.instantiateStreaming||J(K)||"function"!=typeof fetch)return a(o);fetch(K,{credentials:"same-origin"}).then(function(e){return WebAssembly.instantiateStreaming(e,n).then(o,function(e){a(o)})})}(),{}}J(K)||(Oe=K,K=_.locateFile?_.locateFile(Oe,l):l+Oe),_.asm=function(e,t,n){t.memory=f,t.table=new WebAssembly.Table({initial:22,maximum:22,element:"anyfunc"}),t.__memory_base=1024,t.__table_base=0;var r=$(t);return m(r,"binaryen setup failed (no wasm support?)"),r};m(!0);var ee={buffers:[null,[],[]],printChar:function(e,t){var n=ee.buffers[e];m(n),0===t||10===t?((1===e?d:c)(w(n,0)),n.length=0):n.push(t)},varargs:0,get:function(e){return ee.varargs+=4,F[ee.varargs-4>>2]},getStr:function(){return g(ee.get())},get64:function(){var e=ee.get(),t=ee.get();return m(0<=e?0===t:-1===t),e},getZero:function(){m(0===ee.get())}};function te(){return M.length}function ne(t){t=x(t,65536);var n=O.byteLength;try{return-1!==f.grow((t-n)/65536)&&(O=f.buffer,!0)}catch(e){return console.error("emscripten_realloc_buffer: Attempted to grow from "+n+" bytes to "+t+" bytes, but got error: "+e),!1}}var re={abort:Ae,setTempRet0:function(e){T=e},getTempRet0:function(){return T},abortStackOverflow:function(e){Ae("Stack overflow! Attempted to allocate "+e+" bytes on the stack, but stack has only "+(U-ye()+e)+" bytes available!")},nullFunc_ii:function(e){c("Invalid function pointer called with signature 'ii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),c("Build with ASSERTIONS=2 for more info."),Ae(e)},nullFunc_iiii:function(e){c("Invalid function pointer called with signature 'iiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),c("Build with ASSERTIONS=2 for more info."),Ae(e)},nullFunc_jiji:function(e){c("Invalid function pointer called with signature 'jiji'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),c("Build with ASSERTIONS=2 for more info."),Ae(e)},nullFunc_vii:function(e){c("Invalid function pointer called with signature 'vii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),c("Build with ASSERTIONS=2 for more info."),Ae(e)},___lock:function(){},___setErrNo:function(e){return _.___errno_location?F[_.___errno_location()>>2]=e:c("failed to set errno from JS"),e},___syscall140:function(e,t){ee.varargs=t;try{return ee.getStreamFromFD(),ee.get(),ee.get(),ee.get(),ee.get(),Ae("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM"),0}catch(e){return void 0!==G&&e instanceof G.ErrnoError||Ae(e),-e.errno}},___syscall146:function(e,t){ee.varargs=t;try{for(var n=ee.get(),r=ee.get(),i=ee.get(),o=0,a=0;a<i;a++){for(var s=F[r+8*a>>2],u=F[r+(8*a+4)>>2],l=0;l<u;l++)ee.printChar(n,S[s+l]);o+=u}return o}catch(e){return void 0!==G&&e instanceof G.ErrnoError||Ae(e),-e.errno}},___syscall54:function(e,t){ee.varargs=t;try{return 0}catch(e){return void 0!==G&&e instanceof G.ErrnoError||Ae(e),-e.errno}},___syscall6:function(e,t){ee.varargs=t;try{return ee.getStreamFromFD(),Ae("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM"),0}catch(e){return void 0!==G&&e instanceof G.ErrnoError||Ae(e),-e.errno}},___unlock:function(){},_emscripten_get_heap_size:te,_emscripten_memcpy_big:function(e,t,n){S.set(S.subarray(t,t+n),e)},_emscripten_resize_heap:function(e){var t=te();m(t<e);var n=2147418112;if(n<e)return c("Cannot enlarge memory, asked to go up to "+e+" bytes, but the limit is "+n+" bytes!"),!1;for(var r=Math.max(t,16777216);r<e;)(r=r<=536870912?x(2*r,65536):Math.min(x((3*r+2147483648)/4,65536),n))===t&&p("Cannot ask for more memory since we reached the practical limit in browsers (which is just below 2GB), so the request would have failed. Requesting only "+M.length);return ne(r)?(D(),!0):(c("Failed to grow the heap from "+t+" bytes to "+r+" bytes, not enough memory!"),!1)},abortOnCannotGrowMemory:function(e){Ae("Cannot enlarge memory arrays to size "+e+" bytes (OOM). Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+M.length+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")},emscripten_realloc_buffer:ne,flush_NO_FILESYSTEM:function(){var e=_._fflush;e&&e(0);var t=ee.buffers;t[1].length&&ee.printChar(1,10),t[2].length&&ee.printChar(2,10)},tempDoublePtr:15088,DYNAMICTOP_PTR:15072},ie=_.asm({},re,O),oe=ie.___errno_location;ie.___errno_location=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),oe.apply(null,arguments)};var ae=ie._fflush;ie._fflush=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ae.apply(null,arguments)};var se=ie._free;ie._free=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),se.apply(null,arguments)};var ue=ie._freePointer;ie._freePointer=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ue.apply(null,arguments)};var le=ie._llvm_bswap_i32;ie._llvm_bswap_i32=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),le.apply(null,arguments)};var de=ie._malloc;ie._malloc=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),de.apply(null,arguments)};var ce=ie._sbrk;ie._sbrk=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ce.apply(null,arguments)};var fe=ie._unzip;ie._unzip=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),fe.apply(null,arguments)};var Ee=ie.establishStackSpace;ie.establishStackSpace=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Ee.apply(null,arguments)};var Te=ie.stackAlloc;ie.stackAlloc=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Te.apply(null,arguments)};var _e=ie.stackRestore;ie.stackRestore=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_e.apply(null,arguments)};var pe=ie.stackSave;ie.stackSave=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),pe.apply(null,arguments)},_.asm=ie;_.___errno_location=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm.___errno_location.apply(null,arguments)},_._emscripten_replace_memory=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm._emscripten_replace_memory.apply(null,arguments)},_._fflush=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm._fflush.apply(null,arguments)},_._free=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm._free.apply(null,arguments)},_._freePointer=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm._freePointer.apply(null,arguments)},_._llvm_bswap_i32=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm._llvm_bswap_i32.apply(null,arguments)},_._malloc=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm._malloc.apply(null,arguments)},_._memcpy=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm._memcpy.apply(null,arguments)},_._memset=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm._memset.apply(null,arguments)},_._sbrk=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm._sbrk.apply(null,arguments)},_._unzip=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm._unzip.apply(null,arguments)},_.establishStackSpace=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm.establishStackSpace.apply(null,arguments)};var he=_.stackAlloc=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm.stackAlloc.apply(null,arguments)},me=_.stackRestore=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm.stackRestore.apply(null,arguments)},ye=_.stackSave=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm.stackSave.apply(null,arguments)};_.dynCall_ii=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm.dynCall_ii.apply(null,arguments)},_.dynCall_iiii=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm.dynCall_iiii.apply(null,arguments)},_.dynCall_jiji=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm.dynCall_jiji.apply(null,arguments)},_.dynCall_vii=function(){return m(Y,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),m(!0,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_.asm.dynCall_vii.apply(null,arguments)};function Re(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function we(e){function t(){_.calledRun||(_.calledRun=!0,h||(L(),Y||(Y=!0,k(C)),L(),k(Q),_.onRuntimeInitialized&&_.onRuntimeInitialized(),m(!_._main,'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),function(){if(L(),_.postRun)for("function"==typeof _.postRun&&(_.postRun=[_.postRun]);_.postRun.length;)e=_.postRun.shift(),B.unshift(e);var e;k(B)}()))}e=e||_.arguments,0<W||(m(0==(3&U)),v[(U>>2)-1]=34821223,v[(U>>2)-2]=2310721022,function(){if(_.preRun)for("function"==typeof _.preRun&&(_.preRun=[_.preRun]);_.preRun.length;)e=_.preRun.shift(),H.unshift(e);var e;k(H)}(),0<W||_.calledRun||(_.setStatus?(_.setStatus("Running..."),setTimeout(function(){setTimeout(function(){_.setStatus("")},1),t()},1)):t(),L()))}_.asm=ie,_.intArrayFromString||(_.intArrayFromString=function(){Ae("'intArrayFromString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.intArrayToString||(_.intArrayToString=function(){Ae("'intArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.ccall=y,_.cwrap=function(e,t,n,r){return function(){return y(e,t,n,arguments)}},_.setValue||(_.setValue=function(){Ae("'setValue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.getValue=function(e,t,n){switch("*"===(t=t||"i8").charAt(t.length-1)&&(t="i32"),t){case"i1":case"i8":return M[e>>0];case"i16":return b[e>>1];case"i32":case"i64":return F[e>>2];case"float":return I[e>>2];case"double":return N[e>>3];default:Ae("invalid type for getValue: "+t)}return null},_.allocate||(_.allocate=function(){Ae("'allocate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.getMemory||(_.getMemory=function(){Ae("'getMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),_.AsciiToString||(_.AsciiToString=function(){Ae("'AsciiToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.stringToAscii||(_.stringToAscii=function(){Ae("'stringToAscii' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.UTF8ArrayToString||(_.UTF8ArrayToString=function(){Ae("'UTF8ArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.UTF8ToString||(_.UTF8ToString=function(){Ae("'UTF8ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.stringToUTF8Array||(_.stringToUTF8Array=function(){Ae("'stringToUTF8Array' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.stringToUTF8||(_.stringToUTF8=function(){Ae("'stringToUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.lengthBytesUTF8||(_.lengthBytesUTF8=function(){Ae("'lengthBytesUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.UTF16ToString||(_.UTF16ToString=function(){Ae("'UTF16ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.stringToUTF16||(_.stringToUTF16=function(){Ae("'stringToUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.lengthBytesUTF16||(_.lengthBytesUTF16=function(){Ae("'lengthBytesUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.UTF32ToString||(_.UTF32ToString=function(){Ae("'UTF32ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.stringToUTF32||(_.stringToUTF32=function(){Ae("'stringToUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.lengthBytesUTF32||(_.lengthBytesUTF32=function(){Ae("'lengthBytesUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.allocateUTF8||(_.allocateUTF8=function(){Ae("'allocateUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.stackTrace||(_.stackTrace=function(){Ae("'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.addOnPreRun||(_.addOnPreRun=function(){Ae("'addOnPreRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.addOnInit||(_.addOnInit=function(){Ae("'addOnInit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.addOnPreMain||(_.addOnPreMain=function(){Ae("'addOnPreMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.addOnExit||(_.addOnExit=function(){Ae("'addOnExit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.addOnPostRun||(_.addOnPostRun=function(){Ae("'addOnPostRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.writeStringToMemory||(_.writeStringToMemory=function(){Ae("'writeStringToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.writeArrayToMemory||(_.writeArrayToMemory=function(){Ae("'writeArrayToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.writeAsciiToMemory||(_.writeAsciiToMemory=function(){Ae("'writeAsciiToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.addRunDependency||(_.addRunDependency=function(){Ae("'addRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),_.removeRunDependency||(_.removeRunDependency=function(){Ae("'removeRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),_.ENV||(_.ENV=function(){Ae("'ENV' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.FS||(_.FS=function(){Ae("'FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.FS_createFolder||(_.FS_createFolder=function(){Ae("'FS_createFolder' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),_.FS_createPath||(_.FS_createPath=function(){Ae("'FS_createPath' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),_.FS_createDataFile||(_.FS_createDataFile=function(){Ae("'FS_createDataFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),_.FS_createPreloadedFile||(_.FS_createPreloadedFile=function(){Ae("'FS_createPreloadedFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),_.FS_createLazyFile||(_.FS_createLazyFile=function(){Ae("'FS_createLazyFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),_.FS_createLink||(_.FS_createLink=function(){Ae("'FS_createLink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),_.FS_createDevice||(_.FS_createDevice=function(){Ae("'FS_createDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),_.FS_unlink||(_.FS_unlink=function(){Ae("'FS_unlink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),_.GL||(_.GL=function(){Ae("'GL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.dynamicAlloc||(_.dynamicAlloc=function(){Ae("'dynamicAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.warnOnce||(_.warnOnce=function(){Ae("'warnOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.loadDynamicLibrary||(_.loadDynamicLibrary=function(){Ae("'loadDynamicLibrary' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.loadWebAssemblyModule||(_.loadWebAssemblyModule=function(){Ae("'loadWebAssemblyModule' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.getLEB||(_.getLEB=function(){Ae("'getLEB' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.getFunctionTables||(_.getFunctionTables=function(){Ae("'getFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.alignFunctionTables||(_.alignFunctionTables=function(){Ae("'alignFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.registerFunctions||(_.registerFunctions=function(){Ae("'registerFunctions' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.addFunction||(_.addFunction=function(){Ae("'addFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.removeFunction||(_.removeFunction=function(){Ae("'removeFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.getFuncWrapper||(_.getFuncWrapper=function(){Ae("'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.prettyPrint||(_.prettyPrint=function(){Ae("'prettyPrint' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.makeBigInt||(_.makeBigInt=function(){Ae("'makeBigInt' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.dynCall||(_.dynCall=function(){Ae("'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.getCompilerSetting||(_.getCompilerSetting=function(){Ae("'getCompilerSetting' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.stackSave||(_.stackSave=function(){Ae("'stackSave' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.stackRestore||(_.stackRestore=function(){Ae("'stackRestore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.stackAlloc||(_.stackAlloc=function(){Ae("'stackAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.establishStackSpace||(_.establishStackSpace=function(){Ae("'establishStackSpace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.print||(_.print=function(){Ae("'print' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.printErr||(_.printErr=function(){Ae("'printErr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.getTempRet0||(_.getTempRet0=function(){Ae("'getTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.setTempRet0||(_.setTempRet0=function(){Ae("'setTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.Pointer_stringify||(_.Pointer_stringify=function(){Ae("'Pointer_stringify' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),_.ALLOC_NORMAL||Object.defineProperty(_,"ALLOC_NORMAL",{get:function(){Ae("'ALLOC_NORMAL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),_.ALLOC_STACK||Object.defineProperty(_,"ALLOC_STACK",{get:function(){Ae("'ALLOC_STACK' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),_.ALLOC_DYNAMIC||Object.defineProperty(_,"ALLOC_DYNAMIC",{get:function(){Ae("'ALLOC_DYNAMIC' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),_.ALLOC_NONE||Object.defineProperty(_,"ALLOC_NONE",{get:function(){Ae("'ALLOC_NONE' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),(Re.prototype=new Error).constructor=Re,j=function e(){_.calledRun||we(),_.calledRun||(j=e)},_.run=we;var ge=[];function Ae(t){_.onAbort&&_.onAbort(t),h=!0;var n="abort("+(t=void 0!==t?'"'+t+'"':"")+") at "+A();throw ge&&ge.forEach(function(e){n=e(n,t)}),n}if(_.abort=Ae,_.preInit)for("function"==typeof _.preInit&&(_.preInit=[_.preInit]);0<_.preInit.length;)_.preInit.pop()();_.noExitRuntime=!0,we()}else _=null;var Oe,Me=_;e.unzip=Me});