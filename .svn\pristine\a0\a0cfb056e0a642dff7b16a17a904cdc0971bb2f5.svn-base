/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./GeometryAttributes-aacecde6","./IndexDatatype-9435b55f","./createTaskProcessorWorker","./arrayFill-9766fb2e","./GeometryOffsetAttribute-999fc023","./VertexFormat-fe4db402","./BoxGeometry-92274cfb","./CylinderGeometryLibrary-8c0fda9f","./CylinderGeometry-dc527951","./EllipsoidGeometry-d710e362","./Color-69f1845f"],function(P,e,t,Z,r,q,n,a,i,o,d,s,c,f,l,u,W,h,b,p,y,_,g,N,Y,S){function F(e){this.offset=e.offset,this.count=e.count,this.color=e.color,this.batchIds=e.batchIds}var x=new Z.Cartesian3,v=q.Matrix4.packedLength+Z.Cartesian3.packedLength,C=q.Matrix4.packedLength+2,m=q.Matrix4.packedLength+Z.Cartesian3.packedLength,I=Z.Cartesian3.packedLength+1,k={modelMatrix:new q.Matrix4,boundingVolume:new q.BoundingSphere};function j(e,t){var r=t*v,n=Z.Cartesian3.unpack(e,r,x);r+=Z.Cartesian3.packedLength;var a=q.Matrix4.unpack(e,r,k.modelMatrix);q.Matrix4.multiplyByScale(a,n,a);var i=k.boundingVolume;return Z.Cartesian3.clone(Z.Cartesian3.ZERO,i.center),i.radius=Math.sqrt(3),k}function z(e,t){var r=t*C,n=e[r++],a=e[r++],i=Z.Cartesian3.fromElements(n,n,a,x),o=q.Matrix4.unpack(e,r,k.modelMatrix);q.Matrix4.multiplyByScale(o,i,o);var d=k.boundingVolume;return Z.Cartesian3.clone(Z.Cartesian3.ZERO,d.center),d.radius=Math.sqrt(2),k}function H(e,t){var r=t*m,n=Z.Cartesian3.unpack(e,r,x);r+=Z.Cartesian3.packedLength;var a=q.Matrix4.unpack(e,r,k.modelMatrix);q.Matrix4.multiplyByScale(a,n,a);var i=k.boundingVolume;return Z.Cartesian3.clone(Z.Cartesian3.ZERO,i.center),i.radius=1,k}function J(e,t){var r=t*I,n=e[r++],a=Z.Cartesian3.unpack(e,r,x),i=q.Matrix4.fromTranslation(a,k.modelMatrix);q.Matrix4.multiplyByUniformScale(i,n,i);var o=k.boundingVolume;return Z.Cartesian3.clone(Z.Cartesian3.ZERO,o.center),o.radius=1,k}var T=new Z.Cartesian3;function K(e,t,r,n,a){if(P.defined(t)){for(var i=r.length,o=n.attributes.position.values,d=n.indices,s=e.positions,c=e.vertexBatchIds,f=e.indices,l=e.batchIds,u=e.batchTableColors,h=e.batchedIndices,b=e.indexOffsets,p=e.indexCounts,y=e.boundingVolumes,g=e.modelMatrix,x=e.center,v=e.positionOffset,C=e.batchIdIndex,m=e.indexOffset,I=e.batchedIndicesOffset,k=0;k<i;++k){var M=a(t,k),B=M.modelMatrix;q.Matrix4.multiply(g,B,B);for(var w=r[k],A=o.length,O=0;O<A;O+=3){var L=Z.Cartesian3.unpack(o,O,T);q.Matrix4.multiplyByPoint(B,L,L),Z.Cartesian3.subtract(L,x,L),Z.Cartesian3.pack(L,s,3*v+O),c[C++]=w}for(var E=d.length,U=0;U<E;++U)f[m+U]=d[U]+v;var G=k+I;h[G]=new F({offset:m,count:E,color:S.Color.fromRgba(u[w]),batchIds:[w]}),l[G]=w,b[G]=m,p[G]=E,y[G]=q.BoundingSphere.transform(M.boundingVolume,B),v+=A/3,m+=E}e.positionOffset=v,e.batchIdIndex=C,e.indexOffset=m,e.batchedIndicesOffset+=i}}var Q=new Z.Cartesian3,X=new q.Matrix4;function $(e,t,r){var n=r.length,a=2+n*q.BoundingSphere.packedLength+1+function(e){for(var t=e.length,r=0,n=0;n<t;++n)r+=S.Color.packedLength+3+e[n].batchIds.length;return r}(t),i=new Float64Array(a),o=0;i[o++]=e,i[o++]=n;for(var d=0;d<n;++d)q.BoundingSphere.pack(r[d],i,o),o+=q.BoundingSphere.packedLength;var s=t.length;i[o++]=s;for(var c=0;c<s;++c){var f=t[c];S.Color.pack(f.color,i,o),o+=S.Color.packedLength,i[o++]=f.offset,i[o++]=f.count;var l=f.batchIds,u=l.length;i[o++]=u;for(var h=0;h<u;++h)i[o++]=l[h]}return i}return h(function(e,t){var r=P.defined(e.boxes)?new Float32Array(e.boxes):void 0,n=P.defined(e.boxBatchIds)?new Uint16Array(e.boxBatchIds):void 0,a=P.defined(e.cylinders)?new Float32Array(e.cylinders):void 0,i=P.defined(e.cylinderBatchIds)?new Uint16Array(e.cylinderBatchIds):void 0,o=P.defined(e.ellipsoids)?new Float32Array(e.ellipsoids):void 0,d=P.defined(e.ellipsoidBatchIds)?new Uint16Array(e.ellipsoidBatchIds):void 0,s=P.defined(e.spheres)?new Float32Array(e.spheres):void 0,c=P.defined(e.sphereBatchIds)?new Uint16Array(e.sphereBatchIds):void 0,f=P.defined(r)?n.length:0,l=P.defined(a)?i.length:0,u=P.defined(o)?d.length:0,h=P.defined(s)?c.length:0,b=_.BoxGeometry.getUnitBox(),p=N.CylinderGeometry.getUnitCylinder(),y=Y.EllipsoidGeometry.getUnitEllipsoid(),g=b.attributes.position.values,x=p.attributes.position.values,v=y.attributes.position.values,C=g.length*f;C+=x.length*l,C+=v.length*(u+h);var m=b.indices,I=p.indices,k=y.indices,M=m.length*f;M+=I.length*l,M+=k.length*(u+h);var B,w,A,O=new Float32Array(C),L=new Uint16Array(C/3),E=W.IndexDatatype.createTypedArray(C/3,M),U=f+l+u+h,G=new Uint16Array(U),S=new Array(U),F=new Uint32Array(U),T=new Uint32Array(U),V=new Array(U);B=e.packedBuffer,w=new Float64Array(B),A=0,Z.Cartesian3.unpack(w,A,Q),A+=Z.Cartesian3.packedLength,q.Matrix4.unpack(w,A,X);var R={batchTableColors:new Uint32Array(e.batchTableColors),positions:O,vertexBatchIds:L,indices:E,batchIds:G,batchedIndices:S,indexOffsets:F,indexCounts:T,boundingVolumes:V,positionOffset:0,batchIdIndex:0,indexOffset:0,batchedIndicesOffset:0,modelMatrix:X,center:Q};K(R,r,n,b,j),K(R,a,i,p,z),K(R,o,d,y,H),K(R,s,c,y,J);var D=$(E.BYTES_PER_ELEMENT,S,V);return t.push(O.buffer,L.buffer,E.buffer),t.push(G.buffer,F.buffer,T.buffer),t.push(D.buffer),{positions:O.buffer,vertexBatchIds:L.buffer,indices:E.buffer,indexOffsets:F.buffer,indexCounts:T.buffer,batchIds:G.buffer,packedBuffer:D.buffer}})});