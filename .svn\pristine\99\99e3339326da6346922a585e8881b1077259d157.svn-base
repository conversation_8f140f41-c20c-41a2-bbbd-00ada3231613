<template>
    <div class="ele-container">
        <div class="ele-top">
            <div class="ele-item ele-left" @click="totalChargeDialogVisible = true">
                <div class="ele-info">
                    <div class="info-title">累计碳足迹</div>
                    <div class="info-num">
                        <el-tooltip :content="(hisCarbonFootprint.totalConsumption || 0) + ''" placement="top">
                            <span>{{ hisCarbonFootprint.totalConsumption || 0 }}</span>
                        </el-tooltip>
                    </div>
                </div>
                <div class="ele-img">
                    <img src="@/assets/images/carbon-foot.png" alt="充电量" class="img-bg">
                </div>
            </div>
            <div class="ele-item ele-right" @click="totalDischargeDialogVisible=true">
                <div class="ele-info">
                    <div class="info-title">累计碳减排量</div>
                    <div class="info-num">
                        <el-tooltip :content="(hisCarbonReduction.totalReduction || 0) + ''" placement="top">
                            <span>{{ hisCarbonReduction.totalReduction || 0 }}</span>
                        </el-tooltip>
                    </div>
                </div>
                <div class="ele-img">
                    <img src="@/assets/images/carbon-reduction.png" alt="耗电量" class="img-bg">
                </div>
            </div>
        </div>

        <!-- <Card
            :status="dropdownStatus"
            @getDropdownValue="getDropdownValue"         @getDropdownValue="getDropdownValue"
            class="card-type card-item"
        >
            <CarbonChart :dropdownValue="dropdownValue"></CarbonChart>
        </Card> -->

        <Card
            :status="dropdownStatus"
            class="card-type card-item"
        >
            <!-- 下拉框 + 图例放在同一行 -->
            <div class="chart-header">
                <!-- <el-select v-model="timeRange" placeholder="范围" size="mini" @change="updateChartData">
                <el-option label="日" value="day"></el-option>
                <el-option label="月" value="month"></el-option>
                </el-select> -->
                <div class="unit-label">单位/kgCO₂</div>
                <div class="custom-legend">
                    <span class="legend-item"><i class="legend-color color1"></i>碳足迹</span>
                    <span class="legend-item"><i class="legend-color color2"></i>碳减排量</span>
                </div>
            </div>

            <!-- 小图表容器 -->
            <div class="mini-chart-container" >
                <CarbonChart v-if="chartData.carbonFootprint.some(v => v !== 0)" :chartData="chartData" :key="'chart-' + timeRange" />
            </div>
        </Card>
        
    </div>
</template>

<script>
import { h } from 'vue';
//import DetailsChart from '../Chart/DetailsChart.vue';
import CarbonChart from "../CarbonChart/CarbonChart.vue";//柱状图
import constDataSet from "@/utils/const";


export default {
    name: 'CarbonInfo',
    components: { 
        //DetailsChart,
        CarbonChart,
     },
    props: ["dropdownValue2"],
    data() {
        return {
            eleData: {},
            hisCarbonFootprint: {},
            hisCarbonReduction: {},
            totalChargeDialogVisible: false,
            totalDischargeDialogVisible: false,
            //柱状图
            dropdownStatus: true,
            dropdownValue2: 1,
            titleData: constDataSet.titleData,
            //柱状图最新
            timeRange: "day",
            chartData: {
                categories: ["电动挖掘机", "电动装载机", "电动搅拌车", "电动自卸车"],//电动混凝土搅拌车
                carbonFootprint: [0, 0, 0, 0], // 默认初始化
                carbonReduction: [0, 0, 0, 0]  // 默认初始化
                // carbonFootprint: [120, 95, 80, 70],
                // carbonReduction: [30, 25, 20, 15],
            },
        }
    },
    watch: {
        "$store.state.tree.sectionId": {
            handler: function () {
                //this.getLevel();
                this.getHisCarbonFootprint();
                this.getHisCarbonReduction();
                this.updateChartData(this.keyword);
            },
            deep: true,
            immediate: true,
        },
        dropdownValue2: {
            handler() {
                this.keyword = this.dropdownValue2;
                console.log("dropdownValue发生变更，此时keyword为：");
                console.log(this.keyword);
                this.updateChartData(this.keyword); ;
            },
            deep: true,
        },
    },
    mounted() {
        this.keyword = this.dropdownValue2;
        this.updateChartData(this.keyword); // 组件挂载后立即获取默认时间范围的数据
    },

    methods: {
        // async getLevel() {
        //     let res = await this.$http.get(`/equip/build/powerOverview?sectionId=${this.$store.state.tree.sectionId||''}`);
        //     this.eleData = res?.result || {}
        // },

        async getHisCarbonFootprint(){
            let res = await this.$http.get(`/equip/build/carbonFootprint/history?sectionId=${this.$store.state.tree.sectionId||''}`);
            console.log('历史碳足迹',res?.result);
            this.hisCarbonFootprint = res?.result || {}
        },

        async getHisCarbonReduction(){
            let res = await this.$http.get(`/equip/build/carbonReduction/history?sectionId=${this.$store.state.tree.sectionId||''}`);
            this.hisCarbonReduction = res?.result || {}
        },



        //柱状图最新
        async updateChartData(selectedRange) {
            await this.fetchDataAndUpdateChart(selectedRange);
            // let footData, reduceData;

            // if (selectedRange === "day") {
            //     footData = [120, 95, 80, 70];
            //     reduceData = [30, 25, 20, 15];
            // } else {
            //     footData = [1200, 950, 800, 700];
            //     reduceData = [300, 250, 200, 150];
            // }

            // this.chartData = {
            //     categories: ["电动挖掘机", "电动装载机", "电动混凝土搅拌车", "电动自卸车"],
            //     carbonFootprint: footData,
            //     carbonReduction: reduceData,
            // };
        },

        async fetchDataAndUpdateChart(selectedRange) {
            const sectionId = this.$store.state.tree.sectionId || '';
            const today = new Date();
            let dateParam = '';
            let monthParam = '';

            console.log('keyword是:', selectedRange);

            if (selectedRange === 1) {//此时展示当日
                dateParam = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
            } else {
                monthParam = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}`;
            }

            // 请求碳足迹数据
            const footUrl = selectedRange === 1
                ? `/equip/build/dailyCarbonData?sectionId=${sectionId}&date=${dateParam}`
                : `/equip/build/monthlyCarbonData?sectionId=${sectionId}&month=${monthParam}`;

            try {
                const footRes = await this.$http.get(footUrl);
                const result = footRes?.result || {};

                const carbonFootprintData = [
                    parseFloat(result.wjjFootprint) || 0,
                    parseFloat(result.zzjFootprint) || 0,
                    parseFloat(result.hntjbcFootprint) || 0,
                    parseFloat(result.zxcFootprint) || 0
                ];

                // 请求碳减排量数据（结构类似）
                const reduceUrl = selectedRange === 1
                    ? `/equip/build/dailyCarbonReduction?sectionId=${sectionId}&date=${dateParam}`
                    : `/equip/build/monthlyCarbonReduction?sectionId=${sectionId}&month=${monthParam}`;

                const reduceRes = await this.$http.get(reduceUrl);
                const reduceResult = reduceRes?.result || {};

                const carbonReductionData = [
                    parseFloat(reduceResult.wjjReduction) || 0,
                    parseFloat(reduceResult.zzjReduction) || 0,
                    parseFloat(reduceResult.hntjbcReduction) || 0,
                    parseFloat(reduceResult.zxcReduction) || 0
                ];

                // 更新图表数据
                this.chartData = {
                    categories: [...this.chartData.categories],
                    carbonFootprint: [...carbonFootprintData],
                    carbonReduction: [...carbonReductionData]
                };

                console.log('更新数据：', carbonFootprintData, carbonReductionData);

            } catch (error) {
                console.error('Error fetching chart data:', error);
                this.chartData = {
                    categories: [...this.chartData.categories],
                    carbonFootprint: [0, 0, 0, 0],
                    carbonReduction: [0, 0, 0, 0]
                };
            }
        }

    }

}
</script>

<style lang="scss" scoped>
.ele-container {
    width: 370px;
    height: 100%;

    .ele-top {
        width: 100%;
        height: 30%;
        margin-top: 1%;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;

        .ele-item {
            width: 180px;
            height: 100%;
            border-radius: 2px;
            border: 1px solid #B3CAFF;
            padding: 10px;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            cursor: pointer;

            .ele-info {
                width: 120px;
                font-family: '微软雅黑', sans-serif;
                font-weight: 400;
                font-style: normal;
                font-size: 16px;
                color: #3A3F5D;

                .info-title {
                    width: 100%;
                    height: 50%;
                    padding-left: 10px;
                    line-height: 24px;
                }

                .info-num {
                    width: 100%;
                    height: 50%;
                    padding-left: 10px;
                    line-height: 24px;
                    overflow: hidden;
                    /* 确保超出容器的内容被裁剪 */
                    white-space: nowrap;
                    /* 确保文本在一行内显示 */
                    text-overflow: ellipsis;
                    /* 超出部分显示省略号 */

                    span {
                        cursor: pointer;
                    }
                }
            }

            .ele-img {
                width: 39px;
                height: 100%;

                .img-bg {
                    width: 100%;
                    height: 100%;
                    background-size: contain;
                }
            }
        }

        .ele-right {
            margin-left: 10px;
        }
    }

    .unit-label {
        font-size: 14px;
        color: rgb(20, 12, 20);
        text-align: right; /* 可根据需要调整 */
        margin-bottom: 5px;
    }

   
}


.chart-section {
  margin-top: 20px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.select-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.chart-wrapper {
  height: 400px;
  background-color: #ffffff;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 16px;
}

.chart-controls {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}
.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;

  .el-select {
    width: 80px;
    font-size: 12px;
  }
}
.custom-legend {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;

  .legend-item {
    display: flex;
    align-items: center;
    margin-left: 10px;
  }

  .legend-color {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 2px;
    margin-right: 4px;
  }

  .color1 {
    background-color: #409EFF;
  }

  .color2 {
    background-color: #67C23A;
  }
}
.mini-chart-container {
  width: 100%;
  height: 20px; // 图表区域压缩至 140px 高度
}
</style>