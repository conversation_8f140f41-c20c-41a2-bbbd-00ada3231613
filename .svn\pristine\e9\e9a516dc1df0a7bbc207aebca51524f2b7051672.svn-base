<template>
    <div class="productProcess">
        <!-- 如果页面刷新数据比较频繁，可以将loading、showFlag的相关代码删除，防止过于频繁的出现加载动画 -->
        <div class="loading_div" v-show="!showFlag">
            <div>Loading...</div> <!-- 这个loading自己写，代码没贴出来 -->
        </div>
        <div class="success_info_body" v-show="showFlag">
            <!-- 参数名称、列数根据实际情况调整 -->
            <div class="table_body">
                <div class="table_th">
                    <div class="tr1 th_style">序号</div>
                    <div class="tr2 th_style">设备名称</div>
                    <div class="tr3 th_style">管理编码</div>
                    <div class="tr4 th_style">SOC</div>
                    <div class="tr5 th_style">SOH</div>
                    <div class="tr6 th_style">在线状态</div>
                    <div class="tr7 th_style">上传时间</div>
                </div>
                <div class="table_main_body">
                    <div class="table_inner_body" :style="{ top: tableTop + 'px' }">
                        <div class="table_tr" v-for="(item, index) in tableList" :key="index">
                            <div class="tr1 tr">{{ index + 1 }}</div>
                            <div class="tr2 tr">{{ item.deviceName }}</div>
                            <div class="tr3 tr" @click="handleClick(item)">{{ item.managementNumber }}</div>
                            <div class="tr4 tr">{{ Number(item.soc).toFixed(2) || 0 }}%</div>
                            <div class="tr5 tr">{{ Number(item.soh).toFixed(2) || 0 }}%</div>
                            <div class="tr6 tr">{{ item.carStatus }}
                            </div>
                            <div class="tr7 tr">{{ item.createTime }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog :visible.sync="DetailsDialogVisible" title="电量信息详情" width="85%" class="details-dialog">
            <DetailsDialog :id="detailsId" :isclosed="!DetailsDialogVisible"></DetailsDialog>
        </el-dialog>
    </div>
</template>

<script>
import constDataSet from "@/utils/const";
import DetailsDialog from "../../DetailsDialog.vue";
export default {
    components:{
        DetailsDialog,
    },
    data() {
        return {
            showFlag: true,
            tableTimer: null,
            tableTop: 0,
            tableList: constDataSet.tableData,
            tableListSize: 0,
            componentTimer: null,
            //需要根据情况设置的参数
            title: "排产进度",
            visibleSize: 2, //容器内可视最大完整行数
            lineHeight: 49, //每行的实际高度（包含margin-top/bottom,border等）
            componentTimerInterval: 3600000, //刷新数据的时间间隔
            tableTimerInterval: 50, //向上滚动 1px 所需要的时间，越小越快，推荐值 100
            DetailsDialogVisible: false,
            detailsId: null,
        };
    },
    watch: {
        "$store.state.tree.sectionId": {
            handler: function () {
                this.requestList();
            },
            deep: true,
            immediate: true,
        },
    },
    mounted() {
        clearInterval(this.componentTimer);
        this.bsGetProductProcess();
        this.componentTimerFun();
    },
    methods: {
        async requestList() {
            let data = {
                sectionId:this.$store.state.tree.sectionId||'',
            }
            let res = await this.$http.post(`/equip/battery/pages?limit=999&page=1`, data);
            this.tableList = res.result?.records || [];
        },
        //调用数据接口，获取列表数据，根据自己情况填接口url
        bsGetProductProcess() {
            clearInterval(this.tableTimer);
            this.tableTop = 0;
            this.showFlag = true;
            this.tableActionFun();
        },

        tableActionFun() {
            this.tableListSize = this.tableList.length;
            if (this.tableListSize > this.visibleSize) {
                this.tableList = this.tableList.concat(this.tableList);
                this.tableTimerFun();
            } else {
                //this.fillTableList();
            }
        },
        //当数据过少时，不触发自动轮播事件，并填充没有数据的行，参数根据实际情况修改即可
        fillTableList() {
            var addLength = this.visibleSize - this.tableListSize;
            for (var i = 0; i < addLength; i++) {
                this.tableList.push({
                    deviceName: '-',
                    deviceCode: '-',
                    soc: '-',
                    soh: '-',
                    battery: '-',
                });
            }
        },
        tableTimerFun() {
            var count = 0;
            this.tableTimer = setInterval(() => {
                if (count < (this.tableList.length / 2) * this.lineHeight) {
                    this.tableTop -= 1;
                    count++;
                } else {
                    count = 0;
                    this.tableTop = 0;
                }
            }, this.tableTimerInterval);
        },
        componentTimerFun() { //页面自动刷新时间
            this.componentTimer = setInterval(() => {
                this.bsGetProductProcess();
            }, this.componentTimerInterval);
        },
        handleClick(item){
            this.detailsId = item.id;
            this.DetailsDialogVisible = true;
        }
    },
    beforeDestroy() {
        clearInterval(this.componentTimer);
        clearInterval(this.tableTimer);
    },
}
</script>

<style scoped>
.productProcess {
    width: 100%;
    height: 100%;
}

.loading_div {
    color: #eee;
    padding-top: 100px;
}

.title_div {
    width: 100%;
}

.table_body {
    width: 100%;
}

.table_th {
    width: 100%;
    display: flex;
    height: 50px;
    line-height: 50px;
    background-color: #EDF4FE;
    border-bottom: 3px solid #C9DCF4;
}

.tr {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: border-box;
    padding: 0 5px;
    text-align: center;
    font-size: 14px;
}

.tr1 {
    width: 7%;
}

.tr2 {
    width: 13%;
}

.tr3 {
    width: 20%;
}

.tr4 {
    width: 13%;
}

.tr5 {
    width: 13%;
}

.tr6 {
    width: 15%;
}

.tr7 {
    flex: 1;
}

.th_style {
    color: #333;
    font-weight: 400;
    font-size: 14px;
    font-family: '微软雅黑', sans-serif;
    border-right: 2px solid #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: border-box;
    padding: 0 5px;
    text-align: center;
}

.table_main_body {
    width: 100%;
    height: 294px;
    overflow: hidden;
    position: relative;
}

.table_inner_body {
    width: 100%;
    position: absolute;
    left: 0;
}

.table_tr {
    display: flex;
    height: 40px;
    line-height: 40px;
    color: #333;
    font-size: 15px;
    /* background: rgba(3, 145, 167, 0.1); */
    border-bottom: 1px solid #F2F2F2;
    margin-top: 7px;
}
</style>
