/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./EncodedCartesian3-87cd0c1f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./WebMercatorProjection-bc9aa7fe","./arrayRemoveDuplicates-2869246d","./ArcType-66bc286a","./EllipsoidRhumbLine-6ca4b1e6","./EllipsoidGeodesic-db2069b3"],function(B,p,Ve,ze,Be,Ge,e,a,t,je,We,i,n,c,r,Ye,G,C,s,j,W,Y,E){function o(e){e=B.defaultValue(e,{}),this._ellipsoid=B.defaultValue(e.ellipsoid,Be.Ellipsoid.WGS84),this._rectangle=B.defaultValue(e.rectangle,Be.Rectangle.MAX_VALUE),this._projection=new Ge.GeographicProjection(this._ellipsoid),this._numberOfLevelZeroTilesX=B.defaultValue(e.numberOfLevelZeroTilesX,2),this._numberOfLevelZeroTilesY=B.defaultValue(e.numberOfLevelZeroTilesY,1),this._customDPI=e.customDPI,this._scaleDenominators=e.scaleDenominators,this._tileWidth=B.defaultValue(e.tileWidth,256),this._tileHeight=B.defaultValue(e.tileHeight,256),this._beginLevel=B.defaultValue(e.beginLevel,0)}Object.defineProperties(o.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},rectangle:{get:function(){return this._rectangle}},projection:{get:function(){return this._projection}},beginLevel:{get:function(){return this._beginLevel}}}),o.prototype.getNumberOfXTilesAtLevel=function(e){return this._numberOfLevelZeroTilesX<<e-this._beginLevel},o.prototype.getNumberOfYTilesAtLevel=function(e){return this._numberOfLevelZeroTilesY<<e-this._beginLevel},o.prototype.rectangleToNativeRectangle=function(e,a){p.Check.defined("rectangle",e);var t=Ve.CesiumMath.toDegrees(e.west),i=Ve.CesiumMath.toDegrees(e.south),n=Ve.CesiumMath.toDegrees(e.east),r=Ve.CesiumMath.toDegrees(e.north);return B.defined(a)?(a.west=t,a.south=i,a.east=n,a.north=r,a):new Be.Rectangle(t,i,n,r)},o.prototype.tileXYToNativeRectangle=function(e,a,t,i){var n=this.tileXYToRectangle(e,a,t,i);return n.west=Ve.CesiumMath.toDegrees(n.west),n.south=Ve.CesiumMath.toDegrees(n.south),n.east=Ve.CesiumMath.toDegrees(n.east),n.north=Ve.CesiumMath.toDegrees(n.north),n},o.prototype.tileXYToRectangle=function(e,a,t,i){var n=this._rectangle;if(B.defined(this._customDPI)&&B.defined(this._scaleDenominators)){var r=this.calculateResolution(t),s=-Ve.CesiumMath.PI+e*this._tileWidth*r.x,o=-Ve.CesiumMath.PI+(e+1)*this._tileWidth*r.x,l=Ve.CesiumMath.PI_OVER_TWO-a*this._tileHeight*r.y,u=Ve.CesiumMath.PI_OVER_TWO-(a+1)*this._tileHeight*r.y;return B.defined(i)?(i.west=s,i.south=u,i.east=o,i.north=l,i):new Be.Rectangle(s,u,o,l)}var c=this.getNumberOfXTilesAtLevel(t),h=this.getNumberOfYTilesAtLevel(t),C=n.width/c,d=(s=e*C+n.west,o=(e+1)*C+n.west,n.height/h);l=n.north-a*d,u=n.north-(a+1)*d;return B.defined(i)||(i=new Be.Rectangle(s,u,o,l)),i.west=s,i.south=u,i.east=o,i.north=l,i},o.prototype.positionToTileXY=function(e,a,t){var i=this._rectangle;if(Be.Rectangle.contains(i,e)){var n=this.getNumberOfXTilesAtLevel(a),r=this.getNumberOfYTilesAtLevel(a),s=i.width/n,o=i.height/r;if(B.defined(this._customDPI)&&B.defined(this._scaleDenominators)){var l=this.calculateResolution(a);s=this._tileWidth*l.x,o=this._tileHeight*l.y}var u=e.longitude;i.east<i.west&&(u+=Ve.CesiumMath.TWO_PI);var c=(u-i.west)/s|0;n<=c&&(c=n-1);var h=(i.north-e.latitude)/o|0;return r<=h&&(h=r-1),B.defined(t)?(t.x=c,t.y=h,t):new Be.Cartesian2(c,h)}},o.prototype.calculateResolution=function(e){var a=.0254*this._scaleDenominators[e-this._beginLevel]/this._customDPI.x,t=.0254*this._scaleDenominators[e-this._beginLevel]/this._customDPI.y,i=Be.Ellipsoid.WGS84.maximumRadius;return new Be.Cartesian2(a/i,t/i)};var u=new ze.Cartesian3,h=new ze.Cartesian3,d=new ze.Cartographic,g=new ze.Cartesian3,f=new ze.Cartesian3,l=new Ge.BoundingSphere,m=new o,v=[new ze.Cartographic,new ze.Cartographic,new ze.Cartographic,new ze.Cartographic],w=new Be.Cartesian2,Fe={};function y(e){ze.Cartographic.fromRadians(e.east,e.north,0,v[0]),ze.Cartographic.fromRadians(e.west,e.north,0,v[1]),ze.Cartographic.fromRadians(e.east,e.south,0,v[2]),ze.Cartographic.fromRadians(e.west,e.south,0,v[3]);var a,t=0,i=0,n=0,r=0,s=Fe._terrainHeightsMaxLevel;for(a=0;a<=s;++a){for(var o=!1,l=0;l<4;++l){var u=v[l];if(m.positionToTileXY(u,a,w),0===l)n=w.x,r=w.y;else if(n!==w.x||r!==w.y){o=!0;break}}if(o)break;t=n,i=r}if(0!==a)return{x:t,y:i,level:s<a?s:a-1}}Fe.initialize=function(){var e=Fe._initPromise;return B.defined(e)?e:(e=r.Resource.fetchJson(r.buildModuleUrl("Assets/approximateTerrainHeights.json")).then(function(e){Fe._terrainHeights=e}),Fe._initPromise=e)},Fe.getMinimumMaximumHeights=function(e,a){if(p.Check.defined("rectangle",e),!B.defined(Fe._terrainHeights))throw new p.DeveloperError("You must call ApproximateTerrainHeights.initialize and wait for the promise to resolve before using this function");a=B.defaultValue(a,Be.Ellipsoid.WGS84);var t=y(e),i=Fe._defaultMinTerrainHeight,n=Fe._defaultMaxTerrainHeight;if(B.defined(t)){var r=t.level+"-"+t.x+"-"+t.y,s=Fe._terrainHeights[r];B.defined(s)&&(i=s[0],n=s[1]),a.cartographicToCartesian(Be.Rectangle.northeast(e,d),u),a.cartographicToCartesian(Be.Rectangle.southwest(e,d),h),ze.Cartesian3.midpoint(h,u,g);var o=a.scaleToGeodeticSurface(g,f);if(B.defined(o)){var l=ze.Cartesian3.distance(g,o);i=Math.min(i,-l)}else i=Fe._defaultMinTerrainHeight}return{minimumTerrainHeight:i=Math.max(Fe._defaultMinTerrainHeight,i),maximumTerrainHeight:n}},Fe.getBoundingSphere=function(e,a){if(p.Check.defined("rectangle",e),!B.defined(Fe._terrainHeights))throw new p.DeveloperError("You must call ApproximateTerrainHeights.initialize and wait for the promise to resolve before using this function");a=B.defaultValue(a,Be.Ellipsoid.WGS84);var t=y(e),i=Fe._defaultMaxTerrainHeight;if(B.defined(t)){var n=t.level+"-"+t.x+"-"+t.y,r=Fe._terrainHeights[n];B.defined(r)&&(i=r[1])}var s=Ge.BoundingSphere.fromRectangle3D(e,a,0);return Ge.BoundingSphere.fromRectangle3D(e,a,i,l),Ge.BoundingSphere.union(s,l,s)},Fe._terrainHeightsMaxLevel=6,Fe._defaultMaxTerrainHeight=9e3,Fe._defaultMinTerrainHeight=-1e5,Fe._terrainHeights=void 0,Fe._initPromise=void 0,Object.defineProperties(Fe,{initialized:{get:function(){return B.defined(Fe._terrainHeights)}}});var F=[Ge.GeographicProjection,s.WebMercatorProjection],_=F.length,qe=Math.cos(Ve.CesiumMath.toRadians(30)),T=Math.cos(Ve.CesiumMath.toRadians(150)),q=0,U=1e3;function M(e){var a=(e=B.defaultValue(e,B.defaultValue.EMPTY_OBJECT)).positions;if(!B.defined(a)||a.length<2)throw new p.DeveloperError("At least two positions are required.");if(B.defined(e.arcType)&&e.arcType!==W.ArcType.GEODESIC&&e.arcType!==W.ArcType.RHUMB)throw new p.DeveloperError("Valid options for arcType are ArcType.GEODESIC and ArcType.RHUMB.");this.width=B.defaultValue(e.width,1),this._positions=a,this.granularity=B.defaultValue(e.granularity,9999),this.loop=B.defaultValue(e.loop,!1),this.arcType=B.defaultValue(e.arcType,W.ArcType.GEODESIC),this._ellipsoid=B.defaultValue(e.ellipsoid,Be.Ellipsoid.WGS84),this._projectionIndex=0,this._workerName="createGroundPolylineGeometry",this._scene3DOnly=!1}Object.defineProperties(M.prototype,{packedLength:{get:function(){return 1+3*this._positions.length+1+1+1+Be.Ellipsoid.packedLength+1+1}}}),M.setProjectionAndEllipsoid=function(e,a){for(var t=0,i=0;i<_;i++)if(a instanceof F[i]){t=i;break}e._projectionIndex=t,e._ellipsoid=a.ellipsoid};var b=new ze.Cartesian3,P=new ze.Cartesian3,O=new ze.Cartesian3;function X(e,a,t,i,n){var r=Q(i,e,0,b),s=Q(i,e,t,P),o=Q(i,a,0,O),l=Ue(s,r,P),u=Ue(o,r,O);return ze.Cartesian3.cross(u,l,n),ze.Cartesian3.normalize(n,n)}var I=new ze.Cartographic,A=new ze.Cartesian3,D=new ze.Cartesian3,L=new ze.Cartesian3;function Z(e,a,t,i,n,r,s,o,l,u,c){if(0!==n){var h;r===W.ArcType.GEODESIC?h=new E.EllipsoidGeodesic(e,a,s):r===W.ArcType.RHUMB&&(h=new Y.EllipsoidRhumbLine(e,a,s));var C=h.surfaceDistance;if(!(C<n))for(var d=X(e,a,i,s,L),p=Math.ceil(C/n),g=C/p,f=g,m=p-1,v=o.length,w=0;w<m;w++){var y=h.interpolateUsingSurfaceDistance(f,I),_=Q(s,y,t,A),T=Q(s,y,i,D);ze.Cartesian3.pack(d,o,v),ze.Cartesian3.pack(_,l,v),ze.Cartesian3.pack(T,u,v),c.push(y.latitude),c.push(y.longitude),v+=3,f+=g}}}var k=new ze.Cartographic;function Q(e,a,t,i){return ze.Cartographic.clone(a,k),k.height=t,ze.Cartographic.toCartesian(k,e,i)}function Ue(e,a,t){return ze.Cartesian3.subtract(e,a,t),ze.Cartesian3.normalize(t,t),t}M.pack=function(e,a,t){p.Check.typeOf.object("value",e),p.Check.defined("array",a);var i=B.defaultValue(t,0),n=e._positions,r=n.length;a[i++]=r;for(var s=0;s<r;++s){var o=n[s];ze.Cartesian3.pack(o,a,i),i+=3}return a[i++]=e.granularity,a[i++]=e.loop?1:0,a[i++]=e.arcType,Be.Ellipsoid.pack(e._ellipsoid,a,i),i+=Be.Ellipsoid.packedLength,a[i++]=e._projectionIndex,a[i++]=e._scene3DOnly?1:0,a},M.unpack=function(e,a,t){p.Check.defined("array",e);for(var i=B.defaultValue(a,0),n=e[i++],r=new Array(n),s=0;s<n;s++)r[s]=ze.Cartesian3.unpack(e,i),i+=3;var o=e[i++],l=1===e[i++],u=e[i++],c=Be.Ellipsoid.unpack(e,i);i+=Be.Ellipsoid.packedLength;var h=e[i++],C=1===e[i++];if(B.defined(t))return t._positions=r,t.granularity=o,t.loop=l,t.arcType=u,t._ellipsoid=c,t._projectionIndex=h,t._scene3DOnly=C,t;var d=new M({positions:r,granularity:o,loop:l,arcType:u,ellipsoid:c});return d._projectionIndex=h,d._scene3DOnly=C,d};var S=new ze.Cartesian3,x=new ze.Cartesian3,N=new ze.Cartesian3,R=new ze.Cartesian3,H=new C.Plane(ze.Cartesian3.UNIT_X,0),V=new ze.Cartesian3;function J(e,a,t,i,n){var r=Ue(t,a,V),s=Ue(e,a,S),o=Ue(i,a,x),l=ze.Cartesian3.cross(r,s,R);l=ze.Cartesian3.normalize(l,l);var u=C.Plane.fromPointNormal(a,l,H),c=C.Plane.getPointDistance(u,i);if(Ve.CesiumMath.equalsEpsilon(c,0,Ve.CesiumMath.EPSILON7))return ze.Cartesian3.clone(l,n),n;n=ze.Cartesian3.add(o,s,n),n=ze.Cartesian3.normalize(n,n);var h=ze.Cartesian3.cross(r,n,N);return ze.Cartesian3.normalize(h,h),ze.Cartesian3.cross(h,r,n),ze.Cartesian3.normalize(n,n),ze.Cartesian3.dot(o,h)<0&&(n=ze.Cartesian3.negate(n,n)),n}var K=C.Plane.fromPointNormal(ze.Cartesian3.ZERO,ze.Cartesian3.UNIT_Y),$=new ze.Cartesian3,ee=new ze.Cartesian3,ae=new ze.Cartesian3,te=new ze.Cartesian3,ie=new ze.Cartesian3,ne=new ze.Cartesian3,re=new ze.Cartographic,se=new ze.Cartographic,oe=new ze.Cartographic;M.createGeometry=function(e){var a,t,i,n,r,s,o=!e._scene3DOnly,l=e.loop,u=e._ellipsoid,c=e.granularity,h=e.arcType,C=new F[e._projectionIndex](u),d=q,p=U,g=e._positions,f=g.length;2===f&&(l=!1);var m,v,w,y=new Y.EllipsoidRhumbLine(void 0,void 0,u),_=[g[0]];for(t=0;t<f-1;t++)i=g[t],n=g[t+1],m=G.IntersectionTests.lineSegmentPlane(i,n,K,ne),!B.defined(m)||ze.Cartesian3.equalsEpsilon(m,i,Ve.CesiumMath.EPSILON7)||ze.Cartesian3.equalsEpsilon(m,n,Ve.CesiumMath.EPSILON7)||(e.arcType===W.ArcType.GEODESIC?_.push(ze.Cartesian3.clone(m)):e.arcType===W.ArcType.RHUMB&&(w=u.cartesianToCartographic(m,re).longitude,r=u.cartesianToCartographic(i,re),s=u.cartesianToCartographic(n,se),y.setEndPoints(r,s),v=y.findIntersectionWithLongitude(w,oe),m=u.cartographicToCartesian(v,ne),!B.defined(m)||ze.Cartesian3.equalsEpsilon(m,i,Ve.CesiumMath.EPSILON7)||ze.Cartesian3.equalsEpsilon(m,n,Ve.CesiumMath.EPSILON7)||_.push(ze.Cartesian3.clone(m)))),_.push(n);l&&(i=g[f-1],n=g[0],m=G.IntersectionTests.lineSegmentPlane(i,n,K,ne),!B.defined(m)||ze.Cartesian3.equalsEpsilon(m,i,Ve.CesiumMath.EPSILON7)||ze.Cartesian3.equalsEpsilon(m,n,Ve.CesiumMath.EPSILON7)||(e.arcType===W.ArcType.GEODESIC?_.push(ze.Cartesian3.clone(m)):e.arcType===W.ArcType.RHUMB&&(w=u.cartesianToCartographic(m,re).longitude,r=u.cartesianToCartographic(i,re),s=u.cartesianToCartographic(n,se),y.setEndPoints(r,s),v=y.findIntersectionWithLongitude(w,oe),m=u.cartographicToCartesian(v,ne),!B.defined(m)||ze.Cartesian3.equalsEpsilon(m,i,Ve.CesiumMath.EPSILON7)||ze.Cartesian3.equalsEpsilon(m,n,Ve.CesiumMath.EPSILON7)||_.push(ze.Cartesian3.clone(m)))));var T=_.length,E=new Array(T);for(t=0;t<T;t++){var M=ze.Cartographic.fromCartesian(_[t],u);M.height=0,E[t]=M}if(!((T=(E=j.arrayRemoveDuplicates(E,ze.Cartographic.equalsEpsilon)).length)<2)){var b=[],P=[],O=[],I=[],A=$,D=ee,L=ae,k=te,S=ie,x=E[0],N=E[1];for(A=Q(u,E[T-1],d,A),k=Q(u,N,d,k),D=Q(u,x,d,D),L=Q(u,x,p,L),S=l?J(A,D,L,k,S):X(x,N,p,u,S),ze.Cartesian3.pack(S,P,0),ze.Cartesian3.pack(D,O,0),ze.Cartesian3.pack(L,I,0),b.push(x.latitude),b.push(x.longitude),Z(x,N,d,p,c,h,u,P,O,I,b),t=1;t<T-1;++t){A=ze.Cartesian3.clone(D,A),D=ze.Cartesian3.clone(k,D);var R=E[t];Q(u,R,p,L),Q(u,E[t+1],d,k),J(A,D,L,k,S),a=P.length,ze.Cartesian3.pack(S,P,a),ze.Cartesian3.pack(D,O,a),ze.Cartesian3.pack(L,I,a),b.push(R.latitude),b.push(R.longitude),Z(E[t],E[t+1],d,p,c,h,u,P,O,I,b)}var H=E[T-1],V=E[T-2];if(D=Q(u,H,d,D),L=Q(u,H,p,L),l){var z=E[0];S=J(A=Q(u,V,d,A),D,L,k=Q(u,z,d,k),S)}else S=X(V,H,p,u,S);if(a=P.length,ze.Cartesian3.pack(S,P,a),ze.Cartesian3.pack(D,O,a),ze.Cartesian3.pack(L,I,a),b.push(H.latitude),b.push(H.longitude),l){for(Z(H,x,d,p,c,h,u,P,O,I,b),a=P.length,t=0;t<3;++t)P[a+t]=P[t],O[a+t]=O[t],I[a+t]=I[t];b.push(x.latitude),b.push(x.longitude)}return function(e,a,t,i,n,r,s){var o,l,u,c,h,C,d=a._ellipsoid,p=t.length/3-1,g=8*p,f=4*g,m=36*p,v=65535<g?new Uint32Array(m):new Uint16Array(m),w=new Float64Array(3*g),y=new Float32Array(f),_=new Float32Array(f),T=new Float32Array(f),E=new Float32Array(f),M=new Float32Array(f);s&&(u=new Float32Array(f),c=new Float32Array(f),h=new Float32Array(f),C=new Float32Array(2*g));var b=r.length/2,P=0,O=$e;O.height=0;var I=ea;I.height=0;var A=aa,D=ta;if(s)for(l=0,o=1;o<b;o++)O.latitude=r[l],O.longitude=r[l+1],I.latitude=r[l+2],I.longitude=r[l+3],A=a.project(O,A),D=a.project(I,D),P+=ze.Cartesian3.distance(A,D),l+=2;var L=i.length/3;D=ze.Cartesian3.unpack(i,0,D);var k,S=0;for(l=3,o=1;o<L;o++)A=ze.Cartesian3.clone(D,A),D=ze.Cartesian3.unpack(i,l,D),S+=ze.Cartesian3.distance(A,D),l+=3;l=3;var x=0,N=0,R=0,H=0,V=!1,z=ze.Cartesian3.unpack(t,0,na),B=ze.Cartesian3.unpack(i,0,ta),G=ze.Cartesian3.unpack(n,0,sa);if(e){var j=ze.Cartesian3.unpack(t,t.length-6,ia);Xe(G,j,z,B)&&(G=ze.Cartesian3.negate(G,G))}var W=0,Y=0,F=0;for(o=0;o<p;o++){var q,U,X,Z,Q=ze.Cartesian3.clone(z,ia),J=ze.Cartesian3.clone(B,aa),K=ze.Cartesian3.clone(G,ra);if(V&&(K=ze.Cartesian3.negate(K,K)),z=ze.Cartesian3.unpack(t,l,na),B=ze.Cartesian3.unpack(i,l,ta),G=ze.Cartesian3.unpack(n,l,sa),V=Xe(G,Q,z,B),O.latitude=r[x],O.longitude=r[x+1],I.latitude=r[x+2],I.longitude=r[x+3],s){var $=Ke(O,I);q=a.project(O,da);var ee=Ue(U=a.project(I,pa),q,ba);ee.y=Math.abs(ee.y),X=ga,Z=fa,0===$||ze.Cartesian3.dot(ee,ze.Cartesian3.UNIT_Y)>qe?(X=Ze(a,O,K,q,ga),Z=Ze(a,I,G,U,fa)):1===$?(Z=Ze(a,I,G,U,fa),X.x=0,X.y=Ve.CesiumMath.sign(O.longitude-Math.abs(I.longitude)),X.z=0):(X=Ze(a,O,K,q,ga),Z.x=0,Z.y=Ve.CesiumMath.sign(O.longitude-I.longitude),Z.z=0)}var ae=ze.Cartesian3.distance(J,B),te=Ye.EncodedCartesian3.fromCartesian(Q,Ea),ie=ze.Cartesian3.subtract(z,Q,ma),ne=ze.Cartesian3.normalize(ie,ya),re=ze.Cartesian3.subtract(J,Q,va);re=ze.Cartesian3.normalize(re,re);var se=ze.Cartesian3.cross(ne,re,ya);se=ze.Cartesian3.normalize(se,se);var oe=ze.Cartesian3.cross(re,K,_a);oe=ze.Cartesian3.normalize(oe,oe);var le=ze.Cartesian3.subtract(B,z,wa);le=ze.Cartesian3.normalize(le,le);var ue=ze.Cartesian3.cross(G,le,Ta);ue=ze.Cartesian3.normalize(ue,ue);var ce,he,Ce,de=ae/S,pe=W/S,ge=0,fe=0,me=0;if(s){ge=ze.Cartesian3.distance(q,U),ce=Ye.EncodedCartesian3.fromCartesian(q,Ma),he=ze.Cartesian3.subtract(U,q,ba);var ve=(Ce=ze.Cartesian3.normalize(he,Pa)).x;Ce.x=Ce.y,Ce.y=-ve,fe=ge/P,me=Y/P}for(k=0;k<8;k++){var we=H+4*k,ye=N+2*k,_e=we+3,Te=k<4?1:-1,Ee=2===k||3===k||6===k||7===k?1:-1;ze.Cartesian3.pack(te.high,y,we),y[_e]=ie.x,ze.Cartesian3.pack(te.low,_,we),_[_e]=ie.y,ze.Cartesian3.pack(oe,T,we),T[_e]=ie.z,ze.Cartesian3.pack(ue,E,we),E[_e]=de*Te,ze.Cartesian3.pack(se,M,we);var Me=pe*Ee;0===Me&&Ee<0&&(Me=Number.POSITIVE_INFINITY),M[_e]=Me,s&&(u[we]=ce.high.x,u[we+1]=ce.high.y,u[we+2]=ce.low.x,u[we+3]=ce.low.y,h[we]=-X.y,h[we+1]=X.x,h[we+2]=Z.y,h[we+3]=-Z.x,c[we]=he.x,c[we+1]=he.y,c[we+2]=Ce.x,c[we+3]=Ce.y,C[ye]=fe*Te,0===(Me=me*Ee)&&Ee<0&&(Me=Number.POSITIVE_INFINITY),C[ye+1]=Me)}var be=ha,Pe=Ca,Oe=ua,Ie=ca,Ae=Be.Rectangle.fromCartographicArray(oa,la),De=Fe.getMinimumMaximumHeights(Ae,d),Le=De.minimumTerrainHeight,ke=De.maximumTerrainHeight;F+=Le,F+=ke,Qe(Q,J,Le,ke,be,Oe),Qe(z,B,Le,ke,Pe,Ie);var Se=ze.Cartesian3.multiplyByScalar(se,Ve.CesiumMath.EPSILON5,Oa);ze.Cartesian3.add(be,Se,be),ze.Cartesian3.add(Pe,Se,Pe),ze.Cartesian3.add(Oe,Se,Oe),ze.Cartesian3.add(Ie,Se,Ie),Je(be,Pe),Je(Oe,Ie),ze.Cartesian3.pack(be,w,R),ze.Cartesian3.pack(Pe,w,R+3),ze.Cartesian3.pack(Ie,w,R+6),ze.Cartesian3.pack(Oe,w,R+9),Se=ze.Cartesian3.multiplyByScalar(se,-2*Ve.CesiumMath.EPSILON5,Oa),ze.Cartesian3.add(be,Se,be),ze.Cartesian3.add(Pe,Se,Pe),ze.Cartesian3.add(Oe,Se,Oe),ze.Cartesian3.add(Ie,Se,Ie),Je(be,Pe),Je(Oe,Ie),ze.Cartesian3.pack(be,w,R+12),ze.Cartesian3.pack(Pe,w,R+15),ze.Cartesian3.pack(Ie,w,R+18),ze.Cartesian3.pack(Oe,w,R+21),x+=2,l+=3,N+=16,R+=24,H+=32,W+=ae,Y+=ge}var xe=l=0;for(o=0;o<p;o++){for(k=0;k<Da;k++)v[l+k]=Aa[k]+xe;xe+=8,l+=Da}var Ne=Ia;Ge.BoundingSphere.fromVertices(t,ze.Cartesian3.ZERO,3,Ne[0]),Ge.BoundingSphere.fromVertices(i,ze.Cartesian3.ZERO,3,Ne[1]);var Re=Ge.BoundingSphere.fromBoundingSpheres(Ne);Re.radius+=F/(2*p);var He={position:new We.GeometryAttribute({componentDatatype:je.ComponentDatatype.DOUBLE,componentsPerAttribute:3,normalize:!1,values:w}),startHiAndForwardOffsetX:La(y),startLoAndForwardOffsetY:La(_),startNormalAndForwardOffsetZ:La(T),endNormalAndTextureCoordinateNormalizationX:La(E),rightNormalAndTextureCoordinateNormalizationY:La(M)};s&&(He.startHiLo2D=La(u),He.offsetAndRight2D=La(c),He.startEndNormals2D=La(h),He.texcoordNormalization2D=new We.GeometryAttribute({componentDatatype:je.ComponentDatatype.FLOAT,componentsPerAttribute:2,normalize:!1,values:C}));return new We.Geometry({attributes:He,indices:v,boundingSphere:Re})}(l,C,O,I,P,b,o)}};var z=new ze.Cartesian3,le=new Ge.Matrix3,ue=new c.Quaternion;function Xe(e,a,t,i){var n=Ue(t,a,z),r=ze.Cartesian3.dot(n,e);if(qe<r||r<T){var s=Ue(i,t,V),o=r<T?Ve.CesiumMath.PI_OVER_TWO:-Ve.CesiumMath.PI_OVER_TWO,l=c.Quaternion.fromAxisAngle(s,o,ue),u=Ge.Matrix3.fromQuaternion(l,le);return Ge.Matrix3.multiplyByVector(u,e,e),!0}return!1}var ce=new ze.Cartographic,he=new ze.Cartesian3,Ce=new ze.Cartesian3;function Ze(e,a,t,i,n){var r=ze.Cartographic.toCartesian(a,e._ellipsoid,he),s=ze.Cartesian3.add(r,t,Ce),o=!1,l=e._ellipsoid,u=l.cartesianToCartographic(s,ce);Math.abs(a.longitude-u.longitude)>Ve.CesiumMath.PI_OVER_TWO&&(o=!0,s=ze.Cartesian3.subtract(r,t,Ce),u=l.cartesianToCartographic(s,ce)),u.height=0;var c=e.project(u,n);return(n=ze.Cartesian3.subtract(c,i,n)).z=0,n=ze.Cartesian3.normalize(n,n),o&&ze.Cartesian3.negate(n,n),n}var de=new ze.Cartesian3,pe=new ze.Cartesian3;function Qe(e,a,t,i,n,r){var s=ze.Cartesian3.subtract(a,e,de);ze.Cartesian3.normalize(s,s);var o=t-q,l=ze.Cartesian3.multiplyByScalar(s,o,pe);ze.Cartesian3.add(e,l,n);var u=i-U;l=ze.Cartesian3.multiplyByScalar(s,u,pe),ze.Cartesian3.add(a,l,r)}var ge=new ze.Cartesian3;function Je(e,a){var t=C.Plane.getPointDistance(K,e),i=C.Plane.getPointDistance(K,a),n=ge;Ve.CesiumMath.equalsEpsilon(t,0,Ve.CesiumMath.EPSILON2)?(n=Ue(a,e,n),ze.Cartesian3.multiplyByScalar(n,Ve.CesiumMath.EPSILON2,n),ze.Cartesian3.add(e,n,e)):Ve.CesiumMath.equalsEpsilon(i,0,Ve.CesiumMath.EPSILON2)&&(n=Ue(e,a,n),ze.Cartesian3.multiplyByScalar(n,Ve.CesiumMath.EPSILON2,n),ze.Cartesian3.add(a,n,a))}function Ke(e,a){var t=Math.abs(e.longitude),i=Math.abs(a.longitude);if(Ve.CesiumMath.equalsEpsilon(t,Ve.CesiumMath.PI,Ve.CesiumMath.EPSILON11)){var n=Ve.CesiumMath.sign(a.longitude);return e.longitude=n*(t-Ve.CesiumMath.EPSILON11),1}if(Ve.CesiumMath.equalsEpsilon(i,Ve.CesiumMath.PI,Ve.CesiumMath.EPSILON11)){var r=Ve.CesiumMath.sign(e.longitude);return a.longitude=r*(i-Ve.CesiumMath.EPSILON11),2}return 0}var $e=new ze.Cartographic,ea=new ze.Cartographic,aa=new ze.Cartesian3,ta=new ze.Cartesian3,ia=new ze.Cartesian3,na=new ze.Cartesian3,ra=new ze.Cartesian3,sa=new ze.Cartesian3,oa=[$e,ea],la=new Be.Rectangle,ua=new ze.Cartesian3,ca=new ze.Cartesian3,ha=new ze.Cartesian3,Ca=new ze.Cartesian3,da=new ze.Cartesian3,pa=new ze.Cartesian3,ga=new ze.Cartesian3,fa=new ze.Cartesian3,ma=new ze.Cartesian3,va=new ze.Cartesian3,wa=new ze.Cartesian3,ya=new ze.Cartesian3,_a=new ze.Cartesian3,Ta=new ze.Cartesian3,Ea=new Ye.EncodedCartesian3,Ma=new Ye.EncodedCartesian3,ba=new ze.Cartesian3,Pa=new ze.Cartesian3,Oa=new ze.Cartesian3,Ia=[new Ge.BoundingSphere,new Ge.BoundingSphere],Aa=[0,2,1,0,3,2,0,7,3,0,4,7,0,5,4,0,1,5,5,7,4,5,6,7,5,2,6,5,1,2,3,6,2,3,7,6],Da=Aa.length;function La(e){return new We.GeometryAttribute({componentDatatype:je.ComponentDatatype.FLOAT,componentsPerAttribute:4,normalize:!1,values:e})}return M._projectNormal=Ze,function(e,a){return Fe.initialize().then(function(){return B.defined(a)&&(e=M.unpack(e,a)),M.createGeometry(e)})}});