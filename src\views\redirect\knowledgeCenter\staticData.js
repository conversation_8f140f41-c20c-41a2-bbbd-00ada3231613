const originalData = [
  {
    index: 1,
    deviceName: "换电装载机",
    deviceNum: "AA3456",
    componentName: "电池管理系统",
    errorType: 1,
    errorTime: "2024-04-10 08:15:00",
    possibleReason: "温度过高",
    taskStatus: "已申报故障",
    collecterId: "cazg-1",
    deviceType: "换电装载机",
    manufacturer: "检查冷却系统，降低温度",
    projectSite: "芒康隧道入口",
  },
  {
    index: 2,
    deviceName: "充电自卸车",
    deviceNum: "BB7890",
    componentName: "液压系统",
    errorType: 2,
    errorTime: "2024-04-15 09:20:00",
    possibleReason: "液压管路泄漏",
    taskStatus: "忽略",
    collecterId: "cazg-2",
    deviceType: "充电自卸车",
    manufacturer: "更换传感器并校准数据",
    projectSite: "左贡隧道3#斜井",
  },
  {
    index: 3,
    deviceName: "换电自卸车",
    deviceNum: "CC4321",
    componentName: "转向系统",
    errorType: 1,
    errorTime: "2024-04-20 12:35:00",
    possibleReason: "转向失灵",
    taskStatus: "未处理",
    collecterId: "cazg-3",
    deviceType: "换电自卸车",
    manufacturer: "检查电机线路并修复异常",
    projectSite: "邦达施工点",
  },
  {
    index: 4,
    deviceName: "充电挖掘机",
    deviceNum: "DD5643",
    componentName: "控制板",
    errorType: 3,
    errorTime: "2024-04-25 10:40:00",
    possibleReason: "主板故障",
    taskStatus: "已申报故障",
    collecterId: "cazg-4",
    deviceType: "充电挖掘机",
    manufacturer: "检查线路并更换控制器",
    projectSite: "林芝大桥西端",
  },
  {
    index: 5,
    deviceName: "充电混泥土车",
    deviceNum: "EE8877",
    componentName: "动力系统",
    errorType: 2,
    errorTime: "2024-05-01 15:10:00",
    possibleReason: "电机故障",
    taskStatus: "忽略",
    collecterId: "cazg-5",
    deviceType: "充电混泥土车",
    manufacturer: "更换电池或校正电压",
    projectSite: "然乌",
  },
  {
    index: 6,
    deviceName: "换电混泥土车",
    deviceNum: "FF9988",
    componentName: "电池组",
    errorType: 1,
    errorTime: "2024-05-15 14:25:00",
    possibleReason: "电池过热",
    taskStatus: "未处理",
    collecterId: "cazg-6",
    deviceType: "换电混泥土车",
    manufacturer: "检查液压泵，补充油压",
    projectSite: "波密隧道出口",
  },
  {
    index: 7,
    deviceName: "换电挖掘机",
    deviceNum: "GG1123",
    componentName: "冷却系统",
    errorType: 2,
    errorTime: "2024-06-05 17:45:00",
    possibleReason: "散热不足",
    taskStatus: "忽略",
    collecterId: "cazg-7",
    deviceType: "换电挖掘机",
    manufacturer: "更换刹车片或校准系统",
    projectSite: "巴宜隧道东端",
  },
  {
    index: 8,
    deviceName: "充电装载机",
    deviceNum: "HH2345",
    componentName: "液压泵",
    errorType: 1,
    errorTime: "2024-06-15 08:10:00",
    possibleReason: "液压不足",
    taskStatus: "已申报故障",
    collecterId: "cazg-8",
    deviceType: "充电装载机",
    manufacturer: "检查驱动轴和电机连接",
    projectSite: "拉乌山隧道入口",
  },
  {
    index: 9,
    deviceName: "换电装载机",
    deviceNum: "II7765",
    componentName: "电控系统",
    errorType: 3,
    errorTime: "2024-06-20 12:50:00",
    possibleReason: "系统更新失败",
    taskStatus: "未处理",
    collecterId: "cazg-9",
    deviceType: "换电装载机",
    manufacturer: "更换电池并避免过放电工",
    projectSite: "巴塘隧道",
  },
  {
    index: 10,
    deviceName: "充电混泥土车",
    deviceNum: "JJ5432",
    componentName: "传感器",
    errorType: 1,
    errorTime: "2024-07-02 13:20:00",
    possibleReason: "传感器故障",
    taskStatus: "忽略",
    collecterId: "cazg-10",
    deviceType: "充电混泥土车",
    manufacturer: "检查电路接头并修复断路",
    projectSite: "通麦天险施工点",
  },
  {
    index: 11,
    deviceName: "换电挖掘机",
    deviceNum: "LL4567",
    componentName: "电动马达",
    errorType: 1,
    errorTime: "2024-07-10 16:30:00",
    possibleReason: "马达过载",
    taskStatus: "未处理",
    collecterId: "cazg-11",
    deviceType: "换电挖掘机",
    manufacturer: "清理油冷却系统，降温",
    projectSite: "芒康隧道入口",
  },
  {
    index: 12,
    deviceName: "充电装载机",
    deviceNum: "MM6789",
    componentName: "液压系统",
    errorType: 2,
    errorTime: "2024-07-15 09:50:00",
    possibleReason: "液压泄漏",
    taskStatus: "忽略",
    collecterId: "cazg-12",
    deviceType: "充电装载机",
    manufacturer: "更换磨损部件并润滑",
    projectSite: "左贡隧道3#斜井",
  },
  {
    index: 13,
    deviceName: "换电自卸车",
    deviceNum: "NN2341",
    componentName: "冷却系统",
    errorType: 1,
    errorTime: "2024-08-05 15:35:00",
    possibleReason: "冷却液不足",
    taskStatus: "未处理",
    collecterId: "cazg-13",
    deviceType: "换电自卸车",
    manufacturer: "加装保温装置，升温",
    projectSite: "邦达施工点",
  },
  {
    index: 14,
    deviceName: "充电混泥土车",
    deviceNum: "OO5678",
    componentName: "液压泵",
    errorType: 2,
    errorTime: "2024-08-18 11:15:00",
    possibleReason: "液压不足",
    taskStatus: "未处理",
    collecterId: "cazg-14",
    deviceType: "充电混泥土车",
    manufacturer: "检查轮胎气压并补充",
    projectSite: "林芝大桥西端",
  },
  {
    index: 15,
    deviceName: "换电挖掘机",
    deviceNum: "PP1234",
    componentName: "电池管理系统",
    errorType: 1,
    errorTime: "2024-08-23 18:05:00",
    possibleReason: "电池损坏",
    taskStatus: "未处理",
    collecterId: "cazg-15",
    deviceType: "换电挖掘机",
    manufacturer: "检查连接线并重新连接",
    projectSite: "然乌",
  },
  {
    index: 16,
    deviceName: "充电挖掘机",
    deviceNum: "QQ6780",
    componentName: "动力系统",
    errorType: 2,
    errorTime: "2024-08-25 10:20:00",
    possibleReason: "电机过热",
    taskStatus: "已申报故障",
    collecterId: "cazg-16",
    deviceType: "充电挖掘机",
    manufacturer: "减少负载并检查电机",
    projectSite: "波密隧道出口",
  },
  {
    index: 17,
    deviceName: "换电装载机",
    deviceNum: "RR8970",
    componentName: "液压系统",
    errorType: 1,
    errorTime: "2024-09-03 12:55:00",
    possibleReason: "液压不足",
    taskStatus: "忽略",
    collecterId: "cazg-17",
    deviceType: "换电装载机",
    manufacturer: "立即更换电池，清理漏液",
    projectSite: "巴宜隧道东端",
  },
  {
    index: 18,
    deviceName: "充电混泥土车",
    deviceNum: "SS7890",
    componentName: "电动马达",
    errorType: 3,
    errorTime: "2024-09-10 16:35:00",
    possibleReason: "马达故障",
    taskStatus: "未处理",
    collecterId: "cazg-18",
    deviceType: "充电混泥土车",
    manufacturer: "重新校准或更换传感器",
    projectSite: "拉乌山隧道入口",
  },
  {
    index: 19,
    deviceName: "充电装载机",
    deviceNum: "TT5674",
    componentName: "控制板",
    errorType: 1,
    errorTime: "2024-09-15 14:00:00",
    possibleReason: "主板异常",
    taskStatus: "未处理",
    collecterId: "cazg-19",
    deviceType: "充电装载机",
    manufacturer: "检查电机并修复损坏部件",
    projectSite: "巴塘隧道",
  },
  {
    index: 20,
    deviceName: "换电挖掘机",
    deviceNum: "UU2349",
    componentName: "冷却系统",
    errorType: 3,
    errorTime: "2024-09-20 09:15:00",
    possibleReason: "冷却液不足",
    taskStatus: "已申报故障",
    collecterId: "cazg-20",
    deviceType: "换电挖掘机",
    manufacturer: "检查并更换短路电池",
    projectSite: "通麦天险施工点",
  },
  {
    index: 21,
    deviceName: "充电自卸车",
    deviceNum: "VV7895",
    componentName: "动力系统",
    errorType: 2,
    errorTime: "2024-09-25 12:30:00",
    possibleReason: "电机过载",
    taskStatus: "忽略",
    collecterId: "cazg-21",
    deviceType: "充电自卸车",
    manufacturer: "检查制动液并清洁系统",
    projectSite: "芒康隧道入口",
  },
  {
    index: 22,
    deviceName: "换电自卸车",
    deviceNum: "WW4563",
    componentName: "电池管理系统",
    errorType: 1,
    errorTime: "2024-10-02 10:20:00",
    possibleReason: "电池过热",
    taskStatus: "未处理",
    collecterId: "cazg-22",
    deviceType: "换电自卸车",
    manufacturer: "检查并修复漏油点",
    projectSite: "林芝大桥西端",
  },
  {
    index: 23,
    deviceName: "充电挖掘机",
    deviceNum: "XX6788",
    componentName: "电控系统",
    errorType: 2,
    errorTime: "2024-10-10 13:50:00",
    possibleReason: "系统更新失败",
    taskStatus: "已申报故障",
    collecterId: "cazg-23",
    deviceType: "充电挖掘机",
    manufacturer: "重新启动或更换控制器",
    projectSite: "左贡隧道3#斜井",
  },
  {
    index: 24,
    deviceName: "换电装载机",
    deviceNum: "YY3457",
    componentName: "液压泵",
    errorType: 1,
    errorTime: "2024-10-15 16:25:00",
    possibleReason: "液压压力低",
    taskStatus: "未处理",
    collecterId: "cazg-24",
    deviceType: "换电装载机",
    manufacturer: "清理散热口并降温",
    projectSite: "然乌",
  },
  {
    index: 25,
    deviceName: "充电混泥土车",
    deviceNum: "ZZ9874",
    componentName: "动力系统",
    errorType: 3,
    errorTime: "2024-10-20 17:00:00",
    possibleReason: "电机损坏",
    taskStatus: "忽略",
    collecterId: "cazg-25",
    deviceType: "充电混泥土车",
    manufacturer: "检查并修复传动轴问题",
    projectSite: "波密隧道出口",
  },
  {
    index: 26,
    deviceName: "换电自卸车",
    deviceNum: "AB4321",
    componentName: "转向系统",
    errorType: 1,
    errorTime: "2024-10-25 11:45:00",
    possibleReason: "转向失灵",
    taskStatus: "未处理",
    collecterId: "cazg-26",
    deviceType: "换电自卸车",
    manufacturer: "校准或更换新电池",
    projectSite: "巴宜隧道东端",
  },
  {
    index: 27,
    deviceName: "充电挖掘机",
    deviceNum: "BC5647",
    componentName: "冷却系统",
    errorType: 2,
    errorTime: "2024-11-01 08:40:00",
    possibleReason: "冷却液不足",
    taskStatus: "未处理",
    collecterId: "cazg-27",
    deviceType: "充电挖掘机",
    manufacturer: "更换液压泵或润滑部件",
    projectSite: "拉乌山隧道入口",
  },
  {
    index: 28,
    deviceName: "换电混泥土车",
    deviceNum: "CD7893",
    componentName: "液压泵",
    errorType: 2,
    errorTime: "2024-11-10 09:35:00",
    possibleReason: "液压压力不足",
    taskStatus: "已申报故障",
    collecterId: "cazg-28",
    deviceType: "换电混泥土车",
    manufacturer: "调整刹车系统，测试制动",
    projectSite: "邦达施工点",
  },
  {
    index: 29,
    deviceName: "充电挖掘机",
    deviceNum: "DE1234",
    componentName: "电池组",
    errorType: 3,
    errorTime: "2024-11-15 10:30:00",
    possibleReason: "电池老化",
    taskStatus: "未处理",
    collecterId: "cazg-29",
    deviceType: "充电挖掘机",
    manufacturer: "检查线路并更换元件",
    projectSite: "芒康隧道入口",
  },
  {
    index: 30,
    deviceName: "换电装载机",
    deviceNum: "EF5678",
    componentName: "动力系统",
    errorType: 2,
    errorTime: "2024-11-25 15:15:00",
    possibleReason: "电机功率不足",
    taskStatus: "忽略",
    collecterId: "cazg-30",
    deviceType: "换电装载机",
    manufacturer: "检查充电器或更换电池",
    projectSite: "波密隧道出口",
  },
  {
    index: 31,
    deviceName: "充电装载机",
    deviceNum: "FG4322",
    componentName: "传感器",
    errorType: 1,
    errorTime: "2024-12-05 12:00:00",
    possibleReason: "传感器故障",
    taskStatus: "未处理",
    collecterId: "cazg-31",
    deviceType: "充电装载机",
    manufacturer: "重新标定或更换传感器",
    projectSite: "通麦天险施工点",
  },
  {
    index: 32,
    deviceName: "换电混泥土车",
    deviceNum: "GH9876",
    componentName: "控制板",
    errorType: 3,
    errorTime: "2024-12-15 11:50:00",
    possibleReason: "主板过热",
    taskStatus: "已申报故障",
    collecterId: "cazg-32",
    deviceType: "换电混泥土车",
    manufacturer: "检查并调节电机电流",
    projectSite: "左贡隧道3#斜井",
  },
  {
    index: 33,
    deviceName: "充电自卸车",
    deviceNum: "HI8765",
    componentName: "液压系统",
    errorType: 1,
    errorTime: "2024-12-20 16:30:00",
    possibleReason: "液压不足",
    taskStatus: "未处理",
    collecterId: "cazg-33",
    deviceType: "充电自卸车",
    manufacturer: "重启系统并校验数据",
    projectSite: "然乌",
  },
  {
    index: 34,
    deviceName: "换电挖掘机",
    deviceNum: "IJ6543",
    componentName: "电动马达",
    errorType: 2,
    errorTime: "2025-01-05 10:10:00",
    possibleReason: "马达老化",
    taskStatus: "忽略",
    collecterId: "cazg-34",
    deviceType: "换电挖掘机",
    manufacturer: "补充液压油，检查密封",
    projectSite: "邦达施工点",
  },
  {
    index: 35,
    deviceName: "充电挖掘机",
    deviceNum: "JK3456",
    componentName: "液压系统",
    errorType: 1,
    errorTime: "2025-01-10 08:45:00",
    possibleReason: "液压不足",
    taskStatus: "未处理",
    collecterId: "cazg-35",
    deviceType: "充电挖掘机",
    manufacturer: "修复驱动部件并测试",
    projectSite: "巴塘隧道",
  },
  {
    index: 36,
    deviceName: "充电装载机",
    deviceNum: "KL5678",
    componentName: "电池",
    errorType: 2,
    errorTime: "2025-01-15 09:00:00",
    possibleReason: "电池寿命不足",
    taskStatus: "已申报故障",
    collecterId: "cazg-36",
    deviceType: "充电装载机",
    manufacturer: "检查线路或更换传感器",
    projectSite: "拉乌山隧道入口",
  },
  {
    index: 37,
    deviceName: "换电自卸车",
    deviceNum: "LM6789",
    componentName: "动力系统",
    errorType: 3,
    errorTime: "2025-01-20 12:00:00",
    possibleReason: "电机故障",
    taskStatus: "未处理",
    collecterId: "cazg-37",
    deviceType: "换电自卸车",
    manufacturer: "更换新电池并校准电量",
    projectSite: "巴宜隧道东端",
  },
  {
    index: 38,
    deviceName: "充电混泥土车",
    deviceNum: "MN7890",
    componentName: "控制系统",
    errorType: 1,
    errorTime: "2025-01-25 11:10:00",
    possibleReason: "控制系统故障",
    taskStatus: "忽略",
    collecterId: "cazg-38",
    deviceType: "充电混泥土车",
    manufacturer: "更换电容或检查电路",
    projectSite: "林芝大桥西端",
  },
  {
    index: 39,
    deviceName: "换电挖掘机",
    deviceNum: "NO8901",
    componentName: "电池管理系统",
    errorType: 1,
    errorTime: "2025-02-01 15:20:00",
    possibleReason: "电池过热",
    taskStatus: "未处理",
    collecterId: "cazg-39",
    deviceType: "换电挖掘机",
    manufacturer: "检查冷却系统并降温",
    projectSite: "波密隧道出口",
  },
  {
    index: 40,
    deviceName: "充电自卸车",
    deviceNum: "OP1234",
    componentName: "电机",
    errorType: 3,
    errorTime: "2025-02-10 09:45:00",
    possibleReason: "电机过载",
    taskStatus: "未处理",
    collecterId: "cazg-40",
    deviceType: "充电自卸车",
    manufacturer: "更换高容量电池",
    projectSite: "通麦天险施工点",
  },
];

const errorReport = [
  {
    index: 1,
    deviceName: "换电挖掘机",
    deviceNum: "ZZ1234",
    componentName: "动力系统",
    errorType: 2,
    errorTime: "2024-04-22 09:20:00",
    possibleReason: "功率不足",
    taskStatus: "已申报故障",
    collecterId: "cazg-2",
    deviceType: "换电挖掘机",
    manufacturer: "徐工",
    projectSite: "米拉山隧道进口",
    reportPerson: "李伟",
    ReportTime: "2024-04-25 15:30:00",
  },
  {
    index: 2,
    deviceName: "充电装载机",
    deviceNum: "AA4567",
    componentName: "电池管理系统",
    errorType: 3,
    errorTime: "2024-05-30 16:10:00",
    possibleReason: "电池温度过高",
    taskStatus: "已申报故障",
    collecterId: "cazg-5",
    deviceType: "充电装载机",
    manufacturer: "三一重工",
    projectSite: "巴塘隧道",
    reportPerson: "王芳",
    ReportTime: "2024-06-02 14:50:00",
  },
  {
    index: 3,
    deviceName: "充电混泥土车",
    deviceNum: "BB7685",
    componentName: "液压泵",
    errorType: 1,
    errorTime: "2024-06-15 14:00:00",
    possibleReason: "液压故障",
    taskStatus: "已申报故障",
    collecterId: "cazg-9",
    deviceType: "充电混泥土车",
    manufacturer: "柳工",
    projectSite: "芒康隧道出口",
    reportPerson: "张强",
    ReportTime: "2024-06-20 09:10:00",
  },
  {
    index: 4,
    deviceName: "充电挖掘机",
    deviceNum: "QQ6780",
    componentName: "动力系统",
    errorType: 2,
    errorTime: "2024-08-25 10:20:00",
    possibleReason: "电机过热",
    taskStatus: "已申报故障",
    collecterId: "cazg-16",
    deviceType: "充电挖掘机",
    manufacturer: "长安重工",
    projectSite: "波密隧道出口",
    reportPerson: "赵娜",
    ReportTime: "2024-08-29 12:15:00",
  },
  {
    index: 5,
    deviceName: "充电挖掘机",
    deviceNum: "XX6788",
    componentName: "电控系统",
    errorType: 2,
    errorTime: "2024-10-10 13:50:00",
    possibleReason: "系统更新失败",
    taskStatus: "已申报故障",
    collecterId: "cazg-23",
    deviceType: "充电挖掘机",
    manufacturer: "徐工",
    projectSite: "左贡隧道3#斜井",
    reportPerson: "刘杰",
    ReportTime: "2024-10-15 09:00:00",
  },
  {
    index: 6,
    deviceName: "换电混泥土车",
    deviceNum: "CD7893",
    componentName: "液压泵",
    errorType: 2,
    errorTime: "2024-11-10 09:35:00",
    possibleReason: "液压压力不足",
    taskStatus: "已申报故障",
    collecterId: "cazg-28",
    deviceType: "换电混泥土车",
    manufacturer: "柳工",
    projectSite: "邦达施工点",
    reportPerson: "陈云",
    ReportTime: "2024-11-13 14:20:00",
  },
  {
    index: 7,
    deviceName: "换电混泥土车",
    deviceNum: "GH9876",
    componentName: "控制板",
    errorType: 3,
    errorTime: "2024-12-15 11:50:00",
    possibleReason: "主板过热",
    taskStatus: "已申报故障",
    collecterId: "cazg-32",
    deviceType: "换电混泥土车",
    manufacturer: "长安重工",
    projectSite: "左贡隧道3#斜井",
    reportPerson: "孙丽",
    ReportTime: "2024-12-18 10:00:00",
  },
  {
    index: 8,
    deviceName: "充电装载机",
    deviceNum: "KL5678",
    componentName: "电池",
    errorType: 2,
    errorTime: "2025-01-15 09:00:00",
    possibleReason: "电池寿命不足",
    taskStatus: "已申报故障",
    collecterId: "cazg-36",
    deviceType: "充电装载机",
    manufacturer: "柳工",
    projectSite: "拉乌山隧道入口",
    reportPerson: "王强",
    ReportTime: "2025-01-18 16:30:00",
  },
  {
    index: 9,
    deviceName: "换电自卸车",
    deviceNum: "CC9832",
    componentName: "液压系统",
    errorType: 1,
    errorTime: "2024-07-02 12:00:00",
    possibleReason: "液压压力不足",
    taskStatus: "已申报故障",
    collecterId: "cazg-10",
    deviceType: "换电自卸车",
    manufacturer: "长安重工",
    projectSite: "巴塘隧道",
    reportPerson: "杨洋",
    ReportTime: "2024-07-04 11:45:00",
  },
];

export { originalData, errorReport };
