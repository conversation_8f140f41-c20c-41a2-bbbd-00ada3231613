/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./AttributeCompression-75ce15eb","./IndexDatatype-9435b55f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./createTaskProcessorWorker","./EllipsoidTangentPlane-9c25b2da","./OrientedBoundingBox-7b25e901","./TerrainEncoding-3dab0ca0"],function(v,C,ce,ge,me,we,e,i,r,t,n,s,h,u,xe,o,d,p,a,ve,Ce){var ye={clipTriangleAtAxisAlignedThreshold:function(e,i,r,t,n,s){if(!v.defined(e))throw new C.DeveloperError("threshold is required.");if(!v.defined(i))throw new C.DeveloperError("keepAbove is required.");if(!v.defined(r))throw new C.DeveloperError("u0 is required.");if(!v.defined(t))throw new C.DeveloperError("u1 is required.");if(!v.defined(n))throw new C.DeveloperError("u2 is required.");var h,u,o;v.defined(s)?s.length=0:s=[],o=i?(h=r<e,u=t<e,n<e):(h=e<r,u=e<t,e<n);var d,p,a,f,l,c,g=h+u+o;return 1===g?h?(d=(e-r)/(t-r),p=(e-r)/(n-r),s.push(1),s.push(2),1!==p&&(s.push(-1),s.push(0),s.push(2),s.push(p)),1!==d&&(s.push(-1),s.push(0),s.push(1),s.push(d))):u?(a=(e-t)/(n-t),f=(e-t)/(r-t),s.push(2),s.push(0),1!==f&&(s.push(-1),s.push(1),s.push(0),s.push(f)),1!==a&&(s.push(-1),s.push(1),s.push(2),s.push(a))):o&&(l=(e-n)/(r-n),c=(e-n)/(t-n),s.push(0),s.push(1),1!==c&&(s.push(-1),s.push(2),s.push(1),s.push(c)),1!==l&&(s.push(-1),s.push(2),s.push(0),s.push(l))):2===g?h||r===e?u||t===e?o||n===e||(p=(e-r)/(n-r),a=(e-t)/(n-t),s.push(2),s.push(-1),s.push(0),s.push(2),s.push(p),s.push(-1),s.push(1),s.push(2),s.push(a)):(c=(e-n)/(t-n),d=(e-r)/(t-r),s.push(1),s.push(-1),s.push(2),s.push(1),s.push(c),s.push(-1),s.push(0),s.push(1),s.push(d)):(f=(e-t)/(r-t),l=(e-n)/(r-n),s.push(0),s.push(-1),s.push(1),s.push(0),s.push(f),s.push(-1),s.push(2),s.push(0),s.push(l)):3!==g&&(s.push(0),s.push(1),s.push(2)),s},computeBarycentricCoordinates:function(e,i,r,t,n,s,h,u,o){if(!v.defined(e))throw new C.DeveloperError("x is required.");if(!v.defined(i))throw new C.DeveloperError("y is required.");if(!v.defined(r))throw new C.DeveloperError("x1 is required.");if(!v.defined(t))throw new C.DeveloperError("y1 is required.");if(!v.defined(n))throw new C.DeveloperError("x2 is required.");if(!v.defined(s))throw new C.DeveloperError("y2 is required.");if(!v.defined(h))throw new C.DeveloperError("x3 is required.");if(!v.defined(u))throw new C.DeveloperError("y3 is required.");var d=r-h,p=h-n,a=s-u,f=t-u,l=1/(a*d+p*f),c=i-u,g=e-h,m=(a*g+p*c)*l,w=(-f*g+d*c)*l,x=1-m-w;return v.defined(o)?(o.x=m,o.y=w,o.z=x,o):new ge.Cartesian3(m,w,x)},computeLineSegmentLineSegmentIntersection:function(e,i,r,t,n,s,h,u,o){C.Check.typeOf.number("x00",e),C.Check.typeOf.number("y00",i),C.Check.typeOf.number("x01",r),C.Check.typeOf.number("y01",t),C.Check.typeOf.number("x10",n),C.Check.typeOf.number("y10",s),C.Check.typeOf.number("x11",h),C.Check.typeOf.number("y11",u);var d=(u-s)*(r-e)-(h-n)*(t-i);if(0!==d){var p=((h-n)*(i-s)-(u-s)*(e-n))/d,a=((r-e)*(i-s)-(t-i)*(e-n))/d;return 0<=p&&p<=1&&0<=a&&a<=1?(v.defined(o)||(o=new me.Cartesian2),o.x=e+p*(r-e),o.y=i+p*(t-i),o):void 0}}},be=32767,Be=16383,Ie=[],Ae=[],Ee=[],De=new ge.Cartographic,Te=new ge.Cartesian3,Oe=[],ze=[],Me=[],ke=[],Ne=[],Ve=new ge.Cartesian3,qe=new we.BoundingSphere,He=new ve.OrientedBoundingBox,Re=new me.Cartesian2,Se=new ge.Cartesian3;function Ue(){this.vertexBuffer=void 0,this.index=void 0,this.first=void 0,this.second=void 0,this.ratio=void 0}Ue.prototype.clone=function(e){return v.defined(e)||(e=new Ue),e.uBuffer=this.uBuffer,e.vBuffer=this.vBuffer,e.heightBuffer=this.heightBuffer,e.normalBuffer=this.normalBuffer,e.index=this.index,e.first=this.first,e.second=this.second,e.ratio=this.ratio,e},Ue.prototype.initializeIndexed=function(e,i,r,t,n){this.uBuffer=e,this.vBuffer=i,this.heightBuffer=r,this.normalBuffer=t,this.index=n,this.first=void 0,this.second=void 0,this.ratio=void 0},Ue.prototype.initializeFromClipResult=function(e,i,r){var t=i+1;return-1!==e[i]?r[e[i]].clone(this):(this.vertexBuffer=void 0,this.index=void 0,this.first=r[e[t]],++t,this.second=r[e[t]],++t,this.ratio=e[t],++t),t},Ue.prototype.getKey=function(){return this.isIndexed()?this.index:JSON.stringify({first:this.first.getKey(),second:this.second.getKey(),ratio:this.ratio})},Ue.prototype.isIndexed=function(){return v.defined(this.index)},Ue.prototype.getH=function(e,i){if(v.defined(this.index))return this.heightBuffer[this.index];var r=this.first.getH(e,i),t=this.second.getH(e,i);return 0===i+r/be*e||0===i+t/be*e?0:ce.CesiumMath.lerp(this.first.getH(),this.second.getH(),this.ratio)},Ue.prototype.getU=function(){return v.defined(this.index)?this.uBuffer[this.index]:ce.CesiumMath.lerp(this.first.getU(),this.second.getU(),this.ratio)},Ue.prototype.getV=function(){return v.defined(this.index)?this.vBuffer[this.index]:ce.CesiumMath.lerp(this.first.getV(),this.second.getV(),this.ratio)};var f=new me.Cartesian2,l=-1,c=[new ge.Cartesian3,new ge.Cartesian3],g=[new ge.Cartesian3,new ge.Cartesian3];function m(e,i){var r=c[++l],t=g[l];return r=u.AttributeCompression.octDecode(e.first.getNormalX(),e.first.getNormalY(),r),t=u.AttributeCompression.octDecode(e.second.getNormalX(),e.second.getNormalY(),t),Te=ge.Cartesian3.lerp(r,t,e.ratio,Te),ge.Cartesian3.normalize(Te,Te),u.AttributeCompression.octEncode(Te,i),--l,i}Ue.prototype.getNormalX=function(){return v.defined(this.index)?this.normalBuffer[2*this.index]:(f=m(this,f)).x},Ue.prototype.getNormalY=function(){return v.defined(this.index)?this.normalBuffer[2*this.index+1]:(f=m(this,f)).y};var w=[];function Fe(e,i,r,t,n,s,h,u,o,d,p){if(0!==h.length){for(var a=0,f=0;f<h.length;)f=w[a++].initializeFromClipResult(h,f,u);for(var l=0;l<a;++l){var c=w[l];if(c.isIndexed())c.newIndex=s[c.index],c.uBuffer=e,c.vBuffer=i,c.heightBuffer=r,o&&(c.normalBuffer=t);else{var g=c.getKey();if(v.defined(s[g]))c.newIndex=s[g];else{var m=e.length;e.push(c.getU()),i.push(c.getV()),r.push(c.getH(d,p)),o&&(t.push(c.getNormalX()),t.push(c.getNormalY())),c.newIndex=m,s[g]=m}}}3===a?(n.push(w[0].newIndex),n.push(w[1].newIndex),n.push(w[2].newIndex)):4===a&&(n.push(w[0].newIndex),n.push(w[1].newIndex),n.push(w[2].newIndex),n.push(w[0].newIndex),n.push(w[2].newIndex),n.push(w[3].newIndex))}}return w.push(new Ue),w.push(new Ue),w.push(new Ue),w.push(new Ue),p(function(e,i){var r=e.isEastChild,t=e.isNorthChild,n=r?Be:0,s=r?be:Be,h=t?Be:0,u=t?be:Be,o=Oe,d=ze,p=Me,a=Ne;o.length=0,d.length=0,p.length=0,a.length=0;var f=ke;f.length=0;var l={},c=e.vertices,g=e.indices;g=g.subarray(0,e.indexCountWithoutSkirts);var m,w,x,v,C,y=Ce.TerrainEncoding.clone(e.encoding),b=y.hasVertexNormals,B=e.exaggeration,I=0,A=e.vertexCountWithoutSkirts,E=e.minimumHeight,D=e.maximumHeight,T=new Array(A),O=new Array(A),z=new Array(A),M=b?new Array(2*A):void 0;for(x=w=0;w<A;++w,x+=2){var k=y.decodeTextureCoordinates(c,w,Re);if(m=y.decodeHeight(c,w)/B,v=ce.CesiumMath.clamp(k.x*be|0,0,be),C=ce.CesiumMath.clamp(k.y*be|0,0,be),z[w]=ce.CesiumMath.clamp((m-E)/(D-E)*be|0,0,be),v<20&&(v=0),C<20&&(C=0),be-v<20&&(v=be),be-C<20&&(C=be),T[w]=v,O[w]=C,b){var N=y.getOctEncodedNormal(c,w,Se);M[x]=N.x,M[x+1]=N.y}(r&&Be<=v||!r&&v<=Be)&&(t&&Be<=C||!t&&C<=Be)&&(l[w]=I,o.push(v),d.push(C),p.push(z[w]),b&&(a.push(M[x]),a.push(M[x+1])),++I)}var V=[];V.push(new Ue),V.push(new Ue),V.push(new Ue);var q,H=[];for(H.push(new Ue),H.push(new Ue),H.push(new Ue),w=0;w<g.length;w+=3){var R=g[w],S=g[w+1],U=g[w+2],F=T[R],P=T[S],W=T[U];V[0].initializeIndexed(T,O,z,M,R),V[1].initializeIndexed(T,O,z,M,S),V[2].initializeIndexed(T,O,z,M,U);var X=ye.clipTriangleAtAxisAlignedThreshold(Be,r,F,P,W,Ie);(q=0)>=X.length||(q=H[0].initializeFromClipResult(X,q,V))>=X.length||(q=H[1].initializeFromClipResult(X,q,V))>=X.length||(q=H[2].initializeFromClipResult(X,q,V),Fe(o,d,p,a,f,l,ye.clipTriangleAtAxisAlignedThreshold(Be,t,H[0].getV(),H[1].getV(),H[2].getV(),Ae),H,b,D,E),q<X.length&&(H[2].clone(H[1]),H[2].initializeFromClipResult(X,q,V),Fe(o,d,p,a,f,l,ye.clipTriangleAtAxisAlignedThreshold(Be,t,H[0].getV(),H[1].getV(),H[2].getV(),Ae),H,b,D,E)))}var K=r?-be:0,L=t?-be:0,Y=[],_=[],G=[],J=[],Z=Number.MAX_VALUE,j=-Z,Q=Ee;Q.length=0;var $=me.Ellipsoid.clone(e.ellipsoid),ee=me.Rectangle.clone(e.childRectangle),ie=ee.north,re=ee.south,te=ee.east,ne=ee.west;for(te<ne&&(te+=ce.CesiumMath.TWO_PI),w=0;w<o.length;++w)v=(v=Math.round(o[w]))<=n?(Y.push(w),0):s<=v?(G.push(w),be):2*v+K,o[w]=v,C=(C=Math.round(d[w]))<=h?(_.push(w),0):u<=C?(J.push(w),be):2*C+L,d[w]=C,(m=ce.CesiumMath.lerp(E,D,p[w]/be))<Z&&(Z=m),j<m&&(j=m),p[w]=m,De.longitude=ce.CesiumMath.lerp(ne,te,v/be),De.latitude=ce.CesiumMath.lerp(re,ie,C/be),De.height=m,$.cartographicToCartesian(De,Te),Q.push(Te.x),Q.push(Te.y),Q.push(Te.z);var se=we.BoundingSphere.fromVertices(Q,ge.Cartesian3.ZERO,3,qe),he=ve.OrientedBoundingBox.fromRectangle(ee,Z,j,$,He),ue=new Ce.EllipsoidalOccluder($).computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid(se.center,Q,3,se.center,Z,Ve),oe=j-Z,de=new Uint16Array(o.length+d.length+p.length);for(w=0;w<o.length;++w)de[w]=o[w];var pe=o.length;for(w=0;w<d.length;++w)de[pe+w]=d[w];for(pe+=d.length,w=0;w<p.length;++w)de[pe+w]=be*(p[w]-Z)/oe;var ae,fe=xe.IndexDatatype.createTypedArray(o.length,f);if(b){var le=new Uint8Array(a);i.push(de.buffer,fe.buffer,le.buffer),ae=le.buffer}else i.push(de.buffer,fe.buffer);return{vertices:de.buffer,encodedNormals:ae,indices:fe.buffer,minimumHeight:Z,maximumHeight:j,westIndices:Y,southIndices:_,eastIndices:G,northIndices:J,boundingSphere:se,orientedBoundingBox:he,horizonOcclusionPoint:ue}})});