<template>
  <div id="voltage-wrapper"></div>
</template>

<script>
export default {
  name: "Voltage",
  props: ["deviceInfo", "deviceId", "sectionId"],
  data() {
    return {
      time: [],
      batteryVoltage: [],
    };
  },
  mounted() {},
  watch: {
    "deviceInfo.id": {
      handler(val) {
        this.getBatteryInfo();
      },
      deep: true,
    },
  },

  methods: {
    async getBatteryInfo() {
      let time = [];
      let total = [];
      let res = await this.$http.get(
        `/equip/build/batteryVoltageLineChart?vehicleIdentify=${this.deviceId}&sectionId=${this.sectionId||''}`
      );
      res?.result?.map((i) => {
        const timestamp = new Date(i.hour).getTime();
        time.push(timestamp);
        total.push(i.totalVoltage);
      });
      this.time = time;
      this.batteryVoltage = total;
      this.setChart();
    },
    // 初始化
    setChart() {
      let _ = this;
      let myChart = _.$echarts.init(
        document.getElementById("voltage-wrapper"),
        null,
        {
          renderer: "svg",
        }
      );
      myChart.setOption({
        tooltip: {
          trigger: "axis",
          formatter: (params) => {
            const date = new Date(params[0].axisValue);
            return `${date.toLocaleTimeString()}<br/>` + 
                params.map(p => `${p.marker}${p.seriesName}: ${p.value[1]}V`).join('<br/>')
          },
          axisPointer: {
            type: "shadow",
          },
        },
        color: ["#65C1DD"],
        legend: {
          data: ["电池电压"],
          left: "right",
        },
        grid: {
          left: "3%",
          right: "2%",
          bottom: "1%",
          top: 30,
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            // data: this.time,
            axisLabel: {
              formatter: (value) => {
                const date = new Date(value);
                return date.getHours().toString().padStart(2, '0') + ':' +
                    date.getMinutes().toString().padStart(2, '0');
              }
            },
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "电压 / V",
          },
        ],
        series: [
          {
            name: "电池电压",
            type: "bar",
            barWidth: "30%",
            data: this.batteryVoltage.map((v, i) => [this.time[i], v]),
          },
        ],
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
#voltage-wrapper {
  width: 100%;
  height: 100%;
}
</style>