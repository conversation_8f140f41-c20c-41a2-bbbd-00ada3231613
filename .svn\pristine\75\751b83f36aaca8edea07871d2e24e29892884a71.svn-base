const user = {
  state: {
    userInfo: {},
    token: null,
  },
  mutations: {
    SET_USER_INFO(state, view) {
      state.userInfo = view;
    },
    SET_USER_TOKEN(state, view) {
      state.token = view;
    },
  },

  actions: {
    setUserInfo({
      commit
    }, view) {
      commit('SET_USER_INFO', view);
    },
    setUserToken({
      commit
    }, view) {
      commit('SET_USER_TOKEN', view);
    }
  },
}

export default user;
