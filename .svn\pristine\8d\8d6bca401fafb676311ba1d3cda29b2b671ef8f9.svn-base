<template>
    <div class="ele-container">
        <div class="ele-top">
            <div class="ele-item ele-left">
                <div class="ele-info">
                    <div class="info-title">累计充电量</div>
                    <div class="info-num">
                        <el-tooltip :content="(eleData.totalchargeCapacity || 0) + ''" placement="top">
                            <span>{{ eleData.totalchargeCapacity || 0 }}</span>
                        </el-tooltip>
                    </div>
                </div>
                <div class="ele-img">
                    <img src="@/assets/images/dc-green.png" alt="充电量" class="img-bg">
                </div>
            </div>
            <div class="ele-item ele-right">
                <div class="ele-info">
                    <div class="info-title">累计耗电量</div>
                    <div class="info-num">
                        <el-tooltip :content="(eleData.totalDischargeCapacity || 0) + ''" placement="top">
                            <span>{{ eleData.totalDischargeCapacity || 0 }}</span>
                        </el-tooltip>
                    </div>
                </div>
                <div class="ele-img">
                    <img src="@/assets/images/dc-blue.png" alt="耗电量" class="img-bg">
                </div>
            </div>
        </div>
        <div class="ele-center">
            <div class="center-top">
                <div class="center-item">
                    <svg-icon icon-class="cd-l" class="img-bg" />
                    <div class="item-title">今日充电累计</div>
                </div>
                <div class="center-item">
                    <svg-icon icon-class="cd-r" class="img-bg" />
                    <div class="item-title">今日耗电累计</div>
                </div>
            </div>
            <div class="center-bottom">
                <div class="center-item">
                    <div class="item-total">
                        <el-tooltip :content="(eleData.todayChargeTotal || 0) + ''" placement="top">
                            <span>
                                {{ eleData.todayChargeTotal || 0 }}
                            </span>
                        </el-tooltip>
                    </div>
                    <div class="item-title">较昨日</div>
                    <div class="item-img">
                        <svg-icon icon-class="down" class="img-bg" />
                    </div>
                    <div class="item-title">
                        <el-tooltip :content="(eleData.todayChargeTotalCompareToYesterday || 0) + ''" placement="top">
                            <span>
                                {{ eleData.todayChargeTotalCompareToYesterday || 0 }}
                            </span>
                        </el-tooltip>
                    </div>
                </div>
                <div class="center-item">
                    <div class="item-total">
                        <el-tooltip :content="(eleData.todayDischargeTotal || 0) + ''" placement="top">
                            <span>
                                {{ eleData.todayDischargeTotal || 0 }}
                            </span>
                        </el-tooltip>
                    </div>
                    <div class="item-title">较昨日</div>
                    <div class="item-img">
                        <svg-icon icon-class="up" class="img-bg" />
                    </div>
                    <div class="item-title">
                        <el-tooltip :content="(eleData.todayDischargeTotalCompareToYesterday || 0) + ''"
                            placement="top">
                            <span>
                                {{ eleData.todayDischargeTotalCompareToYesterday || 0 }}
                            </span>
                        </el-tooltip>
                    </div>
                </div>
            </div>
        </div>
        <div class="ele-bottom">
            <div class="bottom-top">
                <div class="bottom-item">昨日电量</div>
                <div class="bottom-item">本月日均</div>
            </div>
            <div class="bottom-center">
                <div class="center-item">
                    <svg-icon icon-class="cd-bg" class="img-bg" />
                </div>
                <div class="center-item">
                    <svg-icon icon-class="cd-bg" class="img-bg" />
                </div>
            </div>
            <div class="bottom">
                <div class="item">
                    <div class="item-info">
                        <span>充电</span>
                        <span class="info-num">
                            <el-tooltip :content="(eleData.yesterdayChargeCapacity || 0) + ''" placement="top">
                                <span>
                                    {{ eleData.yesterdayChargeCapacity || 0 }}
                                </span>
                            </el-tooltip>
                        </span>
                    </div>
                    <div class="item-info">
                        <span>耗电</span>
                        <span class="info-num">
                            <el-tooltip :content="(eleData.yesterdayDischargeCapacity || 0) + ''" placement="top">
                                <span>
                                    {{ eleData.yesterdayDischargeCapacity || 0 }}
                                </span>
                            </el-tooltip>
                        </span>
                    </div>
                </div>
                <div class="item">
                    <div class="item-info">
                        <span>充电</span>
                        <span class="info-num">
                            <el-tooltip :content="(eleData.monthAverageChargeCapacity || 0) + ''" placement="top">
                                <span>
                                    {{ eleData.monthAverageChargeCapacity || 0 }}
                                </span>
                            </el-tooltip>
                        </span>
                    </div>
                    <div class="item-info">
                        <span>耗电</span>
                        <span class="info-num">
                            <el-tooltip :content="(eleData.monthAverageDischargeCapacity || 0) + ''" placement="top">
                                <span>
                                    {{ eleData.monthAverageDischargeCapacity || 0 }}
                                </span>
                            </el-tooltip>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ElectricityLevel',
    props: [],
    data() {
        return {
            eleData: {}
        }
    },
    watch: {
        "$store.state.tree.sectionId": {
            handler: function () {
                this.getLevel();
            },
            deep: true,
            immediate: true,
        },
    },
    mounted() {

    },

    methods: {
        async getLevel() {
            let res = await this.$http.get(`/equip/build/powerOverview?sectionId=${this.$store.state.tree.sectionId}`);
            this.eleData = res?.result || {}
        }
    }
}
</script>

<style lang="scss" scoped>
.ele-container {
    width: 370px;
    height: 254px;

    .ele-top {
        width: 100%;
        height: 70px;
        margin-top: 10px;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;

        .ele-item {
            width: 180px;
            height: 100%;
            border-radius: 2px;
            border: 1px solid #B3CAFF;
            padding: 10px;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;

            .ele-info {
                width: 120px;
                font-family: '微软雅黑', sans-serif;
                font-weight: 400;
                font-style: normal;
                font-size: 16px;
                color: #3A3F5D;

                .info-title {
                    width: 100%;
                    height: 50%;
                    padding-left: 10px;
                    line-height: 24px;
                }

                .info-num {
                    width: 100%;
                    height: 50%;
                    padding-left: 10px;
                    line-height: 24px;
                    overflow: hidden;
                    /* 确保超出容器的内容被裁剪 */
                    white-space: nowrap;
                    /* 确保文本在一行内显示 */
                    text-overflow: ellipsis;
                    /* 超出部分显示省略号 */

                    span {
                        cursor: pointer;
                    }
                }
            }

            .ele-img {
                width: 39px;
                height: 100%;

                .img-bg {
                    width: 100%;
                    height: 100%;
                    background-size: contain;
                }
            }
        }

        .ele-right {
            margin-left: 10px;
        }
    }

    .ele-center {
        width: 100%;
        height: 74px;
        margin-top: 15px;
        border-radius: 10px;
        border: 1px solid #B3CAFF;

        .center-top {
            width: 100%;
            height: 30px;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;

            .center-item {
                width: 50%;
                height: 100%;
                position: relative;

                .img-bg {
                    width: 100%;
                    height: 100%;
                    background-size: contain;
                }

                .item-title {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    left: 0px;
                    top: 0px;
                    line-height: 30px;
                    text-align: center;
                    font-size: 16px;
                    font-weight: 400;
                    color: #fff;
                    font-family: '微软雅黑', sans-serif;
                    font-style: normal;
                }
            }
        }

        .center-bottom {
            width: 100%;
            height: 42px;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;

            .center-item {
                width: 50%;
                height: 100%;
                line-height: 42px;
                display: flex;
                flex-direction: row;
                flex-wrap: nowrap;

                .item-total {
                    width: 72px;
                    height: 100%;
                    text-align: center;
                    font-family: '微软雅黑', sans-serif;
                    font-weight: 400;
                    font-style: normal;
                    font-size: 16px;
                    color: #3A3F5D;
                    overflow: hidden;
                    /* 确保超出容器的内容被裁剪 */
                    white-space: nowrap;
                    /* 确保文本在一行内显示 */
                    text-overflow: ellipsis;
                    /* 超出部分显示省略号 */

                    span {
                        cursor: pointer;
                    }
                }

                .item-title {
                    width: 50px;
                    font-family: '微软雅黑', sans-serif;
                    font-weight: 400;
                    font-style: normal;
                    font-size: 16px;
                    overflow: hidden;
                    /* 确保超出容器的内容被裁剪 */
                    white-space: nowrap;
                    /* 确保文本在一行内显示 */
                    text-overflow: ellipsis;
                    /* 超出部分显示省略号 */

                    span {
                        cursor: pointer;
                    }
                }

                .img-bg {
                    width: 7px;
                    height: 12px;
                    margin: 0 3px;
                    background-size: contain;
                }
            }
        }
    }

    .ele-bottom {
        width: 100%;
        height: 60px;
        margin-top: 15px;

        .bottom-top {
            width: 100%;
            height: 24px;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;

            .bottom-item {
                width: 50%;
                height: 100%;
                line-height: 24px;
                text-align: center;
                font-family: '微软雅黑', sans-serif;
                font-weight: 400;
                font-style: normal;
                font-size: 16px;
                color: #333333;
            }
        }

        .bottom-center {
            width: 100%;
            height: 15px;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;

            .center-item {
                width: 50%;
                height: 100%;
                line-height: 6px;
                text-align: center;

                .img-bg {
                    width: 20px;
                    height: 5px;
                    background-size: contain;
                }
            }
        }

        .bottom {
            width: 100%;
            height: 21px;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;

            .item {
                width: 50%;
                height: 100%;
                display: flex;
                flex-direction: row;
                flex-wrap: nowrap;

                .item-info {
                    width: 45%;
                    height: 100%;
                    margin-right: 2.5%;
                    font-family: '微软雅黑', sans-serif;
                    font-weight: 400;
                    font-style: normal;
                    font-size: 16px;
                    justify-content: center;
                    align-items: center;
                }

                .info-num {
                    width: 45px;
                    line-height: 13px;
                    display: inline-block;
                    margin-left: 5px;
                    font-family: '优设标题黑', sans-serif;
                    font-weight: 400;
                    font-style: normal;
                    font-size: 16px;
                    overflow: hidden;
                    /* 确保超出容器的内容被裁剪 */
                    white-space: nowrap;
                    /* 确保文本在一行内显示 */
                    text-overflow: ellipsis;
                    /* 超出部分显示省略号 */

                    span {
                        cursor: pointer;
                    }
                }
            }
        }
    }
}
</style>