<template>
  <div id="Sbtzgl">
    <el-card class="card-content">
      <div class="content-option">
        <!-- <div class="content-title">设备台账管理</div> -->
        <el-form :inline="true" :model="formInline" ref="formInline" class="form-view">
          <el-form-item label="标段">
            <el-input placeholder="请输入标段" v-model="formInline.sectionName" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item>
          <el-form-item label="工区">
            <el-input placeholder="请输入工区" v-model="formInline.projectAreaName" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item>
          <el-form-item label="车辆品牌">
            <el-input placeholder="请输入车辆品牌" v-model="formInline.carBrand" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item>
          <el-form-item label="车架号">
            <el-input placeholder="请输入车架号" v-model="formInline.vehicleIdentify" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-plus" type="primary" @click="handleOperate(null)">新增台账</el-button>
            <el-button icon="el-icon-upload2" @click="handleImport">导入数据</el-button>
            <el-button icon="el-icon-download" @click="handleExport" :loading="loading">导出数据</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="content-table">
        <el-table ref="table" :data="tableData" style="width: 100%;" height="70vh" border :header-cell-style="headerCellStyle"
          :cell-style="cellStyle">
          <el-table-column type="index" width="50" label="序号" align="center" />
          <el-table-column prop="sectionName" min-width="150" label="标段" align="center" />
          
          <!-- <el-table-column prop="projectSite" label="工点" align="center" min-width="150" /> -->
          <el-table-column prop="deviceName" label="设备名称" align="center" min-width="150" />
          <el-table-column prop="vehicleIdentify" label="车架号" align="center" min-width="130" />  
          <el-table-column prop="factoryNum" label="出厂编号" align="center" min-width="180" />
          <el-table-column prop="managementNumber" label="管理编号" align="center" min-width="180" />
          <el-table-column prop="carNum" label="车牌号码" align="center" min-width="130" />
          <!-- <el-table-column prop="dynamicType" label="动力类型" align="center" min-width="130" /> -->
          <el-table-column prop="energyType" label="补能类型" align="center" min-width="130" />

          <el-table-column prop="collecterId" label="采集盒子ID" align="center" min-width="130" />
          
          <el-table-column prop="deviceTypeName" label="机械类型" align="center" min-width="150" />
          <el-table-column prop="entryTime" label="进场时间" align="center" min-width="130" />
          
          <el-table-column prop="projectAreaName" min-width="150" label="工区" align="center" />
          <el-table-column prop="carBrand" label="车辆品牌" align="center" min-width="130" />
          <el-table-column label="型号规格" align="center" min-width="150" prop="specificationModel">
          </el-table-column>
          <!-- <el-table-column prop="deviceNum" label="设备编号" align="center" min-width="130" /> -->
          <!-- <el-table-column label="机械类型" align="center" min-width="130">
            <template slot-scope="scope">
              <span v-if="scope.row.deviceType == '0'">充电挖掘机</span>
              <span v-else-if="scope.row.deviceType == '1'">换电挖掘机</span>
              <span v-else-if="scope.row.deviceType == '2'">充电装载机</span>
              <span v-else-if="scope.row.deviceType == '3'">换电装载机</span>
              <span v-else-if="scope.row.deviceType == '4'">充电自卸车</span>
              <span v-else-if="scope.row.deviceType == '5'">换电自卸车</span>
              <span v-else-if="scope.row.deviceType == '6'">充电混泥土车</span>
              <span v-else-if="scope.row.deviceType == '7'">换电混泥土车</span>
            </template>
          </el-table-column> -->
          <!-- <el-table-column prop="manufacturer" label="制造厂家" align="center" min-width="220" /> -->
          
          <el-table-column prop="entryAcceptance" label="进场验收情况" align="center" min-width="130" />
          
          <el-table-column prop="inspectionReport" label="型式检验报告" align="center" min-width="110" />
          <el-table-column prop="inspectionDate" label="设备检验日期" align="center" min-width="150" />
          <el-table-column prop="operationPerson" label="作业人员" align="center" min-width="150" />
          <el-table-column prop="exitTime" label="退场时间" align="center" min-width="130" />
          <el-table-column prop="remark" label="备注" align="center" min-width="150" />
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button @click="handleOperate(scope.row)" type="text" size="small" class="table-edit">编辑</el-button>
              <el-popconfirm title="确定删除吗？" @confirm="handleDelete(scope.row.id, '/equip/ledger/deleteById')">
                <el-button slot="reference" type="text" size="small"> 删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
          :total="total">
        </el-pagination>
      </div>
    </el-card>
    <AddLedgerDialog v-if="dialogVisible" :curData="curData" :dialogVisible="dialogVisible" @onClose="onClose"
      @onSave="onSave" />
    <ImportDialog v-if="importvisible" :importvisible="importvisible" @onClose="handleClose" />
  </div>
</template>

<script>
import tableUtils from '@/mixins/tableUtils';
import AddLedgerDialog from './components/Dialog/AddLedgerDialog.vue';
import ImportDialog from './components/ImportDialog/ImportDialog.vue';
import { exportPlan } from '@/api/baseApi/common';
export default {
  name: 'Sbtzgl',
  mixins: [tableUtils],
  components: { AddLedgerDialog, ImportDialog },
  data() {
    return {
      formInline: {},
      dialogVisible: false,
      curData: null,
      tableData: [],
      importvisible: false,
      loading: false,
    }
  },
  watch: {
    "$store.state.tree.sectionId": {
      
      
      handler: function () {
        this.requestList();
        
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    async requestList() {
      let params = {
        ...this.formInline,
        sectionId: this.$store.state.tree.sectionId
      };
      let res = await this.$http.post(`/equip/ledger/pages?page=${this.pageParams.page}&limit=${this.pageParams.limit}`, params);
      this.tableData = res.result?.records;
      this.total = res.result?.total;
    },

    async handleExport() {
      this.loading = true;
      let params = {
        ...this.formInline,
        sectionId: this.$store.state.tree.sectionId
      };
      let res = await exportPlan(params);
      var list = new Blob([res], {
        type: "application/vnd.ms-excel;charset=utf-8",
      });
      var downloadUrl = window.URL.createObjectURL(list);
      var anchor = document.createElement("a");
      anchor.href = downloadUrl;
      anchor.download = '设备台账管理表.xlsx';
      anchor.click();
      window.URL.revokeObjectURL(list);
      this.loading = false;
    },

    handleImport() {
      this.importvisible = true;
    },
    // findMatchingVehicle(item) {
    //   const dataArray = [
    //       "LC1HNYBJ6P0010990",
    //       "LC1HNYBJ8P0010991",
    //       "LC1HNYBJXP0010992",
    //       "LC1HNYBJ1P0010993",
    //       "LC1HNYBJ3P0010994",
    //       "LC1HNYBJ1R0004212",
    //       "LC1HNYBJ9R0004216",
    //       "LC1HNYBJ7R0004215",
    //       "LC1HNYBJ5R0004214",
    //       "LC1HNYBJ4R0004219",
    //       "LC1HNYBJ0R0004220",
    //       "LC1HNYBJ2R0004218",
    //       "LC1HNYBJ7R0004263",
    //       "LC1HNYBJ9R0004264",
    //       "LC1HNYBJ0R0004265",
    //       "LC1HNYBJ3R0007886",
    //       "LC1HNYBJ5R0007887",
    //       "LC1HNYBJ7R0007888",
    //       "LC1HNYBJ9R0007889",
    //       "LC1HNYBJ5R0007890",
    //       "LC1HNYBJ7R0007891",
    //       "LC1HNYBJ9R0007892",
    //       "LC1HNYBJ0R0007893",
    //       "LC1HNYBJ2R0007894",
    //       "LC1HNYBJ4R0007895",
    //       "LC1HNYBJ6R0007896",
    //       "LC1HNYBJ8R0007897",
    //       "LC1HNYBJXR0007898",
    //       "LC1HNYBJ6P0006566",
    //       "LC1HNYBJ8P0006567",
    //       "LC1HNYBJXP0006568",
    //       "LC1HNYBJ1P0006569",
    //       "LC1HNYBJ8P0006570",
    //       "LC1HNYBJXP0006571",
    //       "LC1HNYBJ1P0006572",
    //       "LC1HNYBJ3P0006573",
    //       "LC1HNYBJ9R0001946",
    //       "LC1HNYBJ0R0001947",
    //       "LC1HNYBJ2R0001948",
    //       "LC1HNYBJ4R0001949",
    //       "LC1HNYBJ0R0001950",
    //       "LC1HNYBJ2R0001951",
    //       "LC1HNYBJ4R0001952",
    //       "LC1HNYBJ6R0001953",
    //       "LC1HNYBJ8R0001954",
    //       "LC1HNYBJXR0001955",
    //       "LC1HNYBJ6R0004111",
    //       "LC1HNYBJ8R0004112",
    //       "LC1HNYBJXR0004113",
    //       "LC1HNYBJ1R0004114",
    //       "LC1HNYBJ3R0004115",
    //       "LC1HNYBJ5R0004116",
    //       "LC1HNYBJ7R0004117",
    //       "LC1HNYBJ9R0004118",
    //       "LC1HNYBJ0R0004119",
    //       "LC1HNYBJ7R0004120",
    //       "LC1HNYBJ9R0004121"
    //   ];
    //   if (!item || typeof item.vehicleIdentify !== 'string') {
    //     return undefined;
    //   }

    //   const last8Digits = item.vehicleIdentify.slice(-8);

    //   return dataArray.find(vin => vin.slice(-8) === last8Digits);
    // },
    handleOperate(item) {
      if (item) {

        // const vf = this.findMatchingVehicle(item);
        // if(vf){
        //   item.vehicleIdentify = vf;
        //   item.factoryNum = vf;
        //   this.$message.warning('信息已修改');
        // }
        this.curData = { data: { ...item }, type: 2 }
      } else {
        this.curData = { data: {}, type: 1 }
      }
      this.dialogVisible = true
    },

    onClose() {
      this.dialogVisible = false;
    },

    handleClose() {
      this.requestList();
      this.importvisible = false;
    },

    onSave() {
      this.requestList();
      this.dialogVisible = false;
    }
  }
}
</script>

<style lang="scss" scoped>
#Sbtzgl {
  .content-option {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .content-title {
      line-height: 40px;
      font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
      font-weight: 700;
      font-style: normal;
      font-size: 18px;
      color: #3A3F5D;
      margin-right: 5%;
    }

    .form-view {
      flex: 1;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }

    ::v-deep .el-input {
      width: 175px;
    }
  }

  .table-edit {
    margin-right: 10px;
  }

  .pagination-part {
    margin-top: 20px;
  }
}
</style>