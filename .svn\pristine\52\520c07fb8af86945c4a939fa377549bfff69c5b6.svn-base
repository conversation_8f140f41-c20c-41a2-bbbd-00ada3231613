
// @media screen and (min-width: 1200px) {
//   body {
//     -webkit-text-size-adjust: none;
//     font-size: 12px;
//   }
//   #app {
//     // 全局的时间选择器
//     .date_pick_classs {
//       display: block
//     }
//     .appwaper {
//       margin-top: 0px;
//       height: calc(100% - 60px);
//       width: 100%;
//     }
//   }
// }

// @media screen and(min-width:900px and max-width 1200px) {
//   #app {
//     // 全局的时间选择器
//     .date_pick_classs {
//       display: none !important
//     }
//     .appwaper {
//       margin-top: 0px;
//       height: calc(100% - 60px);
//       width: 100%;
//     }
//   }

// }

// @media screen and (max-width: 900px) {
//   body {
//     -webkit-text-size-adjust: none;
//     font-size: 14px;
//   }

//   #header {
//     display: none;
//   }

//   #mobile-bar {
//     display: block;
//   }

//   .el-dialog {
//     margin-top: 16vh;
//     width: 90% !important
//   }

//   .el-date-range-picker {
//     width: 90% !important;
//     z-index: 3033
//   }

//   .el-date-range-picker .el-picker-panel__body {
//     min-width: 80% !important;
//   }

//   .el-date-range-picker__content .el-date-range-picker__header div {
//     margin-left: 10px;
//     margin-right: 10px;
//   }

//   #app {
//     .date_pick_classs {
//       display: none;
//     }
//     .tabContaier .icon-class {
//       display: none !important
//     }

//     /**************主入口***********************/
//     .appwaper {
//       margin-top: 40px;
//       height: calc(100% - 40px);
//       width: 100%;

//       .main-container {
//         margin-left: 36px;
//       }
//     }
//     .fix-sidebar {
//       position: static;
//       .sidebar-container {
//         position: fixed;
//         top: 40px;
//         left: 0;
//         box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
//         transition: all 0.4s cubic-bezier(0.4, 0, 0, 1);
//         -webkit-transform: translate(-280px, 0);
//         transform: translate(0px, 0);
//         z-index: 5555
//       }

//       .sidebar-container .open {
//         -webkit-transform: translate(0, 0);
//         transform: translate(0, 0);
//       }
//       .slideleftCompage {
//         top: 78px;
//       }
//     }

//     // **************************************工程报验
//     .process_inspection {
//       .process_moblie {
//         display: inline-block;
//         width: 40px;
//       }
//     }
//     //-**********************远程报验
//     .long_rang {
//       padding: 5px;

//       .long_header {
//         height: auto;
//         line-height: 0px;

//         .moble_lang_header {
//           display: flex;
//           flex-direction: column;
//         }
//       }

//     }
//   }
// }
