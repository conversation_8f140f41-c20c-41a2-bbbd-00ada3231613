<template>
  <div class="device-container">
    <div class="device-one" v-if="!selectVal">
      <div class="device-item device-top">
        <div class="item-left" @click="handleClick('all')">
          <div class="device-info">
            <div class="device-title">接入数量</div>
            <div class="device-num">{{ deviceInfo.accessNumber || 0 }}</div>
          </div>
          <div class="device-img">
            <img
              src="@/assets/images/frame-green.png"
              alt="接入数量"
              class="img-bg"
            />
          </div>
        </div>
        <div class="item-right">
          <div class="device-info">
            <div class="device-title">未接入数量</div>
            <div class="device-num">{{ deviceInfo.NotAccessNumber || 0 }}</div>
          </div>
          <div class="device-img">
            <img
              src="@/assets/images/frame-orange.png"
              alt="未接入数量"
              class="img-bg"
            />
          </div>
        </div>
      </div>
      <div class="device-item device-bt">
        <div class="item-left" @click="handleClick('in')">
          <div class="device-info">
            <div class="device-title">在线数量</div>
            <div class="device-num">{{ deviceInfo.onlineNumber || 0 }}</div>
          </div>
          <div class="device-img">
            <img
              src="@/assets/images/frame-blue.png"
              alt="工作数量"
              class="img-bg"
            />
          </div>
        </div>
        <div class="item-right" @click="handleClick('out')">
          <div class="device-info">
            <div class="device-title">离线数量</div>
            <div class="device-num">{{ deviceInfo.offlineNumber || 0 }}</div>
          </div>
          <div class="device-img">
            <img
              src="@/assets/images/frame-purple.png"
              alt="离线数量"
              class="img-bg"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="device-two" v-else>
      <div class="center-top">
        <div class="center-item item-in">
          <div class="item-title">接入数量</div>
        </div>
        <div class="center-item item-no">
          <div class="item-title">未接入数量</div>
        </div>
      </div>
      <div class="center-bottom">
        <div class="bottom-item">
          <span class="item-num">{{ deviceInfo.accessNumber || 0 }}</span>
          <span>个</span>
        </div>
        <div class="bottom-item">
          <span class="item-num">{{ deviceInfo.NotAccessNumber || 0 }}</span>
          <span>个</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "PerceptionDevice",
  props: ['selectVal'],
  data() {
    return {
      deviceInfo: {},
    };
  },
  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.getPerceptionDevice();
      },
      deep: true,
      immediate: true,
    },
    "selectVal":{
      handler: function () {
        this.getPerceptionDevice();
      }
    }
  },
  mounted() {},

  methods: {
    async getPerceptionDevice() {
      let res = await this.$http.get(
        `/equip/build/senseDevice?keyword=${this.selectVal?1:0}&sectionId=${this.$store.state.tree.sectionId||''}`
      );
      this.deviceInfo = res?.result || {};
    },

    handleClick(val) {
      this.$router.push({ path: "/devices/access", query: { type: val } });
    },
  },
};
</script>

<style lang="scss" scoped>
.device-container {
  .device-one {
    width: 350px;
    height: 200px;
    .device-item {
      width: 100%;
      height: 50%;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;

      .item-left {
        flex: 1;
        padding: 10px;
        border-right: 6px solid #22c5e3;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
      }

      .item-right {
        padding: 10px;
        flex: 1;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
      }

      .device-info {
        flex: 1;

        .device-title {
          width: 100%;
          height: 50%;
          line-height: 37px;
          text-align: left;
          font-family: "微软雅黑 Bold", "微软雅黑 Regular", "微软雅黑",
            sans-serif;
          font-weight: 700;
          font-style: normal;
          font-size: 16px;
          color: #3a3f5d;
        }

        .device-num {
          width: 100%;
          height: 50%;
          line-height: 37px;
          font-family: "微软雅黑", sans-serif;
          font-weight: 400;
          font-style: normal;
          font-size: 24px;
          color: #3d7af0;
          text-align: left;
        }
      }

      .device-img {
        width: 70px;
        height: 100%;

        .img-bg {
          width: 100%;
          height: 100%;
          background-size: contain;
        }
      }
    }

    .device-top {
      border-bottom: 6px solid #22c5e3;

      .item-left {
        cursor: pointer;
      }
    }

    .device-bt {
      .item-left {
        cursor: pointer;
      }

      .item-right {
        cursor: pointer;
      }
    }
  }

  .device-two {
    border: 1px solid #b3caff;
    border-radius: 10px;

    .center-top {
      width: 100%;
      height: 60px;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;

      .center-item {
        width: 50%;
        height: 100%;
        position: relative;

        .item-title {
          width: 100%;
          height: 100%;
          position: absolute;
          left: 0px;
          top: 0px;
          line-height: 60px;
          text-align: center;
          font-size: 18px;
          font-weight: 400;
          color: #fff;
          font-family: "微软雅黑", sans-serif;
          font-style: normal;
        }
      }

      .item-in{
        background: linear-gradient(45deg, #29CDBD, #44DEB2);
        border-top-left-radius: 10px;
      }

      .item-no{
        background: linear-gradient(45deg, #149BEB, #24CAE1);
        border-top-right-radius: 10px;
      }
    }

    .center-bottom {
      width: 100%;
      height: 140px;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;

      .bottom-item {
        width: 50%;
        height: 100%;
        text-align: center;
        line-height: 140px;
        font-family: "优设标题黑", sans-serif;
        font-weight: 400;
        font-size: 16px;

        .item-num {
          font-size: 38px;
        }
      }
    }
  }
}
</style>
