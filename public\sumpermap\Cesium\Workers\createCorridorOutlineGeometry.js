/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./GeometryAttributes-aacecde6","./IndexDatatype-9435b55f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./arrayFill-9766fb2e","./GeometryOffsetAttribute-999fc023","./arrayRemoveDuplicates-2869246d","./EllipsoidTangentPlane-9c25b2da","./EllipsoidRhumbLine-6ca4b1e6","./earcut-2.2.1-b404d9e6","./PolygonPipeline-cc78b34e","./PolylineVolumeGeometryLibrary-ac3b176f","./EllipsoidGeodesic-db2069b3","./PolylinePipeline-65700d85","./CorridorGeometryLibrary-f6551651"],function(R,h,y,B,c,b,e,t,i,F,U,g,r,o,a,j,Y,n,s,_,E,m,l,d,u,G,q,p,f,W){var J=new B.Cartesian3,z=new B.Cartesian3,K=new B.Cartesian3;function T(e,t){var i,r,o,a=[],n=e.positions,s=e.corners,l=e.endPositions,d=new j.GeometryAttributes,u=0,p=0,f=0;for(r=0;r<n.length;r+=2)u+=o=n[r].length-3,f+=o/3*4,p+=n[r+1].length-3;for(u+=3,p+=3,r=0;r<s.length;r++){i=s[r];var h=s[r].leftPositions;R.defined(h)?u+=o=h.length:p+=o=s[r].rightPositions.length,f+=o/3*2}var c,y=R.defined(l);y&&(u+=c=l[0].length-3,p+=c,f+=4*(c/=3));var b,g,m,v,A,C,_=u+p,E=new Float64Array(_),G=0,T=_-1,P=c/2,w=Y.IndexDatatype.createTypedArray(_/3,f+4),L=0;if(w[L++]=G/3,w[L++]=(T-2)/3,y){a.push(G/3),C=J,A=z;var D=l[0];for(r=0;r<P;r++)C=B.Cartesian3.fromArray(D,3*(P-1-r),C),A=B.Cartesian3.fromArray(D,3*(P+r),A),W.CorridorGeometryLibrary.addAttribute(E,A,G),W.CorridorGeometryLibrary.addAttribute(E,C,void 0,T),v=(g=G/3)+1,m=(b=(T-2)/3)-1,w[L++]=b,w[L++]=m,w[L++]=g,w[L++]=v,G+=3,T-=3}var k=0,O=n[k++],N=n[k++];for(E.set(O,G),E.set(N,T-N.length+1),o=N.length-3,a.push(G/3,(T-2)/3),r=0;r<o;r+=3)v=(g=G/3)+1,m=(b=(T-2)/3)-1,w[L++]=b,w[L++]=m,w[L++]=g,w[L++]=v,G+=3,T-=3;for(r=0;r<s.length;r++){var V,x,H=(i=s[r]).leftPositions,I=i.rightPositions,S=K;if(R.defined(H)){for(T-=3,x=m,a.push(v),V=0;V<H.length/3;V++)S=B.Cartesian3.fromArray(H,3*V,S),w[L++]=x-V-1,w[L++]=x-V,W.CorridorGeometryLibrary.addAttribute(E,S,void 0,T),T-=3;a.push(x-Math.floor(H.length/6)),t===q.CornerType.BEVELED&&a.push((T-2)/3+1),G+=3}else{for(G+=3,x=v,a.push(m),V=0;V<I.length/3;V++)S=B.Cartesian3.fromArray(I,3*V,S),w[L++]=x+V,w[L++]=x+V+1,W.CorridorGeometryLibrary.addAttribute(E,S,G),G+=3;a.push(x+Math.floor(I.length/6)),t===q.CornerType.BEVELED&&a.push(G/3-1),T-=3}for(O=n[k++],N=n[k++],O.splice(0,3),N.splice(N.length-3,3),E.set(O,G),E.set(N,T-N.length+1),o=N.length-3,V=0;V<N.length;V+=3)g=(v=G/3)-1,b=(m=(T-2)/3)+1,w[L++]=b,w[L++]=m,w[L++]=g,w[L++]=v,G+=3,T-=3;G-=3,T+=3,a.push(G/3,(T-2)/3)}if(y){G+=3,T-=3,C=J,A=z;var M=l[1];for(r=0;r<P;r++)C=B.Cartesian3.fromArray(M,3*(c-r-1),C),A=B.Cartesian3.fromArray(M,3*r,A),W.CorridorGeometryLibrary.addAttribute(E,C,void 0,T),W.CorridorGeometryLibrary.addAttribute(E,A,G),g=(v=G/3)-1,b=(m=(T-2)/3)+1,w[L++]=b,w[L++]=m,w[L++]=g,w[L++]=v,G+=3,T-=3;a.push(G/3)}else a.push(G/3,(T-2)/3);return w[L++]=G/3,w[L++]=(T-2)/3,d.position=new U.GeometryAttribute({componentDatatype:F.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:E}),{attributes:d,indices:w,wallIndices:a}}function v(e){var t=(e=R.defaultValue(e,R.defaultValue.EMPTY_OBJECT)).positions,i=e.width;h.Check.typeOf.object("options.positions",t),h.Check.typeOf.number("options.width",i);var r=R.defaultValue(e.height,0),o=R.defaultValue(e.extrudedHeight,r);this._positions=t,this._ellipsoid=c.Ellipsoid.clone(R.defaultValue(e.ellipsoid,c.Ellipsoid.WGS84)),this._width=i,this._height=Math.max(r,o),this._extrudedHeight=Math.min(r,o),this._cornerType=R.defaultValue(e.cornerType,q.CornerType.ROUNDED),this._granularity=R.defaultValue(e.granularity,y.CesiumMath.RADIANS_PER_DEGREE),this._offsetAttribute=e.offsetAttribute,this._workerName="createCorridorOutlineGeometry",this.packedLength=1+t.length*B.Cartesian3.packedLength+c.Ellipsoid.packedLength+6}v.pack=function(e,t,i){h.Check.typeOf.object("value",e),h.Check.typeOf.object("array",t),i=R.defaultValue(i,0);var r=e._positions,o=r.length;t[i++]=o;for(var a=0;a<o;++a,i+=B.Cartesian3.packedLength)B.Cartesian3.pack(r[a],t,i);return c.Ellipsoid.pack(e._ellipsoid,t,i),i+=c.Ellipsoid.packedLength,t[i++]=e._width,t[i++]=e._height,t[i++]=e._extrudedHeight,t[i++]=e._cornerType,t[i++]=e._granularity,t[i]=R.defaultValue(e._offsetAttribute,-1),t};var A=c.Ellipsoid.clone(c.Ellipsoid.UNIT_SPHERE),C={positions:void 0,ellipsoid:A,width:void 0,height:void 0,extrudedHeight:void 0,cornerType:void 0,granularity:void 0,offsetAttribute:void 0};return v.unpack=function(e,t,i){h.Check.typeOf.object("array",e),t=R.defaultValue(t,0);for(var r=e[t++],o=new Array(r),a=0;a<r;++a,t+=B.Cartesian3.packedLength)o[a]=B.Cartesian3.unpack(e,t);var n=c.Ellipsoid.unpack(e,t,A);t+=c.Ellipsoid.packedLength;var s=e[t++],l=e[t++],d=e[t++],u=e[t++],p=e[t++],f=e[t];return R.defined(i)?(i._positions=o,i._ellipsoid=c.Ellipsoid.clone(n,i._ellipsoid),i._width=s,i._height=l,i._extrudedHeight=d,i._cornerType=u,i._granularity=p,i._offsetAttribute=-1===f?void 0:f,i):(C.positions=o,C.width=s,C.height=l,C.extrudedHeight=d,C.cornerType=u,C.granularity=p,C.offsetAttribute=-1===f?void 0:f,new v(C))},v.createGeometry=function(e){var t=e._positions,i=e._width,r=e._ellipsoid;t=function(e,t){for(var i=0;i<e.length;i++)e[i]=t.scaleToGeodeticSurface(e[i],e[i]);return e}(t,r);var o=m.arrayRemoveDuplicates(t,B.Cartesian3.equalsEpsilon);if(!(o.length<2||i<=0)){var a,n=e._height,s=e._extrudedHeight,l=!y.CesiumMath.equalsEpsilon(n,s,0,y.CesiumMath.EPSILON2),d={ellipsoid:r,positions:o,width:i,cornerType:e._cornerType,granularity:e._granularity,saveAttributes:!1};if(l)d.height=n,d.extrudedHeight=s,d.offsetAttribute=e._offsetAttribute,a=function(e){var t=e.ellipsoid,i=T(W.CorridorGeometryLibrary.computePositions(e),e.cornerType),r=i.wallIndices,o=e.height,a=e.extrudedHeight,n=i.attributes,s=i.indices,l=n.position.values,d=l.length,u=new Float64Array(d);u.set(l);var p,f=new Float64Array(2*d);if(l=G.PolygonPipeline.scaleToGeodeticHeight(l,o,t),u=G.PolygonPipeline.scaleToGeodeticHeight(u,a,t),f.set(l),f.set(u,d),n.position.values=f,d/=3,R.defined(e.offsetAttribute)){var h=new Uint8Array(2*d);if(e.offsetAttribute===E.GeometryOffsetAttribute.TOP)h=_.arrayFill(h,1,0,d);else{var c=e.offsetAttribute===E.GeometryOffsetAttribute.NONE?0:1;h=_.arrayFill(h,c)}n.applyOffset=new U.GeometryAttribute({componentDatatype:F.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:h})}var y=s.length,b=Y.IndexDatatype.createTypedArray(f.length/3,2*(y+r.length));b.set(s);var g,m,v=y;for(p=0;p<y;p+=2){var A=s[p],C=s[p+1];b[v++]=A+d,b[v++]=C+d}for(p=0;p<r.length;p++)m=(g=r[p])+d,b[v++]=g,b[v++]=m;return{attributes:n,indices:b}}(d);else if((a=T(W.CorridorGeometryLibrary.computePositions(d),d.cornerType)).attributes.position.values=G.PolygonPipeline.scaleToGeodeticHeight(a.attributes.position.values,n,r),R.defined(e._offsetAttribute)){var u=a.attributes.position.values.length,p=new Uint8Array(u/3),f=e._offsetAttribute===E.GeometryOffsetAttribute.NONE?0:1;_.arrayFill(p,f),a.attributes.applyOffset=new U.GeometryAttribute({componentDatatype:F.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:p})}var h=a.attributes,c=b.BoundingSphere.fromVertices(h.position.values,void 0,3);return new U.Geometry({attributes:h,indices:a.indices,primitiveType:g.PrimitiveType.LINES,boundingSphere:c,offsetAttribute:e._offsetAttribute})}},function(e,t){return R.defined(t)&&(e=v.unpack(e,t)),e._ellipsoid=c.Ellipsoid.clone(e._ellipsoid),v.createGeometry(e)}});