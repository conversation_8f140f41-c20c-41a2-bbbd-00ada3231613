<template>
    <div class="warn-container">
        <div class="warn-top">
            <div class="warn-item warn-left">
                <img src="@/assets/images/warn-total.png" alt="异常总数" class="img-bg">
                <div class="box-info">
                    <div class="info-item info-title">异常总数</div>
                    <div class="info-item info-num">
                        <el-tooltip :content="(warnInfo.exceptionTotal || 0) + ''" placement="top">
                            <span class="num-v">{{ warnInfo.exceptionTotal || 0 }}</span>
                        </el-tooltip>
                        <span>次</span>
                    </div>
                </div>
            </div>
            <div class="warn-item warn-right">
                <img src="@/assets/images/warn-device.png" alt="异常机械" class="img-bg">
                <div class="box-info">
                    <div class="info-item info-title">异常机械</div>
                    <div class="info-item info-num">
                        <el-tooltip :content="(warnInfo.exceptionMachinery || 0) + ''" placement="top">
                            <span class="num-v">{{ warnInfo.exceptionMachinery || 0 }}</span>
                        </el-tooltip>
                        <span>台</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="warn-bottom">
            <div class="warning-item">
                <div class="warn-box"></div>
                <svg-icon icon-class="warn-info" class="img-bg" />
                <div class="item-info">
                    <div class="info-title">机械离线报警</div>
                    <div class="info-total">
                        {{ warnInfo.machineryOfflineAlarm || 0 }}
                        <span>台</span>
                    </div>
                </div>
            </div>
            <div class="warning-item">
                <div class="warn-box"></div>
                <svg-icon icon-class="warn-info" class="img-bg" />
                <div class="item-info">
                    <div class="info-title">机械故障报警</div>
                    <div class="info-total">
                        {{ warnInfo.machineryFaultAlarm || 0 }}
                        <span>台</span>
                        <div class="total-warn">
                            <svg-icon icon-class="alarm" class="alert-message" />
                            <div class="alert-info">
                                <el-tooltip :content="(warnInfo.majorFaultAmount || 0) + ''" placement="top">
                                    <span class="num-v">{{ warnInfo.majorFaultAmount || 0 }}</span>
                                </el-tooltip>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="warning-item">
                <div class="warn-box"></div>
                <svg-icon icon-class="warn-info" class="img-bg" />
                <div class="item-info">
                    <div class="info-title">低电量报警</div>
                    <div class="info-total">
                        {{ warnInfo.lowBatteryAlarm || 0 }}
                        <span>台</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'WarnInfo',
    props: [],
    data() {
        return {
            warnInfo: {}
        }
    },
    mounted() {

    },

    watch: {
        "$store.state.tree.sectionId": {
            handler: function () {
                this.getWarnInfo();
            },
            deep: true,
            immediate: true,
        },
    },

    methods: {
        async getWarnInfo() {
            let res = await this.$http.get(`/equip/build/alarm?sectionId=${this.$store.state.tree.sectionId||''}`);
            this.warnInfo = res?.result || {}
        }
    }
}
</script>

<style lang="scss" scoped>
.warn-container {
    width: 370px;
    height: 100%;
    margin: 0 15px;

    .warn-top {
        width: 100%;
        height: 40%;
        margin-top: 10px;
        background-color: #fff;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;

        .warn-item {
            width: 165px;
            height: 80px;
            position: relative;

            .img-bg {
                width: 100%;
                height: 100%;
                background-size: contain;
            }

            .box-info {
                width: 80px;
                height: 60px;
                position: absolute;
                left: 15px;
                top: 10px;

                .info-item {
                    width: 80px;
                    height: 30px;
                    line-height: 30px;
                    text-align: left;
                    font-family: '微软雅黑', sans-serif;
                    font-weight: 400;
                    font-style: normal;
                    font-size: 16px;
                    color: #3A3F5D;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }

                .info-num {
                    font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
                    font-weight: 700;
                    color: #3D7AF0;
                    text-align: left;

                    .num-v {
                        cursor: pointer;
                    }
                }
            }
        }

        .warn-right {
            margin-left: 25px;
        }
    }

    .warn-bottom {
        width: 100%;
        height: 60%;

        .warning-item {
            width: 100%;
            height: 24%;
            margin-top: 2%;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            position: relative;

            .img-bg {
                width: calc(100% - 20px);
                height: 100%;
                background-size: contain;
            }

            .warn-box {
                width: 3px;
                height: 100%;
                background: linear-gradient(to bottom, #E34D65, #F49F38);
            }

            .item-info {
                position: absolute;
                width: calc(100% - 3px);
                height: 100%;
                top: 0px;
                left: 3px;
                line-height: 35px;
                display: flex;
                flex-direction: row;
                flex-wrap: nowrap;
                padding: 0 20px;

                .info-title {
                    width: 50%;
                    height: 100%;
                    text-align: left;
                    font-family: '微软雅黑', sans-serif;
                    font-weight: 400;
                    font-style: normal;
                    font-size: 16px;
                    color: #3A3F5D;
                }

                .info-total {
                    width: 50%;
                    height: 100%;
                    text-align: center;
                    font-family: '微软雅黑', sans-serif;
                    font-weight: 400;
                    font-style: normal;
                    font-size: 16px;
                    color: #3D7AF0;
                    position: relative;

                    .total-warn {
                        position: absolute;
                        width: 50px;
                        height: 100%;
                        top: 0;
                        right: 0px;
                        color: #fff;
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        .alert-message {
                            width: 30px;
                            height: 30px;
                            background-size: contain;
                            animation: blink-warning 1s infinite;
                            margin-top: -5px;
                        }

                        .alert-info {
                            position: absolute;
                            width: 15px;
                            height: 15px;
                            line-height: 20px;
                            left: 18px;
                            top: 8px;
                            font-weight: 600;
                            font-size: 12px;

                            .num-v {
                                cursor: pointer;
                            }
                        }


                        @keyframes blink-warning {
                            0% {
                                opacity: 0;
                            }

                            50% {
                                opacity: 1;
                            }

                            100% {
                                opacity: 0;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>