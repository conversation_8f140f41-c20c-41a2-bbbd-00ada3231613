<template>
    <div id="Hdjgz">
        <el-card class="card-content">
            <div class="content-option">
                <div class="content-title">换电设备故障</div>
                <el-form :inline="true" :model="formInline" ref="formInline" class="form-view">
                    <el-form-item label="故障序号">
                        <el-input v-model="formInline.serialNo" clearable
                        prefix-icon="el-icon-search"></el-input>
                    </el-form-item>
                    <el-form-item label="设备ID">
                        <el-input v-model="formInline.deviceId" clearable
                        prefix-icon="el-icon-search"></el-input>
                    </el-form-item>
                    <el-form-item label="开始时间">
                        <el-date-picker
                        v-model="startTime"
                        type="datetime"
                        placeholder="选择开始日期"
                        clearable
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="结束时间">
                        <el-date-picker
                        v-model="endTime"
                        type="datetime"
                        placeholder="选择结束日期"
                        clearable
                        ></el-date-picker>
                    </el-form-item>
                    <!-- <el-form-item label="故障类型">
                        <el-select v-model="formInline.faultType" clearable >
                            <el-option
                                v-for="item in options"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item> -->
                    <el-form-item class="button-container">
                        <el-button type="primary" @click="handleQuery()">查询</el-button>
                        <el-button @click="handelExport()" :loading="loading">导出</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="content-table">
                <el-table ref="table" :data="tableData" style="width: 100%"  border :header-cell-style="headerCellStyle" :cell-style="cellStyle">
                    <el-table-column type="index" width="50" label="序号" align="center" />
                    <el-table-column prop="serialNo" label="故障记录序号" align="center" width="120" />
                    <el-table-column prop="deviceId" label="设备ID" align="center" width="120" />
                    <el-table-column prop="devName" label="设备名称" align="center" width="120" />
                    <el-table-column prop="occurTime" label="故障发生时间" align="center" min-width="90" />
                    <el-table-column prop="endTime" label="故障消除时间" align="center" min-width="90" />
                    <el-table-column label="故障持续时间(分钟)" align="center" min-width="60">
                        <template slot-scope="scope">
                            <!-- 使用formatter属性定义计算公式 -->
                            {{ calculateDuration(scope.row) }}
                        </template>
                    </el-table-column>
                    <!-- 故障持续时间与故障类型暂时没有 -->
                    <el-table-column prop="faultType" label="故障类型" align="center" min-width="60" />
                    <el-table-column prop="description" label="故障描述" align="center" min-width="60" />
                </el-table>
                <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
                :total="total">
                </el-pagination>
            </div>
        </el-card>
    </div>
</template>
<script>
import tableUtils from '@/mixins/tableUtils';
import { formattedTime, calculateDuration } from '@/utils/timer';
import { exportExchangeFaultRecordExcel } from '@/api/baseApi/common'
export default{
    name:'Hdjgz',
    mixins: [tableUtils],
    components: {},
    mounted(){
        this.getRecodes();
    },
    computed: {
        treeInfo() {
            return this.$store.state.tree.sectionId;
        }
    },
    watch:{
        treeInfo(){
            console.log(this.treeInfo);
            this.getRecodes()
        }
    },
    data(){
        return{
            formInline: {},
            options: [
                { value: 'option1', label: '换电故障' },
                { value: 'option2', label: '全部' },
            ],
            loading:false,
            startTime:'',
            endTime:'',
            tableData:[  
            ],
            pageParams:{
                page: 1,
                limit: 20,
            },
            total:0,
        }
    },
    methods:{
        calculateDuration,
        handleSizeChange(val){
            this.pageParams = {
                ...this.pageParams,
                limit: val
            }
            this.handleQuery()
        },
        handleCurrentChange(val){
            this.pageParams = {
                ...this.pageParams,
                page: val
            }
            this.handleQuery()
        },
        async handelExport(){
            this.loading = true;
            let params = {
                ...this.formInline,
                occurTime:formattedTime(this.startTime),
                endTime:formattedTime(this.endTime),

                // ...this.requestLoad,
                sectionId: this.treeInfo
            };
            console.log(params);
            let res = await exportExchangeFaultRecordExcel(params)
            var list = new Blob([res], {
                type: "application/vnd.ms-excel;charset=utf-8",
            });
            var downloadUrl = window.URL.createObjectURL(list);
            var anchor = document.createElement("a");
            anchor.href = downloadUrl;
            anchor.download = '换电设备故障表.xlsx';
            anchor.click();
            window.URL.revokeObjectURL(list);
            this.loading = false;
        },
        handleQuery(){
            this.getRecodes()
        },
        showSelect() {
            this.isSelectVisible = !this.isSelectVisible;
        },
        async getRecodes(){
            let params = {
                ...this.formInline,
                occurTime:formattedTime(this.startTime),
                endTime:formattedTime(this.endTime),
                // ...this.requestLoad,
                sectionId: this.treeInfo
            };
            let res = await this.$http.post(`/equip/changeElectricityFault/page?limit=${this.pageParams.limit}&page=${this.pageParams.page}`, params)
            this.tableData = res.result?.records;
            this.total = res.result?.total;

            console.log(res);
        },
    }
}
</script>

<style lang="scss" scoped>
#Hdjgz {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width:100%;
    // height: 100%;
    .card-content{
        .content-option {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            width:100%;

            .content-title {
                line-height: 40px;
                font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
                font-weight: 700;
                font-style: normal;
                font-size: 18px;
                color: #3A3F5D;
                margin-right: 5%;
            }

            .form-view {
                display: flex;
                align-items: flex-end;
                // float: right;
                // width: 80%;
                // flex-direction: row;
                // flex-wrap: wrap;
                .button-container{
                    min-width:150px;
                }
            }
        }
    }

}
</style>