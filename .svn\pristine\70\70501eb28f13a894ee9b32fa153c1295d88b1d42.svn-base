/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./GeometryAttributes-aacecde6","./IndexDatatype-9435b55f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./arrayRemoveDuplicates-2869246d","./EllipsoidRhumbLine-6ca4b1e6","./EllipsoidGeodesic-db2069b3","./PolylinePipeline-65700d85","./WallGeometryLibrary-80ce00e5"],function(k,m,x,G,p,L,e,i,t,P,T,V,r,n,a,S,I,o,s,l,d,u,f,R){var q=new G.Cartesian3,M=new G.Cartesian3;function h(e){var i=(e=k.defaultValue(e,k.defaultValue.EMPTY_OBJECT)).positions,t=e.maximumHeights,r=e.minimumHeights;if(!k.defined(i))throw new m.DeveloperError("options.positions is required.");if(k.defined(t)&&t.length!==i.length)throw new m.DeveloperError("options.positions and options.maximumHeights must have the same length.");if(k.defined(r)&&r.length!==i.length)throw new m.DeveloperError("options.positions and options.minimumHeights must have the same length.");var n=k.defaultValue(e.granularity,x.CesiumMath.RADIANS_PER_DEGREE),a=k.defaultValue(e.ellipsoid,p.Ellipsoid.WGS84);this._positions=i,this._minimumHeights=r,this._maximumHeights=t,this._granularity=n,this._ellipsoid=p.Ellipsoid.clone(a),this._workerName="createWallOutlineGeometry";var o=1+i.length*G.Cartesian3.packedLength+2;k.defined(r)&&(o+=r.length),k.defined(t)&&(o+=t.length),this.packedLength=o+p.Ellipsoid.packedLength+1}h.pack=function(e,i,t){if(!k.defined(e))throw new m.DeveloperError("value is required");if(!k.defined(i))throw new m.DeveloperError("array is required");var r;t=k.defaultValue(t,0);var n=e._positions,a=n.length;for(i[t++]=a,r=0;r<a;++r,t+=G.Cartesian3.packedLength)G.Cartesian3.pack(n[r],i,t);var o=e._minimumHeights;if(a=k.defined(o)?o.length:0,i[t++]=a,k.defined(o))for(r=0;r<a;++r)i[t++]=o[r];var s=e._maximumHeights;if(a=k.defined(s)?s.length:0,i[t++]=a,k.defined(s))for(r=0;r<a;++r)i[t++]=s[r];return p.Ellipsoid.pack(e._ellipsoid,i,t),i[t+=p.Ellipsoid.packedLength]=e._granularity,i};var c=p.Ellipsoid.clone(p.Ellipsoid.UNIT_SPHERE),g={positions:void 0,minimumHeights:void 0,maximumHeights:void 0,ellipsoid:c,granularity:void 0};return h.unpack=function(e,i,t){if(!k.defined(e))throw new m.DeveloperError("array is required");var r;i=k.defaultValue(i,0);var n,a,o=e[i++],s=new Array(o);for(r=0;r<o;++r,i+=G.Cartesian3.packedLength)s[r]=G.Cartesian3.unpack(e,i);if(0<(o=e[i++]))for(n=new Array(o),r=0;r<o;++r)n[r]=e[i++];if(0<(o=e[i++]))for(a=new Array(o),r=0;r<o;++r)a[r]=e[i++];var l=p.Ellipsoid.unpack(e,i,c),d=e[i+=p.Ellipsoid.packedLength];return k.defined(t)?(t._positions=s,t._minimumHeights=n,t._maximumHeights=a,t._ellipsoid=p.Ellipsoid.clone(l,t._ellipsoid),t._granularity=d,t):(g.positions=s,g.minimumHeights=n,g.maximumHeights=a,g.granularity=d,new h(g))},h.fromConstantHeights=function(e){var i,t,r=(e=k.defaultValue(e,k.defaultValue.EMPTY_OBJECT)).positions;if(!k.defined(r))throw new m.DeveloperError("options.positions is required.");var n=e.minimumHeight,a=e.maximumHeight,o=k.defined(n),s=k.defined(a);if(o||s){var l=r.length;i=o?new Array(l):void 0,t=s?new Array(l):void 0;for(var d=0;d<l;++d)o&&(i[d]=n),s&&(t[d]=a)}return new h({positions:r,maximumHeights:t,minimumHeights:i,ellipsoid:e.ellipsoid})},h.createGeometry=function(e){var i=e._positions,t=e._minimumHeights,r=e._maximumHeights,n=e._granularity,a=e._ellipsoid,o=R.WallGeometryLibrary.computePositions(a,i,r,t,n,!1);if(k.defined(o)){var s,l=o.pos.bottomPositions,d=o.pos.topPositions,m=d.length,p=2*m,u=new Float64Array(p),f=0;for(m/=3,s=0;s<m;++s){var h=3*s,c=G.Cartesian3.fromArray(d,h,q),g=G.Cartesian3.fromArray(l,h,M);u[f++]=g.x,u[f++]=g.y,u[f++]=g.z,u[f++]=c.x,u[f++]=c.y,u[f++]=c.z}var y=new S.GeometryAttributes({position:new T.GeometryAttribute({componentDatatype:P.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:u})}),v=p/3;p=2*v-4+v;var E=I.IndexDatatype.createTypedArray(v,p),b=0;for(s=0;s<v-2;s+=2){var w=s,_=s+2,C=G.Cartesian3.fromArray(u,3*w,q),H=G.Cartesian3.fromArray(u,3*_,M);if(!G.Cartesian3.equalsEpsilon(C,H,x.CesiumMath.EPSILON10)){var A=s+1,D=s+3;E[b++]=A,E[b++]=w,E[b++]=A,E[b++]=D,E[b++]=w,E[b++]=_}}return E[b++]=v-2,E[b++]=v-1,new T.Geometry({attributes:y,indices:E,primitiveType:V.PrimitiveType.LINES,boundingSphere:new L.BoundingSphere.fromVertices(u)})}},function(e,i){return k.defined(i)&&(e=h.unpack(e,i)),e._ellipsoid=p.Ellipsoid.clone(e._ellipsoid),h.createGeometry(e)}});