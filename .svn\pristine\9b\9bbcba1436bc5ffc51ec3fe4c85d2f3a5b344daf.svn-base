<template>
  <DetailsDialog :id="deviceId" :isclosed="false"/>

</template>

<script>
import DetailsDialog from "./DetailsDialog.vue";
export default {
  name: "Sgsbxq",
  components: {
    DetailsDialog,
  },
  data() {
    return {
      deviceId: null,
      deviceInfo: {},
      sectionId: this.$store.state.tree.sectionId||'',
    };
  },

  beforeMount() {
    this.deviceId = this.$route.query.id;
    console.log(this.deviceId);
    
  },

  mounted() {
  },

  methods: {

  },
};
</script>

<style lang="scss" scoped>

</style>