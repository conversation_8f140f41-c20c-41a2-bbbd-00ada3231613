<template>
  <div id="Chdzgl">
    <div class="container-left">
      <Card :title="titleData.chargingOverview" class="card-preview">
        <div class="charge-card">
          <div class="charge-card-title">累计数据</div>
          <div class="charge-card-content">
            <div class="charge-card-item">
              <div class="charge-card-item-value">
                <router-link to="/devices/chargingrecords" class="value">{{ chargingOverview.totalChargeCount||0 }}</router-link>
                <div class="quantifier">
                  <div class="text">次</div>
                </div>
              </div>
              <div class="charge-card-item-title">充电次数</div>
            </div>
            <div class="charge-card-item">
              <div class="charge-card-item-value">
                <router-link to="/devices/exchangerecords" class="value">{{ chargingOverview.totalElectricityChangeCount||0 }}</router-link>
                <div class="quantifier">
                  <div class="text">次</div>
                </div>
              </div>
              <div class="charge-card-item-title">换电次数</div>
            </div>
            <div class="charge-card-item">
              <div class="charge-card-item-value">
                <div class="value">{{ ((chargingOverview.totalChargeAmount||0)/10000.0).toFixed(2) }}</div>
                <div class="quantifier">
                  <div class="text">万KWH</div>
                </div>
              </div>
              <div class="charge-card-item-title">充电电量</div>
            </div>
            <div class="charge-card-item" style="max-width: 20%;">
              <div class="charge-card-item-img">
                <img src="@/assets/images/charging-plugin.png" alt="累计充电数据" class="img">
              </div>
            </div>
          </div>
        </div>
        <div class="charge-card">
          <div class="charge-card-title" style="background-image: linear-gradient(45deg, #2DDBB4, #07CDBE);">今日数据</div>
          <div class="charge-card-content" style="background-image: linear-gradient(to left, #F5F5F5, #C9E5FF);">
            <div class="charge-card-item">
              <div class="charge-card-item-value">
                <router-link to="/devices/chargingrecords" class="value">{{ chargingOverview.todayTotalChargeCount||0 }}</router-link>
                <div class="quantifier">
                  <div class="text">次</div>
                </div>
              </div>
              <div class="charge-card-item-title">充电次数</div>
            </div>
            <div class="charge-card-item">
              <div class="charge-card-item-value">
                <router-link to="/devices/exchangerecords" class="value">{{ chargingOverview.todayTotalElectricityChangeCount||0 }}</router-link>
                <div class="quantifier">
                  <div class="text">次</div>
                </div>
              </div>
              <div class="charge-card-item-title">换电次数</div>
            </div>
            <div class="charge-card-item">
              <div class="charge-card-item-value">
                <div class="value">{{ ((chargingOverview.todayTotalChargeAmount||0)/10000.0).toFixed(2) }}</div>
                <div class="quantifier">
                  <div class="text">万KWH</div>
                </div>
              </div>
              <div class="charge-card-item-title">充电电量</div>
            </div>
            <div class="charge-card-item" style="max-width: 20%;">
              <div class="charge-card-item-img">
                <img src="@/assets/images/charging-plug.png" alt="今日充电数据" class="img">
              </div>
            </div>
          </div>
        </div>
        <div class="charge-chart">
          <div class="switch-data">
            <select v-model="selectedDate" @change="switchData" class="selector">
              <option
                v-for="item in dateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </option>
            </select>
          </div>
          <LineChart :ChartData="chargingChartData" ref="lineChart" />
        </div>
      </Card>
      <Card :title="titleData.averageBatterySwappingTime" class="card-type">
        <div class="switch-data">
            <select v-model="selectedDate" @change="switchData" class="selector">
              <option
                v-for="item in dateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </option>
            </select>
          </div>
        <LineChart :ChartData="batterySwappingTimeChartData" />
      </Card>
    </div>
    <div class="container-center">
      <Card :title="titleData.chargingCompartment" class="card-preview">
        <div class="vertical-subcard-container">
          <div class="set-index-button" :class="{'hovered': chargeStartIndex>0&&isHoveredLeft }" @mouseover="isHoveredLeft = true"
            @mouseleave="isHoveredLeft = false" @click="handleLeftIndexButton"><</div>
          <SubCard v-for="item in batteryCompartmentStatus.slice(chargeStartIndex,chargeEndIndex)" :title="`${item.devName?item.devName:0}`" :subtitle="item.workStatus?workStatusList[parseInt(item.workStatus)]:''" :icon="item.workStatus?batteryIconSrc[parseInt(item.workStatus)].icon:null" class="vertical-subcard">
            <div class="subcard-content" @click="onClickDlg(item)">
              <img class="subcard-img" :src='item.workStatus?batteryIconSrc[parseInt(item.workStatus)].imgSrc:batteryIconSrc[batteryIconSrc.length-2].imgSrc'></img>
              <div class="split-line"></div>
              <div class="text-content">充电序号：{{ `${item.chargeSerial||0}` }}</div>
              <div class="text-content">起始SOC：{{ `${item.info?.startSoc||0}%` }}</div>
              <div class="text-content">起始电量：{{ `${item.info?.startMeterValue||0} kwh` }}</div>
              <div class="text-content">已充电量：{{ `${item.info?.chargingElectricity||0} kwh` }}</div>
              <div class="text-content">充电进度：{{ `${item.info?.endSoc||0}%` }}</div>
            </div>
            <el-dialog :visible.sync="item.dialogVisible" class="batteryDialog">
              <span slot='title' class="title">{{item.devName}}</span>
              <div class="dailog-container">
                <div class="left-container">
                  <div class="pointer-container">
                    <div class="pointer">
                      <CirclrPointer :currentPointer="parseFloat(item.chargeVoltage)" :pointerScale="400" :src="voltageSrc" :quantifier="'V'" :desc="'当前电压'"></CirclrPointer>
                    </div>
                    <div class="pointer">
                      <CirclrPointer :currentPointer="parseFloat(item.chargeCurrent)" :pointerScale="400" :src="voltageSrc" :quantifier="'A'" :desc="'当前电流'"></CirclrPointer>
                    </div>
                  </div>
                  <div class="progress-bar-container">
                    <div class="text-container">
                      <div class='text'>充电完成度</div>
                      <div class="icon-container">
                        <div class="text">{{ `${item.info?.endSoc||0}%` }}</div>
                        <svg-icon class="svg-icon" icon-class="charge-progress"></svg-icon>
                      </div>
                      
                    </div>
                    <progress :value="item.info?.endSoc||0" max="100" class="progress-bar">
                      {{ item.info?.endSoc||0 }}%
                    </progress>
                  </div>
                </div>
                <div class="right-container">
                  <div class="title">充电信息</div>
                  <div class="text-container">
                    <div class="text">设备名称：{{ item.info?.devName||'' }}</div>
                    <div class="text">设备编号：{{ item.info?.chargeNo||'' }}</div>
                    <div class="text">电池编号：{{ item.info?.chargeSerial||'' }}</div>
                    <div class="text">起始SOC： {{ item.info?.startSoc||'' }}</div>
                    <div class="text">起始电量：{{ item.info?.startMeterValue||'' }}</div>
                    <div class="text">已充电量：{{ item.info?.chargingAvgElectricity||'' }}</div>
                    <div class="text">已充时间：{{ item.info?.chargeTime||'' }}</div>
                    <div class="text">开始时间：{{ item.info?.startTime||'' }}</div>
                    <div class="text">剩余时间：{{ item.info?.remainingTime||'' }}</div>
                    <!-- 剩余时间没有数据 -->
                  </div>
                </div>
                
              </div>

            </el-dialog>
          </SubCard>
          <div class="set-index-button" style="left: 94.5%;" :class="{'hovered': chargeEndIndex<batteryCompartmentStatus.length&&isHoveredRight}" @mouseover="isHoveredRight = true"
          @mouseleave="isHoveredRight = false" @click="handleRightIndexButton">></div>
        </div>
        <div class="horizontal-subcard-container">
          <div class="set-index-button" :class="{'hovered': exchangeStartIndex>0&&isHoveredLeftE }" @mouseover="isHoveredLeftE = true"
            @mouseleave="isHoveredLeftE = false" @click="handleLeftIndexButtonE"><</div>
          <SubCard v-for="item in exchangeBatteryInfo.slice(exchangeStartIndex,exchangeEndIndex)" :key="item.id" :title="`换电区`" :subtitle="item.workStatus?item.workStatus:''" :icon="'charge-subtitle-green'" class="horizontal-subcard">
            <div class="horizontal-subcard-content">
              <div class="text-content">{{ `车辆名称：${item.devName||''} | 车辆编号：${item.carBodyNo||''} | 车辆号码：${item.carNo||''}` }}</div>
              <div class="up-content">
                <div :style="item.mode>0?'background: linear-gradient(to right, #08CDBD, #33DEB4);color:#fff;':''" class="rectangle">取车辆电池</div>
                <div :style="item.mode>1?'background: linear-gradient(to right, #08CDBD, #33DEB4);color:#fff;':''" class="rectangle">存入电池架</div>
                <div :style="item.mode>2?'background: linear-gradient(to right, #08CDBD, #33DEB4);color:#fff;':''" class="rectangle">去电池架电池</div>
              </div>
              <div class="down-content">
                <div :style="item.mode>3?'background: linear-gradient(to right, #08CDBD, #33DEB4);color:#fff;':''" class="rectangle">存入车辆</div>
                <div :style="item.mode>4?'background: linear-gradient(to right, #08CDBD, #33DEB4);color:#fff;':''" class="rectangle">机器人回待机位</div>
                <div :style="item.mode>5?'background: linear-gradient(to right, #08CDBD, #33DEB4);color:#fff;':''" class="rectangle">换电完成</div>
              </div>
            </div>
          </SubCard>
          <div class="set-index-button" style="left: 94.5%;" :class="{'hovered': exchangeEndIndex<exchangeBatteryInfo.length&&isHoveredRightE}" @mouseover="isHoveredRightE = true"
          @mouseleave="isHoveredRightE = false" @click="handleRightIndexButtonE">></div>
        </div>
      </Card>
      <Card :title="titleData.accumulatedChargingTrend" class="card-type">
        <div class="switch-data">
            <select v-model="selectedDate" @change="switchData" class="selector">
              <option
                v-for="item in dateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </option>
            </select>
          </div>
        <LineChart :ChartData="chargingTrendChartData" />
      </Card>
    </div>
    <div class="container-right">
      <Card :title="titleData.faultMonitoring" class="card-preview">
        <div class="subcard-container">
          <router-link to="/devices/chargingfault" class="subcard">
            <div class="subcard-title">充电机故障</div>
            <div class="subcard-content">
              <div class="value-content" >{{ faultAndEnvmonitorData.chargeMachineFaultCount }}</div>
              <div class="quantifier">
                <div class="text">
                  次
                </div>
              </div>
            </div>
            <div class="subcard-img">
              <img src="@/assets/images/charging-monitor.png" alt="充电机" class="img">
            </div>
          </router-link>
          <router-link to="/devices/exchangingfault" class="subcard">
            <div class="subcard-title">换电设备故障</div>
            <div class="subcard-content">
              <div class="value-content">{{ faultAndEnvmonitorData.changeElectricityFaultCount }}</div>
              <div class="quantifier">
                <div class="text">
                  次
                </div>
              </div>
            </div>
            <div class="subcard-img">
              <img src="@/assets/images/exchanging-monitor.png" alt="换电设备" class="img">
            </div>
            
          </router-link>
        </div>
        <div class="chart-container">
          <PieChart :ChartData="faultMonitoringChartData" class="pie-chart"></PieChart>
        </div>
        <div class="monitor-container" @click="monitorDialogVisible=true">
          <div class="smoke-monitor">
            <div class="title">
              烟雾浓度监控
            </div>
            <div class="content-container">
              <div class="value">{{ faultAndEnvmonitorData.smokeSenseCurrentData[0]?.smokeVal||0 }}</div>
              <div class="quantifier">
                <div class="text">OBS/M</div>
              </div>
            </div>
            <div class="status-container">
              <svg-icon class="svg-icon" icon-class="charge-subtitle-green"></svg-icon>
              <div class="status">正常</div>
            </div>
          </div>
          <div class="temperature-monitor">
            <div class="title">温度监控</div>
            <div class="content-container">
              <div class="value">{{ faultAndEnvmonitorData.smokeSenseCurrentData[0]?.temperature||0 }}</div>
              <div class="quantifier">
                <div class="text">℃</div>
              </div>
            </div>
            <div class="status-container">
              <svg-icon class="svg-icon" icon-class="charge-subtitle-green"></svg-icon>
              <div class="status">正常</div>
            </div>
          </div>
        </div>
        <el-dialog :visible.sync="monitorDialogVisible" :title="'充电监控详情'" class="monitor-dialog" :width="'75%'">
            <div class="dialog-container">
              <div class="down-container">
                <div class="battery-container">
                  <SubCard v-for="item in batteryCompartmentStatus" :title="item.devName" :subtitle="item.compartmentStatus" :icon="item.icon" class="battery-subcard">
                    <div class="subcard-content">
                      <CirclrPointer :current-pointer="parseFloat(item.info?.endSoc||0)" :pointer-scale="100" :quantifier="'%'" :src="socSrc" :desc="'当前SOC'" class="soc-pointer"></CirclrPointer>
                      <div class="content-container">
                        <div class="content-item">
                          <div class="text">总电压</div>
                          <div class="value">{{ `${item.info?.maxSingleVoltage||0} V` }}</div>
                        </div>
                        <div class="content-item">
                          <div class="text">温度</div>
                          <div class="value">{{ `${item.temperature||0} ℃` }}</div>
                        </div>
                        <div class="content-item">
                          <div class="text">烟雾浓度</div>
                          <div class="value">{{ `${item.smokeConcentration||0} OBS/M` }}</div>
                        </div>
                      </div>
                      <div class="split-line"></div>
                    </div>
                  </SubCard>
                </div>
                <div class="moniter-container">
                  <SubCard v-for="item in monitorStreams" :title="`${item.id} ${item.cameraName}`"  class="monitor-subcard">
                    <div class="subcard-content">
                      <video-player :sources="item.videoSources" :options="videoOptions"></video-player>
                    </div>
                  </SubCard>
                </div>
              </div>
            </div>
          </el-dialog>
      </Card>
      <Card :title="titleData.videoSurveillance" class="card-type">
        <div class="monitor-container">
          <div class="scroll-container">
            <SubCard v-for="item in monitorStreams" :title="`${item.id} ${item.cameraName}`" class="monitor-subcard">
                    <div class="subcard-content">
                      <video-player :sources="item.videoSources" :options="videoOptions"></video-player>
                    </div>
            </SubCard>
          </div>
        </div>
      </Card>
    </div>
  </div>
</template>

<script>
import Card from './components/Card/Card.vue';
import SubCard from './components/SubCard/SubCard.vue';
import LineChart from './components/LineChart/LineChart.vue';
import constDataSet from '@/utils/const';
import PieChart from './components/PieChart/PieChart.vue';
import CirclrPointer from './components/CirclePointer/CirclePointer.vue';
import VideoPlayer from "./components/VideoPlayer/VideoPlayer.vue"
export default {
  name: "Chdzgl",
  components: { Card, LineChart, SubCard, PieChart, CirclrPointer, VideoPlayer},
  data() {
    return {
      isHoveredLeft:false,
      isHoveredRight:false,
      isHoveredLeftE:false,
      isHoveredRightE:false,
      titleData: constDataSet.powerstationTitle,
      selectedDate:2,
      dateOptions:[
        {
          label:'按日',
          value:2
        },
        {
          label:'按月',
          value:1
        },
        {
          label:'按年',
          value:0
        }
      ],
      chargingOverview:{
        totalChargeCount: 0,
        todayTotalElectricityChangeCount: 0,
        todayTotalChargeCount: 0,
        totalElectricityChangeCount: 0,
        totalChargeAmount:0,
        todayTotalChargeAmount: 0,
      },
      //充电概况图数据
      chargingChartData:{
        labels:[],
        dataSets:[{
          label:'电量(KWH)',
          data:[],
          borderColor: '#00B6E6',
          borderWidth: 3,
          yAxisID: 'y-axis-1',
          // lineTension:0.5,
        },
        {
          label:'充电次数',
          data:[],
          borderColor: '#1AD4B9',
          borderWidth: 3,
          yAxisID: 'y-axis-2',
          // lineTension:0.5,
        },
        {
          label:'换电次数',
          data:[],
          borderColor: '#F89C6B',
          borderWidth: 3,
          yAxisID: 'y-axis-2',
          // lineTension:0.5,
        }],
        options:{
          scales: {
                yAxes: [{
                  display: true,
                  position: 'left',
                  id: 'y-axis-1',
                  gridLines:{
                    display:false,
                  },
                  scaleLabel:{
                    display:false,
                    labelString:"次数",
                  }
                },
                {
                  display: true,
                  position: 'right',
                  id: 'y-axis-2',
                  scaleLabel:{
                    display:false,
                    labelString:"电量/KW·h",
                  }
                }],
              }
        }
      },
      //平均换电时间图数据
      batterySwappingTimeChartData:{
        labels:[],
        dataSets:[{
          label:'时长/min',
          data:[],
          borderColor: '#00B6E6',
          borderWidth: 3,
          yAxisID: 'y-axis-1',
        }],
        options:{
          scales: {
                yAxes: [{
                  display: true,
                  position: 'left',
                  id: 'y-axis-1',
                  scaleLabel:{
                    display:false,
                    labelString:"时长/min",
                  }
                }],
              }
        }
      },
      //换电区数据
      exchangeBatteryInfo:[
        {}
      ],
      //换电趋势图数据
      chargingTrendChartData:{
        labels:[],
        dataSets:[
          {
            label:'换电次数',
            data:[],
            borderColor: '#00B6E6',
            borderWidth: 2,
            yAxisID: 'y-axis-1',
          },
          {
            label:'单日最大换电次数',
            data:[],
            borderColor: '#1AD4B9',
            borderWidth: 2,
            yAxisID: 'y-axis-1',
          },
        ],
        options:{
          scales: {
                yAxes: [
                  {
                    display: true,
                    position: 'left',
                    id: 'y-axis-1',
                    scaleLabel:{
                      display:false,
                      labelString:"次",
                    }
                  },
                ],
              }
        }
      },
      //充电区数据
      batteryCompartmentStatus:[
        {},
        {},
        {},
        {}
      ],
      //充电区位置索引
      chargeStartIndex:0,
      chargeEndIndex:4,
      workStatusList:["空闲", "连接", "启动", "充电", "停止", "充满", "离线", "故障"],
      //换电区位置索引
      exchangeStartIndex:0,
      exchangeEndIndex:1,
      batteryIconSrc:[
      {
        icon:'charge-subtitle-red',
        imgSrc: require('@/assets/images/charge-subcard-red.png'),
      },
      {
        icon:'charge-subtitle-green',
        imgSrc: require('@/assets/images/charge-subcard-green.png'),
      },
      {
        icon:'charge-subtitle-green',
        imgSrc: require('@/assets/images/charge-subcard-green.png'),
      },
      {
        icon:'charge-subtitle-green',
        imgSrc: require('@/assets/images/charge-subcard-green.png'),
      },
      {
        icon:'charge-subtitle-green',
        imgSrc: require('@/assets/images/charge-subcard-green.png'),
      },
      {
        icon:'charge-subtitle-grey',
        imgSrc: require('@/assets/images/charge-subcard-grey.png'),
      },
      {
        icon:'charge-subtitle-grey',
        imgSrc: require('@/assets/images/charge-subcard-grey.png'),
      },
      {
        icon:'charge-subtitle-red',
        imgSrc: require('@/assets/images/charge-subcard-red.png'),
      },
    ],
      timer:null,
      voltageSrc:require("@/assets/images/circle-voltage.png"),
      currentSrc:require("@/assets/images/circle-current.png"),
      socSrc:require("@/assets/images/soc-alpha.png"),
      faultAndEnvmonitorData:{
        changeElectricityFaultCount: '',
        chargeMachineFaultCount: '',
        equipmentFaultStatistics: '',
        smokeSenseCurrentData: '',
      },
      chargingFaultData:0,
      exchangingFaultData:0,
      faultMonitoringChartData:{
        labels:[
            '机器人\t',
            '充电机\t',
            '水冷机组',
            '电池\t\t',
          ],
        data:[0, 0, 0, 0],
      },
      videoOptions: {
        autoplay: true,
        controls: true,
        mute: true,
        loop: true,
        width: '100%',
        height: '100%',
      },
      monitorDialogVisible:false,
      monitorStreams:[{
        stream:'',
        cameraName:'摄像头',
        id:1,
        time:'2024-03-29 11:28:49',
        videoSources: [{
          type: 'video/mp4', 
          src: require('@/assets/videos/test1.mp4') 
        }],
        // imgSrc:require('@/assets/images/monitor-stream-1.png'),

      },
      {
        stream:'',
        cameraName:'摄像头',
        id:2,
        time:'2024-03-29 11:28:49',
        videoSources: [{
          type: 'video/mp4', 
          src: require('@/assets/videos/test2.mp4') 
        }],
        // imgSrc:require('@/assets/images/monitor-stream-2.png'),
      },
      {
        stream:'',
        cameraName:'摄像头',
        id:3,
        time:'2024-03-29 11:28:49',
        videoSources: [{
          type: 'video/mp4', 
          src: require('@/assets/videos/test3.mp4') 
        }],
        // imgSrc:require('@/assets/images/monitor-stream-2.png'),
      },
      {
        stream:'',
        cameraName:'摄像头',
        id:4,
        time:'2024-03-29 11:28:49',
        videoSources: [{
          type: 'video/mp4', 
          src: require('@/assets/videos/test4.mp4') 
        }],
        // imgSrc:require('@/assets/images/monitor-stream-2.png'),
      },
    ]
    };
  },
  created() {
    this.$set = this.$set.bind(this);
  },
  mounted() {
    this.updateData();
    this.timer = setInterval(this.updateCardInfo, 60000);//随时间更新
  },
  beforeUnmount(){
    clearInterval(this.timer)
    console.log('清除了计时器');
  },

  computed: {
    treeInfo() {
      return this.$store.state.tree.sectionId;
    }
  },
  watch:{
    treeInfo(){
      console.log(this.treeInfo);
      // clearInterval(this.timer)
      this.updateData()
    }
  },
  methods: {
    handleLeftIndexButton(){
      if(this.chargeStartIndex>0){
        this.chargeEndIndex-=1
        this.chargeStartIndex-=1
      }
    },
    handleRightIndexButton(){
      if(this.chargeEndIndex<this.batteryCompartmentStatus.length){
        this.chargeEndIndex+=1
        this.chargeStartIndex+=1
      }
    },
    handleLeftIndexButtonE(){
      if(this.exchangeStartIndex>0){
        this.exchangeEndIndex-=1
        this.exchangeStartIndex-=1
      }
    },
    handleRightIndexButtonE(){
      if(this.exchangeEndIndex<this.exchangeBatteryInfo.length){
        this.exchangeEndIndex+=1
        this.exchangeStartIndex+=1
      }
    },
    onClickDlg(item){
      // for(var i=0; i< this.batteryCompartmentStatus.length; i++){
      //   if (this.batteryCompartmentStatus[i].id === item.id){
      //     this.batteryCompartmentStatus[i].dialogVisible = true
      //   }
      // }
      item.dialogVisible = true
      // console.log(this.batteryCompartmentStatus);
    },
    fixlabels(item){
      const total = item.data.reduce((a,b)=>a+b,0)
      var newChartData = {
        labels:[],
        data:item.data,
      }
      for (var i=0; i< item.labels.length;i++)
      {
        newChartData.labels.push(item.labels[i] + '\t\t' + '(' + item.data[i] + ' / ' + Math.round(item.data[i]/total*100) +'%)')
      }
      return newChartData
    },
    checkData(res){
      if(res){
        if (res.code != 0){
          console.log(res);
          this.$message.error(res.msg);
          return false
        }
        return true
      }else{
        return false
      }
    },
    switchData(){
      this.updateChargingChartData()
      this.updateExchangeBatteryTimeChartData()
      this.updateExchangeTrendChartData()
    },
    updateData(){
      console.log(this.treeInfo);
      if (this.treeInfo === '')
      {
        this.$message.error('请选择一个区域');
        return
      }
      // this.updateOverviewData()
      // this.updateChargingChartData()
      // this.updateExchangeBatteryTimeChartData()
      // this.updateExchangeTrendChartData()
      // this.updateCardInfo()
      // this.updateMonitorData()
      this.updateOverviewData()
      this.updateChargingChartData()
      this.updateExchangeBatteryTimeChartData()
      this.updateExchangeTrendChartData()
      this.updateCardInfo()
      
    },
    async updateOverviewData(){
      let chargeOverviewData = await this.$http.get(`/equip/power/chareOverview?sectionId=${this.treeInfo}`)
      // console.log(chargeOverviewData)
      if (this.checkData(chargeOverviewData)){
        this.chargingOverview = chargeOverviewData.result
        // console.log('测试',chargeOverviewData);
      }
      else{
        console.log('获取充电概况数据失败');
      }
    },
    async updateChargingChartData(){
      let chargingChartData = await this.$http.get(`/equip/power/chareOverviewLineChart?keyword=${this.selectedDate}&sectionId=${this.treeInfo}`)
      if(this.checkData(chargingChartData)){
        if(chargingChartData.result!={}){
          // console.log('充电概况',chargingChartData)
          chargingChartData.result.chargeCountMap.sort((a,b)=>{return a.date-b.date})
          chargingChartData.result.electricityChangeCountMap.sort((a,b)=>{return a.date-b.date})
          chargingChartData.result.chargeAmount.sort((a,b)=>{return a.date-b.date})
          this.chargingChartData.labels = chargingChartData.result.chargeCountMap.map((item)=>item.date)
          this.chargingChartData.dataSets[0].data = chargingChartData.result.chargeCountMap.map((item)=>item.num)
          this.chargingChartData.dataSets[1].data = chargingChartData.result.electricityChangeCountMap.map((item)=>item.num)
          this.chargingChartData.dataSets[2].data = chargingChartData.result.chargeAmount.map((item)=>item.amount)
        }
        
      }
      else{
        console.log('获取充电概况图表数据失败');
      }
      // console.log(chargingChartData);
    },
    async updateExchangeBatteryTimeChartData(){
      let exchangeBatteryTimeChartData = await this.$http.get(`/equip/power/avgChangeElectricityLineChart?keyword=${this.selectedDate}&sectionId=${this.treeInfo}`)
      if (this.checkData(exchangeBatteryTimeChartData)){
        exchangeBatteryTimeChartData.result.sort((a,b)=>{return a.date-b.date})

        // 获取标签与数据后更新以下两个变量的值
        this.batterySwappingTimeChartData.labels = exchangeBatteryTimeChartData.result.map((item)=>item.date)
        this.batterySwappingTimeChartData.dataSets[0].data = exchangeBatteryTimeChartData.result.map((item)=>item.minutes)
        // console.log(exchangeBatteryTimeChartData);
      }
      else{
        console.log('获取换电时间图表数据失败');
      }
    },
    async updateExchangeTrendChartData(){
      let exchangeTrendInfo = await this.$http.get(`/equip/power/changeElectricityTrendLineChart?keyword=${this.selectedDate}&sectionId=${this.treeInfo}`)
      if(this.checkData(exchangeTrendInfo)){
        
        exchangeTrendInfo.result.maxChangeElectTotal?.sort((a,b)=>{return a.date-b.date})
        exchangeTrendInfo.result.avgChangeElectTotal?.sort((a,b)=>{return a.date-b.date})
        let LABEL = exchangeTrendInfo.result.avgChangeElectTotal?.map((item)=>item.date)
        let DATA = [
          // exchangeTrendInfo.result.map((item)=>item.soc),
          exchangeTrendInfo.result.maxChangeElectTotal?.map((item)=>item.count),
          exchangeTrendInfo.result.avgChangeElectTotal?.map((item)=>item.count),
        ]
        // 获取标签与数据后更新以下变量的值
        this.chargingTrendChartData.labels = LABEL
        this.chargingTrendChartData.dataSets[0].data = DATA[0]
        this.chargingTrendChartData.dataSets[1].data = DATA[1]
        // console.log('换电趋势',DATA)
      }
      else{
        console.log('获取换电趋势图表数据失败');
      }

    },
    async updateChargeInfo(){
      let chargeStorageSpace = await this.$http.get(`/equip/power/chagerMachine?sectionId=${this.treeInfo}`)
      if (this.checkData(chargeStorageSpace)){
        // this.batteryCompartmentStatus = {...this.batteryCompartmentStatus,...chargeStorageSpace.result}
        let dataNum = 0
        for(let itemList of chargeStorageSpace.result.values()){
          // console.log(itemList);
          
          if (itemList.chargeRecord.length===0){
            let newData = {...itemList.record, dialogVisible: false}
            if (dataNum < this.batteryCompartmentStatus.length){
              for(let key in newData){
                if(key in this.batteryCompartmentStatus[dataNum]){
                  this.batteryCompartmentStatus[dataNum][key] = newData[key]
                }else{
                  this.$set(this.batteryCompartmentStatus[dataNum], key, newData[key])
                }
              }
            }else{
              this.$set(this.batteryCompartmentStatus, this.batteryCompartmentStatus.length, {
                ...newData
              });
            }
            dataNum+=1
            // console.log(this.batteryCompartmentStatus);
          }
          for (let item of itemList.chargeRecord.values()){
            let newData
            if (dataNum < this.batteryCompartmentStatus.length){
              if('dialogVisible' in this.batteryCompartmentStatus[dataNum]){
              newData = {...itemList.record, info:{...item}}
              }else{
                newData = {...itemList.record, dialogVisible: false, info:{...item}}
              }
              for(let key in newData){
                if(key in this.batteryCompartmentStatus[dataNum]){
                  this.batteryCompartmentStatus[dataNum][key] = newData[key]
                }else{
                  this.$set(this.batteryCompartmentStatus[dataNum], key, newData[key])
                }
              }
            }else{
              newData = {...itemList.record, dialogVisible: false, info:{...item}}
              this.$set(this.batteryCompartmentStatus, this.batteryCompartmentStatus.length, {
                ...newData
              });
            }
            // console.log(this.batteryCompartmentStatus);
            // console.log(item);
            dataNum+=1
            break//取第一条数据
          }
        }
        // console.log(this.batteryCompartmentStatus);

        // this.batteryCompartmentStatus = []
        // for(let i = 0; i < chargeNoList.length; i++){
        //   let chargeInfo = await this.getChargeInfo(chargeNoList[i])
        //   if (chargeInfo){
        //     let newData = {...chargeStorageSpace.result[i], dialogVisible: false, info:{...chargeInfo}}
        //     if (i < this.batteryCompartmentStatus.length){
        //       for(let key in newData){
        //         if(key in this.batteryCompartmentStatus[i]){
        //           this.batteryCompartmentStatus[i][key] = newData[key]
        //         }else{
        //           this.$set(this.batteryCompartmentStatus[i], key, newData[key])
        //         }
        //       }
        //     }else{
        //       this.$set(this.batteryCompartmentStatus, this.batteryCompartmentStatus.length, {
        //         ...newData
        //       });
        //     }
        //   }
        // }
        let popNum = this.batteryCompartmentStatus.length - dataNum
        // // console.log(popNum);
        for(let i = 0; i<popNum; i++){
          this.batteryCompartmentStatus.pop()
        }
        while (this.batteryCompartmentStatus.length<4){
          this.batteryCompartmentStatus.push({})
        }
      }
      else{
        console.log('获取充电仓位数据失败');
      }
      this.updateMonitorData()
    },
    async getChargeInfo(warehouseNo){
      let chargeInfo = await this.$http.get(`/equip/chargeRecord/getByWarehouseNo/${warehouseNo}`)
      if(this.checkData(chargeInfo)){
        return chargeInfo.result
      }
      else{
        console.log('获取充电仓位详细数据失败');
      }
      return null
    },
    async updateExchangeInfo(){
      let exchangeBatteryRecord = await this.$http.get(`/equip/power/changeElectricityArea?sectionId=${this.treeInfo}`)
      if(this.checkData(exchangeBatteryRecord)){
        let dataNum = 0
        for(let item of exchangeBatteryRecord.result.values()){
          let newData
          if(item.swapProcessList.length>0){
            newData = {...item.record, ...item.swapProcessList[item.swapProcessList.length-1]}//取第一条数据
          }
          else{
            newData = {...item.record, mode:0}
          }
          if(dataNum<this.exchangeBatteryInfo.length){
            for(let key in newData){
              if (key in this.exchangeBatteryInfo[dataNum]){
                this.exchangeBatteryInfo[dataNum][key] = newData[key]
              }else{
                this.$set(this.exchangeBatteryInfo[dataNum], key, newData[key])
              }
            }
          }else{
            this.$set(this.exchangeBatteryInfo, this.exchangeBatteryInfo.length, {
                ...newData
              });
          }
          dataNum+=1
          // break//取第一个换电仓数据
        }
        // console.log(this.exchangeBatteryInfo);
        if(dataNum===0){
          this.exchangeBatteryInfo.pop()
          this.exchangeBatteryInfo.push({})
        }
        // console.log('更新了exchangeInfo', this.exchangeBatteryInfo);
      }
      else{
        console.log('获取换电仓位数据失败');
      }
    },
    updateCardInfo(){
      this.updateChargeInfo()
      this.updateExchangeInfo()
    },
    async updateMonitorData(){
      let monitorData = await this.$http.get(`/equip/power/faultAndEnvmonitor?sectionId=${this.treeInfo}`)
      if (this.checkData(monitorData)){
        this.faultAndEnvmonitorData = monitorData.result
        let countList = monitorData.result.equipmentFaultStatistics.map((item)=>item.totalCount)
        let labelList = monitorData.result.equipmentFaultStatistics.map((item)=>item.devName)
        this.faultMonitoringChartData = this.fixlabels({labels:labelList,data:countList})
        for(let i = 0; i < this.faultAndEnvmonitorData.smokeSenseCurrentData.length; i++){
          for(let j=0; j<this.batteryCompartmentStatus.length;j++){
            // console.log(this.batteryCompartmentStatus[j].devId);
            if(monitorData.result.smokeSenseCurrentData[i].machineDevId == this.batteryCompartmentStatus[j].devId){
              this.batteryCompartmentStatus[j].temperature = this.faultAndEnvmonitorData.smokeSenseCurrentData[i].temperature
              this.batteryCompartmentStatus[j].smokeConcentration = this.faultAndEnvmonitorData.smokeSenseCurrentData[i].smokeVal
            }
          }
          
        }
      }
      else{
        console.log('获取环境监控数据失败');
      }

      // console.log(this.faultAndEnvmonitorData);
    },
    async updateMonitorCardData(){
      let monitorCard = await this.$http.get(`/equip/smokeSenseCurrentData/list?sectionId=${this.treeInfo}`)
      monitorCard.result
    },
  },
};
</script>

<style lang="scss" scoped>
#Chdzgl {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width:100%;
  height: 100%;


  .container-left {
    // width: 450px;
    width:28%;
    min-width: 370px;
    .card-preview {
      min-height: 420px;

      // height: 525px;
      height: 63%;
      margin-bottom: 10px;
      
      .charge-card{
        height: 23%;
        display: flex;
        width: 100%;
        margin-bottom: 2%;
        
        border: 2px solid #D0DDFB;
        .charge-card-title{
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
          width: 9%;
          writing-mode: vertical-lr;
          font-family: '思源黑体 CN Medium', '思源黑体 CN Regular', '思源黑体 CN', sans-serif;
          font-weight: 500;
          font-style: normal;
          font-size: 16px;
          @media (max-width: 1500px){
            font-size: 14px;
          }
          color: #FFFFFF;
          
          letter-spacing: 0.3vh;
          background-image: linear-gradient(45deg, #9BCFF5, #6CDBE7);

        }
        .charge-card-content{
          width: 91%;
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          justify-content: space-around;
          background-image: linear-gradient(to right, #F5F5F5, #C9E5FF);
          .charge-card-item{
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            // max-width: 90px;
            .charge-card-item-img{
              // max-height: 100%;
              // max-width: 80px;
              display: flex;
              justify-content: center;
              align-items: center;
              width: 80%;
              height: 100%;
              align-items: center;
              .img{
                width: 100%; /* 宽度自动，根据高度和长宽比计算 */
                height: 80%; /* 高度自动，根据宽度和长宽比计算 */
                object-fit: contain; /* 保持长宽比，确保整个图片都在容器内 */
              }
            }
            .charge-card-item-value{
              display: flex;
              justify-content: space-around;
              margin-bottom: 1vh;
              .value{
                font-size: 32px;
                @media (max-width: 1500px){
                  font-size: 24px;
                }
                font-family: '优设标题黑', sans-serif;
                font-weight: 400;
                font-style: normal;
                color: #3A3F5D;
                text-align: left;
                line-height: 32px;
                margin-right: 4%;
              }
              .quantifier{
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                
                .text{
                  font-size: 16px;
                  font-family: '优设标题黑', sans-serif;
                  font-weight: 400;
                  font-style: normal;
                  color: #3A3F5D;
                  white-space: nowrap;
                  @media (max-width: 1500px){
                    font-size: 14px;
                  }
                }
              }
            }
            .charge-card-item-title{
              white-space: nowrap;
              overflow: hidden;
              font-family: '微软雅黑', sans-serif;
              font-weight: 400;
              font-style: normal;
              font-size: 16px;
              @media (max-width: 1500px){
                font-size: 14px;
              }
              color: #3A3F5D;
            }
          }
        }
      }
      .charge-chart{
        height: 50%;
        position: relative;
        .switch-data{
          position: absolute;
          width:15%;
          height:15%;
          left:85%;
          top:2%;
          .selector{
            border: 1px solid #d0d0d0;
            border-radius: 5px;
            padding:3%;
          }
        }
      }
    }
    .card-type{
      // height: 295px;
      height: 35%;
      min-height: 225px;
      position: relative;
        .switch-data{
          position: absolute;
          width:15%;
          height:15%;
          left:85%;
          top:20%;
          .selector{
            border: 1px solid #d0d0d0;
            border-radius: 5px;
            padding:3%;
          }
        }
    }
  }

  .container-center {
    // height: 720px;
    width: 44%;
    // flex: 1;
    margin: 0 10px;
    min-width: 525px;

    .card-preview {
      // height: 525px;
      min-height: 420px;

      height: 63%;
      margin-bottom: 10px;
      width:100%;
      .vertical-subcard-container{
        white-space: nowrap; /* 使内容不换行 */
        // overflow-x: auto; /* 启用水平滚动 */
        // overflow-y: hidden;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        height:57%;
        position: relative;
        .set-index-button{
          margin-top: 0.5%;
          left: 0.5%;
          top:50%;
          width: 40px;
          height: 40px;
          display: flex;
          justify-content: center;
          font-size: large;
          color: #ccc;
          text-align: center;
          align-items: center;
          position: absolute;
          cursor: pointer;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          border-radius: 50%;
          font-size: 30px;
          font-weight: bolder;
        }
        .set-index-button.hovered{
          background-color: #aaa;
          
        }
        .vertical-subcard{
          // min-width: 160px;
          width: 24%;
          height: 98%;
          .subcard-content{
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            height:100%;
            // min-width: 170px;
            cursor: pointer;
            .subcard-img{
              margin-bottom: 5%;
              margin-top: 5%;
              height:50%
            }
            .split-line{
              width: 70%;
              height: 2%;
              background-color: #00A0EA;
            }
            .text-content{
              display: flex;
              background-color: #E6ECF9;
              width:95%;
              height:13%;
              font-family: '思源黑体 CN', sans-serif;
              font-weight: 400;
              font-style: normal;
              font-size: 14px;
              color: #3A3F5D;
              white-space: nowrap;
              overflow: hidden;
              text-align: left;
              margin: 1%;
              padding-left: 5%;
              align-items: center;
              @media (max-width: 1500px){
                font-size: 11px;
              }
            }

          }

          .batteryDialog{
            // min-height: 200px;
            
            .title{
              font-size: 18px;
            }
            .dailog-container{
              width:100%;
              display: flex;
              flex-direction: row;
              min-width:700px;
              .left-container{
                display: flex;
                flex-direction: column;
                width: 57%;
                min-width:400px;
                // margin:5%;
                // min-height: 200px;
                .pointer-container{
                  display: flex;
                  justify-content: space-around;
                  align-items: center;
                  flex-direction: row;
                  width:100%;
                  .pointer{
                    width: 48%;
                    margin: 2%;
                  }
                }
                .progress-bar-container{
                  width:100%;
                  display: flex;
                  flex-direction: column;
                  justify-content: space-around;
                  .text-container{
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    width:96%;
                    margin: 0 2% 0 2%;
                    border-bottom: 5px solid #00A8E9;
                    padding-bottom: 2%;
                    .text{
                      font-family: '优设标题黑', sans-serif;
                      font-weight: 400;
                      font-style: normal;
                      font-size: 18px;
                      text-align: left;
                      letter-spacing: normal;
                      color: #333333;
                      display: flex;
                      float: left;
                    }
                    .icon-container{
                      display: flex;
                      flex-direction: row;
                      align-items: center;
                      .text{
                        font-family: '优设标题黑', sans-serif;
                        font-weight: 400;
                        font-style: normal;
                        font-size: 18px;
                        text-align: left;
                        letter-spacing: normal;
                        color: #333333;

                      }
                      
                    }
                  }
                  .progress-bar{
                    margin:2%;
                    width:96%;
                    height:20px;
                    background-color: #D0D3D8;
                    border-radius: 5px;
                  }
                  .progress-bar::-webkit-progress-bar {
                    background-color: #D0D3D8;
                    border-radius: 5px;
                  }
                  .progress-bar::-webkit-progress-value {
                    background-color: #26D9B7;
                    border-radius: 5px;
                  }
                  .progress-bar::progress-bar {
                    background-color: #26D9B7;
                    border-radius: 5px;
                  }
                }
              }
              .right-container{
                display: flex;
                flex-direction: column;
                width:43%;
                min-width:300px;
                .title{
                  margin-left:2%;
                  margin-bottom: 2%;
                  font-family: '思源黑体 CN Medium', '思源黑体 CN Regular', '思源黑体 CN', sans-serif;
                  font-weight: 600;
                  font-style: normal;
                  font-size: 16px;
                  color: #3A3F5D;
                  white-space: nowrap;
                  text-transform: none;
                }
                .text-container{
                  display:flex;
                  flex-direction: column;
                  justify-content: space-around;
                  .text{
                    font-family: '思源黑体 CN Medium', '思源黑体 CN Regular', '思源黑体 CN', sans-serif;
                    font-weight: 500;
                    font-style: normal;
                    font-size: 16px;
                    color: #3A3F5D;
                    white-space: nowrap;
                    text-transform: none;
                    margin: 2%;
                  }
                }
              }
            }
          }
        }
      }
      .horizontal-subcard-container{
        display: flex;
        height: 40%;
        margin-top: 1%;
        width: 100%;
        // flex-direction: row;
        overflow: hidden;
        white-space: nowrap; /* 使内容不换行 */
        // overflow-x: auto; /* 启用水平滚动 */
        // overflow-y: hidden;
        position: relative;
        .set-index-button{
          margin-top: 0.5%;
          left: 0.5%;
          top:50%;
          width: 40px;
          height: 40px;
          display: flex;
          justify-content: center;
          font-size: large;
          color: #ccc;
          text-align: center;
          align-items: center;
          position: absolute;
          cursor: pointer;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          border-radius: 50%;
          font-size: 30px;
          font-weight: bolder;
        }
        .set-index-button.hovered{
          background-color: #aaa;
          
        }
        .horizontal-subcard{
            width: 100%;
            height: 98%;
            // min-width: 600px;
          .horizontal-subcard-content{
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-around;
            flex-direction: column;
            .text-content{
              font-family: '微软雅黑', sans-serif;
              font-weight: 400;
              font-style: normal;
              color: #3A3F5D;
              line-height: 22px;
              font-size: 14px;
              @media (max-width: 1500px){
                font-size: 12px;
              }
              text-align: left;
              margin-left: 2.5%;
            }
            .up-content, .down-content{
              width: 100%;
              height: 30%;
              display: flex;
              flex-direction: row;
              justify-content: space-around;
              .rectangle{
                background: linear-gradient(to right, #76B4F3, #6CD4E8);
                width:25%;
                height:100%;
                display: flex;
                justify-content: center;
                align-items: center;
                font-family: '思源黑体 CN', sans-serif;
                font-weight: 400;
                font-style: normal;
                font-size: 20px;
                @media (max-width: 1500px){
                  font-size: 15px;
                }
                color: #eee;
                line-height: 25px;
                border: 1px solid #76B4F3;
                text-wrap: nowrap;

              }
            }
            
          }
        }

      }
    }
    .card-type{
      // height: 295px;
      height: 35%;
      min-height: 225px;
      position: relative;
        .switch-data{
          position: absolute;
          width:15%;
          height:15%;
          left:85%;
          top:20%;
          .selector{
            border: 1px solid #d0d0d0;
            border-radius: 5px;
            padding:3%;
          }
        }
    }
  }

  .container-right {
    // width: 450px;
    width: 28%;
    min-width: 370px;
    .card-preview {
      // height: 525px;
      min-height: 420px;

      height: 63%;
      // width:100%;
      // display: flex;
      margin-bottom: 10px;
      white-space: nowrap; /* 使内容不换行 */
      overflow: hidden;
      .subcard-container{
        height:30%;
        width: 100%;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-around;
        
        .subcard{
          height: 100%;
          width:48%;
          border: 1px solid transparent;
          border-image: linear-gradient(to left top, #B3C8FF, white) 2 4;
          display: flex;
          flex-direction: column;
          flex-wrap: nowrap;
          justify-content: space-around;
          background-image: linear-gradient(to bottom, #F3F6FF, white);
          .subcard-title{
            font-family: '思源黑体 CN', sans-serif;
            font-weight: 400;
            font-style: normal;
            font-size: 16px;
            @media (max-width: 1500px){
                font-size: 14px;
              }
            display: flex;
            justify-content: center;
            align-items: center;
            height: 20%;

          }
          .subcard-content{
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: center;
            height: 20%;
            .value-content{
              font-family: 'Arial Normal', 'Arial', sans-serif;
              font-weight: 400;
              font-style: normal;
              font-size: 22px;
              @media (max-width: 1500px){
                font-size: 20px;
              }
              letter-spacing: normal;
              color: #333333;
              // vertical-align: none;
              text-align: center;
              line-height: normal;
              text-transform: none;
              display: flex;
              align-items: center;
              
            }
            .quantifier{
              display: flex;
              flex-direction: column;
              justify-content: flex-end;
              .text{
                font-size: 16px;
                @media (max-width: 1500px){
                  font-size: 14px;
                }
                font-family: '优设标题黑', sans-serif;
                font-weight: 400;
                font-style: normal;
                color: #3A3F5D;
              }
            }

          }
          .subcard-img{
            display: flex;
            justify-content: center;
            align-items: center;
            // width: 100%;
            height:50%;
            .img{
              height: 100%;
            }
          }
        }
      }
      .chart-container{
        display: flex;
        height:45%;
        width:100%;
        align-items: center;
        justify-content: center;
        // margin-top: 2%;
        // margin-bottom: 2%;
        // border-bottom: 1px solid #B1B1E5;
        .pie-chart{
          // display: flex;
          // width: 100%;
          height:96%;
          border-bottom: 1px solid #B1B1E5;
          padding-bottom: 2%;
          padding-top: 2%;
          // min-height: 156px;
          width:380px;
        }
      }
      .monitor-container{
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: center;
        height: 25%;
        width: 100%;
        border: 1px solid #B1B1E5;
        border-radius: 10px;
        overflow: hidden; 
        cursor: pointer;
        .smoke-monitor{
          height: 100%;
          width:50%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .title{
            font-family: '微软雅黑', sans-serif;
            font-weight: 400;
            font-style: normal;
            font-size: 16px;
            @media (max-width: 1500px){
                font-size: 14px;
              }
            color: #FFFFFF;
            background-color: #31DDB3;
            width:100%;
            height: 35%;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .content-container{
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            height:40%;
            padding:3%;
            .value{
              font-family: '优设标题黑', sans-serif;
              font-weight: 400;
              font-style: normal;
              font-size: 26px;
              @media (max-width: 1500px){
                font-size: 23px;
              }
              height: 100%;
              display: flex;
              align-items: center;
            }
            .quantifier{
              height:100%;
              display: flex;
              flex-direction: column;
              justify-content: flex-end;
              // padding-bottom: 13%;
              padding-left: 5%;
              .text{
                font-family: '优设标题黑', sans-serif;
                font-weight: 400; 
                font-style: normal;
                font-size: 16px;
                @media (max-width: 1500px){
                  font-size: 14px;
                }
              }
            }
          }
          .status-container{
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            height: 21%;
            margin-bottom: 4%;
            .status{
              font-family: '微软雅黑', sans-serif;
              font-weight: 400;
              font-style: normal;
              font-size: 16px;
              @media (max-width: 1500px){
                font-size: 14px;
              }
              color: rgba(0, 0, 0, 0.647058823529412);
              line-height: 22px;
            }
          }
        }
        .temperature-monitor{
          height: 100%;
          width:50%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .title{
            font-family: '微软雅黑', sans-serif;
            font-weight: 400;
            font-style: normal;
            font-size: 16px;
            @media (max-width: 1500px){
                font-size: 14px;
              }
            color: #FFFFFF;
            background-color: #009EEB;
            width:100%;
            height: 35%;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .content-container{
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            height:40%;
            padding:3%;
            .value{
              font-family: '优设标题黑', sans-serif;
              font-weight: 400;
              font-style: normal;
              font-size: 26px;
              @media (max-width: 1500px){
                font-size: 23px;
              }
              height: 100%;
              display: flex;
              align-items: center;
            }
            .quantifier{
              height:100%;
              display: flex;
              flex-direction: column;
              justify-content: flex-end;
              // padding-bottom: 13%;
              padding-left: 5%;
              .text{
                font-family: '优设标题黑', sans-serif;
                font-weight: 400; 
                font-style: normal;
                font-size: 16px;
                @media (max-width: 1500px){
                  font-size: 14px;
                }
              }
            }
          }
          .status-container{
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            height: 21%;
            margin-bottom: 4%;
            .status{
              font-family: '微软雅黑', sans-serif;
              font-weight: 400;
              font-style: normal;
              font-size: 16px;
              color: rgba(0, 0, 0, 0.647058823529412);
              line-height: 22px;
            }
          }

        }
        
      }
      .monitor-dialog{
        width: 100%;
        // min-width: 800px;
        .dialog-container{
          display: flex;
          flex-direction: column;
          width: 70vw;
          height:42vw;
          min-width: 947px;
          min-height:580px;
          // width: 800px;
          // height: 800px;
          .down-container{
            width: 100%;
            height:100%;
            display: flex;
            flex-direction: row;
            .battery-container{
              
              overflow-y: auto;
              overflow-x: hidden;
              display: flex;
              flex-wrap: wrap;
              // flex-direction: column;
              // flex-direction: row;
              width: 50%;
              height: 100%;
              .battery-subcard{
                width:48%;
                height: 48%;
                margin: 1%;
                .subcard-content{
                  height:100;
                  width:100%;
                  display: flex;
                  flex-direction: column;
                  justify-content: space-around;
                  align-items: center;
                  .soc-pointer{
                    border:none;
                    background-image: none;
                    width:80%;
                    padding:1%;
                  }
                  .content-container{
                    display: flex;
                    flex-direction: row;
                    justify-content: space-around;
                    align-items: center;
                    width:100%;
                    .content-item{
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      .text{
                        font-family: '思源黑体 CN', sans-serif;
                        font-weight: 400;
                        font-style: normal;
                        font-size: 14px;
                        color: #3A3F5D;
                      }
                      .value{
                        font-family: '优设标题黑', sans-serif;
                        font-weight: 400;
                        font-style: normal;
                        font-size: 16px;
                        color: #3A3F5D;
                      }
                    }

                  }
                  .split-line{
                    margin-top: 3%;
                    width:90%;
                    height:5px;
                    background-image: linear-gradient(to right, #0098EC, #00C9E2);
                  }
                }

              }
            }
            .moniter-container{
              display: flex;
              flex-wrap: wrap;
              width: 50%;
              height: 100%;
              .monitor-subcard{
                width:48%;
                height: 48%;
                margin: 1%;
                .subcard-content{
                  width:100%;
                  height:100%;
                  overflow: hidden;
                  // background-image: linear-gradient(to right, #0099ec15, #00c8e228);
                }
              }
            }
          }
        }
      }
      
    }
    .card-type{
      // height: 295px;
      min-height: 225px;
      height: 35%;
      .monitor-container{
        width:100%;
        height:100%;
        .scroll-container{
          height:100%;
          width:100%;
          white-space: nowrap; /* 使内容不换行 */
          overflow-x: auto; /* 启用水平滚动 */
          overflow-y: hidden;
          .monitor-subcard{
            width:50%;
            height:100%;
            display: inline-block; /* 使项目水平排列 */
            .subcard-content{
              width:100%;
              height:100%;
              overflow: hidden;
              // background-image: linear-gradient(to right, #0099ec15, #00c8e228);
            }
          }
        }
      }
    }
  }
}
</style>
