<template>
  <div class="device-container">
    <div class="device-one" >
      
      <div class="device-item device-top">
        <div class="item-left" @click="handleClick('dev')">

          <!-- <div class="device-info">
            <div class="device-title-info" >
              <div class="device-title">接入数量</div>
              <div class="device-num">{{ (deviceInfo.accessNumber ||0) + (deviceInfoPlatform.accessNumber || 0)}}</div>
            </div>

            <div class="text-info">
              <div class="text-item">
                <span class="text-title">感知设备接入：</span>
                <span class="text-num">{{ deviceInfo.accessNumber || 0 }}</span>
              </div>
              <div class="text-item">
                <span class="text-title">系统传输接入：</span>
                <span class="text-num">{{ deviceInfoPlatform.accessNumber || 0 }}</span>
              </div>
              
            </div>
          </div> -->
          <div class="device-info">
            <div class="device-title-info">
              <div class="device-title">感知设备接入数量</div>
              <div class="device-num">{{ deviceInfo.collectIdAccessNumber || 0 }}</div>
            </div>
          </div>
          <div class="device-img">
            <img
              src="@/assets/images/frame-green.png"
              alt="感知设备接入数量"
              class="img-bg"
            />
          </div>
        </div>
        <el-dialog :visible.sync="deviceNumberDialogVisible" title="感知设备接入设备详情" width="85%" class="details-dialog">
          <PerceptionTable type="dev" :isclosed="!deviceNumberDialogVisible"></PerceptionTable>
        </el-dialog>

        <div class="item-right" @click="handleClick('plt')">
          <!-- <div class="device-info">
            <div class="device-title-info">
              <div class="device-title">未接入数量</div>
              <div class="device-num">{{ (deviceInfo.NotAccessNumber || 0) + (deviceInfoPlatform.NotAccessNumber || 0)}}</div>
            </div>
            <div class="text-info">
              <div class="text-item">
                <span class="text-title">感知设备未接入：</span>
                <span class="text-num">{{ deviceInfo.NotAccessNumber || 0 }}</span>
              </div>
              <div class="text-item">
                <span class="text-title">系统传输未接入：</span>
                <span class="text-num">{{ deviceInfoPlatform.NotAccessNumber || 0 }}</span>
              </div>
              
            </div>
          </div> -->
          <div class="device-info">
            <div class="device-title-info">
              <div class="device-title">平台传输接入数量</div>
              <div class="device-num">{{ deviceInfo.accessNumber || 0 }}</div>
            </div>
          </div>
          <div class="device-img">
            <img
              src="@/assets/images/frame-orange.png"
              alt="平台传输接入数量"
              class="img-bg"
            />
          </div>
        </div>
        <el-dialog :visible.sync="platformNumberDialogVisible" title="平台传输接入设备详情" width="85%" class="details-dialog">
          <PerceptionTable type="plt" :isclosed="!platformNumberDialogVisible"></PerceptionTable>
        </el-dialog>
      </div>
      <div class="device-item device-bt">
        
        <div class="item-left" @click="handleClick('out')">
          <div class="device-info">
            <div class="device-title-info">
              <div class="device-title">离线数量</div>
              <div class="device-num">{{ deviceInfo.offlineNumber || 0 }}</div>
            </div>

          </div>
          <div class="device-img">
            <img
              src="@/assets/images/frame-purple.png"
              alt="离线数量"
              class="img-bg"
            />
          </div>
        </div>
        <el-dialog :visible.sync="offlineNumberDialogVisible" title="离线设备详情" width="85%" class="details-dialog">
          <PerceptionTable type="out" :isclosed="!offlineNumberDialogVisible"></PerceptionTable>
        </el-dialog>
        <div class="item-right" @click="handleClick('in')">
          <div class="device-info">
            <div class="device-title-info">
              <div class="device-title">在线数量</div>
              <div class="device-num">{{ (deviceInfo.onlineNumber||0) }}</div>
            </div>
            <!-- <div class="text-info">
              <div class="text-item">
                <span class="text-title">感知设备在线：</span>
                <span class="text-num">{{ deviceInfo.onlineNumber || 0 }}</span>
              </div>
              <div class="text-item">
                <span class="text-title">平台数据在线：</span>
                <span class="text-num">{{ deviceInfoPlatform.accessNumber || 0 }}</span>
              </div>
              
            </div> -->
          </div>
          <div class="device-img">
            <img
              src="@/assets/images/frame-blue.png"
              alt="工作数量"
              class="img-bg"
            />
          </div>
        </div>
        <el-dialog :visible.sync="onlineNumberDialogVisible" title="在线设备详情" width="85%" class="details-dialog">
          <PerceptionTable type="in" :isclosed="!onlineNumberDialogVisible"></PerceptionTable>
        </el-dialog>
      </div>
    </div>
    <!-- <hr/> -->
    <!-- <div class="device-two">
      
      <div class="center-top">
        <div class="center-item item-in">
          <div class="item-title">接入数量</div>
        </div>
        <div class="center-item item-no">
          <div class="item-title">未接入数量</div>
        </div>
      </div>
      <div class="center-bottom">
        <div class="bottom-item">
          <span class="item-num">{{ deviceInfoPlatform.accessNumber || 0 }}</span>
          <span>个</span>
        </div>
        <div class="bottom-item">
          <span class="item-num">{{ deviceInfoPlatform.NotAccessNumber || 0 }}</span>
          <span>个</span>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>

import PerceptionTable from "../Table/PerceptionTable.vue"
export default {
  name: "PerceptionDevice",
  props: ['selectVal'],
  components: {
    PerceptionTable,
  },
  data() {
    return {
      deviceInfo: {},
      deviceInfoPlatform: {},
      accessNumberDialogVisible: false,
      onlineNumberDialogVisible: false,
      offlineNumberDialogVisible: false,
      notAccessNumberDialogVisible: false,
      platformNumberDialogVisible: false,
      deviceNumberDialogVisible: false,
    };
  },
  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.getPerceptionDevice();
      },
      deep: true,
      immediate: true,
    },
    // "selectVal":{
    //   handler: function () {
    //     this.getPerceptionDevice();
    //   }
    // }
  },
  mounted() {},

  methods: {
    async getPerceptionDevice() {
      let res = await this.$http.get(
        `/equip/build/senseDevice?sectionId=${this.$store.state.tree.sectionId||''}`
      );
      this.deviceInfo = res?.result || {};
      // let platformRes = await this.$http.get(
      //   `/equip/build/senseDevice?keyword=1&sectionId=${this.$store.state.tree.sectionId||''}`
      // );
      // this.deviceInfoPlatform = platformRes?.result || {};
    },

    handleClick(val) {
      // this.$router.push({ path: "/devices/access", query: { type: val } });
      if (val === 'all'){
        this.accessNumberDialogVisible = true;
      }
      else if (val === 'in'){
        this.onlineNumberDialogVisible = true;
      }
      else if (val === 'out'){
        this.offlineNumberDialogVisible = true;
      }else if(val === 'not'){
        this.notAccessNumberDialogVisible = true;
      }else if (val === 'plt'){
        this.platformNumberDialogVisible = true;
      }else if (val === 'dev'){
        this.deviceNumberDialogVisible = true;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.device-container {
  height: 100%;
  width: 100%;
  .device-one {
    // width: 350px;
    height: 100%;
    width: 100%;
    .device-item {
      width: 100%;
      height: 50%;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;

      .item-left {
        width: 50%;
        // flex: 1;
        padding: 4px;
        border-right: 6px solid #22c5e3;
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
      }

      .item-right {
        padding: 4px;
        width: 50%;
        // flex: 1;
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        cursor: pointer;
      }

      .device-info {
        width: 100%;
        // flex: 1;
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        justify-content: space-between;
        .device-title-info{
          display: flex;
          flex-direction: row;
          .device-title {
          width: 50%;
          height: 100%;
          line-height: 30px;
          text-align: left;
          font-family: "微软雅黑 Bold", "微软雅黑 Regular", "微软雅黑",
            sans-serif;
          font-weight: 700;
          font-style: normal;
          font-size: 16px;
          color: #3a3f5d;
          // 不换行
          white-space: nowrap;

        }

        .device-num {
          width: 50%;
          height: 100%;
          line-height: 30px;
          font-family: "微软雅黑", sans-serif;
          font-weight: 400;
          font-style: normal;
          font-size: 24px;
          color: #3d7af0;
          text-align: right;
          // padding-right: 100px;
        }
        }
        .text-info{
          display: flex;
          flex-direction: column;
          .text-item{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            .text-title {
              width: 70%;
              height: 20px;
              line-height: 20px;
              text-align: left;
              font-family: "微软雅黑 Bold", "微软雅黑 Regular", "微软雅黑",
                sans-serif;
              font-weight: 700;
              margin-left: 5px;
            }
            .text-num{
              height: 20px;
              line-height: 20px;
              text-align: left;
              font-family: "微软雅黑 Bold", "微软雅黑 Regular", "微软雅黑",
                sans-serif;
              font-weight: 700;
              margin-right: 5px;
            }
          }

        }

      }

      .device-img {
        width: 30%;
        height: 100%;
        display: flex;
        flex-direction: row;
        justify-content: end;
        .img-bg {
          width: 100%;
          height: 100%;
          background-size: contain;
          
        }
      }
    }

    .device-top {
      border-bottom: 6px solid #22c5e3;

      .item-left {
        cursor: pointer;
      }
      .item-right {
        cursor: pointer;
      }
    }

    .device-bt {
      .item-left {
        cursor: pointer;
      }


    }
  }
  // .device-item-title{
  //     margin-top: 4px;
  //     margin-bottom: 5px;
  //     width: 100%;
  //     height: 6%;
  //     line-height: 6%;
  //     // text-align: center;
  //     font-size: 16px;
  //     color: #000000;
  //     // font-weight: 700;
  //     font-family: "微软雅黑 Bold", "微软雅黑 Regular", "微软雅黑",
  //     sans-serif;
  //   }
  // .device-two {
  //   border: 1px solid #b3caff;
  //   border-radius: 10px;

  //   .center-top {
  //     width: 100%;
  //     height: 30px;
  //     display: flex;
  //     flex-direction: row;
  //     flex-wrap: nowrap;

  //     .center-item {
  //       width: 50%;
  //       height: 100%;
  //       position: relative;

  //       .item-title {
  //         width: 100%;
  //         height: 100%;
  //         position: absolute;
  //         left: 0px;
  //         top: 0px;
  //         line-height: 30px;
  //         text-align: center;
  //         font-size: 18px;
  //         font-weight: 400;
  //         color: #fff;
  //         font-family: "微软雅黑", sans-serif;
  //         font-style: normal;
  //       }
  //     }

  //     .item-in{
  //       background: linear-gradient(45deg, #29CDBD, #44DEB2);
  //       border-top-left-radius: 10px;
  //     }

  //     .item-no{
  //       background: linear-gradient(45deg, #149BEB, #24CAE1);
  //       border-top-right-radius: 10px;
  //     }
  //   }

  //   .center-bottom {
  //     width: 100%;
  //     height: 50px;
  //     display: flex;
  //     flex-direction: row;
  //     flex-wrap: nowrap;

  //     .bottom-item {
  //       width: 50%;
  //       height: 100%;
  //       text-align: center;
  //       line-height: 50px;
  //       font-family: "优设标题黑", sans-serif;
  //       font-weight: 400;
  //       font-size: 16px;
        

  //       .item-num {
  //         font-size: 30px;
  //       }
  //     }
  //   }
  // }
}
</style>
