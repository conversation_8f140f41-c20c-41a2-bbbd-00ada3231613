<template>
  <el-dialog
    :title="titleStr"
    :visible.sync="dialogVisible"
    width="55%"
    :before-close="handleClose"
    class="popup-dialog"
  >
    <div class="dialog-part">
      <div class="item">
        <el-form ref="form" :model="formData" label-width="100px">
          <el-form-item label="维修单号">
            <el-input class="wide-input" 
              v-model="formData.repairNo"
              placeholder="请输入维修单号"
            ></el-input>
          </el-form-item>
          <el-form-item label="设备编号">
            <el-input class="wide-input"
              v-model="formData.equipmentNo"
              placeholder="请输入设备编号"
            ></el-input>
          </el-form-item>
          <el-form-item label="设备名称">
            <el-input class="wide-input"
              v-model="formData.equipmentName"
              placeholder="请输入设备名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="故障简介">
            <el-input class="wide-input"
              v-model="formData.faultIntro"
              placeholder="请输入故障简介"
            ></el-input>
          </el-form-item>
          <el-form-item label="报修原因">
            <el-input class="wide-input"
              v-model="formData.repairReason"
              placeholder="请输入报修原因"
            ></el-input>
          </el-form-item>
          <el-form-item label="故障原因">
            <el-input class="wide-input"
              v-model="formData.faultReason"
              placeholder="请输入故障原因"
            ></el-input>
          </el-form-item>  
        </el-form>
      </div>
      <div class="item item-right">
        <el-form ref="form" :model="formData" label-width="100px">
          <el-form-item label="维修起始时间">
            <el-date-picker class="wide-input"
              v-model="formData.startRepair"
              type="date"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="维修结束时间">
            <el-date-picker class="wide-input"
              v-model="formData.endRepair"
              type="date"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="报修人">
            <el-input class="wide-input"
              v-model="formData.submitPerson"
              placeholder="请输入报修人"
            ></el-input>
          </el-form-item>
          <el-form-item label="维修人">
            <el-input class="wide-input"
              v-model="formData.repairPerson"
              placeholder="请输入维修人"
            ></el-input>
          </el-form-item>
          <el-form-item label="工单状态">
            <el-input class="wide-input"
              v-model="formData.status"
              placeholder="请输入工单状态"
            ></el-input>
          </el-form-item>

          
        </el-form>
      </div>
      
    </div>
    <el-form ref="form" :model="formData" label-width="100px">
      <el-form-item label="上传附件">
        <el-upload
          :limit="1"
          :action="upload_url"
          :on-remove="handleRemoved"
          :file-list="fileList1"
          :on-success="handleSucceed"
        >
          <el-button size="small" type="primary">点击上传</el-button>
          <div slot="tip" class="el-upload__tip">
            支持扩展名：.xls,.xlsx,.doc,.docx,.pdf,.jpg,.jpeg,.png,.git
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="备注说明">
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入内容"
          v-model="formData.remark"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click.native.prevent="submit">保存</el-button>
      <el-button @click="onClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import FormUtils from "@/mixins/formUtils";
export default {
  name: "AddLedgerDialog",
  mixins: [FormUtils],
  props: ["curData", "dialogVisible"],
  components: {},
  data() {
    return {
      formData: {},
      fileList: [],
      fileList1: [],
      deviceTypeData: [],
      dynamicTypeData: [
        {
          label: "纯电",
          value: "纯电",
        },
        {
          label: "混动",
          value: "混动",
        },
        {
          label: "拖电",
          value: "拖电",
        },
        {
          label: "氢燃料",
          value: "氢燃料",
        },
      ],
      energyTypeData: [
        {
          label: "充电",
          value: "充电",
        },
        {
          label: "换电",
          value: "换电",
        },
      ],
    };
  },
  computed: {
    titleStr() {
      return this.curData.type == "1" ? "新增设备台账" : "编辑设备台账";
    },
  },

  created() {
    let arr = this.curData.data.annxArray
      ? JSON.parse(this.curData.data.annxArray)
      : [];
    if (this.curData.type != "1" && arr) {
      this.fileList = arr.map((item) => {
        return { id: item.id, name: item.name };
      });
      this.fileList1 = arr.map((item) => {
        return {
          name: item.name,
          url: `/equip/fileinfo/download?id=${item.id}`,
        };
      });
    }
  },

  mounted() {
    this.formData = this.curData.data;
    this.getDeviceType();
  },

  methods: {
    onClose() {
      this.$emit("onClose");
    },

    async getDeviceType() {
      let device = [];
      let res = await this.$http.get(`/equip/dictData/type?dictName=机械类型`);
      res?.result.map((item) => {
        device.push({
          label: item.dictLabel,
          value: item.dictValue,
        });
      });
      this.deviceTypeData = device;
    },

    async submit() {
      let url =
        this.curData.type === 1
          ? "/equip/ledger/insert"
          : "/equip/ledger/saveOrUpdate";
      let params = {
        ...this.formData,
        annxArray:
          this.fileList.length > 0 ? JSON.stringify(this.fileList) : "",
        sectionId: this.$store.state.tree.sectionId,
      };
      const res = await this.$http.post(url, params);
      this.$emit("onSave");
      this.$msgSuccess(res.msg);
    },

    handleClose() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("onClose");
        })
        .catch((_) => {});
    },

    handleRemoved(file, files) {
      this.handleRemove(file, files, "fileList");
    },

    handleSucceed(file, files) {
      this.handleSuccess(file, files, [], "fileList");
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-dialog {
  .dialog-part {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    // .wide-input {
    //   //width: 50% !important; /* 调整为所需宽度 */
    //   width: 300px; /* 调整为所需宽度 */
    // }
    
    .item {
      flex: 1;
    }

    .item-right {
      margin-left: 10px;
    }

    
  }

  ::v-deep .el-dialog__header {
    background-color: #4575e7;

    .el-dialog__title {
      color: #fff;
    }

    .el-dialog__close {
      color: #fff;
    }
  }

  ::v-deep .el-date-editor {
    width: 100%;
  }

  ::v-deep .el-select {
    width: 100%;
  }

  ::v-deep .el-input {
    width: 175px;
  }
}

</style>