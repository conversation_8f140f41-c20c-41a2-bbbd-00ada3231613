<template>
    <div class="map-container">
        <div id="map" ref="rootmap">
            <svg-icon icon-class="expend" id="expend-icon"></svg-icon>
        </div>
        <div class="map-table">
            <Table></Table>
        </div>
    </div>
</template>

<script>
import Table from '../Table/Table.vue';
import screenfull from "screenfull";
import Bubble from './bubble/index.js'
import DragEntity from './dragentity.js'
export default {
    name: 'DeviceMap',
    props: [],
    components: { Table },
    data() {
        return {
            ViewerMap: null,
            map_baseUrl: "http://************:8090/iserver",
            carSite: [],
            carEntity: {},
        }
    },

    mounted() {
        const element = document.getElementById("map"); //指定全屏区域元素
        document.getElementById("expend-icon").addEventListener("click", () => {
            if (screenfull.isEnabled) {
                screenfull.request(element);
            }
            screenfull.toggle();
        }); //实现模块全屏
        this.initMap();
    },

    methods: {
        initMap() {
            if (Cesium) {
                this.ViewerMap = new Cesium.Viewer("map", {
                    infoBox: false,
                    selectionIndicator: false, // 点击实体绿色选择框
                    skyAtmosphere: false,
                    navigation: false,
                    useBrowserRecommendedResolution: true,
                });
                this.ViewerMap._cesiumWidget._creditContainer.style.display = "none"; // 关闭supermap logo
                this.add_TDT_server();
                this.loadMarker();
                this.addServer(); // 加载标段信息
                let viewer = this.ViewerMap
                this.handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
                let _this = this
                let id
                _this.handler.setInputAction(function (movement) {
                    let pick = viewer.scene.pick(movement.position);
                    if (Cesium.defined(pick) && (pick.id.id)) {
                        id = pick.id.id;
                        _this.bubble(id)
                    } else {
                        if (_this.bubbles) {
                            _this.bubbles.windowClose()
                        }

                    }
                }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
            }
        },

        // 加载车辆标注
        async loadMarker() {
            let res = await this.$http.get(`/equip/build/map?sectionId=${this.$store.state.tree.sectionId}`);
            // let res = await this.$http.get(`/equip/build/map`);
            if (res.result && res.result.length > 0) {
                this.carSite = res?.result || [];
                this.dragEntity() // 添加实体
            }
        },

        // 弹窗模板
        bubble(id) {
            if (this.bubbles) {
                this.bubbles.windowClose()
            }
            this.bubbles = new Bubble(Object.assign(this.carEntity[id], {
                viewer: this.ViewerMap
            }))

        },

        //加载实体
        dragEntity() {
            let drag = new DragEntity({
                viewer: this.ViewerMap,
            })
            let _this = this;
            _this.carSite.forEach(item => {
                let entity = drag.addEntity(item);
                _this.carEntity[item.id] = entity;
            })
        },

        // 天地图矢量地图
        add_TDT_server() {
            Cesium.Ion.defaultAccessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"; // 自己申请 或者放在全局变量 或者挂载原型上调用
            // vec(矢量)、img(影像)、cia(影像中文注记)、cva(矢量中文注记)
            var layer = new Cesium.WebMapTileServiceImageryProvider({
                url: "http://t0.tianditu.gov.cn/img_w/wmts?tk=b6e16a5ffc7b6e167bef81496433f1c9",
                subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
                layer: "img",
                style: "default",
                tileMatrixSetID: "w",
                format: "tiles",
                maximumLevel: 18// 必须加上最大级数
            });

            this.ViewerMap.imageryLayers.addImageryProvider(layer);
            var layer0 = new Cesium.WebMapTileServiceImageryProvider({
                url: "http://t0.tianditu.gov.cn/vec_w/wmts?tk=b6e16a5ffc7b6e167bef81496433f1c9",
                subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
                layer: "vec",
                style: "default",
                tileMatrixSetID: "w",
                format: "tiles",
                maximumLevel: 18// 必须加上最大级数
            });

            this.ViewerMap.imageryLayers.addImageryProvider(layer0);
            // 加载影像注记
            var layer1 = new Cesium.WebMapTileServiceImageryProvider({
                url: "http://t0.tianditu.gov.cn/cia_w/wmts?tk=b6e16a5ffc7b6e167bef81496433f1c9",
                subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
                layer: "cia",
                style: "default",
                tileMatrixSetID: "w",
                format: "tiles",
                maximumLevel: 18
            });
            this.ViewerMap.imageryLayers.addImageryProvider(layer1);
            var layer2 = new Cesium.WebMapTileServiceImageryProvider({
                url: "http://t0.tianditu.gov.cn/cva_w/wmts?tk=b6e16a5ffc7b6e167bef81496433f1c9",
                subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
                layer: "cva",
                style: "default",
                tileMatrixSetID: "w",
                format: "tiles",
                maximumLevel: 18
            });
            this.ViewerMap.imageryLayers.addImageryProvider(layer2);
            this.ViewerMap.imageryLayers.get(0).show = false;
            this.ViewerMap.imageryLayers.get(1).show = false;
            this.flyTo(null, 104.06585, 30.657361, 800000, 0.0, -0.0, 0) // 成都
            // this.flyTo(null, 16.37255, 2.26275, 100000, 0.0, -0.0, 0)
        },

        // 线路服务
        addServer() {
            this.ViewerMap.imageryLayers.addImageryProvider(
                new Cesium.SuperMapImageryProvider({
                    url: this.map_baseUrl + "/services/map-station/rest/maps/station",
                })
            );
        },

        // 移动视口 有动画3s
        flyTo(cb, longitude, latitude, height, heading, pitch, roll) {
            this.ViewerMap.camera.flyTo({
                destination: Cesium.Cartesian3.fromDegrees(
                    parseFloat(longitude),
                    parseFloat(latitude),
                    parseFloat(height)
                ),
                orientation: {
                    heading: heading ? Cesium.Math.toRadians(heading) : null,
                    pitch: pitch ? Cesium.Math.toRadians(pitch) : null,
                    roll: roll ? Cesium.Math.toRadians(roll) : null,
                },
                complete: () => {
                    cb && cb();
                },
                cancel() {
                    cb && cb();
                },
            });
        },
    }
};
</script>

<style lang="scss" scoped>
.map-container {
    width: 100%;
    height: 100%;

    #map {
        width: 100%;
        height: 574px;
        position: relative;

        #expend-icon {
            width: 20px;
            height: 20px;
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 999;
            cursor: pointer;
        }
    }

    .map-table {
        width: 100%;
        height: 315px;
        margin-top: 10px;
        overflow: hidden;
    }
}
</style>
