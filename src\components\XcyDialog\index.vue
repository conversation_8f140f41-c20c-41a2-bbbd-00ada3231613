<template>
  <el-dialog v-dialogDrag ref="xcyDialogRef" :title="title" :fullscreen="fullscreen" :visible.sync="show"
    :append-to-body="true" :show-close="false" :width="width" :close-on-click-modal="false" class="xcy-dialog"
    :class="minimize ? 'is-minimize' : ''" @opened="handleOpened" @open="handleOpen" @close="handleClose">
    <div slot="title" class="medium">
      <div class="lefts">
        <span>{{ title }}</span>
      </div>
      <div class="icons">
        <i v-show="!minimize" title="关闭" class="el-icon-close" style="font-size: 24px" @click="closeDialog"></i>
      </div>
    </div>

    <div v-show="!minimize">
      <slot></slot>
    </div>
    <div slot="footer" v-show="!minimize">
      <slot name="footer"></slot>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: "XcyDialog",
  props: {
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '1100px'
    },
    visible: {
      type: Boolean,
      default: false
    },
    initFullscreen: {
      type: Boolean,
      default: false
    },
    disableFullscreen: {
      type: Boolean,
      default: false
    },
    disableMinimize: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      fullscreen: false, // 全屏
      minimize: false, // 最小化
      show: false,
      top: '0',
      left: '0'
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    show(val) {
      if (val) {
        // 获取初始位置
        const el = this.$refs['xcyDialogRef'].$el.querySelector(".el-dialog");
        this.top = el.style.top;
        this.left = el.style.left;
        this.fullscreen = this.initFullscreen;
      }
    },
  },
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', false)
    },
    handleOpened() {
      this.$emit('opened', true)
    },
    handleOpen() {
      this.$emit('open', true)
    },
    handleClose() {
      this.$emit('update:visible', false)
    }
  },

};
</script>

<style lang="scss" scoped>
.xcy-dialog {
  ::v-deep .el-dialog.is-fullscreen {
    top: 0 !important;
    left: 0 !important;
  }

  ::v-deep .el-dialog.is-minimize {
    top: auto !important;
    left: auto !important;
    right: 20px !important;
    bottom: 120px !important;
    width: 300px;
  }

  ::v-deep .el-dialog__header {
    padding: 10px;
    background: #f8f8f8;
    border-radius: 4px 4px 0 0;
  }

  ::v-deep .el-dialog__body {
    overflow: auto;
    .clearfix{
      font-size: 14px;
    }
  }

  .medium {
    width: 100%;
    height: 100%;
    display: flex;

    div {
      flex: 1;
    }

    .lefts {
      margin-top: 3px;

      span {
        text-align: left;
        font-size: 16px;
        font-weight: 600;
        margin-left: 15px;
      }
    }

    .icons {
      display: flex;
      justify-content: flex-end;

      i {
        display: block;
        padding: 3px;
      }

      i:hover {
        background: #dcdfe6;
        cursor: pointer;
      }

      .el-icon-close:hover {
        background: #f00;
        color: #fff;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}

.xcy-dialog.is-minimize {

  ::v-deep .el-dialog,
  ::v-deep .el-dialog.is-fullscreen {
    top: auto !important;
    left: auto !important;
    right: 20px !important;
    bottom: 20px !important;
    width: 360px !important;
    max-height: 70px !important;
  }

  ::v-deep .el-dialog__body {
    display: none;
  }

  ::v-deep .el-dialog__footer {
    display: none;
  }
}
</style>
