import Cookies from "js-cookie";
const TokenKey = "wzgylpt-token";
//设置key
const TimeKey = "wzgylpt-timestamp";

export function setTimestamp() {
  // 得到当前时间戳
  return Cookies.set(<PERSON><PERSON><PERSON>, Date.now());
}

export function getTimestamp() {
 // 生成目前的时间戳
  return Cookies.set(TimeKey);
}

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(TokenKey, token);
}

export function removeToken() {
  return Cookies.remove(TokenKey);
}
