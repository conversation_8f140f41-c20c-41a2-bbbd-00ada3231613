/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./GeometryAttributes-aacecde6","./AttributeCompression-75ce15eb","./GeometryPipeline-8e55e413","./EncodedCartesian3-87cd0c1f","./IndexDatatype-9435b55f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./arrayFill-9766fb2e","./GeometryOffsetAttribute-999fc023","./GeometryInstance-9ddb8c73","./arrayRemoveDuplicates-2869246d","./EllipsoidTangentPlane-9c25b2da","./ArcType-66bc286a","./EllipsoidRhumbLine-6ca4b1e6","./earcut-2.2.1-b404d9e6","./PolygonPipeline-cc78b34e","./PolygonGeometryLibrary-ec05daff"],function(v,c,E,e,f,A,t,i,r,G,_,T,o,n,a,H,l,C,y,L,s,p,O,D,I,u,w,x,d,h,k,S){var R=[],M=[];function N(e,t,i,r,o){var n,a,l=w.EllipsoidTangentPlane.fromPoints(t,e).projectPointsOntoPlane(t,R);k.PolygonPipeline.computeWindingOrder2D(l)===k.WindingOrder.CLOCKWISE&&(l.reverse(),t=t.slice().reverse());var y=t.length,s=0;if(r)for(n=new Float64Array(2*y*3),a=0;a<y;a++){var p=t[a],u=t[(a+1)%y];n[s++]=p.x,n[s++]=p.y,n[s++]=p.z,n[s++]=u.x,n[s++]=u.y,n[s++]=u.z}else{var d=0;if(o===x.ArcType.GEODESIC)for(a=0;a<y;a++)d+=S.PolygonGeometryLibrary.subdivideLineCount(t[a],t[(a+1)%y],i);else if(o===x.ArcType.RHUMB)for(a=0;a<y;a++)d+=S.PolygonGeometryLibrary.subdivideRhumbLineCount(e,t[a],t[(a+1)%y],i);for(n=new Float64Array(3*d),a=0;a<y;a++){var c;o===x.ArcType.GEODESIC?c=S.PolygonGeometryLibrary.subdivideLine(t[a],t[(a+1)%y],i,M):o===x.ArcType.RHUMB&&(c=S.PolygonGeometryLibrary.subdivideRhumbLine(e,t[a],t[(a+1)%y],i,M));for(var f=c.length,h=0;h<f;++h)n[s++]=c[h]}}var g=2*(y=n.length/3),b=L.IndexDatatype.createTypedArray(y,g);for(a=s=0;a<y-1;a++)b[s++]=a,b[s++]=a+1;return b[s++]=y-1,b[s++]=0,new I.GeometryInstance({geometry:new _.Geometry({attributes:new H.GeometryAttributes({position:new _.GeometryAttribute({componentDatatype:G.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:n})}),indices:b,primitiveType:T.PrimitiveType.LINES})})}function U(e,t,i,r,o){var n,a,l=w.EllipsoidTangentPlane.fromPoints(t,e).projectPointsOntoPlane(t,R);k.PolygonPipeline.computeWindingOrder2D(l)===k.WindingOrder.CLOCKWISE&&(l.reverse(),t=t.slice().reverse());var y=t.length,s=new Array(y),p=0;if(r)for(n=new Float64Array(2*y*3*2),a=0;a<y;++a){s[a]=p/3;var u=t[a],d=t[(a+1)%y];n[p++]=u.x,n[p++]=u.y,n[p++]=u.z,n[p++]=d.x,n[p++]=d.y,n[p++]=d.z}else{var c=0;if(o===x.ArcType.GEODESIC)for(a=0;a<y;a++)c+=S.PolygonGeometryLibrary.subdivideLineCount(t[a],t[(a+1)%y],i);else if(o===x.ArcType.RHUMB)for(a=0;a<y;a++)c+=S.PolygonGeometryLibrary.subdivideRhumbLineCount(e,t[a],t[(a+1)%y],i);for(n=new Float64Array(3*c*2),a=0;a<y;++a){var f;s[a]=p/3,o===x.ArcType.GEODESIC?f=S.PolygonGeometryLibrary.subdivideLine(t[a],t[(a+1)%y],i,M):o===x.ArcType.RHUMB&&(f=S.PolygonGeometryLibrary.subdivideRhumbLine(e,t[a],t[(a+1)%y],i,M));for(var h=f.length,g=0;g<h;++g)n[p++]=f[g]}}y=n.length/6;var b=s.length,m=2*(2*y+b),P=L.IndexDatatype.createTypedArray(y,m);for(a=p=0;a<y;++a)P[p++]=a,P[p++]=(a+1)%y,P[p++]=a+y,P[p++]=(a+1)%y+y;for(a=0;a<b;a++){var v=s[a];P[p++]=v,P[p++]=v+y}return new I.GeometryInstance({geometry:new _.Geometry({attributes:new H.GeometryAttributes({position:new _.GeometryAttribute({componentDatatype:G.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:n})}),indices:P,primitiveType:T.PrimitiveType.LINES})})}function g(e){if(c.Check.typeOf.object("options",e),c.Check.typeOf.object("options.polygonHierarchy",e.polygonHierarchy),e.perPositionHeight&&v.defined(e.height))throw new c.DeveloperError("Cannot use both options.perPositionHeight and options.height");if(v.defined(e.arcType)&&e.arcType!==x.ArcType.GEODESIC&&e.arcType!==x.ArcType.RHUMB)throw new c.DeveloperError("Invalid arcType. Valid options are ArcType.GEODESIC and ArcType.RHUMB.");var t=e.polygonHierarchy,i=v.defaultValue(e.ellipsoid,f.Ellipsoid.WGS84),r=v.defaultValue(e.granularity,E.CesiumMath.RADIANS_PER_DEGREE),o=v.defaultValue(e.perPositionHeight,!1),n=o&&v.defined(e.extrudedHeight),a=v.defaultValue(e.arcType,x.ArcType.GEODESIC),l=v.defaultValue(e.height,0),y=v.defaultValue(e.extrudedHeight,l);if(!n){var s=Math.max(l,y);y=Math.min(l,y),l=s}this._ellipsoid=f.Ellipsoid.clone(i),this._granularity=r,this._height=l,this._extrudedHeight=y,this._arcType=a,this._polygonHierarchy=t,this._perPositionHeight=o,this._perPositionHeightExtrude=n,this._offsetAttribute=e.offsetAttribute,this._workerName="createPolygonOutlineGeometry",this.packedLength=S.PolygonGeometryLibrary.computeHierarchyPackedLength(t)+f.Ellipsoid.packedLength+8}g.pack=function(e,t,i){return c.Check.typeOf.object("value",e),c.Check.defined("array",t),i=v.defaultValue(i,0),i=S.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,i),f.Ellipsoid.pack(e._ellipsoid,t,i),i+=f.Ellipsoid.packedLength,t[i++]=e._height,t[i++]=e._extrudedHeight,t[i++]=e._granularity,t[i++]=e._perPositionHeightExtrude?1:0,t[i++]=e._perPositionHeight?1:0,t[i++]=e._arcType,t[i++]=v.defaultValue(e._offsetAttribute,-1),t[i]=e.packedLength,t};var b=f.Ellipsoid.clone(f.Ellipsoid.UNIT_SPHERE),m={polygonHierarchy:{}};return g.unpack=function(e,t,i){c.Check.defined("array",e),t=v.defaultValue(t,0);var r=S.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t);t=r.startingIndex,delete r.startingIndex;var o=f.Ellipsoid.unpack(e,t,b);t+=f.Ellipsoid.packedLength;var n=e[t++],a=e[t++],l=e[t++],y=1===e[t++],s=1===e[t++],p=e[t++],u=e[t++],d=e[t];return v.defined(i)||(i=new g(m)),i._polygonHierarchy=r,i._ellipsoid=f.Ellipsoid.clone(o,i._ellipsoid),i._height=n,i._extrudedHeight=a,i._granularity=l,i._perPositionHeight=s,i._perPositionHeightExtrude=y,i._arcType=p,i._offsetAttribute=-1===u?void 0:u,i.packedLength=d,i},g.fromPositions=function(e){return e=v.defaultValue(e,v.defaultValue.EMPTY_OBJECT),c.Check.defined("options.positions",e.positions),new g({polygonHierarchy:{positions:e.positions},height:e.height,extrudedHeight:e.extrudedHeight,ellipsoid:e.ellipsoid,granularity:e.granularity,perPositionHeight:e.perPositionHeight,arcType:e.arcType,offsetAttribute:e.offsetAttribute})},g.createGeometry=function(e){var t=e._ellipsoid,i=e._granularity,r=e._polygonHierarchy,o=e._perPositionHeight,n=e._arcType,a=S.PolygonGeometryLibrary.polygonOutlinesFromHierarchy(r,!o,t);if(0!==a.length){var l,y,s,p=[],u=E.CesiumMath.chordLength(i,t.maximumRadius),d=e._height,c=e._extrudedHeight;if(e._perPositionHeightExtrude||!E.CesiumMath.equalsEpsilon(d,c,0,E.CesiumMath.EPSILON2))for(s=0;s<a.length;s++){if((l=U(t,a[s],u,o,n)).geometry=S.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(l.geometry,d,c,t,o),v.defined(e._offsetAttribute)){var f=l.geometry.attributes.position.values.length/3,h=new Uint8Array(f);h=e._offsetAttribute===D.GeometryOffsetAttribute.TOP?O.arrayFill(h,1,0,f/2):(y=e._offsetAttribute===D.GeometryOffsetAttribute.NONE?0:1,O.arrayFill(h,y)),l.geometry.attributes.applyOffset=new _.GeometryAttribute({componentDatatype:G.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:h})}p.push(l)}else for(s=0;s<a.length;s++){if((l=N(t,a[s],u,o,n)).geometry.attributes.position.values=k.PolygonPipeline.scaleToGeodeticHeight(l.geometry.attributes.position.values,d,t,!o),v.defined(e._offsetAttribute)){var g=l.geometry.attributes.position.values.length,b=new Uint8Array(g/3);y=e._offsetAttribute===D.GeometryOffsetAttribute.NONE?0:1,O.arrayFill(b,y),l.geometry.attributes.applyOffset=new _.GeometryAttribute({componentDatatype:G.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:b})}p.push(l)}var m=C.GeometryPipeline.combineInstances(p)[0],P=A.BoundingSphere.fromVertices(m.attributes.position.values);return new _.Geometry({attributes:m.attributes,indices:m.indices,primitiveType:m.primitiveType,boundingSphere:P,offsetAttribute:e._offsetAttribute})}},function(e,t){return v.defined(t)&&(e=g.unpack(e,t)),e._ellipsoid=f.Ellipsoid.clone(e._ellipsoid),g.createGeometry(e)}});