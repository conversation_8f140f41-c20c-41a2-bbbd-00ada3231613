/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./Transforms-1509c877","./EllipsoidTangentPlane-9c25b2da","./PolylinePipeline-65700d85"],function(a,U,_,l,j,e,Q,C,q){var Y=Object.freeze({ROUNDED:0,MITERED:1,BEVELED:2}),Z=[new _.Cartesian3,new _.Cartesian3],k=new _.Cartesian3,H=new _.Cartesian3,J=new _.Cartesian3,K=new _.Cartesian3,W=new _.Cartesian3,X=new _.Cartesian3,$=new _.Cartesian3,aa=new _.Cartesian3,ea=new _.Cartesian3,ra=new _.Cartesian3,f=new _.Cartesian3,na={},i=new _.Cartographic;function ta(a,e){for(var r=new Array(a.length),n=0;n<a.length;n++){var t=a[n];i=e.cartesianToCartographic(t,i),r[n]=i.height,a[n]=e.scaleToGeodeticSurface(t,t)}return r}function ia(a,e,r,n){var t,i=a[0],s=a[1],o=_.Cartesian3.angleBetween(i,s),l=Math.ceil(o/n),C=new Array(l);if(e===r){for(t=0;t<l;t++)C[t]=e;return C.push(r),C}var c=(r-e)/l;for(t=1;t<l;t++){var u=e+t*c;C[t]=u}return C[0]=e,C.push(r),C}var c=new _.Cartesian3,u=new _.Cartesian3;function p(a,e,r,n){var t=new C.EllipsoidTangentPlane(r,n),i=t.projectPointOntoPlane(_.Cartesian3.add(r,a,c),c),s=t.projectPointOntoPlane(_.Cartesian3.add(r,e,u),u),o=l.Cartesian2.angleBetween(i,s);return 0<=s.x*i.y-s.y*i.x?-o:o}var g=new _.Cartesian3(-1,0,0),h=j.Matrix4.clone(j.Matrix4.IDENTITY),v=new j.Matrix4,w=new j.Matrix3,x=j.Matrix3.IDENTITY.clone(),M=new _.Cartesian3,P=new e.Cartesian4,E=new _.Cartesian3;function F(a,e,r,n,t,i,s,o){var l=M,C=P;h=Q.Transforms.eastNorthUpToFixedFrame(a,t,h),l=j.Matrix4.multiplyByPointAsVector(h,g,l);var c=p(l=_.Cartesian3.normalize(l,l),e,a,t);w=j.Matrix3.fromRotationZ(c,w),E.z=i,h=j.Matrix4.multiplyTransformation(h,j.Matrix4.fromRotationTranslation(w,E,v),h);var u=x;u[0]=s;for(var y=0;y<o;y++)for(var m=0;m<r.length;m+=3)C=_.Cartesian3.fromArray(r,m,C),C=j.Matrix3.multiplyByVector(u,C,C),C=j.Matrix4.multiplyByPoint(h,C,C),n.push(C.x,C.y,C.z);return n}function sa(a,e,r,n,t,i,s,o,l){var C=M,c=P;h=Q.Transforms.eastNorthUpToFixedFrame(a,t,h),C=j.Matrix4.multiplyByPointAsVector(h,g,C);var u=p(C=_.Cartesian3.normalize(C,C),e,a,t);w=j.Matrix3.fromRotationZ(u,w),E.z=i,h=j.Matrix4.multiplyTransformation(h,j.Matrix4.fromRotationTranslation(w,E,v),h);var y=x;y[0]=s;for(var m=0;m<o;m++)for(var d=0;d<r.length;d+=3)c=_.Cartesian3.fromArray(r,d,c),c=j.Matrix3.multiplyByVector(y,c,c),c=j.Matrix4.multiplyByPoint(h,c,c),c=j.Matrix4.multiplyByPoint(l,c,c),n.push(c.x,c.y,c.z);return n}var oa=new _.Cartesian3;function la(a,e,r,n,t,i,s){for(var o=0;o<a.length;o+=3){n=F(_.Cartesian3.fromArray(a,o,oa),e,r,n,t,i[o/3],s,1)}return n}function Ca(a,e){var r=a.length,n=new Array(6*r),t=0,i=e.x+e.width/2,s=e.y+e.height/2,o=a[0];n[t++]=o.x-i,n[t++]=0,n[t++]=o.y-s;for(var l=1;l<r;l++){var C=(o=a[l]).x-i,c=o.y-s;n[t++]=C,n[t++]=0,n[t++]=c,n[t++]=C,n[t++]=0,n[t++]=c}return o=a[0],n[t++]=o.x-i,n[t++]=0,n[t++]=o.y-s,n}function ca(a,e){for(var r=a.length,n=new Array(3*r),t=0,i=e.x+e.width/2,s=e.y+e.height/2,o=0;o<r;o++)n[t++]=a[o].x-i,n[t++]=0,n[t++]=a[o].y-s;return n}var B=new Q.Quaternion,T=new _.Cartesian3,z=new j.Matrix3;function ua(a,e,r,n,t,i,s,o,l,C){var c,u,y=_.Cartesian3.angleBetween(_.Cartesian3.subtract(e,a,ra),_.Cartesian3.subtract(r,a,f)),m=n===Y.BEVELED?0:Math.ceil(y/U.CesiumMath.toRadians(5));if(c=t?j.Matrix3.fromQuaternion(Q.Quaternion.fromAxisAngle(_.Cartesian3.negate(a,ra),y/(m+1),B),z):j.Matrix3.fromQuaternion(Q.Quaternion.fromAxisAngle(a,y/(m+1),B),z),e=_.Cartesian3.clone(e,T),0<m)for(var d=C?2:1,p=0;p<m;p++)e=j.Matrix3.multiplyByVector(c,e,e),u=_.Cartesian3.subtract(e,a,ra),u=_.Cartesian3.normalize(u,u),t||(u=_.Cartesian3.negate(u,u)),s=F(i.scaleToGeodeticSurface(e,f),u,o,s,i,l,1,d);else u=_.Cartesian3.subtract(e,a,ra),u=_.Cartesian3.normalize(u,u),t||(u=_.Cartesian3.negate(u,u)),s=F(i.scaleToGeodeticSurface(e,f),u,o,s,i,l,1,1),r=_.Cartesian3.clone(r,T),u=_.Cartesian3.subtract(r,a,ra),u=_.Cartesian3.normalize(u,u),t||(u=_.Cartesian3.negate(u,u)),s=F(i.scaleToGeodeticSurface(r,f),u,o,s,i,l,1,1);return s}na.removeDuplicatesFromShape=function(a){for(var e=a.length,r=[],n=e-1,t=0;t<e;n=t++){var i=a[n],s=a[t];l.Cartesian2.equals(i,s)||r.push(s)}return r},na.angleIsGreaterThanPi=function(a,e,r,n){var t=new C.EllipsoidTangentPlane(r,n),i=t.projectPointOntoPlane(_.Cartesian3.add(r,a,c),c),s=t.projectPointOntoPlane(_.Cartesian3.add(r,e,u),u);return 0<=s.x*i.y-s.y*i.x};var ya=new _.Cartesian3,ma=new _.Cartesian3;na.computePositions=function(a,e,r,n,t){var i=n._ellipsoid,s=ta(a,i),o=n._granularity,l=n._cornerType,C=t?Ca(e,r):ca(e,r),c=t?ca(e,r):void 0,u=r.height/2,y=r.width/2,m=a.length,d=[],p=t?[]:void 0,f=k,g=H,h=J,v=K,w=W,x=X,M=$,P=aa,E=ea,B=a[0],T=a[1];v=i.geodeticSurfaceNormal(B,v),f=_.Cartesian3.subtract(T,B,f),f=_.Cartesian3.normalize(f,f),P=_.Cartesian3.cross(v,f,P),P=_.Cartesian3.normalize(P,P);var z,S=s[0],A=s[1];t&&(p=F(B,P,c,p,i,S+u,1,1)),E=_.Cartesian3.clone(B,E),B=T,g=_.Cartesian3.negate(f,g);for(var b=1;b<m-1;b++){var D=t?2:1;T=a[b+1],f=_.Cartesian3.subtract(T,B,f),f=_.Cartesian3.normalize(f,f),h=_.Cartesian3.add(f,g,h),h=_.Cartesian3.normalize(h,h),v=i.geodeticSurfaceNormal(B,v);var N=_.Cartesian3.multiplyByScalar(v,_.Cartesian3.dot(f,v),ya);_.Cartesian3.subtract(f,N,N),_.Cartesian3.normalize(N,N);var O=_.Cartesian3.multiplyByScalar(v,_.Cartesian3.dot(g,v),ma);if(_.Cartesian3.subtract(g,O,O),_.Cartesian3.normalize(O,O),!U.CesiumMath.equalsEpsilon(Math.abs(_.Cartesian3.dot(N,O)),1,U.CesiumMath.EPSILON7)){h=_.Cartesian3.cross(h,v,h),h=_.Cartesian3.cross(v,h,h),h=_.Cartesian3.normalize(h,h);var V=1/Math.max(.25,_.Cartesian3.magnitude(_.Cartesian3.cross(h,g,ra))),R=na.angleIsGreaterThanPi(f,g,B,i);E=(R?(w=_.Cartesian3.add(B,_.Cartesian3.multiplyByScalar(h,V*y,h),w),x=_.Cartesian3.add(w,_.Cartesian3.multiplyByScalar(P,y,x),x),Z[0]=_.Cartesian3.clone(E,Z[0]),Z[1]=_.Cartesian3.clone(x,Z[1]),z=ia(Z,S+u,A+u,o),d=la(q.PolylinePipeline.generateArc({positions:Z,granularity:o,ellipsoid:i}),P,C,d,i,z,1),P=_.Cartesian3.cross(v,f,P),P=_.Cartesian3.normalize(P,P),M=_.Cartesian3.add(w,_.Cartesian3.multiplyByScalar(P,y,M),M),l===Y.ROUNDED||l===Y.BEVELED?ua(w,x,M,l,R,i,d,C,A+u,t):d=F(B,h=_.Cartesian3.negate(h,h),C,d,i,A+u,V,D)):(w=_.Cartesian3.add(B,_.Cartesian3.multiplyByScalar(h,V*y,h),w),x=_.Cartesian3.add(w,_.Cartesian3.multiplyByScalar(P,-y,x),x),Z[0]=_.Cartesian3.clone(E,Z[0]),Z[1]=_.Cartesian3.clone(x,Z[1]),z=ia(Z,S+u,A+u,o),d=la(q.PolylinePipeline.generateArc({positions:Z,granularity:o,ellipsoid:i}),P,C,d,i,z,1),P=_.Cartesian3.cross(v,f,P),P=_.Cartesian3.normalize(P,P),M=_.Cartesian3.add(w,_.Cartesian3.multiplyByScalar(P,-y,M),M),l===Y.ROUNDED||l===Y.BEVELED?ua(w,x,M,l,R,i,d,C,A+u,t):d=F(B,h,C,d,i,A+u,V,D)),_.Cartesian3.clone(M,E)),g=_.Cartesian3.negate(f,g)}else d=F(E,P,C,d,i,S+u,1,1),E=B;S=A,A=s[b+1],B=T}Z[0]=_.Cartesian3.clone(E,Z[0]),Z[1]=_.Cartesian3.clone(B,Z[1]),z=ia(Z,S+u,A+u,o),d=la(q.PolylinePipeline.generateArc({positions:Z,granularity:o,ellipsoid:i}),P,C,d,i,z,1),t&&(p=F(B,P,c,p,i,A+u,1,1)),m=d.length;var I=t?m+p.length:m,L=new Float64Array(I);return L.set(d),t&&L.set(p,m),L},na.computeLocalPositions=function(a,e,r,n,t,i){var s=n._ellipsoid,o=ta(a,s),l=n._granularity,C=n._cornerType,c=t?Ca(e,r):ca(e,r),u=t?ca(e,r):void 0,y=r.width/2,m=a.length,d=[],p=t?[]:void 0,f=k,g=H,h=J,v=K,w=W,x=X,M=$,P=aa,E=ea,B=Q.Transforms.eastNorthUpToFixedFrame(i,s,new j.Matrix4),T=j.Matrix4.inverse(B,new j.Matrix4),z=a[0],S=a[1];v=s.geodeticSurfaceNormal(z,v),f=_.Cartesian3.subtract(S,z,f),f=_.Cartesian3.normalize(f,f),P=_.Cartesian3.cross(v,f,P),P=_.Cartesian3.normalize(P,P);var A,b=o[0],D=o[1];t&&(p=sa(z,P,u,p,s,b+0,1,1,T)),E=_.Cartesian3.clone(z,E),z=S,g=_.Cartesian3.negate(f,g);for(var N=1;N<m-1;N++){var O=t?2:1;S=a[N+1],f=_.Cartesian3.subtract(S,z,f),f=_.Cartesian3.normalize(f,f),h=_.Cartesian3.add(f,g,h),h=_.Cartesian3.normalize(h,h),v=s.geodeticSurfaceNormal(z,v);var V=_.Cartesian3.multiplyByScalar(v,_.Cartesian3.dot(f,v),ya);_.Cartesian3.subtract(f,V,V),_.Cartesian3.normalize(V,V);var R=_.Cartesian3.multiplyByScalar(v,_.Cartesian3.dot(g,v),ma);if(_.Cartesian3.subtract(g,R,R),_.Cartesian3.normalize(R,R),!U.CesiumMath.equalsEpsilon(Math.abs(_.Cartesian3.dot(V,R)),1,U.CesiumMath.EPSILON7)){h=_.Cartesian3.cross(h,v,h),h=_.Cartesian3.cross(v,h,h),h=_.Cartesian3.normalize(h,h);var I=1/Math.max(.25,_.Cartesian3.magnitude(_.Cartesian3.cross(h,g,ra))),L=na.angleIsGreaterThanPi(f,g,z,s);E=(L?(w=_.Cartesian3.add(z,_.Cartesian3.multiplyByScalar(h,I*y,h),w),x=_.Cartesian3.add(w,_.Cartesian3.multiplyByScalar(P,y,x),x),Z[0]=_.Cartesian3.clone(E,Z[0]),Z[1]=_.Cartesian3.clone(x,Z[1]),A=ia(Z,b+0,D+0,l),d=la(q.PolylinePipeline.generateArc({positions:Z,granularity:l,ellipsoid:s}),P,c,d,s,A,1,fromEnu),P=_.Cartesian3.cross(v,f,P),P=_.Cartesian3.normalize(P,P),M=_.Cartesian3.add(w,_.Cartesian3.multiplyByScalar(P,y,M),M),C===Y.ROUNDED||C===Y.BEVELED?ua(w,x,M,C,L,s,d,c,D+0,t):d=sa(z,h=_.Cartesian3.negate(h,h),c,d,s,D+0,I,O,T)):(w=_.Cartesian3.add(z,_.Cartesian3.multiplyByScalar(h,I*y,h),w),x=_.Cartesian3.add(w,_.Cartesian3.multiplyByScalar(P,-y,x),x),Z[0]=_.Cartesian3.clone(E,Z[0]),Z[1]=_.Cartesian3.clone(x,Z[1]),A=ia(Z,b+0,D+0,l),d=la(q.PolylinePipeline.generateArc({positions:Z,granularity:l,ellipsoid:s}),P,c,d,s,A,1),P=_.Cartesian3.cross(v,f,P),P=_.Cartesian3.normalize(P,P),M=_.Cartesian3.add(w,_.Cartesian3.multiplyByScalar(P,-y,M),M),C===Y.ROUNDED||C===Y.BEVELED?ua(w,x,M,C,L,s,d,c,D+0,t):d=sa(z,h,c,d,s,D+0,I,O,T)),_.Cartesian3.clone(M,E)),g=_.Cartesian3.negate(f,g)}else d=sa(E,P,c,d,s,b+0,1,1,T),E=z;b=D,D=o[N+1],z=S}Z[0]=_.Cartesian3.clone(E,Z[0]),Z[1]=_.Cartesian3.clone(z,Z[1]),A=ia(Z,b+0,D+0,l),d=function(a,e,r,n,t,i,s,o){for(var l=0;l<a.length;l+=3)n=sa(_.Cartesian3.fromArray(a,l,oa),e,r,n,t,i[l/3],s,1,o);return n}(q.PolylinePipeline.generateArc({positions:Z,granularity:l,ellipsoid:s}),P,c,d,s,A,1,T),t&&(p=sa(z,P,u,p,s,D+0,1,1,T)),m=d.length;var F=t?m+p.length:m,G=new Float64Array(F);return G.set(d),t&&G.set(p,m),G},a.CornerType=Y,a.PolylineVolumeGeometryLibrary=na});