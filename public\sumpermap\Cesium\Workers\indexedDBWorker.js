/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./createTaskProcessorWorker"],function(d,e){function v(e){return null!=e}function c(e){var t;this.name="DeveloperError",this.message=e;try{throw new Error}catch(e){t=e.stack}this.stack=t}v(Object.create)&&((c.prototype=Object.create(Error.prototype)).constructor=c),c.prototype.toString=function(){var e=this.name+": "+this.message;return v(this.stack)&&(e+="\n"+this.stack.toString()),e},c.throwInstantiationError=function(){throw new c("This function defines an interface and should not be called directly.")};var f=1,b=2,p=3;function n(e){if(!v(e.name))throw new c("options.name is required.");var t=d.when.defer();this.dbname=e.name;var r=indexedDB.open(this.dbname),n=this;return r.onsuccess=function(e){n.db=e.target.result,n.version=n.db.version,v(n.cachestatus)||(n.cachestatus={}),t.resolve(n)},r.onupgradeneeded=function(e){n.db=e.target.result,n.version=n.db.version,t.resolve(n)},r.onerror=function(e){n.db=null,t.reject("create database fail, error code : "+e.target.errorcode)},this.layer=e.layer||null,this.storageType=e.storageType||"arrayBuffer",this.creatingTable=!1,this.cachestatus={},t.promise}n.prototype.checkObjectStoreExit=function(e){return!!v(this.db)&&this.db.objectStoreNames.contains(e)},n.prototype.createObjectStore=function(n){var c=d.when.defer();if(this.creatingTable)c.reject(!1);else{if(this.db.objectStoreNames.contains(n))return c.reject(!1),c.promise;this.creatingTable=!0;var o=this,e=parseInt(o.db.version);o.db.close();var t=indexedDB.open(o.dbname,e+1);t.onupgradeneeded=function(e){var t=e.target.result,r=(o.db=t).createObjectStore(n,{keyPath:"id"});v(r)?(r.createIndex("value","value",{unique:!1}),o.creatingTable=!1,v(o.cachestatus)||(o.cachestatus={}),o.cachestatus[n]={},o.db.close(),indexedDB.open(o.dbname).onsuccess=function(e){var t=e.target.result;o.db=t,c.resolve(!0)}):(o.creatingTable=!1,c.reject(!1))},t.onsuccess=function(e){e.target.result.close(),c.resolve(!0)},t.onerror=function(e){o.creatingTable=!1,c.reject(!1)}}return c.promise},n.prototype.putElementInDB=function(o,a,s,i){var u,l=d.when.defer();if(!v(this.db))return l.reject(!1),l.promise;var h=this;if(v(h.cachestatus[o])&&!v(i)&&v(h.cachestatus[o][a])&&(h.cachestatus[o][a]===f||h.cachestatus[o][a]===b))return l.resolve(!1),l.promise;if(this.db.objectStoreNames.contains(o)){var e;v(h.cachestatus[o])||(h.cachestatus[o]={});try{e=this.db.transaction([o],"readwrite")}catch(e){return l.reject(null),l.promise}if(u=e.objectStore(o),v(i))if(i instanceof Array){for(var t=0,r=i.length;t<r;t++)h.cachestatus[o][i[t].key]!==b&&(u.add({id:i[t].key,value:i[t].value}),h.cachestatus[o][i[t].key]=b);l.resolve(!0)}else{for(var a in i)isNaN(1*a)||u.add({id:a,value:i[a]});l.resolve(!0)}else{if(!v(a)||!v(s))return;if(a instanceof Array&&s instanceof Array){for(t=0,r=a.length;t<r;t++)h.cachestatus[o][a[t]]!==b&&(u.add({id:a[t],value:s[t]}),h.cachestatus[o][a[t]]=b);e.oncomplete=function(e){l.resolve(!0)},e.onerror=function(e){l.reject(!1)}}else{var n=u.add({id:a,value:s});h.cachestatus[o][a]=f,n.onsuccess=function(e){h.cachestatus[o][a]=b,l.resolve(!0)},n.onerror=function(e){h.cachestatus[o][a]=p,l.reject(!1)}}}}else this.createObjectStore(o).then(function(e){var t=h.db.transaction([o],"readwrite");if(u=t.objectStore(o),v(i)){for(var r=0,n=i.length;r<n;r++)u.add({id:i[r].key,value:i[r].value});l.resolve(!0)}else{var c=u.add({id:a,value:s});c.onsuccess=function(e){l.resolve(!0)},c.onerror=function(e){l.reject(!1)}}},function(e){l.reject(!1)});return l.promise},n.prototype.getRangeFromDB=function(e,t){var r,n,c=d.when.defer();if(!v(this.db))return null;if(!this.db.objectStoreNames.contains(e))return null;try{r=this.db.transaction([e])}catch(e){return c.reject(null),c.promise}try{n=r.objectStore(e)}catch(e){c.reject(null)}var o=n.openCursor(IDBKeyRange.bound(t[0],t[1])),a=[];return o.onsuccess=function(e){var t=e.target.result;v(t)?(a.push(t.value),t.continue()):c.resolve(a)},o.onerror=function(e){c.reject(null)},c.promise},n.prototype.getElementFromDB=function(e,t){var r,n,c=d.when.defer();if(!v(this.db))return null;if(!this.db.objectStoreNames.contains(e))return null;try{r=this.db.transaction([e])}catch(e){return c.reject(null),c.promise}try{n=r.objectStore(e)}catch(e){c.reject(null)}var o=n.get(t);return o.onsuccess=function(e){v(e.target.result)?c.resolve(e.target.result.value):c.reject(null)},o.onerror=function(e){c.reject(null)},c.promise},n.prototype.getAllElementFromDB=function(e){var t,r,n=d.when.defer();if(!v(this.db))return null;if(!this.db.objectStoreNames.contains(e))return null;if(null!=this.transaction)t=this.transaction;else try{t=this.db.transaction([e])}catch(e){return n.reject(null),n.promise}try{r=t.objectStore(e)}catch(e){n.reject(null)}var c=r.getAll();return c.onsuccess=function(e){v(e.target.result)?n.resolve(e.target.result):n.reject(null)},c.onerror=function(e){n.reject(null)},n.promise},n.prototype.updateElementInDB=function(e,n,c,o){var a=d.when.defer();if(!v(this.db))return a.resolve(!1),a.promise;if(!this.db.objectStoreNames.contains(e))return a.resolve(!1),a.promise;var s,t=this.db.transaction([e],"readwrite");try{s=t.objectStore(e)}catch(e){a.resolve(!1)}var r=s.get(n);return r.onsuccess=function(e){var t=e.target.result;v(t)||(t={id:n}),t.value=!0===o?Object.assign(t.value,c):c;var r=s.put(t);r.onsuccess=function(e){a.resolve(!0)},r.onerror=function(e){a.resolve(!1)}},r.onerror=function(e){a.resolve(!1)},a.promise},n.prototype.removeElementFromDB=function(e,t){var r=d.when.defer();if(!v(this.db))return r.resolve(!1),r.promise;if(!this.db.objectStoreNames.contains(e))return r.resolve(!1),r.promise;var n,c=this.db.transaction([e],"readwrite");try{n=c.objectStore(e)}catch(e){r.resolve(!1)}var o=n.delete(t);return o.onerror=function(e){r.resolve(!1)},o.onsuccess=function(e){r.resolve(!0)},r.promise},n.prototype.clear=function(e){var t=d.when.defer();if(!v(this.db))return t.resolve(!1),t.promise;if(!this.db.objectStoreNames.contains(e))return t.resolve(!1),t.promise;var r,n=this.db.transaction([e],"readwrite");try{r=n.objectStore(e)}catch(e){t.resolve(!1)}var c=r.clear();return c.onerror=function(e){t.resolve(!1)},c.onsuccess=function(e){t.resolve(!0)},t.promise};var l={};function h(e,t,r){null===r.scheduler?r.creatingDB||(r.creatingDB=!0,new n({name:e}).then(function(e){r.creatingDB=!1,(r.scheduler=e).checkObjectStoreExit(t)?(e.putElementInDB(t,null,null,r.cache),r.cache=[]):r.creatingTable||(r.creatingTable=!0,e.createObjectStore(t).then(function(){r.creatingTable=!1,e.putElementInDB(t,null,null,r.cache),r.cache=[]}))})):r.scheduler.checkObjectStoreExit(t)?(r.scheduler.putElementInDB(t,null,null,r.cache),r.cache=[]):r.creatingTable||(r.creatingTable=!0,r.scheduler.createObjectStore(t).then(function(){r.creatingTable=!1,r.scheduler.putElementInDB(t,null,null,r.cache),r.cache=[]}))}return e(function(e,t){var r=e.blob,n=e.key;if(void 0!==r&&void 0!==n){var c=e.tablename,o=e.dbname;void 0===l[u=o+c]&&(l[u]={cache:[],creatingDB:!1,scheduler:null,creatingTable:!1});var a=50;void 0!==e.reserveCount&&(a=e.reserveCount),l[u].cache.length<a&&l[u].cache.push({key:n,value:r})}else for(var s=e.nameArray,i=0;i<s.length;i++){var u;c=s[i].tablename,o=s[i].dbname,void 0!==l[u=o+c]&&0!==Object.keys(l[u].cache).length&&h(o,c,l[u])}})});