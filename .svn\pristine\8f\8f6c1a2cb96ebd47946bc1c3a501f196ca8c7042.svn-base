<template>
  <div id="Sbtzgl">
    <el-card class="card-content">
      <div class="content-option">
        <div class="content-title">维修台账管理</div>
        <el-form :inline="true" :model="formInline" ref="formInline" class="form-view">
          <el-form-item label="维修单号">
            <el-input placeholder="请输入维修单号" v-model="formInline.deviceName" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item>
          <el-form-item label="设备编号">
            <el-input placeholder="请输入设备编号" v-model="formInline.specificationModel" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item>
          <el-form-item label="工单状态">
            <el-input placeholder="请输入工单状态" v-model="formInline.manufacturer" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-plus" type="primary" @click="handleOperate(null)">新增台账</el-button>
            <!-- <el-button icon="el-icon-upload2" @click="handleImport">导入数据</el-button> -->
            <el-button icon="el-icon-download" @click="handleExport" :loading="loading">导出数据</el-button>
            <el-button @click="" :loading="loading" class="jump-to-screen">跳转至维保大屏</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="content-table">
        <!-- 设备编号、设备名称、故障简介、报修原因、维修时间、故障原因、故障图片、报修人和维修人 -->
        <el-table ref="table" :data="tableData" style="width: 100%"  border :header-cell-style="headerCellStyle" :cell-style="cellStyle">
                    <el-table-column type="index" width="50" label="序号" align="center" />
                    <el-table-column prop="repairNo" label="维修单号" align="center" width="120" />
                    <el-table-column prop="equipmentNo" label="设备编号" align="center" width="110" />
                    <el-table-column prop="equipmentName" label="设备名称" align="center" width="230" />
                    <el-table-column prop="faultIntro" label="故障简介" align="center" width="150" />
                    <el-table-column prop="repairReason" label="报修原因" align="center" min-width="160" />
                    <el-table-column prop="faultReason" label="故障原因" align="center" min-width="160" />
                    <el-table-column prop="startRepair" label="维修起始时间" align="center" min-width="120" />
                    <el-table-column prop="endRepair" label="维修结束时间" align="center" min-width="120" />
                    <el-table-column prop="submitPerson" label="报修人" align="center" min-width="80" />
                    <el-table-column prop="repairPerson" label="维修人" align="center" min-width="80" />
                    <el-table-column prop="status" label="工单状态" align="center" width="100">
                        <template v-slot="scope">
                           <el-select v-model="scope.row.status" placeholder="请选择">
                                <el-option
                                   v-for="item in statusOptions"
                                   :key="item.value"
                                   :label="item.label"
                                   :value="item.value">
                                </el-option>
                           </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="center" fixed="right">
                       <template slot-scope="scope">
                          <el-button @click="handleOperate(scope.row)" type="text" size="small" class="table-edit">编辑</el-button>
                            <el-popconfirm title="确定删除吗？" @confirm="handleDelete(scope.row.id, '/equip/ledger/deleteById')">
                               <el-button slot="reference" type="text" size="small"> 删除</el-button>
                           </el-popconfirm>
                       </template>
                   </el-table-column>
                    <!-- <el-table-column prop="endReason" label="结束原因" align="center" width="160" fixed="right"/> -->
        </el-table>
          
        <!-- 分页组件，用于处理表格数据的分页显示 -->
        <!-- <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
          :total="total">
        </el-pagination> -->
        <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
          :total="127" :pages="7">
        </el-pagination>
        
      </div>
    </el-card>
    <AddLedgerDialog v-if="dialogVisible" :curData="curData" :dialogVisible="dialogVisible" @onClose="onClose"
      @onSave="onSave" />
    <ImportDialog v-if="importvisible" :importvisible="importvisible" @onClose="handleClose" />
  </div>
</template>

<script>
import tableUtils from '@/mixins/tableUtils';
import AddLedgerDialog from './Dialog/AddLedgerDialog.vue';
import ImportDialog from './ImportDialog/ImportDialog.vue';
import { exportPlan } from '@/api/baseApi/common';
export default {
  name: 'Sbtzgl',
  mixins: [tableUtils],
  components: { AddLedgerDialog, ImportDialog },
  data() {
    return {
      // formInline: {},
      // dialogVisible: false,
      // curData: null,
      // tableData: [],
      // importvisible: false,
      // loading: false,
      formInline: {},
      dialogVisible: false,
      curData: null,
      tableData: [],
      importvisible: false,
      loading: false,
      
      startTime:'',
      endTime:'',
      statusOptions: [
          { value: '待处理', label: '待处理' },
          { value: '处理中', label: '处理中' },
          { value: '已完成', label: '已完成' }
      ],
      tableData:[
          {
              repairNo: '202407000001',
              equipmentNo: '387983076',//数据库中的设备编号，此为车辆
              equipmentName: '电动装载机-CR2003-CZTL-SB-146',
              faultIntro: '电池系统故障',
              repairReason: '设备续航能力受到影响',
              startRepair: '2024-07-17',
              endRepair: '2024-07-19',
              faultReason: '电池老化',
              submitPerson: '孙志远',
              repairPerson:'朱梓豪',
              status:'已完成',
          },
          {
              repairNo: '202407000002',
              equipmentNo: '387983077',
              equipmentName: '电动自卸车-CR2003-CZTL-SB-119',
              faultIntro: '机械臂故障',
              repairReason: '设备装卸能力受到影响',
              startRepair: '2024-07-20',
              endRepair: '2024-07-21',
              faultReason: '机械臂磨损',
              submitPerson: '孙志远',
              repairPerson:'侯浩然',
              status:'已完成',
          },
          {
              repairNo: '202407000003',
              equipmentNo: '1724979386367',
              equipmentName: '充电机-001',
              faultIntro: '充电接口损坏',
              repairReason: '充电机无法提供充电服务',
              startRepair: '2024-07-20',
              endRepair: '2024-07-22',
              faultReason: '该充电机使用频率高',
              submitPerson: '郑俊杰',
              repairPerson:'廖一飞',
              status:'已完成',
          },
          {
              repairNo: '202407000004',
              equipmentNo: '387983072',
              equipmentName: '电动装载机-CR2003-CZTL-SB-201',
              faultIntro: '控制系统故障',
              repairReason: '控制机器的过程中有异常',
              startRepair: '2024-07-22',
              endRepair: '2024-07-23',
              faultReason: '控制系统参数存在问题',
              submitPerson: '孙志远',
              repairPerson:'孔梓轩',
              status:'已完成',
          },
          {
              repairNo: '202407000005',
              equipmentNo: '1724979386368',
              equipmentName: '充电机-002',
              faultIntro: '该机充电线故障',
              repairReason: '无法提供充电服务',
              startRepair: '2024-07-25',
              endRepair: '2024-07-26',
              faultReason: '线缆接口处损坏',
              submitPerson: '郑俊杰',
              repairPerson:'廖一飞',
              status:'已完成',
          },
          {
              repairNo: '202407000006',
              equipmentNo: '387983079',
              equipmentName: '电动装载机-CR2003-CZTL-SB-203',
              faultIntro: '电机无法启动',
              repairReason: '设备完全无法运行',
              startRepair: '2024-07-25',
              endRepair: '2024-07-27',
              faultReason: '电机绕组短路或断路',
              submitPerson: '孙志远',
              repairPerson:'侯浩然',
              status:'已完成',
          },
          {
              repairNo: '202407000007',
              equipmentNo: '387983083',
              equipmentName: '电动装载机-CR2003-CZTL-SB-301',
              faultIntro: '液压油泄漏',
              repairReason: '造成液压油浪费',
              startRepair: '2024-07-27',
              endRepair: '2024-07-28',
              faultReason: '密封件老化',
              submitPerson: '孙志远',
              repairPerson:'孔梓轩',
              status:'已完成',
          },
          {
              repairNo: '202407000008',
              equipmentNo: '387983088',
              equipmentName: '电动自卸车-CR2003-CZTL-SB-121',
              faultIntro: '充电缓慢',
              repairReason: '耽误隧道作业',
              startRepair: '2024-07-27',
              endRepair: '2024-07-28',
              faultReason: '电池管理系统异常',
              submitPerson: '孙志远',
              repairPerson:'孔梓轩',
              status:'已完成',
          },
          {
              repairNo: '202407000009',
              equipmentNo: '1724979386370',
              equipmentName: '充电机-004',
              faultIntro: '充电机过热',
              repairReason: '电机过热引发安全问题',
              startRepair: '2024-07-27',
              endRepair: '2024-07-29',
              faultReason: '充电机散热系统故障',
              submitPerson: '郑俊杰',
              repairPerson:'廖一飞',
              status:'已完成',
          },
          {
              repairNo: '202407000010',
              equipmentNo: '387983085',
              equipmentName: '电动装载机-CR2003-CZTL-SB-303',
              faultIntro: '电机运行时有异常噪音',
              repairReason: '噪音较大影响工作',
              startRepair: '2024-07-28',
              endRepair: '2024-07-29',
              faultReason: '电机内部零件松动',
              submitPerson: '孙志远',
              repairPerson:'侯浩然',
              status:'已完成',
          },
          {
              repairNo: '202407000011',
              equipmentNo: '387983089',
              equipmentName: '电动自卸车-CR2003-CZTL-SB-192',
              faultIntro: '电机输出功率下降',
              repairReason: '车辆行驶速度减慢',
              startRepair: '2024-07-28',
              endRepair: '2024-07-29',
              faultReason: '电机控制器参数设置不当',
              submitPerson: '孙志远',
              repairPerson:'孔梓轩',
              status:'已完成',
          },
          {
              repairNo: '202407000012',
              equipmentNo: '3879830890',
              equipmentName: '电动自卸车-CR2003-CZTL-SB-193',
              faultIntro: '液压系统压力不足',
              repairReason: '耽误作业时间',
              startRepair: '2024-07-28',
              endRepair: '2024-07-30',
              faultReason: '液压泵故障',
              submitPerson: '孙志远',
              repairPerson:'孔梓轩',
              status:'已完成',
          },
          {
              repairNo: '202407000013',
              equipmentNo: '1724979386371',
              equipmentName: '充电机-005',
              faultIntro: '无法启动充电',
              repairReason: '影响设备的正常使用',
              startRepair: '2024-07-28',
              endRepair: '2024-07-29',
              faultReason: '充电机内部控制电路故障',
              submitPerson: '郑俊杰',
              repairPerson:'廖一飞',
              status:'已完成',
          },
          {
              repairNo: '202407000014',
              equipmentNo: '1724979386372',
              equipmentName: '充电机-006',
              faultIntro: '充电中断',
              repairReason: '频繁中断影响工作进度',
              startRepair: '2024-07-29',
              endRepair: '2024-07-30',
              faultReason: '电池故障触发保护机制',
              submitPerson: '郑俊杰',
              repairPerson:'廖一飞',
              status:'已完成',
          },
          {
              repairNo: '202407000015',
              equipmentNo: '387983092',
              equipmentName: '电动装载机-CR2003-CZTL-SB-305',
              faultIntro: '线路故障',
              repairReason: '影响操作安全和设备监控',
              startRepair: '2024-07-29',
              endRepair: '2024-07-30',
              faultReason: '线路短路',
              submitPerson: '孙志远',
              repairPerson:'侯浩然',
              status:'已完成',
          },
          {
              repairNo: '202407000016',
              equipmentNo: '387983095',
              equipmentName: '电动装载机-CR2003-CZTL-SB-308',
              faultIntro: '控制器故障',
              repairReason: '装载机操作失灵',
              startRepair: '2024-07-29',
              endRepair: '2024-07-31',
              faultReason: '控制器内部元件损坏',
              submitPerson: '孙志远',
              repairPerson:'侯浩然',
              status:'已完成',
          },
          {
              repairNo: '202408000001',
              equipmentNo: '387983096',
              equipmentName: '电动自卸车-CR2003-CZTL-SB-196',
              faultIntro: '车架变形',
              repairReason: '影响车辆稳定性和安全性',
              startRepair: '2024-08-02',
              endRepair: '2024-08-03',
              faultReason: '受到外力撞击',
              submitPerson: '孙志远',
              repairPerson:'孔梓轩',
              status:'已完成',
          },
          {
              repairNo: '202408000002',
              equipmentNo: '387983097',
              equipmentName: '电动自卸车-CR2003-CZTL-SB-197',
              faultIntro: '制动系统故障',
              repairReason: '行车安全无法保障',
              startRepair: '2024-08-02',
              endRepair: '2024-08-04',
              faultReason: '制动片磨损',
              submitPerson: '孙志远',
              repairPerson:'孔梓轩',
              status:'已完成',
          },
          {
              repairNo: '202408000003',
              equipmentNo: '1724979386374',
              equipmentName: '充电机-008',
              faultIntro: '充电显示异常',
              repairReason: '无法准确了解充电进度',
              startRepair: '2024-08-03',
              endRepair: '2024-08-04',
              faultReason: '控制板通信故障',
              submitPerson: '郑俊杰',
              repairPerson:'廖一飞',
              status:'已完成',
          },
          {
              repairNo: '202408000004',
              equipmentNo: '1724979386375',
              equipmentName: '充电机-009',
              faultIntro: '充电机噪声过大',
              repairReason: '噪音干扰环境',
              startRepair: '2024-08-04',
              endRepair: '2024-08-05',
              faultReason: '充电机内部风扇故障',
              submitPerson: '郑俊杰',
              repairPerson:'廖一飞',
              status:'已完成',
          }
          
      ],
      pageParams:{
          page: 1,
          limit: 20,
      },
      total:0,
      requestLoad : {
          sectionId: 0,
      },
    }
  },
  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.requestList();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    async requestList() {//目前为静态页面，注释此方法，此方法会从后台获取数据
      // let params = {
      //   ...this.formInline,
      //   sectionId: this.$store.state.tree.sectionId
      // };
      // let res = await this.$http.post(`/equip/ledger/page?page=${this.pageParams.page}&limit=${this.pageParams.limit}`, params);
      // this.tableData = res.result?.records;
      // this.total = res.result?.total;
    },

    async handleExport() {
      this.loading = true;
      let params = {
        ...this.formInline,
        sectionId: this.$store.state.tree.sectionId
      };
      let res = await exportPlan(params);
      var list = new Blob([res], {
        type: "application/vnd.ms-excel;charset=utf-8",
      });
      var downloadUrl = window.URL.createObjectURL(list);
      var anchor = document.createElement("a");
      anchor.href = downloadUrl;
      anchor.download = '设备台账管理表.xlsx';
      anchor.click();
      window.URL.revokeObjectURL(list);
      this.loading = false;
    },

    handleImport() {
      this.importvisible = true;
    },

    handleOperate(item) {
      if (item) {
        this.curData = { data: { ...item }, type: 2 }
      } else {
        this.curData = { data: {}, type: 1 }
      }
      this.dialogVisible = true
    },

    onClose() {
      this.dialogVisible = false;
    },

    handleClose() {
      this.requestList();
      this.importvisible = false;
    },

    onSave() {
      this.requestList();
      this.dialogVisible = false;
    }
  }
}
</script>

<style lang="scss" scoped>
#Sbtzgl {
  .content-option {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .content-title {
      line-height: 40px;
      font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
      font-weight: 700;
      font-style: normal;
      font-size: 18px;
      color: #3A3F5D;
      margin-right: 5%;
    }

    .form-view {
      flex: 1;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }

    ::v-deep .el-input {
      width: 175px;
    }
  }

  .table-edit {
    margin-right: 10px;
  }

  .pagination-part {
    margin-top: 20px;
  }
}
.jump-to-screen {
  margin-left: 320px;
  background-color: #379de5;
  color: white;
}
</style>