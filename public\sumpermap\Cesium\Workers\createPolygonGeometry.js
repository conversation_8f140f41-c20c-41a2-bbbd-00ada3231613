/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./GeometryAttributes-aacecde6","./AttributeCompression-75ce15eb","./GeometryPipeline-8e55e413","./EncodedCartesian3-87cd0c1f","./IndexDatatype-9435b55f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./arrayFill-9766fb2e","./GeometryOffsetAttribute-999fc023","./VertexFormat-fe4db402","./GeometryInstance-9ddb8c73","./arrayRemoveDuplicates-2869246d","./BoundingRectangle-3d4f3d01","./EllipsoidTangentPlane-9c25b2da","./ArcType-66bc286a","./EllipsoidRhumbLine-6ca4b1e6","./earcut-2.2.1-b404d9e6","./PolygonPipeline-cc78b34e","./PolygonGeometryLibrary-ec05daff","./EllipsoidGeodesic-db2069b3"],function(Y,b,j,W,Q,q,e,t,r,K,Z,o,a,J,i,n,s,O,l,N,u,p,X,$,v,R,c,y,D,d,g,m,L,M,h){var f=new W.Cartographic,_=new W.Cartographic;function ee(e,t,r,o){var a=o.cartesianToCartographic(e,f).height,i=o.cartesianToCartographic(t,_);i.height=a,o.cartographicToCartesian(i,t);var n=o.cartesianToCartographic(r,_);n.height=a-100,o.cartographicToCartesian(n,r)}var S=new y.BoundingRectangle,te=new W.Cartesian3,re=new W.Cartesian3,oe=new W.Cartesian3,ae=new W.Cartesian3,ie=new W.Cartesian3,ne=new W.Cartesian3,se=new W.Cartesian3,le=new W.Cartesian3,ue=new W.Cartesian3,pe=new Q.Cartesian2,ce=new Q.Cartesian2,ye=new W.Cartesian3,de=new J.Quaternion,ge=new q.Matrix3,me=new q.Matrix3;function k(e){var t=e.vertexFormat,r=e.geometry,o=e.shadowVolume,a=r.attributes.position.values,i=a.length,n=e.wall,s=e.top||n,l=e.bottom||n;if(t.st||t.normal||t.tangent||t.bitangent||o){var u=e.boundingRectangle,p=e.tangentPlane,c=e.ellipsoid,y=e.stRotation,d=e.perPositionHeight,g=pe;g.x=u.x,g.y=u.y;var m,h=t.st?new Float32Array(i/3*2):void 0;t.normal&&(m=d&&s&&!n?r.attributes.normal.values:new Float32Array(i));var f=t.tangent?new Float32Array(i):void 0,b=t.bitangent?new Float32Array(i):void 0,v=o?new Float32Array(i):void 0,_=0,P=0,C=re,T=oe,w=ae,x=!0,A=ge,E=me;if(0!==y){var I=J.Quaternion.fromAxisAngle(p._plane.normal,y,de);A=q.Matrix3.fromQuaternion(I,A),I=J.Quaternion.fromAxisAngle(p._plane.normal,-y,de),E=q.Matrix3.fromQuaternion(I,E)}else A=q.Matrix3.clone(q.Matrix3.IDENTITY,A),E=q.Matrix3.clone(q.Matrix3.IDENTITY,E);var G=0,H=0;s&&l&&(G=i/2,H=i/3,i/=2);for(var V=0;V<i;V+=3){var F=W.Cartesian3.fromArray(a,V,ye);if(t.st){var O=q.Matrix3.multiplyByVector(A,F,te);O=c.scaleToGeodeticSurface(O,O);var N=p.projectPointOntoPlane(O,ce);Q.Cartesian2.subtract(N,g,N);var R=j.CesiumMath.clamp(N.x/u.width,0,1),D=j.CesiumMath.clamp(N.y/u.height,0,1);l&&(h[_+H]=R,h[_+1+H]=D),s&&(h[_]=R,h[_+1]=D),_+=2}if(t.normal||t.tangent||t.bitangent||o){var L=P+1,M=P+2;if(n){if(V+3<i){var S=W.Cartesian3.fromArray(a,V+3,ie);if(x){var k=W.Cartesian3.fromArray(a,V+i,ne);d&&ee(F,S,k,c),W.Cartesian3.subtract(S,F,S),W.Cartesian3.subtract(k,F,k),C=W.Cartesian3.normalize(W.Cartesian3.cross(k,S,C),C),x=!1}W.Cartesian3.equalsEpsilon(S,F,j.CesiumMath.EPSILON10)&&(x=!0)}(t.tangent||t.bitangent)&&(w=c.geodeticSurfaceNormal(F,w),t.tangent&&(T=W.Cartesian3.normalize(W.Cartesian3.cross(w,C,T),T)))}else C=c.geodeticSurfaceNormal(F,C),(t.tangent||t.bitangent)&&(d&&(se=W.Cartesian3.fromArray(m,P,se),le=W.Cartesian3.cross(W.Cartesian3.UNIT_Z,se,le),le=W.Cartesian3.normalize(q.Matrix3.multiplyByVector(E,le,le),le),t.bitangent&&(ue=W.Cartesian3.normalize(W.Cartesian3.cross(se,le,ue),ue))),T=W.Cartesian3.cross(W.Cartesian3.UNIT_Z,C,T),T=W.Cartesian3.normalize(q.Matrix3.multiplyByVector(E,T,T),T),t.bitangent&&(w=W.Cartesian3.normalize(W.Cartesian3.cross(C,T,w),w)));t.normal&&(e.wall?(m[P+G]=C.x,m[L+G]=C.y,m[M+G]=C.z):l&&(m[P+G]=-C.x,m[L+G]=-C.y,m[M+G]=-C.z),(s&&!d||n)&&(m[P]=C.x,m[L]=C.y,m[M]=C.z)),o&&(n&&(C=c.geodeticSurfaceNormal(F,C)),v[P+G]=-C.x,v[L+G]=-C.y,v[M+G]=-C.z),t.tangent&&(e.wall?(f[P+G]=T.x,f[L+G]=T.y,f[M+G]=T.z):l&&(f[P+G]=-T.x,f[L+G]=-T.y,f[M+G]=-T.z),s&&(f[M]=d?(f[P]=le.x,f[L]=le.y,le.z):(f[P]=T.x,f[L]=T.y,T.z))),t.bitangent&&(l&&(b[P+G]=w.x,b[L+G]=w.y,b[M+G]=w.z),s&&(b[M]=d?(b[P]=ue.x,b[L]=ue.y,ue.z):(b[P]=w.x,b[L]=w.y,w.z))),P+=3}}t.st&&(r.attributes.st=new Z.GeometryAttribute({componentDatatype:K.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:h})),t.normal&&(r.attributes.normal=new Z.GeometryAttribute({componentDatatype:K.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:m})),t.tangent&&(r.attributes.tangent=new Z.GeometryAttribute({componentDatatype:K.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:f})),t.bitangent&&(r.attributes.bitangent=new Z.GeometryAttribute({componentDatatype:K.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:b})),o&&(r.attributes.extrudeDirection=new Z.GeometryAttribute({componentDatatype:K.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:v}))}if(e.extrude&&Y.defined(e.offsetAttribute)){var B=a.length/3,z=new Uint8Array(B);if(e.offsetAttribute===$.GeometryOffsetAttribute.TOP)s&&l||n?z=X.arrayFill(z,1,0,B/2):s&&(z=X.arrayFill(z,1));else{var U=e.offsetAttribute===$.GeometryOffsetAttribute.NONE?0:1;z=X.arrayFill(z,U)}r.attributes.applyOffset=new Z.GeometryAttribute({componentDatatype:K.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:z})}return r}var P=new W.Cartographic,C=new W.Cartographic,T={west:0,east:0},w=new h.EllipsoidGeodesic;function x(e,t,r,o,a){if(a=Y.defaultValue(a,new Q.Rectangle),!Y.defined(e)||e.length<3)return a.west=0,a.north=0,a.south=0,a.east=0,a;if(r===d.ArcType.RHUMB)return Q.Rectangle.fromCartesianArray(e,t,a);w.ellipsoid.equals(t)||(w=new h.EllipsoidGeodesic(void 0,void 0,t)),a.west=Number.POSITIVE_INFINITY,a.east=Number.NEGATIVE_INFINITY,a.south=Number.POSITIVE_INFINITY,a.north=Number.NEGATIVE_INFINITY,T.west=Number.POSITIVE_INFINITY,T.east=Number.NEGATIVE_INFINITY;for(var i,n=1/j.CesiumMath.chordLength(o,t.maximumRadius),s=e.length,l=t.cartesianToCartographic(e[0],C),u=P,p=1;p<s;p++)i=u,u=l,l=t.cartesianToCartographic(e[p],i),w.setEndPoints(u,l),E(w,n,a,T);return i=u,u=l,l=t.cartesianToCartographic(e[0],i),w.setEndPoints(u,l),E(w,n,a,T),a.east-a.west>T.west-T.east&&(a.east=T.east,a.west=T.west),a}var A=new W.Cartographic;function E(e,t,r,o){for(var a=e.surfaceDistance,i=Math.ceil(a*t),n=0<i?a/(i-1):Number.POSITIVE_INFINITY,s=0,l=0;l<i;l++){var u=e.interpolateUsingSurfaceDistance(s,A);s+=n;var p=u.longitude,c=u.latitude;r.west=Math.min(r.west,p),r.east=Math.max(r.east,p),r.south=Math.min(r.south,c),r.north=Math.max(r.north,c),o.west=0<p?Math.min(p,o.west):o.west,o.east=p<0?Math.max(p,o.east):o.east}}var V=[];function B(e,t,r,o,a,i,n,s,l,u){var p,c={walls:[]};if(i||n){var y,d,g=M.PolygonGeometryLibrary.createGeometryFromPositions(e,t,r,a,s,l),m=g.attributes.position.values,h=g.indices;if(i&&n){var f=m.concat(m);y=f.length/3,(d=N.IndexDatatype.createTypedArray(y,2*h.length)).set(h);var b=h.length,v=y/2;for(p=0;p<b;p+=3){var _=d[p]+v,P=d[p+1]+v,C=d[p+2]+v;d[p+b]=C,d[p+1+b]=P,d[p+2+b]=_}if(g.attributes.position.values=f,a&&s.normal){var T=g.attributes.normal.values;g.attributes.normal.values=new Float32Array(f.length),g.attributes.normal.values.set(T)}g.indices=d}else if(n){for(y=m.length/3,d=N.IndexDatatype.createTypedArray(y,h.length),p=0;p<h.length;p+=3)d[p]=h[p+2],d[p+1]=h[p+1],d[p+2]=h[p];g.indices=d}c.topAndBottom=new R.GeometryInstance({geometry:g})}var w,x=o.outerRing,A=D.EllipsoidTangentPlane.fromPoints(x,e),E=A.projectPointsOntoPlane(x,V),I=L.PolygonPipeline.computeWindingOrder2D(E);I===L.WindingOrder.CLOCKWISE&&(x=x.slice().reverse()),u&&(w=M.PolygonGeometryLibrary.computeWallGeometry(x,e,r,a,l),c.walls.push(new R.GeometryInstance({geometry:w})));var G=o.holes;for(p=0;p<G.length;p++){var H=G[p];E=(A=D.EllipsoidTangentPlane.fromPoints(H,e)).projectPointsOntoPlane(H,V),(I=L.PolygonPipeline.computeWindingOrder2D(E))===L.WindingOrder.COUNTER_CLOCKWISE&&(H=H.slice().reverse()),w=M.PolygonGeometryLibrary.computeWallGeometry(H,e,r,a,l),c.walls.push(new R.GeometryInstance({geometry:w}))}return c}function I(e){if(b.Check.typeOf.object("options",e),b.Check.typeOf.object("options.polygonHierarchy",e.polygonHierarchy),Y.defined(e.perPositionHeight)&&e.perPositionHeight&&Y.defined(e.height))throw new b.DeveloperError("Cannot use both options.perPositionHeight and options.height");if(Y.defined(e.arcType)&&e.arcType!==d.ArcType.GEODESIC&&e.arcType!==d.ArcType.RHUMB)throw new b.DeveloperError("Invalid arcType. Valid options are ArcType.GEODESIC and ArcType.RHUMB.");var t=e.polygonHierarchy,r=Y.defaultValue(e.vertexFormat,v.VertexFormat.DEFAULT),o=Y.defaultValue(e.ellipsoid,Q.Ellipsoid.WGS84),a=Y.defaultValue(e.granularity,j.CesiumMath.RADIANS_PER_DEGREE),i=Y.defaultValue(e.stRotation,0),n=Y.defaultValue(e.perPositionHeight,!1),s=n&&Y.defined(e.extrudedHeight),l=Y.defaultValue(e.height,0),u=Y.defaultValue(e.extrudedHeight,l);if(!s){var p=Math.max(l,u);u=Math.min(l,u),l=p}this._vertexFormat=v.VertexFormat.clone(r),this._ellipsoid=Q.Ellipsoid.clone(o),this._granularity=a,this._stRotation=i,this._height=l,this._extrudedHeight=u,this._closeTop=Y.defaultValue(e.closeTop,!0),this._closeBottom=Y.defaultValue(e.closeBottom,!0),this._extrudeOutering=Y.defaultValue(e.extrudeOutering,!0),this._polygonHierarchy=t,this._perPositionHeight=n,this._perPositionHeightExtrude=s,this._shadowVolume=Y.defaultValue(e.shadowVolume,!1),this._workerName="createPolygonGeometry",this._offsetAttribute=e.offsetAttribute,this._arcType=Y.defaultValue(e.arcType,d.ArcType.GEODESIC),this._rectangle=void 0,this._textureCoordinateRotationPoints=void 0,this.packedLength=M.PolygonGeometryLibrary.computeHierarchyPackedLength(t)+Q.Ellipsoid.packedLength+v.VertexFormat.packedLength+12}I.fromPositions=function(e){return e=Y.defaultValue(e,Y.defaultValue.EMPTY_OBJECT),b.Check.defined("options.positions",e.positions),new I({polygonHierarchy:{positions:e.positions},height:e.height,extrudedHeight:e.extrudedHeight,vertexFormat:e.vertexFormat,stRotation:e.stRotation,ellipsoid:e.ellipsoid,granularity:e.granularity,perPositionHeight:e.perPositionHeight,closeTop:e.closeTop,closeBottom:e.closeBottom,offsetAttribute:e.offsetAttribute,arcType:e.arcType})},I.pack=function(e,t,r){return b.Check.typeOf.object("value",e),b.Check.defined("array",t),r=Y.defaultValue(r,0),r=M.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,r),Q.Ellipsoid.pack(e._ellipsoid,t,r),r+=Q.Ellipsoid.packedLength,v.VertexFormat.pack(e._vertexFormat,t,r),r+=v.VertexFormat.packedLength,t[r++]=e._height,t[r++]=e._extrudedHeight,t[r++]=e._granularity,t[r++]=e._stRotation,t[r++]=e._perPositionHeightExtrude?1:0,t[r++]=e._perPositionHeight?1:0,t[r++]=e._closeTop?1:0,t[r++]=e._closeBottom?1:0,t[r++]=e._shadowVolume?1:0,t[r++]=Y.defaultValue(e._offsetAttribute,-1),t[r++]=e._arcType,t[r]=e.packedLength,t};var G=Q.Ellipsoid.clone(Q.Ellipsoid.UNIT_SPHERE),H=new v.VertexFormat,F={polygonHierarchy:{}};return I.unpack=function(e,t,r){b.Check.defined("array",e),t=Y.defaultValue(t,0);var o=M.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t);t=o.startingIndex,delete o.startingIndex;var a=Q.Ellipsoid.unpack(e,t,G);t+=Q.Ellipsoid.packedLength;var i=v.VertexFormat.unpack(e,t,H);t+=v.VertexFormat.packedLength;var n=e[t++],s=e[t++],l=e[t++],u=e[t++],p=1===e[t++],c=1===e[t++],y=1===e[t++],d=1===e[t++],g=1===e[t++],m=e[t++],h=e[t++],f=e[t];return Y.defined(r)||(r=new I(F)),r._polygonHierarchy=o,r._ellipsoid=Q.Ellipsoid.clone(a,r._ellipsoid),r._vertexFormat=v.VertexFormat.clone(i,r._vertexFormat),r._height=n,r._extrudedHeight=s,r._granularity=l,r._stRotation=u,r._perPositionHeightExtrude=p,r._perPositionHeight=c,r._closeTop=y,r._closeBottom=d,r._shadowVolume=g,r._offsetAttribute=-1===m?void 0:m,r._arcType=h,r.packedLength=f,r},I.computeRectangle=function(e,t){b.Check.typeOf.object("options",e),b.Check.typeOf.object("options.polygonHierarchy",e.polygonHierarchy);var r=Y.defaultValue(e.granularity,j.CesiumMath.RADIANS_PER_DEGREE),o=Y.defaultValue(e.arcType,d.ArcType.GEODESIC);if(o!==d.ArcType.GEODESIC&&o!==d.ArcType.RHUMB)throw new b.DeveloperError("Invalid arcType. Valid options are ArcType.GEODESIC and ArcType.RHUMB.");var a=e.polygonHierarchy,i=Y.defaultValue(e.ellipsoid,Q.Ellipsoid.WGS84);return x(a.positions,i,o,r,t)},I.createGeometry=function(e){var t=e._vertexFormat,r=e._ellipsoid,o=e._granularity,a=e._stRotation,i=e._polygonHierarchy,n=e._perPositionHeight,s=e._closeTop,l=e._closeBottom,u=e._arcType,p=i.positions;if(!(p.length<3)){var c=D.EllipsoidTangentPlane.fromPoints(p,r),y=M.PolygonGeometryLibrary.polygonsFromHierarchy(i,c.projectPointsOntoPlane.bind(c),!n,r),d=y.hierarchy,g=y.polygons;if(0!==d.length){p=d[0].outerRing;var m,h=M.PolygonGeometryLibrary.computeBoundingRectangle(c.plane.normal,c.projectPointOntoPlane.bind(c),p,a,S),f=[],b=e._height,v=e._extrudedHeight,_={perPositionHeight:n,vertexFormat:t,geometry:void 0,tangentPlane:c,boundingRectangle:h,ellipsoid:r,stRotation:a,bottom:!1,top:!0,wall:!1,extrude:!1,arcType:u};if(e._perPositionHeightExtrude||!j.CesiumMath.equalsEpsilon(b,v,0,j.CesiumMath.EPSILON2))for(_.extrude=!0,_.top=s,_.bottom=l,_.shadowVolume=e._shadowVolume,_.offsetAttribute=e._offsetAttribute,m=0;m<g.length;m++){var P,C=B(r,g[m],o,d[m],n,s,l,t,u,e._extrudeOutering);s&&l?(P=C.topAndBottom,_.geometry=M.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(P.geometry,b,v,r,n)):s?((P=C.topAndBottom).geometry.attributes.position.values=L.PolygonPipeline.scaleToGeodeticHeight(P.geometry.attributes.position.values,b,r,!n),_.geometry=P.geometry):l&&((P=C.topAndBottom).geometry.attributes.position.values=L.PolygonPipeline.scaleToGeodeticHeight(P.geometry.attributes.position.values,v,r,!0),_.geometry=P.geometry),(s||l)&&(_.wall=!1,P.geometry=k(_),f.push(P));var T=C.walls;_.wall=!0;for(var w=0;w<T.length;w++){var x=T[w];_.geometry=M.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(x.geometry,b,v,r,n),x.geometry=k(_),f.push(x)}}else for(m=0;m<g.length;m++){var A=new R.GeometryInstance({geometry:M.PolygonGeometryLibrary.createGeometryFromPositions(r,g[m],o,n,t,u)});if(A.geometry.attributes.position.values=L.PolygonPipeline.scaleToGeodeticHeight(A.geometry.attributes.position.values,b,r,!n),_.geometry=A.geometry,A.geometry=k(_),Y.defined(e._offsetAttribute)){var E=A.geometry.attributes.position.values.length,I=new Uint8Array(E/3),G=e._offsetAttribute===$.GeometryOffsetAttribute.NONE?0:1;X.arrayFill(I,G),A.geometry.attributes.applyOffset=new Z.GeometryAttribute({componentDatatype:K.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:I})}f.push(A)}var H=O.GeometryPipeline.combineInstances(f)[0];H.attributes.position.values=new Float64Array(H.attributes.position.values),H.indices=N.IndexDatatype.createTypedArray(H.attributes.position.values.length/3,H.indices);var V=H.attributes,F=q.BoundingSphere.fromVertices(V.position.values);return t.position||delete V.position,new Z.Geometry({attributes:V,indices:H.indices,primitiveType:H.primitiveType,boundingSphere:F,offsetAttribute:e._offsetAttribute})}}},I.createShadowVolume=function(e,t,r){var o=e._granularity,a=e._ellipsoid,i=t(o,a),n=r(o,a);return new I({polygonHierarchy:e._polygonHierarchy,ellipsoid:a,stRotation:e._stRotation,granularity:o,perPositionHeight:!1,extrudedHeight:i,height:n,vertexFormat:v.VertexFormat.POSITION_ONLY,shadowVolume:!0,arcType:e._arcType})},Object.defineProperties(I.prototype,{rectangle:{get:function(){if(!Y.defined(this._rectangle)){var e=this._polygonHierarchy.positions;this._rectangle=x(e,this._ellipsoid,this._arcType,this._granularity)}return this._rectangle}},textureCoordinateRotationPoints:{get:function(){return Y.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(e){var t=-e._stRotation;if(0===t)return[0,0,0,1,1,0];var r=e._ellipsoid,o=e._polygonHierarchy.positions,a=e.rectangle;return Z.Geometry._textureCoordinateRotationPoints(o,t,r,a)}(this)),this._textureCoordinateRotationPoints}}}),function(e,t){return Y.defined(t)&&(e=I.unpack(e,t)),e._ellipsoid=Q.Ellipsoid.clone(e._ellipsoid),I.createGeometry(e)}});