/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./PrimitiveType-97893bc7","./Transforms-1509c877"],function(e,i,u,N,A,I,t,V){var r=Object.freeze({NONE:0,TRIANGLES:1,LINES:2,POLYLINES:3});function q(e,t,r,n){this[0]=i.defaultValue(e,0),this[1]=i.defaultValue(r,0),this[2]=i.defaultValue(t,0),this[3]=i.defaultValue(n,0)}q.packedLength=4,q.pack=function(e,t,r){return u.Check.typeOf.object("value",e),u.Check.defined("array",t),r=i.defaultValue(r,0),t[r++]=e[0],t[r++]=e[1],t[r++]=e[2],t[r++]=e[3],t},q.unpack=function(e,t,r){return u.Check.defined("array",e),t=i.defaultValue(t,0),i.defined(r)||(r=new q),r[0]=e[t++],r[1]=e[t++],r[2]=e[t++],r[3]=e[t++],r},q.clone=function(e,t){if(i.defined(e))return i.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):new q(e[0],e[2],e[1],e[3])},q.fromArray=function(e,t,r){return u.Check.defined("array",e),t=i.defaultValue(t,0),i.defined(r)||(r=new q),r[0]=e[t],r[1]=e[t+1],r[2]=e[t+2],r[3]=e[t+3],r},q.fromColumnMajorArray=function(e,t){return u.Check.defined("values",e),q.clone(e,t)},q.fromRowMajorArray=function(e,t){return u.Check.defined("values",e),i.defined(t)?(t[0]=e[0],t[1]=e[2],t[2]=e[1],t[3]=e[3],t):new q(e[0],e[1],e[2],e[3])},q.fromScale=function(e,t){return u.Check.typeOf.object("scale",e),i.defined(t)?(t[0]=e.x,t[1]=0,t[2]=0,t[3]=e.y,t):new q(e.x,0,0,e.y)},q.fromUniformScale=function(e,t){return u.Check.typeOf.number("scale",e),i.defined(t)?(t[0]=e,t[1]=0,t[2]=0,t[3]=e,t):new q(e,0,0,e)},q.fromRotation=function(e,t){u.Check.typeOf.number("angle",e);var r=Math.cos(e),n=Math.sin(e);return i.defined(t)?(t[0]=r,t[1]=n,t[2]=-n,t[3]=r,t):new q(r,-n,n,r)},q.toArray=function(e,t){return u.Check.typeOf.object("matrix",e),i.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):[e[0],e[1],e[2],e[3]]},q.getElementIndex=function(e,t){return u.Check.typeOf.number.greaterThanOrEquals("row",t,0),u.Check.typeOf.number.lessThanOrEquals("row",t,1),u.Check.typeOf.number.greaterThanOrEquals("column",e,0),u.Check.typeOf.number.lessThanOrEquals("column",e,1),2*e+t},q.getColumn=function(e,t,r){u.Check.typeOf.object("matrix",e),u.Check.typeOf.number.greaterThanOrEquals("index",t,0),u.Check.typeOf.number.lessThanOrEquals("index",t,1),u.Check.typeOf.object("result",r);var n=2*t,a=e[n],i=e[n+1];return r.x=a,r.y=i,r},q.setColumn=function(e,t,r,n){u.Check.typeOf.object("matrix",e),u.Check.typeOf.number.greaterThanOrEquals("index",t,0),u.Check.typeOf.number.lessThanOrEquals("index",t,1),u.Check.typeOf.object("cartesian",r),u.Check.typeOf.object("result",n);var a=2*t;return(n=q.clone(e,n))[a]=r.x,n[a+1]=r.y,n},q.getRow=function(e,t,r){u.Check.typeOf.object("matrix",e),u.Check.typeOf.number.greaterThanOrEquals("index",t,0),u.Check.typeOf.number.lessThanOrEquals("index",t,1),u.Check.typeOf.object("result",r);var n=e[t],a=e[t+2];return r.x=n,r.y=a,r},q.setRow=function(e,t,r,n){return u.Check.typeOf.object("matrix",e),u.Check.typeOf.number.greaterThanOrEquals("index",t,0),u.Check.typeOf.number.lessThanOrEquals("index",t,1),u.Check.typeOf.object("cartesian",r),u.Check.typeOf.object("result",n),(n=q.clone(e,n))[t]=r.x,n[t+2]=r.y,n};var n=new A.Cartesian2;q.getScale=function(e,t){return u.Check.typeOf.object("matrix",e),u.Check.typeOf.object("result",t),t.x=A.Cartesian2.magnitude(A.Cartesian2.fromElements(e[0],e[1],n)),t.y=A.Cartesian2.magnitude(A.Cartesian2.fromElements(e[2],e[3],n)),t};var a=new A.Cartesian2;function o(e){e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT),u.Check.typeOf.object("options.attributes",e.attributes),this.attributes=e.attributes,this.indices=e.indices,this.primitiveType=i.defaultValue(e.primitiveType,t.PrimitiveType.TRIANGLES),this.boundingSphere=e.boundingSphere,this.geometryType=i.defaultValue(e.geometryType,r.NONE),this.boundingSphereCV=e.boundingSphereCV,this.offsetAttribute=e.offsetAttribute}q.getMaximumScale=function(e){return q.getScale(e,a),A.Cartesian2.maximumComponent(a)},q.multiply=function(e,t,r){u.Check.typeOf.object("left",e),u.Check.typeOf.object("right",t),u.Check.typeOf.object("result",r);var n=e[0]*t[0]+e[2]*t[1],a=e[0]*t[2]+e[2]*t[3],i=e[1]*t[0]+e[3]*t[1],o=e[1]*t[2]+e[3]*t[3];return r[0]=n,r[1]=i,r[2]=a,r[3]=o,r},q.add=function(e,t,r){return u.Check.typeOf.object("left",e),u.Check.typeOf.object("right",t),u.Check.typeOf.object("result",r),r[0]=e[0]+t[0],r[1]=e[1]+t[1],r[2]=e[2]+t[2],r[3]=e[3]+t[3],r},q.subtract=function(e,t,r){return u.Check.typeOf.object("left",e),u.Check.typeOf.object("right",t),u.Check.typeOf.object("result",r),r[0]=e[0]-t[0],r[1]=e[1]-t[1],r[2]=e[2]-t[2],r[3]=e[3]-t[3],r},q.multiplyByVector=function(e,t,r){u.Check.typeOf.object("matrix",e),u.Check.typeOf.object("cartesian",t),u.Check.typeOf.object("result",r);var n=e[0]*t.x+e[2]*t.y,a=e[1]*t.x+e[3]*t.y;return r.x=n,r.y=a,r},q.multiplyByScalar=function(e,t,r){return u.Check.typeOf.object("matrix",e),u.Check.typeOf.number("scalar",t),u.Check.typeOf.object("result",r),r[0]=e[0]*t,r[1]=e[1]*t,r[2]=e[2]*t,r[3]=e[3]*t,r},q.multiplyByScale=function(e,t,r){return u.Check.typeOf.object("matrix",e),u.Check.typeOf.object("scale",t),u.Check.typeOf.object("result",r),r[0]=e[0]*t.x,r[1]=e[1]*t.x,r[2]=e[2]*t.y,r[3]=e[3]*t.y,r},q.negate=function(e,t){return u.Check.typeOf.object("matrix",e),u.Check.typeOf.object("result",t),t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t},q.transpose=function(e,t){u.Check.typeOf.object("matrix",e),u.Check.typeOf.object("result",t);var r=e[0],n=e[2],a=e[1],i=e[3];return t[0]=r,t[1]=n,t[2]=a,t[3]=i,t},q.abs=function(e,t){return u.Check.typeOf.object("matrix",e),u.Check.typeOf.object("result",t),t[0]=Math.abs(e[0]),t[1]=Math.abs(e[1]),t[2]=Math.abs(e[2]),t[3]=Math.abs(e[3]),t},q.equals=function(e,t){return e===t||i.defined(e)&&i.defined(t)&&e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]},q.equalsArray=function(e,t,r){return e[0]===t[r]&&e[1]===t[r+1]&&e[2]===t[r+2]&&e[3]===t[r+3]},q.equalsEpsilon=function(e,t,r){return u.Check.typeOf.number("epsilon",r),e===t||i.defined(e)&&i.defined(t)&&Math.abs(e[0]-t[0])<=r&&Math.abs(e[1]-t[1])<=r&&Math.abs(e[2]-t[2])<=r&&Math.abs(e[3]-t[3])<=r},q.IDENTITY=Object.freeze(new q(1,0,0,1)),q.ZERO=Object.freeze(new q(0,0,0,0)),q.COLUMN0ROW0=0,q.COLUMN0ROW1=1,q.COLUMN1ROW0=2,q.COLUMN1ROW1=3,Object.defineProperties(q.prototype,{length:{get:function(){return q.packedLength}}}),q.prototype.clone=function(e){return q.clone(this,e)},q.prototype.equals=function(e){return q.equals(this,e)},q.prototype.equalsEpsilon=function(e,t){return q.equalsEpsilon(this,e,t)},q.prototype.toString=function(){return"("+this[0]+", "+this[2]+")\n("+this[1]+", "+this[3]+")"},o.computeNumberOfVertices=function(e){u.Check.typeOf.object("geometry",e);var t=-1;for(var r in e.attributes)if(e.attributes.hasOwnProperty(r)&&i.defined(e.attributes[r])&&i.defined(e.attributes[r].values)){var n=e.attributes[r],a=n.values.length/n.componentsPerAttribute;if(t!==a&&-1!==t)throw new u.DeveloperError("All attribute lists must have the same number of attributes.");t=a}return t};var P=new N.Cartographic,S=new N.Cartesian3,R=new I.Matrix4,L=[new N.Cartographic,new N.Cartographic,new N.Cartographic],B=[new A.Cartesian2,new A.Cartesian2,new A.Cartesian2],D=[new A.Cartesian2,new A.Cartesian2,new A.Cartesian2],Y=new N.Cartesian3,_=new V.Quaternion,G=new I.Matrix4,U=new q;o._textureCoordinateRotationPoints=function(e,t,r,n){var a,i=A.Rectangle.center(n,P),o=N.Cartographic.toCartesian(i,r,S),u=V.Transforms.eastNorthUpToFixedFrame(o,r,R),c=I.Matrix4.inverse(u,R),s=B,f=L;f[0].longitude=n.west,f[0].latitude=n.south,f[1].longitude=n.west,f[1].latitude=n.north,f[2].longitude=n.east,f[2].latitude=n.south;var l=Y;for(a=0;a<3;a++)N.Cartographic.toCartesian(f[a],r,l),l=I.Matrix4.multiplyByPointAsVector(c,l,l),s[a].x=l.x,s[a].y=l.y;var h=V.Quaternion.fromAxisAngle(N.Cartesian3.UNIT_Z,-t,_),p=I.Matrix3.fromQuaternion(h,G),y=e.length,b=Number.POSITIVE_INFINITY,m=Number.POSITIVE_INFINITY,C=Number.NEGATIVE_INFINITY,d=Number.NEGATIVE_INFINITY;for(a=0;a<y;a++)l=I.Matrix4.multiplyByPointAsVector(c,e[a],l),l=I.Matrix3.multiplyByVector(p,l,l),b=Math.min(b,l.x),m=Math.min(m,l.y),C=Math.max(C,l.x),d=Math.max(d,l.y);var O=q.fromRotation(t,U),k=D;k[0].x=b,k[0].y=m,k[1].x=b,k[1].y=d,k[2].x=C,k[2].y=m;var x=s[0],w=s[2].x-x.x,g=s[1].y-x.y;for(a=0;a<3;a++){var j=k[a];q.multiplyByVector(O,j,j),j.x=(j.x-x.x)/w,j.y=(j.y-x.y)/g}var v=k[0],E=k[1],T=k[2],M=new Array(6);return A.Cartesian2.pack(v,M),A.Cartesian2.pack(E,M,2),A.Cartesian2.pack(T,M,4),M},e.Geometry=o,e.GeometryAttribute=function(e){if(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT),!i.defined(e.componentDatatype))throw new u.DeveloperError("options.componentDatatype is required.");if(!i.defined(e.componentsPerAttribute))throw new u.DeveloperError("options.componentsPerAttribute is required.");if(e.componentsPerAttribute<1||4<e.componentsPerAttribute)throw new u.DeveloperError("options.componentsPerAttribute must be between 1 and 4.");if(!i.defined(e.values))throw new u.DeveloperError("options.values is required.");this.componentDatatype=e.componentDatatype,this.componentsPerAttribute=e.componentsPerAttribute,this.normalize=i.defaultValue(e.normalize,!1),this.values=e.values},e.GeometryType=r,e.Matrix2=q});