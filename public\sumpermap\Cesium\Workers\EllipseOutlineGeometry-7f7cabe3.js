/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./GeometryAttributes-aacecde6","./IndexDatatype-9435b55f","./arrayFill-9766fb2e","./GeometryOffsetAttribute-999fc023","./EllipseGeometryLibrary-d33811c0"],function(e,A,c,_,g,m,v,E,x,u,M,w,C,D,G){var L=new g.Cartesian3,d=new g.Cartesian3;var O=new v.BoundingSphere,S=new v.BoundingSphere;function h(e){var t=(e=A.defaultValue(e,A.defaultValue.EMPTY_OBJECT)).center,i=A.defaultValue(e.ellipsoid,m.Ellipsoid.WGS84),r=e.semiMajorAxis,a=e.semiMinorAxis,n=A.defaultValue(e.granularity,_.CesiumMath.RADIANS_PER_DEGREE);if(!A.defined(t))throw new c.DeveloperError("center is required.");if(!A.defined(r))throw new c.DeveloperError("semiMajorAxis is required.");if(!A.defined(a))throw new c.DeveloperError("semiMinorAxis is required.");if(r<a)throw new c.DeveloperError("semiMajorAxis must be greater than or equal to the semiMinorAxis.");if(n<=0)throw new c.DeveloperError("granularity must be greater than zero.");var o=A.defaultValue(e.height,0),s=A.defaultValue(e.extrudedHeight,o);this._center=g.Cartesian3.clone(t),this._semiMajorAxis=r,this._semiMinorAxis=a,this._ellipsoid=m.Ellipsoid.clone(i),this._rotation=A.defaultValue(e.rotation,0),this._height=Math.max(s,o),this._granularity=n,this._extrudedHeight=Math.min(s,o),this._numberOfVerticalLines=Math.max(A.defaultValue(e.numberOfVerticalLines,16),0),this._offsetAttribute=e.offsetAttribute,this._workerName="createEllipseOutlineGeometry"}h.packedLength=g.Cartesian3.packedLength+m.Ellipsoid.packedLength+8,h.pack=function(e,t,i){if(!A.defined(e))throw new c.DeveloperError("value is required");if(!A.defined(t))throw new c.DeveloperError("array is required");return i=A.defaultValue(i,0),g.Cartesian3.pack(e._center,t,i),i+=g.Cartesian3.packedLength,m.Ellipsoid.pack(e._ellipsoid,t,i),i+=m.Ellipsoid.packedLength,t[i++]=e._semiMajorAxis,t[i++]=e._semiMinorAxis,t[i++]=e._rotation,t[i++]=e._height,t[i++]=e._granularity,t[i++]=e._extrudedHeight,t[i++]=e._numberOfVerticalLines,t[i]=A.defaultValue(e._offsetAttribute,-1),t};var y=new g.Cartesian3,b=new m.Ellipsoid,V={center:y,ellipsoid:b,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0};h.unpack=function(e,t,i){if(!A.defined(e))throw new c.DeveloperError("array is required");t=A.defaultValue(t,0);var r=g.Cartesian3.unpack(e,t,y);t+=g.Cartesian3.packedLength;var a=m.Ellipsoid.unpack(e,t,b);t+=m.Ellipsoid.packedLength;var n=e[t++],o=e[t++],s=e[t++],l=e[t++],u=e[t++],d=e[t++],f=e[t++],p=e[t];return A.defined(i)?(i._center=g.Cartesian3.clone(r,i._center),i._ellipsoid=m.Ellipsoid.clone(a,i._ellipsoid),i._semiMajorAxis=n,i._semiMinorAxis=o,i._rotation=s,i._height=l,i._granularity=u,i._extrudedHeight=d,i._numberOfVerticalLines=f,i._offsetAttribute=-1===p?void 0:p,i):(V.height=l,V.extrudedHeight=d,V.granularity=u,V.rotation=s,V.semiMajorAxis=n,V.semiMinorAxis=o,V.numberOfVerticalLines=f,V.offsetAttribute=-1===p?void 0:p,new h(V))},h.createGeometry=function(e){if(!(e._semiMajorAxis<=0||e._semiMinorAxis<=0)){var t=e._height,i=e._extrudedHeight,r=!_.CesiumMath.equalsEpsilon(t,i,0,_.CesiumMath.EPSILON2);e._center=e._ellipsoid.scaleToGeodeticSurface(e._center,e._center);var a,n={center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:e._ellipsoid,rotation:e._rotation,height:t,granularity:e._granularity,numberOfVerticalLines:e._numberOfVerticalLines};if(r)n.extrudedHeight=i,n.offsetAttribute=e._offsetAttribute,a=function(e){var t=e.center,i=e.ellipsoid,r=e.semiMajorAxis,a=g.Cartesian3.multiplyByScalar(i.geodeticSurfaceNormal(t,L),e.height,L);O.center=g.Cartesian3.add(t,a,O.center),O.radius=r,a=g.Cartesian3.multiplyByScalar(i.geodeticSurfaceNormal(t,a),e.extrudedHeight,a),S.center=g.Cartesian3.add(t,a,S.center),S.radius=r;var n=G.EllipseGeometryLibrary.computeEllipsePositions(e,!1,!0).outerPositions,o=new M.GeometryAttributes({position:new x.GeometryAttribute({componentDatatype:E.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:G.EllipseGeometryLibrary.raisePositionsToHeight(n,e,!0)})});n=o.position.values;var s=v.BoundingSphere.union(O,S),l=n.length/3;if(A.defined(e.offsetAttribute)){var u=new Uint8Array(l);if(e.offsetAttribute===D.GeometryOffsetAttribute.TOP)u=C.arrayFill(u,1,0,l/2);else{var d=e.offsetAttribute===D.GeometryOffsetAttribute.NONE?0:1;u=C.arrayFill(u,d)}o.applyOffset=new x.GeometryAttribute({componentDatatype:E.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:u})}var f=A.defaultValue(e.numberOfVerticalLines,16);f=_.CesiumMath.clamp(f,0,l/2);var p=w.IndexDatatype.createTypedArray(l,2*l+2*f);l/=2;var c,m,h=0;for(c=0;c<l;++c)p[h++]=c,p[h++]=(c+1)%l,p[h++]=c+l,p[h++]=(c+1)%l+l;if(0<f){var y=Math.min(f,l);m=Math.round(l/y);var b=Math.min(m*f,l);for(c=0;c<b;c+=m)p[h++]=c,p[h++]=c+l}return{boundingSphere:s,attributes:o,indices:p}}(n);else if(a=function(e){var t=e.center;d=g.Cartesian3.multiplyByScalar(e.ellipsoid.geodeticSurfaceNormal(t,d),e.height,d),d=g.Cartesian3.add(t,d,d);for(var i=new v.BoundingSphere(d,e.semiMajorAxis),r=G.EllipseGeometryLibrary.computeEllipsePositions(e,!1,!0).outerPositions,a=new M.GeometryAttributes({position:new x.GeometryAttribute({componentDatatype:E.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:G.EllipseGeometryLibrary.raisePositionsToHeight(r,e,!1)})}),n=r.length/3,o=w.IndexDatatype.createTypedArray(n,2*n),s=0,l=0;l<n;++l)o[s++]=l,o[s++]=(l+1)%n;return{boundingSphere:i,attributes:a,indices:o}}(n),A.defined(e._offsetAttribute)){var o=a.attributes.position.values.length,s=new Uint8Array(o/3),l=e._offsetAttribute===D.GeometryOffsetAttribute.NONE?0:1;C.arrayFill(s,l),a.attributes.applyOffset=new x.GeometryAttribute({componentDatatype:E.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:s})}return new x.Geometry({attributes:a.attributes,indices:a.indices,primitiveType:u.PrimitiveType.LINES,boundingSphere:a.boundingSphere,offsetAttribute:e._offsetAttribute})}},e.EllipseOutlineGeometry=h});