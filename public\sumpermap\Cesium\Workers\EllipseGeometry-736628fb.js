/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./Transforms-1509c877","./GeometryAttributes-aacecde6","./GeometryPipeline-8e55e413","./IndexDatatype-9435b55f","./arrayFill-9766fb2e","./GeometryOffsetAttribute-999fc023","./VertexFormat-fe4db402","./EllipseGeometryLibrary-d33811c0","./GeometryInstance-9ddb8c73"],function(e,U,f,c,Q,W,q,J,Z,v,K,X,_,C,$,ee,h,B,w){var te=new Q.Cartesian3,re=new Q.Cartesian3,ae=new Q.Cartesian3,ie=new Q.Cartesian3,ne=new W.Cartesian2,oe=new q.Matrix3,Y=new q.Matrix3,se=new K.Quaternion,le=new Q.Cartesian3,ue=new Q.Cartesian3,me=new Q.Cartesian3,pe=new Q.Cartographic,ce=new Q.Cartesian3,ye=new W.Cartesian2,de=new W.Cartesian2;function M(e,t,r){var a=t.vertexFormat,i=t.center,n=t.semiMajorAxis,o=t.semiMinorAxis,s=t.ellipsoid,l=t.stRotation,u=r?e.length/3*2:e.length/3,m=t.shadowVolume,p=a.st?new Float32Array(2*u):void 0,c=a.normal?new Float32Array(3*u):void 0,y=a.tangent?new Float32Array(3*u):void 0,d=a.bitangent?new Float32Array(3*u):void 0,f=m?new Float32Array(3*u):void 0,h=0,A=le,x=ue,g=me,b=new q.GeographicProjection(s),v=b.project(s.cartesianToCartographic(i,pe),ce),_=s.scaleToGeodeticSurface(i,te);s.geodeticSurfaceNormal(_,_);var C=oe,w=Y;if(0!==l){var M=K.Quaternion.fromAxisAngle(_,l,se);C=q.Matrix3.fromQuaternion(M,C),M=K.Quaternion.fromAxisAngle(_,-l,se),w=q.Matrix3.fromQuaternion(M,w)}else C=q.Matrix3.clone(q.Matrix3.IDENTITY,C),w=q.Matrix3.clone(q.Matrix3.IDENTITY,w);for(var E=W.Cartesian2.fromElements(Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,ye),I=W.Cartesian2.fromElements(Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,de),T=e.length,G=r?T:0,N=G/3*2,P=0;P<T;P+=3){var F=P+1,D=P+2,V=Q.Cartesian3.fromArray(e,P,te);if(a.st){var O=q.Matrix3.multiplyByVector(C,V,re),S=b.project(s.cartesianToCartographic(O,pe),ae);Q.Cartesian3.subtract(S,v,S),ne.x=(S.x+n)/(2*n),ne.y=(S.y+o)/(2*o),E.x=Math.min(ne.x,E.x),E.y=Math.min(ne.y,E.y),I.x=Math.max(ne.x,I.x),I.y=Math.max(ne.y,I.y),r&&(p[h+N]=ne.x,p[h+1+N]=ne.y),p[h++]=ne.x,p[h++]=ne.y}(a.normal||a.tangent||a.bitangent||m)&&(A=s.geodeticSurfaceNormal(V,A),m&&(f[P+G]=-A.x,f[F+G]=-A.y,f[D+G]=-A.z),(a.normal||a.tangent||a.bitangent)&&((a.tangent||a.bitangent)&&(x=Q.Cartesian3.normalize(Q.Cartesian3.cross(Q.Cartesian3.UNIT_Z,A,x),x),q.Matrix3.multiplyByVector(w,x,x)),a.normal&&(c[P]=A.x,c[F]=A.y,c[D]=A.z,r&&(c[P+G]=-A.x,c[F+G]=-A.y,c[D+G]=-A.z)),a.tangent&&(y[P]=x.x,y[F]=x.y,y[D]=x.z,r&&(y[P+G]=-x.x,y[F+G]=-x.y,y[D+G]=-x.z)),a.bitangent&&(g=Q.Cartesian3.normalize(Q.Cartesian3.cross(A,x,g),g),d[P]=g.x,d[F]=g.y,d[D]=g.z,r&&(d[P+G]=g.x,d[F+G]=g.y,d[D+G]=g.z))))}if(a.st){T=p.length;for(var L=0;L<T;L+=2)p[L]=(p[L]-E.x)/(I.x-E.x),p[L+1]=(p[L+1]-E.y)/(I.y-E.y)}var R=new X.GeometryAttributes;if(a.position){var j=B.EllipseGeometryLibrary.raisePositionsToHeight(e,t,r);R.position=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:j})}if(a.st&&(R.st=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:p})),a.normal&&(R.normal=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:c})),a.tangent&&(R.tangent=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:y})),a.bitangent&&(R.bitangent=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:d})),m&&(R.extrudeDirection=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:f})),r&&U.defined(t.offsetAttribute)){var k=new Uint8Array(u);if(t.offsetAttribute===ee.GeometryOffsetAttribute.TOP)k=$.arrayFill(k,1,0,u/2);else{var z=t.offsetAttribute===ee.GeometryOffsetAttribute.NONE?0:1;k=$.arrayFill(k,z)}R.applyOffset=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:k})}return R}function E(e){var t,r,a,i,n,o=new Array(e*(e+1)*12-6),s=0;for(a=1,i=t=0;i<3;i++)o[s++]=a++,o[s++]=t,o[s++]=a;for(i=2;i<e+1;++i){for(a=i*(i+1)-1,t=(i-1)*i-1,o[s++]=a++,o[s++]=t,o[s++]=a,r=2*i,n=0;n<r-1;++n)o[s++]=a,o[s++]=t++,o[s++]=t,o[s++]=a++,o[s++]=t,o[s++]=a;o[s++]=a++,o[s++]=t,o[s++]=a}for(r=2*e,++a,++t,i=0;i<r-1;++i)o[s++]=a,o[s++]=t++,o[s++]=t,o[s++]=a++,o[s++]=t,o[s++]=a;for(o[s++]=a,o[s++]=t++,o[s++]=t,o[s++]=a++,o[s++]=t++,o[s++]=t,++t,i=e-1;1<i;--i){for(o[s++]=t++,o[s++]=t,o[s++]=a,r=2*i,n=0;n<r-1;++n)o[s++]=a,o[s++]=t++,o[s++]=t,o[s++]=a++,o[s++]=t,o[s++]=a;o[s++]=t++,o[s++]=t++,o[s++]=a++}for(i=0;i<3;i++)o[s++]=t++,o[s++]=t,o[s++]=a;return o}var u=new Q.Cartesian3;var I=new q.BoundingSphere,T=new q.BoundingSphere;function m(e){var t=e.center,r=e.ellipsoid,a=e.semiMajorAxis,i=Q.Cartesian3.multiplyByScalar(r.geodeticSurfaceNormal(t,te),e.height,te);I.center=Q.Cartesian3.add(t,i,I.center),I.radius=a,i=Q.Cartesian3.multiplyByScalar(r.geodeticSurfaceNormal(t,i),e.extrudedHeight,i),T.center=Q.Cartesian3.add(t,i,T.center),T.radius=a;var n=B.EllipseGeometryLibrary.computeEllipsePositions(e,!0,!0),o=n.positions,s=n.numPts,l=n.outerPositions,u=q.BoundingSphere.union(I,T),m=M(o,e,!0),p=E(s),c=p.length;p.length=2*c;for(var y=o.length/3,d=0;d<c;d+=3)p[d+c]=p[d+2]+y,p[d+1+c]=p[d+1]+y,p[d+2+c]=p[d]+y;var f=C.IndexDatatype.createTypedArray(2*y/3,p),h=new Z.Geometry({attributes:m,indices:f,primitiveType:v.PrimitiveType.TRIANGLES}),A=function(e,t){var r=t.vertexFormat,a=t.center,i=t.semiMajorAxis,n=t.semiMinorAxis,o=t.ellipsoid,s=t.height,l=t.extrudedHeight,u=t.stRotation,m=e.length/3*2,p=new Float64Array(3*m),c=r.st?new Float32Array(2*m):void 0,y=r.normal?new Float32Array(3*m):void 0,d=r.tangent?new Float32Array(3*m):void 0,f=r.bitangent?new Float32Array(3*m):void 0,h=t.shadowVolume,A=h?new Float32Array(3*m):void 0,x=0,g=le,b=ue,v=me,_=new q.GeographicProjection(o),C=_.project(o.cartesianToCartographic(a,pe),ce),w=o.scaleToGeodeticSurface(a,te);o.geodeticSurfaceNormal(w,w);for(var M=K.Quaternion.fromAxisAngle(w,u,se),E=q.Matrix3.fromQuaternion(M,oe),I=W.Cartesian2.fromElements(Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,ye),T=W.Cartesian2.fromElements(Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,de),G=e.length,N=G/3*2,P=0;P<G;P+=3){var F,D=P+1,V=P+2,O=Q.Cartesian3.fromArray(e,P,te);if(r.st){var S=q.Matrix3.multiplyByVector(E,O,re),L=_.project(o.cartesianToCartographic(S,pe),ae);Q.Cartesian3.subtract(L,C,L),ne.x=(L.x+i)/(2*i),ne.y=(L.y+n)/(2*n),I.x=Math.min(ne.x,I.x),I.y=Math.min(ne.y,I.y),T.x=Math.max(ne.x,T.x),T.y=Math.max(ne.y,T.y),c[x+N]=ne.x,c[x+1+N]=ne.y,c[x++]=ne.x,c[x++]=ne.y}O=o.scaleToGeodeticSurface(O,O),F=Q.Cartesian3.clone(O,re),g=o.geodeticSurfaceNormal(O,g),h&&(A[P+G]=-g.x,A[D+G]=-g.y,A[V+G]=-g.z);var R=Q.Cartesian3.multiplyByScalar(g,s,ie);if(O=Q.Cartesian3.add(O,R,O),R=Q.Cartesian3.multiplyByScalar(g,l,R),F=Q.Cartesian3.add(F,R,F),r.position&&(p[P+G]=F.x,p[D+G]=F.y,p[V+G]=F.z,p[P]=O.x,p[D]=O.y,p[V]=O.z),r.normal||r.tangent||r.bitangent){v=Q.Cartesian3.clone(g,v);var j=Q.Cartesian3.fromArray(e,(P+3)%G,ie);Q.Cartesian3.subtract(j,O,j);var k=Q.Cartesian3.subtract(F,O,ae);g=Q.Cartesian3.normalize(Q.Cartesian3.cross(k,j,g),g),r.normal&&(y[P]=g.x,y[D]=g.y,y[V]=g.z,y[P+G]=g.x,y[D+G]=g.y,y[V+G]=g.z),r.tangent&&(b=Q.Cartesian3.normalize(Q.Cartesian3.cross(v,g,b),b),d[P]=b.x,d[D]=b.y,d[V]=b.z,d[P+G]=b.x,d[P+1+G]=b.y,d[P+2+G]=b.z),r.bitangent&&(f[P]=v.x,f[D]=v.y,f[V]=v.z,f[P+G]=v.x,f[D+G]=v.y,f[V+G]=v.z)}}if(r.st){G=c.length;for(var z=0;z<G;z+=2)c[z]=(c[z]-I.x)/(T.x-I.x),c[z+1]=(c[z+1]-I.y)/(T.y-I.y)}var B=new X.GeometryAttributes;if(r.position&&(B.position=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:p})),r.st&&(B.st=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:c})),r.normal&&(B.normal=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:y})),r.tangent&&(B.tangent=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:d})),r.bitangent&&(B.bitangent=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:f})),h&&(B.extrudeDirection=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:A})),U.defined(t.offsetAttribute)){var Y=new Uint8Array(m);if(t.offsetAttribute===ee.GeometryOffsetAttribute.TOP)Y=$.arrayFill(Y,1,0,m/2);else{var H=t.offsetAttribute===ee.GeometryOffsetAttribute.NONE?0:1;Y=$.arrayFill(Y,H)}B.applyOffset=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:Y})}return B}(l,e);p=function(e){for(var t=e.length/3,r=C.IndexDatatype.createTypedArray(t,6*t),a=0,i=0;i<t;i++){var n=i,o=i+t,s=(n+1)%t,l=s+t;r[a++]=n,r[a++]=o,r[a++]=s,r[a++]=s,r[a++]=o,r[a++]=l}return r}(l);var x=C.IndexDatatype.createTypedArray(2*l.length/3,p),g=new Z.Geometry({attributes:A,indices:x,primitiveType:v.PrimitiveType.TRIANGLES}),b=_.GeometryPipeline.combineInstances([new w.GeometryInstance({geometry:h}),new w.GeometryInstance({geometry:g})]);return{boundingSphere:u,attributes:b[0].attributes,indices:b[0].indices}}function l(e,t,r,a,i,n,o){for(var s=B.EllipseGeometryLibrary.computeEllipsePositions({center:e,semiMajorAxis:t,semiMinorAxis:r,rotation:a,granularity:i},!1,!0).outerPositions,l=s.length/3,u=new Array(l),m=0;m<l;++m)u[m]=Q.Cartesian3.fromArray(s,3*m);var p=W.Rectangle.fromCartesianArray(u,n,o);return p.width>c.CesiumMath.PI&&(p.north=0<p.north?c.CesiumMath.PI_OVER_TWO-c.CesiumMath.EPSILON7:p.north,p.south=p.south<0?c.CesiumMath.EPSILON7-c.CesiumMath.PI_OVER_TWO:p.south,p.east=c.CesiumMath.PI,p.west=-c.CesiumMath.PI),p}function A(e){var t=(e=U.defaultValue(e,U.defaultValue.EMPTY_OBJECT)).center,r=U.defaultValue(e.ellipsoid,W.Ellipsoid.WGS84),a=e.semiMajorAxis,i=e.semiMinorAxis,n=U.defaultValue(e.granularity,c.CesiumMath.RADIANS_PER_DEGREE),o=U.defaultValue(e.vertexFormat,h.VertexFormat.DEFAULT);if(f.Check.defined("options.center",t),f.Check.typeOf.number("options.semiMajorAxis",a),f.Check.typeOf.number("options.semiMinorAxis",i),a<i)throw new f.DeveloperError("semiMajorAxis must be greater than or equal to the semiMinorAxis.");if(n<=0)throw new f.DeveloperError("granularity must be greater than zero.");var s=U.defaultValue(e.height,0),l=U.defaultValue(e.extrudedHeight,s);this._center=Q.Cartesian3.clone(t),this._semiMajorAxis=a,this._semiMinorAxis=i,this._ellipsoid=W.Ellipsoid.clone(r),this._rotation=U.defaultValue(e.rotation,0),this._stRotation=U.defaultValue(e.stRotation,0),this._height=Math.max(l,s),this._granularity=n,this._vertexFormat=h.VertexFormat.clone(o),this._extrudedHeight=Math.min(l,s),this._shadowVolume=U.defaultValue(e.shadowVolume,!1),this._workerName="createEllipseGeometry",this._offsetAttribute=e.offsetAttribute,this._rectangle=void 0,this._textureCoordinateRotationPoints=void 0}A.packedLength=Q.Cartesian3.packedLength+W.Ellipsoid.packedLength+h.VertexFormat.packedLength+9,A.pack=function(e,t,r){return f.Check.defined("value",e),f.Check.defined("array",t),r=U.defaultValue(r,0),Q.Cartesian3.pack(e._center,t,r),r+=Q.Cartesian3.packedLength,W.Ellipsoid.pack(e._ellipsoid,t,r),r+=W.Ellipsoid.packedLength,h.VertexFormat.pack(e._vertexFormat,t,r),r+=h.VertexFormat.packedLength,t[r++]=e._semiMajorAxis,t[r++]=e._semiMinorAxis,t[r++]=e._rotation,t[r++]=e._stRotation,t[r++]=e._height,t[r++]=e._granularity,t[r++]=e._extrudedHeight,t[r++]=e._shadowVolume?1:0,t[r]=U.defaultValue(e._offsetAttribute,-1),t};var x=new Q.Cartesian3,g=new W.Ellipsoid,b=new h.VertexFormat,G={center:x,ellipsoid:g,vertexFormat:b,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,stRotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,shadowVolume:void 0,offsetAttribute:void 0};A.unpack=function(e,t,r){f.Check.defined("array",e),t=U.defaultValue(t,0);var a=Q.Cartesian3.unpack(e,t,x);t+=Q.Cartesian3.packedLength;var i=W.Ellipsoid.unpack(e,t,g);t+=W.Ellipsoid.packedLength;var n=h.VertexFormat.unpack(e,t,b);t+=h.VertexFormat.packedLength;var o=e[t++],s=e[t++],l=e[t++],u=e[t++],m=e[t++],p=e[t++],c=e[t++],y=1===e[t++],d=e[t];return U.defined(r)?(r._center=Q.Cartesian3.clone(a,r._center),r._ellipsoid=W.Ellipsoid.clone(i,r._ellipsoid),r._vertexFormat=h.VertexFormat.clone(n,r._vertexFormat),r._semiMajorAxis=o,r._semiMinorAxis=s,r._rotation=l,r._stRotation=u,r._height=m,r._granularity=p,r._extrudedHeight=c,r._shadowVolume=y,r._offsetAttribute=-1===d?void 0:d,r):(G.height=m,G.extrudedHeight=c,G.granularity=p,G.stRotation=u,G.rotation=l,G.semiMajorAxis=o,G.semiMinorAxis=s,G.shadowVolume=y,G.offsetAttribute=-1===d?void 0:d,new A(G))},A.computeRectangle=function(e,t){var r=(e=U.defaultValue(e,U.defaultValue.EMPTY_OBJECT)).center,a=U.defaultValue(e.ellipsoid,W.Ellipsoid.WGS84),i=e.semiMajorAxis,n=e.semiMinorAxis,o=U.defaultValue(e.granularity,c.CesiumMath.RADIANS_PER_DEGREE),s=U.defaultValue(e.rotation,0);if(f.Check.defined("options.center",r),f.Check.typeOf.number("options.semiMajorAxis",i),f.Check.typeOf.number("options.semiMinorAxis",n),i<n)throw new f.DeveloperError("semiMajorAxis must be greater than or equal to the semiMinorAxis.");if(o<=0)throw new f.DeveloperError("granularity must be greater than zero.");return l(r,i,n,s,o,a,t)},A.createGeometry=function(e){if(!(e._semiMajorAxis<=0||e._semiMinorAxis<=0)){var t=e._height,r=e._extrudedHeight,a=!c.CesiumMath.equalsEpsilon(t,r,0,c.CesiumMath.EPSILON2);e._center=e._ellipsoid.scaleToGeodeticSurface(e._center,e._center);var i,n={center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:e._ellipsoid,rotation:e._rotation,height:t,granularity:e._granularity,vertexFormat:e._vertexFormat,stRotation:e._stRotation};if(a)n.extrudedHeight=r,n.shadowVolume=e._shadowVolume,n.offsetAttribute=e._offsetAttribute,i=m(n);else if(i=function(e){var t=e.center;u=Q.Cartesian3.multiplyByScalar(e.ellipsoid.geodeticSurfaceNormal(t,u),e.height,u),u=Q.Cartesian3.add(t,u,u);var r=new q.BoundingSphere(u,e.semiMajorAxis),a=B.EllipseGeometryLibrary.computeEllipsePositions(e,!0,!1),i=a.positions,n=a.numPts,o=M(i,e,!1),s=E(n);return{boundingSphere:r,attributes:o,indices:s=C.IndexDatatype.createTypedArray(i.length/3,s)}}(n),U.defined(e._offsetAttribute)){var o=i.attributes.position.values.length,s=new Uint8Array(o/3),l=e._offsetAttribute===ee.GeometryOffsetAttribute.NONE?0:1;$.arrayFill(s,l),i.attributes.applyOffset=new Z.GeometryAttribute({componentDatatype:J.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:s})}return new Z.Geometry({attributes:i.attributes,indices:i.indices,primitiveType:v.PrimitiveType.TRIANGLES,boundingSphere:i.boundingSphere,offsetAttribute:e._offsetAttribute})}},A.createShadowVolume=function(e,t,r){var a=e._granularity,i=e._ellipsoid,n=t(a,i),o=r(a,i);return new A({center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:i,rotation:e._rotation,stRotation:e._stRotation,granularity:a,extrudedHeight:n,height:o,vertexFormat:h.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(A.prototype,{rectangle:{get:function(){return U.defined(this._rectangle)||(this._rectangle=l(this._center,this._semiMajorAxis,this._semiMinorAxis,this._rotation,this._granularity,this._ellipsoid)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return U.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(e){var t=-e._stRotation;if(0===t)return[0,0,0,1,1,0];for(var r=B.EllipseGeometryLibrary.computeEllipsePositions({center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,rotation:e._rotation,granularity:e._granularity},!1,!0).outerPositions,a=r.length/3,i=new Array(a),n=0;n<a;++n)i[n]=Q.Cartesian3.fromArray(r,3*n);var o=e._ellipsoid,s=e.rectangle;return Z.Geometry._textureCoordinateRotationPoints(i,t,o,s)}(this)),this._textureCoordinateRotationPoints}}}),e.EllipseGeometry=A});