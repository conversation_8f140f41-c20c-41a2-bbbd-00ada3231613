/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./createTaskProcessorWorker","./CompressedTextureBuffer-21cababf","./PixelFormat-8e0e5be1"],function(when,RuntimeError,WebGLConstants,createTaskProcessorWorker,CompressedTextureBuffer,PixelFormat){var Module;Module||(Module=(void 0!==Module?Module:null)||{});var moduleOverrides={};for(var key in Module)Module.hasOwnProperty(key)&&(moduleOverrides[key]=Module[key]);var ENVIRONMENT_IS_WEB=!1,ENVIRONMENT_IS_WORKER=!1,ENVIRONMENT_IS_NODE=!1,ENVIRONMENT_IS_SHELL=!1,nodeFS,nodePath;if(Module.ENVIRONMENT)if("WEB"===Module.ENVIRONMENT)ENVIRONMENT_IS_WEB=!0;else if("WORKER"===Module.ENVIRONMENT)ENVIRONMENT_IS_WORKER=!0;else if("NODE"===Module.ENVIRONMENT)ENVIRONMENT_IS_NODE=!0;else{if("SHELL"!==Module.ENVIRONMENT)throw new Error("The provided Module['ENVIRONMENT'] value is not valid. It must be one of: WEB|WORKER|NODE|SHELL.");ENVIRONMENT_IS_SHELL=!0}else ENVIRONMENT_IS_WEB="object"==typeof window,ENVIRONMENT_IS_WORKER="function"==typeof importScripts,ENVIRONMENT_IS_NODE="object"==typeof process&&"function"==typeof require&&!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER,ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;if(ENVIRONMENT_IS_NODE)Module.print||(Module.print=console.log),Module.printErr||(Module.printErr=console.warn),Module.read=function(e,r){nodeFS||(nodeFS=require("fs")),nodePath||(nodePath=require("path")),e=nodePath.normalize(e);var i=nodeFS.readFileSync(e);return r?i:i.toString()},Module.readBinary=function(e){var r=Module.read(e,!0);return r.buffer||(r=new Uint8Array(r)),assert(r.buffer),r},Module.load=function(e){globalEval(read(e))},Module.thisProgram||(1<process.argv.length?Module.thisProgram=process.argv[1].replace(/\\/g,"/"):Module.thisProgram="unknown-program"),Module.arguments=process.argv.slice(2),"undefined"!=typeof module&&(module.exports=Module),process.on("uncaughtException",function(e){if(!(e instanceof ExitStatus))throw e}),Module.inspect=function(){return"[Emscripten Module object]"};else if(ENVIRONMENT_IS_SHELL)Module.print||(Module.print=print),"undefined"!=typeof printErr&&(Module.printErr=printErr),"undefined"!=typeof read?Module.read=read:Module.read=function(){throw"no read() available"},Module.readBinary=function(e){if("function"==typeof readbuffer)return new Uint8Array(readbuffer(e));var r=read(e,"binary");return assert("object"==typeof r),r},"undefined"!=typeof scriptArgs?Module.arguments=scriptArgs:void 0!==arguments&&(Module.arguments=arguments),"function"==typeof quit&&(Module.quit=function(e,r){quit(e)});else{if(!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER)throw"Unknown runtime environment. Where are we?";if(Module.read=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},ENVIRONMENT_IS_WORKER&&(Module.readBinary=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),Module.readAsync=function(e,r,i){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?r(n.response):i()},n.onerror=i,n.send(null)},void 0!==arguments&&(Module.arguments=arguments),"undefined"!=typeof console)Module.print||(Module.print=function(e){console.log(e)}),Module.printErr||(Module.printErr=function(e){console.warn(e)});else{var TRY_USE_DUMP=!1;Module.print||(Module.print=TRY_USE_DUMP&&"undefined"!=typeof dump?function(e){dump(e)}:function(e){})}ENVIRONMENT_IS_WORKER&&(Module.load=importScripts),void 0===Module.setWindowTitle&&(Module.setWindowTitle=function(e){document.title=e})}function globalEval(e){eval.call(null,e)}for(var key in!Module.load&&Module.read&&(Module.load=function(e){globalEval(Module.read(e))}),Module.print||(Module.print=function(){}),Module.printErr||(Module.printErr=Module.print),Module.arguments||(Module.arguments=[]),Module.thisProgram||(Module.thisProgram="./this.program"),Module.quit||(Module.quit=function(e,r){throw r}),Module.print=Module.print,Module.printErr=Module.printErr,Module.preRun=[],Module.postRun=[],moduleOverrides)moduleOverrides.hasOwnProperty(key)&&(Module[key]=moduleOverrides[key]);moduleOverrides=void 0;var Runtime={setTempRet0:function(e){return tempRet0=e},getTempRet0:function(){return tempRet0},stackSave:function(){return STACKTOP},stackRestore:function(e){STACKTOP=e},getNativeTypeSize:function(e){switch(e){case"i1":case"i8":return 1;case"i16":return 2;case"i32":return 4;case"i64":return 8;case"float":return 4;case"double":return 8;default:if("*"===e[e.length-1])return Runtime.QUANTUM_SIZE;if("i"!==e[0])return 0;var r=parseInt(e.substr(1));return assert(r%8==0),r/8}},getNativeFieldSize:function(e){return Math.max(Runtime.getNativeTypeSize(e),Runtime.QUANTUM_SIZE)},STACK_ALIGN:16,prepVararg:function(e,r){return"double"===r||"i64"===r?7&e&&(assert(4==(7&e)),e+=4):assert(0==(3&e)),e},getAlignSize:function(e,r,i){return i||"i64"!=e&&"double"!=e?e?Math.min(r||(e?Runtime.getNativeFieldSize(e):0),Runtime.QUANTUM_SIZE):Math.min(r,8):8},dynCall:function(e,r,i){return i&&i.length?Module["dynCall_"+e].apply(null,[r].concat(i)):Module["dynCall_"+e].call(null,r)},functionPointers:[],addFunction:function(e){for(var r=0;r<Runtime.functionPointers.length;r++)if(!Runtime.functionPointers[r])return Runtime.functionPointers[r]=e,2*(1+r);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."},removeFunction:function(e){Runtime.functionPointers[(e-2)/2]=null},warnOnce:function(e){Runtime.warnOnce.shown||(Runtime.warnOnce.shown={}),Runtime.warnOnce.shown[e]||(Runtime.warnOnce.shown[e]=1,Module.printErr(e))},funcWrappers:{},getFuncWrapper:function(r,i){assert(i),Runtime.funcWrappers[i]||(Runtime.funcWrappers[i]={});var e=Runtime.funcWrappers[i];return e[r]||(1===i.length?e[r]=function(){return Runtime.dynCall(i,r)}:2===i.length?e[r]=function(e){return Runtime.dynCall(i,r,[e])}:e[r]=function(){return Runtime.dynCall(i,r,Array.prototype.slice.call(arguments))}),e[r]},getCompilerSetting:function(e){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work"},stackAlloc:function(e){var r=STACKTOP;return STACKTOP=(STACKTOP=STACKTOP+e|0)+15&-16,r},staticAlloc:function(e){var r=STATICTOP;return STATICTOP=(STATICTOP=STATICTOP+e|0)+15&-16,r},dynamicAlloc:function(e){var r=HEAP32[DYNAMICTOP_PTR>>2],i=-16&(r+e+15|0);if((HEAP32[DYNAMICTOP_PTR>>2]=i,TOTAL_MEMORY<=i)&&!enlargeMemory())return HEAP32[DYNAMICTOP_PTR>>2]=r,0;return r},alignMemory:function(e,r){return e=Math.ceil(e/(r||16))*(r||16)},makeBigInt:function(e,r,i){return i?+(e>>>0)+4294967296*+(r>>>0):+(e>>>0)+4294967296*+(0|r)},GLOBAL_BASE:8,QUANTUM_SIZE:4,__dummy__:0};Module.Runtime=Runtime;var ABORT=0,cwrap,ccall;function assert(e,r){e||abort("Assertion failed: "+r)}function getCFunc(ident){var func=Module["_"+ident];if(!func)try{func=eval("_"+ident)}catch(e){}return assert(func,"Cannot call unknown function "+ident+" (perhaps LLVM optimizations or closure removed it?)"),func}function setValue(e,r,i,n){switch("*"===(i=i||"i8").charAt(i.length-1)&&(i="i32"),i){case"i1":case"i8":HEAP8[e>>0]=r;break;case"i16":HEAP16[e>>1]=r;break;case"i32":HEAP32[e>>2]=r;break;case"i64":tempI64=[r>>>0,(tempDouble=r,1<=+Math_abs(tempDouble)?0<tempDouble?(0|Math_min(+Math_floor(tempDouble/4294967296),4294967295))>>>0:~~+Math_ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[e>>2]=tempI64[0],HEAP32[e+4>>2]=tempI64[1];break;case"float":HEAPF32[e>>2]=r;break;case"double":HEAPF64[e>>3]=r;break;default:abort("invalid type for setValue: "+i)}}function getValue(e,r,i){switch("*"===(r=r||"i8").charAt(r.length-1)&&(r="i32"),r){case"i1":case"i8":return HEAP8[e>>0];case"i16":return HEAP16[e>>1];case"i32":case"i64":return HEAP32[e>>2];case"float":return HEAPF32[e>>2];case"double":return HEAPF64[e>>3];default:abort("invalid type for setValue: "+r)}return null}!function(){var JSfuncs={stackSave:function(){Runtime.stackSave()},stackRestore:function(){Runtime.stackRestore()},arrayToC:function(e){var r=Runtime.stackAlloc(e.length);return writeArrayToMemory(e,r),r},stringToC:function(e){var r=0;if(null!=e&&0!==e){var i=1+(e.length<<2);stringToUTF8(e,r=Runtime.stackAlloc(i),i)}return r}},toC={string:JSfuncs.stringToC,array:JSfuncs.arrayToC};ccall=function(e,r,i,n,t){var o=getCFunc(e),a=[],u=0;if(n)for(var f=0;f<n.length;f++){var l=toC[i[f]];a[f]=l?(0===u&&(u=Runtime.stackSave()),l(n[f])):n[f]}var c=o.apply(null,a);if("string"===r&&(c=Pointer_stringify(c)),0!==u){if(t&&t.async)return void EmterpreterAsync.asyncFinalizers.push(function(){Runtime.stackRestore(u)});Runtime.stackRestore(u)}return c};var sourceRegex=/^function\s*[a-zA-Z$_0-9]*\s*\(([^)]*)\)\s*{\s*([^*]*?)[\s;]*(?:return\s*(.*?)[;\s]*)?}$/;function parseJSFunc(e){var r=e.toString().match(sourceRegex).slice(1);return{arguments:r[0],body:r[1],returnValue:r[2]}}var JSsource=null;function ensureJSsource(){if(!JSsource)for(var e in JSsource={},JSfuncs)JSfuncs.hasOwnProperty(e)&&(JSsource[e]=parseJSFunc(JSfuncs[e]))}cwrap=function cwrap(ident,returnType,argTypes){argTypes=argTypes||[];var cfunc=getCFunc(ident),numericArgs=argTypes.every(function(e){return"number"===e}),numericRet="string"!==returnType;if(numericRet&&numericArgs)return cfunc;var argNames=argTypes.map(function(e,r){return"$"+r}),funcstr="(function("+argNames.join(",")+") {",nargs=argTypes.length;if(!numericArgs){ensureJSsource(),funcstr+="var stack = "+JSsource.stackSave.body+";";for(var i=0;i<nargs;i++){var arg=argNames[i],type=argTypes[i];if("number"!==type){var convertCode=JSsource[type+"ToC"];funcstr+="var "+convertCode.arguments+" = "+arg+";",funcstr+=convertCode.body+";",funcstr+=arg+"=("+convertCode.returnValue+");"}}}var cfuncname=parseJSFunc(function(){return cfunc}).returnValue;if(funcstr+="var ret = "+cfuncname+"("+argNames.join(",")+");",!numericRet){var strgfy=parseJSFunc(function(){return Pointer_stringify}).returnValue;funcstr+="ret = "+strgfy+"(ret);"}return numericArgs||(ensureJSsource(),funcstr+=JSsource.stackRestore.body.replace("()","(stack)")+";"),funcstr+="return ret})",eval(funcstr)}}(),Module.ccall=ccall,Module.cwrap=cwrap,Module.setValue=setValue,Module.getValue=getValue;var ALLOC_NORMAL=0,ALLOC_STACK=1,ALLOC_STATIC=2,ALLOC_DYNAMIC=3,ALLOC_NONE=4;function allocate(e,r,i,n){var t,o;o="number"==typeof e?(t=!0,e):(t=!1,e.length);var a,u="string"==typeof r?r:null;if(a=i==ALLOC_NONE?n:["function"==typeof _malloc?_malloc:Runtime.staticAlloc,Runtime.stackAlloc,Runtime.staticAlloc,Runtime.dynamicAlloc][void 0===i?ALLOC_STATIC:i](Math.max(o,u?1:r.length)),t){var f;n=a;for(assert(0==(3&a)),f=a+(-4&o);n<f;n+=4)HEAP32[n>>2]=0;for(f=a+o;n<f;)HEAP8[n++>>0]=0;return a}if("i8"===u)return e.subarray||e.slice?HEAPU8.set(e,a):HEAPU8.set(new Uint8Array(e),a),a;for(var l,c,s,_=0;_<o;){var d=e[_];"function"==typeof d&&(d=Runtime.getFunctionIndex(d)),0!==(l=u||r[_])?("i64"==l&&(l="i32"),setValue(a+_,d,l),s!==l&&(c=Runtime.getNativeTypeSize(l),s=l),_+=c):_++}return a}function getMemory(e){return staticSealed?runtimeInitialized?_malloc(e):Runtime.dynamicAlloc(e):Runtime.staticAlloc(e)}function Pointer_stringify(e,r){if(0===r||!e)return"";for(var i,n=0,t=0;n|=i=HEAPU8[e+t>>0],(0!=i||r)&&(t++,!r||t!=r););r||(r=t);var o="";if(n<128){for(var a;0<r;)a=String.fromCharCode.apply(String,HEAPU8.subarray(e,e+Math.min(r,1024))),o=o?o+a:a,e+=1024,r-=1024;return o}return Module.UTF8ToString(e)}function AsciiToString(e){for(var r="";;){var i=HEAP8[e++>>0];if(!i)return r;r+=String.fromCharCode(i)}}function stringToAscii(e,r){return writeAsciiToMemory(e,r,!1)}Module.ALLOC_NORMAL=ALLOC_NORMAL,Module.ALLOC_STACK=ALLOC_STACK,Module.ALLOC_STATIC=ALLOC_STATIC,Module.ALLOC_DYNAMIC=ALLOC_DYNAMIC,Module.ALLOC_NONE=ALLOC_NONE,Module.allocate=allocate,Module.getMemory=getMemory,Module.Pointer_stringify=Pointer_stringify,Module.AsciiToString=AsciiToString,Module.stringToAscii=stringToAscii;var UTF8Decoder="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function UTF8ArrayToString(e,r){for(var i=r;e[i];)++i;if(16<i-r&&e.subarray&&UTF8Decoder)return UTF8Decoder.decode(e.subarray(r,i));for(var n,t,o,a,u,f="";;){if(!(n=e[r++]))return f;if(128&n)if(t=63&e[r++],192!=(224&n))if(o=63&e[r++],(n=224==(240&n)?(15&n)<<12|t<<6|o:(a=63&e[r++],240==(248&n)?(7&n)<<18|t<<12|o<<6|a:(u=63&e[r++],248==(252&n)?(3&n)<<24|t<<18|o<<12|a<<6|u:(1&n)<<30|t<<24|o<<18|a<<12|u<<6|63&e[r++])))<65536)f+=String.fromCharCode(n);else{var l=n-65536;f+=String.fromCharCode(55296|l>>10,56320|1023&l)}else f+=String.fromCharCode((31&n)<<6|t);else f+=String.fromCharCode(n)}}function UTF8ToString(e){return UTF8ArrayToString(HEAPU8,e)}function stringToUTF8Array(e,r,i,n){if(!(0<n))return 0;for(var t=i,o=i+n-1,a=0;a<e.length;++a){var u=e.charCodeAt(a);if(55296<=u&&u<=57343&&(u=65536+((1023&u)<<10)|1023&e.charCodeAt(++a)),u<=127){if(o<=i)break;r[i++]=u}else if(u<=2047){if(o<=i+1)break;r[i++]=192|u>>6,r[i++]=128|63&u}else if(u<=65535){if(o<=i+2)break;r[i++]=224|u>>12,r[i++]=128|u>>6&63,r[i++]=128|63&u}else if(u<=2097151){if(o<=i+3)break;r[i++]=240|u>>18,r[i++]=128|u>>12&63,r[i++]=128|u>>6&63,r[i++]=128|63&u}else if(u<=67108863){if(o<=i+4)break;r[i++]=248|u>>24,r[i++]=128|u>>18&63,r[i++]=128|u>>12&63,r[i++]=128|u>>6&63,r[i++]=128|63&u}else{if(o<=i+5)break;r[i++]=252|u>>30,r[i++]=128|u>>24&63,r[i++]=128|u>>18&63,r[i++]=128|u>>12&63,r[i++]=128|u>>6&63,r[i++]=128|63&u}}return r[i]=0,i-t}function stringToUTF8(e,r,i){return stringToUTF8Array(e,HEAPU8,r,i)}function lengthBytesUTF8(e){for(var r=0,i=0;i<e.length;++i){var n=e.charCodeAt(i);55296<=n&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++i)),n<=127?++r:r+=n<=2047?2:n<=65535?3:n<=2097151?4:n<=67108863?5:6}return r}Module.UTF8ArrayToString=UTF8ArrayToString,Module.UTF8ToString=UTF8ToString,Module.stringToUTF8Array=stringToUTF8Array,Module.stringToUTF8=stringToUTF8,Module.lengthBytesUTF8=lengthBytesUTF8;var UTF16Decoder="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function demangle(e){var r=Module.___cxa_demangle||Module.__cxa_demangle;if(r){try{var i=e.substr(1),n=lengthBytesUTF8(i)+1,t=_malloc(n);stringToUTF8(i,t,n);var o=_malloc(4),a=r(t,0,0,o);if(0===getValue(o,"i32")&&a)return Pointer_stringify(a)}catch(e){}finally{t&&_free(t),o&&_free(o),a&&_free(a)}return e}return Runtime.warnOnce("warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),e}function demangleAll(e){return e.replace(/__Z[\w\d_]+/g,function(e){var r=demangle(e);return e===r?e:e+" ["+r+"]"})}function jsStackTrace(){var r=new Error;if(!r.stack){try{throw new Error(0)}catch(e){r=e}if(!r.stack)return"(no stack trace available)"}return r.stack.toString()}function stackTrace(){var e=jsStackTrace();return Module.extraStackTrace&&(e+="\n"+Module.extraStackTrace()),demangleAll(e)}Module.stackTrace=stackTrace;var WASM_PAGE_SIZE=65536,ASMJS_PAGE_SIZE=16777216,MIN_TOTAL_MEMORY=16777216,HEAP,buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64,STATIC_BASE,STATICTOP,staticSealed,STACK_BASE,STACKTOP,STACK_MAX,DYNAMIC_BASE,DYNAMICTOP_PTR,byteLength;function alignUp(e,r){return 0<e%r&&(e+=r-e%r),e}function updateGlobalBuffer(e){Module.buffer=buffer=e}function updateGlobalBufferViews(){Module.HEAP8=HEAP8=new Int8Array(buffer),Module.HEAP16=HEAP16=new Int16Array(buffer),Module.HEAP32=HEAP32=new Int32Array(buffer),Module.HEAPU8=HEAPU8=new Uint8Array(buffer),Module.HEAPU16=HEAPU16=new Uint16Array(buffer),Module.HEAPU32=HEAPU32=new Uint32Array(buffer),Module.HEAPF32=HEAPF32=new Float32Array(buffer),Module.HEAPF64=HEAPF64=new Float64Array(buffer)}function abortOnCannotGrowMemory(){abort("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+TOTAL_MEMORY+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime but prevents some optimizations, (3) set Module.TOTAL_MEMORY to a higher value before the program runs, or (4) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")}function enlargeMemory(){var e=Module.usingWasm?WASM_PAGE_SIZE:ASMJS_PAGE_SIZE,r=2147483648-e;if(HEAP32[DYNAMICTOP_PTR>>2]>r)return!1;var i=TOTAL_MEMORY;for(TOTAL_MEMORY=Math.max(TOTAL_MEMORY,MIN_TOTAL_MEMORY);TOTAL_MEMORY<HEAP32[DYNAMICTOP_PTR>>2];)TOTAL_MEMORY=TOTAL_MEMORY<=536870912?alignUp(2*TOTAL_MEMORY,e):Math.min(alignUp((3*TOTAL_MEMORY+2147483648)/4,e),r);var n=Module.reallocBuffer(TOTAL_MEMORY);return n&&n.byteLength==TOTAL_MEMORY?(updateGlobalBuffer(n),updateGlobalBufferViews(),!0):(TOTAL_MEMORY=i,!1)}STATIC_BASE=STATICTOP=STACK_BASE=STACKTOP=STACK_MAX=DYNAMIC_BASE=DYNAMICTOP_PTR=0,staticSealed=!1,Module.reallocBuffer||(Module.reallocBuffer=function(e){var r;try{if(ArrayBuffer.transfer)r=ArrayBuffer.transfer(buffer,e);else{var i=HEAP8;r=new ArrayBuffer(e),new Int8Array(r).set(i)}}catch(e){return!1}return!!_emscripten_replace_memory(r)&&r});try{byteLength=Function.prototype.call.bind(Object.getOwnPropertyDescriptor(ArrayBuffer.prototype,"byteLength").get),byteLength(new ArrayBuffer(4))}catch(e){byteLength=function(e){return e.byteLength}}var TOTAL_STACK=Module.TOTAL_STACK||5242880,TOTAL_MEMORY=Module.TOTAL_MEMORY||16777216;function getTotalMemory(){return TOTAL_MEMORY}if(TOTAL_MEMORY<TOTAL_STACK&&Module.printErr("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+TOTAL_MEMORY+"! (TOTAL_STACK="+TOTAL_STACK+")"),buffer=Module.buffer?Module.buffer:new ArrayBuffer(TOTAL_MEMORY),updateGlobalBufferViews(),HEAP32[0]=1668509029,HEAP16[1]=25459,115!==HEAPU8[2]||99!==HEAPU8[3])throw"Runtime error: expected the system to be little-endian!";function callRuntimeCallbacks(e){for(;0<e.length;){var r=e.shift();if("function"!=typeof r){var i=r.func;"number"==typeof i?void 0===r.arg?Module.dynCall_v(i):Module.dynCall_vi(i,r.arg):i(void 0===r.arg?null:r.arg)}else r()}}Module.HEAP=HEAP,Module.buffer=buffer,Module.HEAP8=HEAP8,Module.HEAP16=HEAP16,Module.HEAP32=HEAP32,Module.HEAPU8=HEAPU8,Module.HEAPU16=HEAPU16,Module.HEAPU32=HEAPU32,Module.HEAPF32=HEAPF32,Module.HEAPF64=HEAPF64;var __ATPRERUN__=[],__ATINIT__=[],__ATMAIN__=[],__ATEXIT__=[],__ATPOSTRUN__=[],runtimeInitialized=!1;function preRun(){if(Module.preRun)for("function"==typeof Module.preRun&&(Module.preRun=[Module.preRun]);Module.preRun.length;)addOnPreRun(Module.preRun.shift());callRuntimeCallbacks(__ATPRERUN__)}function ensureInitRuntime(){runtimeInitialized||(runtimeInitialized=!0,callRuntimeCallbacks(__ATINIT__))}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){callRuntimeCallbacks(__ATEXIT__)}function postRun(){if(Module.postRun)for("function"==typeof Module.postRun&&(Module.postRun=[Module.postRun]);Module.postRun.length;)addOnPostRun(Module.postRun.shift());callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(e){__ATPRERUN__.unshift(e)}function addOnInit(e){__ATINIT__.unshift(e)}function addOnPreMain(e){__ATMAIN__.unshift(e)}function addOnExit(e){__ATEXIT__.unshift(e)}function addOnPostRun(e){__ATPOSTRUN__.unshift(e)}function intArrayFromString(e,r,i){var n=0<i?i:lengthBytesUTF8(e)+1,t=new Array(n),o=stringToUTF8Array(e,t,0,t.length);return r&&(t.length=o),t}function intArrayToString(e){for(var r=[],i=0;i<e.length;i++){var n=e[i];255<n&&(n&=255),r.push(String.fromCharCode(n))}return r.join("")}function writeStringToMemory(e,r,i){var n,t;Runtime.warnOnce("writeStringToMemory is deprecated and should not be called! Use stringToUTF8() instead!"),i&&(t=r+lengthBytesUTF8(e),n=HEAP8[t]),stringToUTF8(e,r,1/0),i&&(HEAP8[t]=n)}function writeArrayToMemory(e,r){HEAP8.set(e,r)}function writeAsciiToMemory(e,r,i){for(var n=0;n<e.length;++n)HEAP8[r++>>0]=e.charCodeAt(n);i||(HEAP8[r>>0]=0)}Module.addOnPreRun=addOnPreRun,Module.addOnInit=addOnInit,Module.addOnPreMain=addOnPreMain,Module.addOnExit=addOnExit,Module.addOnPostRun=addOnPostRun,Module.intArrayFromString=intArrayFromString,Module.intArrayToString=intArrayToString,Module.writeStringToMemory=writeStringToMemory,Module.writeArrayToMemory=writeArrayToMemory,Module.writeAsciiToMemory=writeAsciiToMemory,Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(e,r){var i=65535&e,n=65535&r;return i*n+((e>>>16)*n+i*(r>>>16)<<16)|0}),Math.imul=Math.imul,Math.clz32||(Math.clz32=function(e){e>>>=0;for(var r=0;r<32;r++)if(e&1<<31-r)return r;return 32}),Math.clz32=Math.clz32,Math.trunc||(Math.trunc=function(e){return e<0?Math.ceil(e):Math.floor(e)}),Math.trunc=Math.trunc;var Math_abs=Math.abs,Math_ceil=Math.ceil,Math_floor=Math.floor,Math_min=Math.min,runDependencies=0,dependenciesFulfilled=null;function addRunDependency(e){runDependencies++,Module.monitorRunDependencies&&Module.monitorRunDependencies(runDependencies)}function removeRunDependency(e){if(runDependencies--,Module.monitorRunDependencies&&Module.monitorRunDependencies(runDependencies),0==runDependencies&&dependenciesFulfilled){var r=dependenciesFulfilled;dependenciesFulfilled=null,r()}}Module.addRunDependency=addRunDependency,Module.removeRunDependency=removeRunDependency,Module.preloadedImages={},Module.preloadedAudios={},STATIC_BASE=Runtime.GLOBAL_BASE,STATICTOP=STATIC_BASE+6192,__ATINIT__.push(),allocate([228,2,0,0,81,16,0,0,12,3,0,0,177,16,0,0,32,0,0,0,0,0,0,0,12,3,0,0,94,16,0,0,48,0,0,0,0,0,0,0,228,2,0,0,127,16,0,0,12,3,0,0,140,16,0,0,16,0,0,0,0,0,0,0,12,3,0,0,183,17,0,0,32,0,0,0,0,0,0,0,12,3,0,0,147,17,0,0,72,0,0,0,0,0,0,0,108,0,0,0,5,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,32,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,248,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,224,1,0,0,5,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,3,0,0,0,2,0,0,0,40,20,0,0,0,4,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,10,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,16,0,0,0,1,0,0,0,2,0,0,0,3,0,0,0,4,0,0,0,5,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,56,0,0,0,1,0,0,0,5,0,0,0,3,0,0,0,4,0,0,0,5,0,0,0,2,0,0,0,2,0,0,0,2,0,0,0,37,115,40,37,117,41,58,32,65,115,115,101,114,116,105,111,110,32,102,97,105,108,117,114,101,58,32,34,37,115,34,10,0,109,95,115,105,122,101,32,60,61,32,109,95,99,97,112,97,99,105,116,121,0,46,47,105,110,99,92,99,114,110,95,100,101,99,111,109,112,46,104,0,109,105,110,95,110,101,119,95,99,97,112,97,99,105,116,121,32,60,32,40,48,120,55,70,70,70,48,48,48,48,85,32,47,32,101,108,101,109,101,110,116,95,115,105,122,101,41,0,110,101,119,95,99,97,112,97,99,105,116,121,32,38,38,32,40,110,101,119,95,99,97,112,97,99,105,116,121,32,62,32,109,95,99,97,112,97,99,105,116,121,41,0,110,117,109,95,99,111,100,101,115,91,99,93,0,115,111,114,116,101,100,95,112,111,115,32,60,32,116,111,116,97,108,95,117,115,101,100,95,115,121,109,115,0,112,67,111,100,101,115,105,122,101,115,91,115,121,109,95,105,110,100,101,120,93,32,61,61,32,99,111,100,101,115,105,122,101,0,116,32,60,32,40,49,85,32,60,60,32,116,97,98,108,101,95,98,105,116,115,41,0,109,95,108,111,111,107,117,112,91,116,93,32,61,61,32,99,85,73,78,84,51,50,95,77,65,88,0,99,114,110,100,95,109,97,108,108,111,99,58,32,115,105,122,101,32,116,111,111,32,98,105,103,0,99,114,110,100,95,109,97,108,108,111,99,58,32,111,117,116,32,111,102,32,109,101,109,111,114,121,0,40,40,117,105,110,116,51,50,41,112,95,110,101,119,32,38,32,40,67,82,78,68,95,77,73,78,95,65,76,76,79,67,95,65,76,73,71,78,77,69,78,84,32,45,32,49,41,41,32,61,61,32,48,0,99,114,110,100,95,114,101,97,108,108,111,99,58,32,98,97,100,32,112,116,114,0,99,114,110,100,95,102,114,101,101,58,32,98,97,100,32,112,116,114,0,102,97,108,115,101,0,40,116,111,116,97,108,95,115,121,109,115,32,62,61,32,49,41,32,38,38,32,40,116,111,116,97,108,95,115,121,109,115,32,60,61,32,112,114,101,102,105,120,95,99,111,100,105,110,103,58,58,99,77,97,120,83,117,112,112,111,114,116,101,100,83,121,109,115,41,0,17,18,19,20,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15,16,48,0,110,117,109,95,98,105,116,115,32,60,61,32,51,50,85,0,109,95,98,105,116,95,99,111,117,110,116,32,60,61,32,99,66,105,116,66,117,102,83,105,122,101,0,116,32,33,61,32,99,85,73,78,84,51,50,95,77,65,88,0,109,111,100,101,108,46,109,95,99,111,100,101,95,115,105,122,101,115,91,115,121,109,93,32,61,61,32,108,101,110,0,0,2,3,1,0,2,3,4,5,6,7,1,40,108,101,110,32,62,61,32,49,41,32,38,38,32,40,108,101,110,32,60,61,32,99,77,97,120,69,120,112,101,99,116,101,100,67,111,100,101,83,105,122,101,41,0,105,32,60,32,109,95,115,105,122,101,0,110,101,120,116,95,108,101,118,101,108,95,111,102,115,32,62,32,99,117,114,95,108,101,118,101,108,95,111,102,115,0,1,2,2,3,3,3,3,4,0,0,0,0,0,0,1,1,0,1,0,1,0,0,1,2,1,2,0,0,0,1,0,2,1,0,2,0,0,1,2,3,110,117,109,32,38,38,32,40,110,117,109,32,61,61,32,126,110,117,109,95,99,104,101,99,107,41,0,17,0,10,0,17,17,17,0,0,0,0,5,0,0,0,0,0,0,9,0,0,0,0,11,0,0,0,0,0,0,0,0,17,0,15,10,17,17,17,3,10,7,0,1,19,9,11,11,0,0,9,6,11,0,0,11,0,6,17,0,0,0,17,17,17,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,0,17,0,10,10,17,17,17,0,10,0,0,2,0,9,11,0,0,0,9,0,11,0,0,11,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,12,0,0,0,0,9,12,0,0,0,0,0,12,0,0,12,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,14,0,0,0,0,0,0,0,0,0,0,0,13,0,0,0,4,13,0,0,0,0,9,14,0,0,0,0,0,14,0,0,14,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16,0,0,0,0,0,0,0,0,0,0,0,15,0,0,0,0,15,0,0,0,0,9,16,0,0,0,0,0,16,0,0,16,0,0,18,0,0,0,18,18,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,18,0,0,0,18,18,18,0,0,0,0,0,0,9,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,0,0,0,0,10,0,0,0,0,10,0,0,0,0,9,11,0,0,0,0,0,11,0,0,11,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,12,0,0,0,0,9,12,0,0,0,0,0,12,0,0,12,0,0,45,43,32,32,32,48,88,48,120,0,40,110,117,108,108,41,0,45,48,88,43,48,88,32,48,88,45,48,120,43,48,120,32,48,120,0,105,110,102,0,73,78,70,0,110,97,110,0,78,65,78,0,48,49,50,51,52,53,54,55,56,57,65,66,67,68,69,70,46,0,84,33,34,25,13,1,2,3,17,75,28,12,16,4,11,29,18,30,39,104,110,111,112,113,98,32,5,6,15,19,20,21,26,8,22,7,40,36,23,24,9,10,14,27,31,37,35,131,130,125,38,42,43,60,61,62,63,67,71,74,77,88,89,90,91,92,93,94,95,96,97,99,100,101,102,103,105,106,107,108,114,115,116,121,122,123,124,0,73,108,108,101,103,97,108,32,98,121,116,101,32,115,101,113,117,101,110,99,101,0,68,111,109,97,105,110,32,101,114,114,111,114,0,82,101,115,117,108,116,32,110,111,116,32,114,101,112,114,101,115,101,110,116,97,98,108,101,0,78,111,116,32,97,32,116,116,121,0,80,101,114,109,105,115,115,105,111,110,32,100,101,110,105,101,100,0,79,112,101,114,97,116,105,111,110,32,110,111,116,32,112,101,114,109,105,116,116,101,100,0,78,111,32,115,117,99,104,32,102,105,108,101,32,111,114,32,100,105,114,101,99,116,111,114,121,0,78,111,32,115,117,99,104,32,112,114,111,99,101,115,115,0,70,105,108,101,32,101,120,105,115,116,115,0,86,97,108,117,101,32,116,111,111,32,108,97,114,103,101,32,102,111,114,32,100,97,116,97,32,116,121,112,101,0,78,111,32,115,112,97,99,101,32,108,101,102,116,32,111,110,32,100,101,118,105,99,101,0,79,117,116,32,111,102,32,109,101,109,111,114,121,0,82,101,115,111,117,114,99,101,32,98,117,115,121,0,73,110,116,101,114,114,117,112,116,101,100,32,115,121,115,116,101,109,32,99,97,108,108,0,82,101,115,111,117,114,99,101,32,116,101,109,112,111,114,97,114,105,108,121,32,117,110,97,118,97,105,108,97,98,108,101,0,73,110,118,97,108,105,100,32,115,101,101,107,0,67,114,111,115,115,45,100,101,118,105,99,101,32,108,105,110,107,0,82,101,97,100,45,111,110,108,121,32,102,105,108,101,32,115,121,115,116,101,109,0,68,105,114,101,99,116,111,114,121,32,110,111,116,32,101,109,112,116,121,0,67,111,110,110,101,99,116,105,111,110,32,114,101,115,101,116,32,98,121,32,112,101,101,114,0,79,112,101,114,97,116,105,111,110,32,116,105,109,101,100,32,111,117,116,0,67,111,110,110,101,99,116,105,111,110,32,114,101,102,117,115,101,100,0,72,111,115,116,32,105,115,32,100,111,119,110,0,72,111,115,116,32,105,115,32,117,110,114,101,97,99,104,97,98,108,101,0,65,100,100,114,101,115,115,32,105,110,32,117,115,101,0,66,114,111,107,101,110,32,112,105,112,101,0,73,47,79,32,101,114,114,111,114,0,78,111,32,115,117,99,104,32,100,101,118,105,99,101,32,111,114,32,97,100,100,114,101,115,115,0,66,108,111,99,107,32,100,101,118,105,99,101,32,114,101,113,117,105,114,101,100,0,78,111,32,115,117,99,104,32,100,101,118,105,99,101,0,78,111,116,32,97,32,100,105,114,101,99,116,111,114,121,0,73,115,32,97,32,100,105,114,101,99,116,111,114,121,0,84,101,120,116,32,102,105,108,101,32,98,117,115,121,0,69,120,101,99,32,102,111,114,109,97,116,32,101,114,114,111,114,0,73,110,118,97,108,105,100,32,97,114,103,117,109,101,110,116,0,65,114,103,117,109,101,110,116,32,108,105,115,116,32,116,111,111,32,108,111,110,103,0,83,121,109,98,111,108,105,99,32,108,105,110,107,32,108,111,111,112,0,70,105,108,101,110,97,109,101,32,116,111,111,32,108,111,110,103,0,84,111,111,32,109,97,110,121,32,111,112,101,110,32,102,105,108,101,115,32,105,110,32,115,121,115,116,101,109,0,78,111,32,102,105,108,101,32,100,101,115,99,114,105,112,116,111,114,115,32,97,118,97,105,108,97,98,108,101,0,66,97,100,32,102,105,108,101,32,100,101,115,99,114,105,112,116,111,114,0,78,111,32,99,104,105,108,100,32,112,114,111,99,101,115,115,0,66,97,100,32,97,100,100,114,101,115,115,0,70,105,108,101,32,116,111,111,32,108,97,114,103,101,0,84,111,111,32,109,97,110,121,32,108,105,110,107,115,0,78,111,32,108,111,99,107,115,32,97,118,97,105,108,97,98,108,101,0,82,101,115,111,117,114,99,101,32,100,101,97,100,108,111,99,107,32,119,111,117,108,100,32,111,99,99,117,114,0,83,116,97,116,101,32,110,111,116,32,114,101,99,111,118,101,114,97,98,108,101,0,80,114,101,118,105,111,117,115,32,111,119,110,101,114,32,100,105,101,100,0,79,112,101,114,97,116,105,111,110,32,99,97,110,99,101,108,101,100,0,70,117,110,99,116,105,111,110,32,110,111,116,32,105,109,112,108,101,109,101,110,116,101,100,0,78,111,32,109,101,115,115,97,103,101,32,111,102,32,100,101,115,105,114,101,100,32,116,121,112,101,0,73,100,101,110,116,105,102,105,101,114,32,114,101,109,111,118,101,100,0,68,101,118,105,99,101,32,110,111,116,32,97,32,115,116,114,101,97,109,0,78,111,32,100,97,116,97,32,97,118,97,105,108,97,98,108,101,0,68,101,118,105,99,101,32,116,105,109,101,111,117,116,0,79,117,116,32,111,102,32,115,116,114,101,97,109,115,32,114,101,115,111,117,114,99,101,115,0,76,105,110,107,32,104,97,115,32,98,101,101,110,32,115,101,118,101,114,101,100,0,80,114,111,116,111,99,111,108,32,101,114,114,111,114,0,66,97,100,32,109,101,115,115,97,103,101,0,70,105,108,101,32,100,101,115,99,114,105,112,116,111,114,32,105,110,32,98,97,100,32,115,116,97,116,101,0,78,111,116,32,97,32,115,111,99,107,101,116,0,68,101,115,116,105,110,97,116,105,111,110,32,97,100,100,114,101,115,115,32,114,101,113,117,105,114,101,100,0,77,101,115,115,97,103,101,32,116,111,111,32,108,97,114,103,101,0,80,114,111,116,111,99,111,108,32,119,114,111,110,103,32,116,121,112,101,32,102,111,114,32,115,111,99,107,101,116,0,80,114,111,116,111,99,111,108,32,110,111,116,32,97,118,97,105,108,97,98,108,101,0,80,114,111,116,111,99,111,108,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,83,111,99,107,101,116,32,116,121,112,101,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,78,111,116,32,115,117,112,112,111,114,116,101,100,0,80,114,111,116,111,99,111,108,32,102,97,109,105,108,121,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,65,100,100,114,101,115,115,32,102,97,109,105,108,121,32,110,111,116,32,115,117,112,112,111,114,116,101,100,32,98,121,32,112,114,111,116,111,99,111,108,0,65,100,100,114,101,115,115,32,110,111,116,32,97,118,97,105,108,97,98,108,101,0,78,101,116,119,111,114,107,32,105,115,32,100,111,119,110,0,78,101,116,119,111,114,107,32,117,110,114,101,97,99,104,97,98,108,101,0,67,111,110,110,101,99,116,105,111,110,32,114,101,115,101,116,32,98,121,32,110,101,116,119,111,114,107,0,67,111,110,110,101,99,116,105,111,110,32,97,98,111,114,116,101,100,0,78,111,32,98,117,102,102,101,114,32,115,112,97,99,101,32,97,118,97,105,108,97,98,108,101,0,83,111,99,107,101,116,32,105,115,32,99,111,110,110,101,99,116,101,100,0,83,111,99,107,101,116,32,110,111,116,32,99,111,110,110,101,99,116,101,100,0,67,97,110,110,111,116,32,115,101,110,100,32,97,102,116,101,114,32,115,111,99,107,101,116,32,115,104,117,116,100,111,119,110,0,79,112,101,114,97,116,105,111,110,32,97,108,114,101,97,100,121,32,105,110,32,112,114,111,103,114,101,115,115,0,79,112,101,114,97,116,105,111,110,32,105,110,32,112,114,111,103,114,101,115,115,0,83,116,97,108,101,32,102,105,108,101,32,104,97,110,100,108,101,0,82,101,109,111,116,101,32,73,47,79,32,101,114,114,111,114,0,81,117,111,116,97,32,101,120,99,101,101,100,101,100,0,78,111,32,109,101,100,105,117,109,32,102,111,117,110,100,0,87,114,111,110,103,32,109,101,100,105,117,109,32,116,121,112,101,0,78,111,32,101,114,114,111,114,32,105,110,102,111,114,109,97,116,105,111,110,0,0,116,101,114,109,105,110,97,116,105,110,103,32,119,105,116,104,32,37,115,32,101,120,99,101,112,116,105,111,110,32,111,102,32,116,121,112,101,32,37,115,58,32,37,115,0,116,101,114,109,105,110,97,116,105,110,103,32,119,105,116,104,32,37,115,32,101,120,99,101,112,116,105,111,110,32,111,102,32,116,121,112,101,32,37,115,0,116,101,114,109,105,110,97,116,105,110,103,32,119,105,116,104,32,37,115,32,102,111,114,101,105,103,110,32,101,120,99,101,112,116,105,111,110,0,116,101,114,109,105,110,97,116,105,110,103,0,117,110,99,97,117,103,104,116,0,83,116,57,101,120,99,101,112,116,105,111,110,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,54,95,95,115,104,105,109,95,116,121,112,101,95,105,110,102,111,69,0,83,116,57,116,121,112,101,95,105,110,102,111,0,78,49,48,95,95,99,120,120,97,98,105,118,49,50,48,95,95,115,105,95,99,108,97,115,115,95,116,121,112,101,95,105,110,102,111,69,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,55,95,95,99,108,97,115,115,95,116,121,112,101,95,105,110,102,111,69,0,112,116,104,114,101,97,100,95,111,110,99,101,32,102,97,105,108,117,114,101,32,105,110,32,95,95,99,120,97,95,103,101,116,95,103,108,111,98,97,108,115,95,102,97,115,116,40,41,0,99,97,110,110,111,116,32,99,114,101,97,116,101,32,112,116,104,114,101,97,100,32,107,101,121,32,102,111,114,32,95,95,99,120,97,95,103,101,116,95,103,108,111,98,97,108,115,40,41,0,99,97,110,110,111,116,32,122,101,114,111,32,111,117,116,32,116,104,114,101,97,100,32,118,97,108,117,101,32,102,111,114,32,95,95,99,120,97,95,103,101,116,95,103,108,111,98,97,108,115,40,41,0,116,101,114,109,105,110,97,116,101,95,104,97,110,100,108,101,114,32,117,110,101,120,112,101,99,116,101,100,108,121,32,114,101,116,117,114,110,101,100,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,57,95,95,112,111,105,110,116,101,114,95,116,121,112,101,95,105,110,102,111,69,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,55,95,95,112,98,97,115,101,95,116,121,112,101,95,105,110,102,111,69,0],"i8",ALLOC_NONE,Runtime.GLOBAL_BASE);var tempDoublePtr=STATICTOP;function _abort(){Module.abort()}function __ZSt18uncaught_exceptionv(){return!!__ZSt18uncaught_exceptionv.uncaught_exception}STATICTOP+=16;var EXCEPTIONS={last:0,caught:[],infos:{},deAdjust:function(e){if(!e||EXCEPTIONS.infos[e])return e;for(var r in EXCEPTIONS.infos){if(EXCEPTIONS.infos[r].adjusted===e)return r}return e},addRef:function(e){e&&EXCEPTIONS.infos[e].refcount++},decRef:function(e){if(e){var r=EXCEPTIONS.infos[e];assert(0<r.refcount),r.refcount--,0!==r.refcount||r.rethrown||(r.destructor&&Module.dynCall_vi(r.destructor,e),delete EXCEPTIONS.infos[e],___cxa_free_exception(e))}},clearRef:function(e){e&&(EXCEPTIONS.infos[e].refcount=0)}};function ___cxa_begin_catch(e){var r=EXCEPTIONS.infos[e];return r&&!r.caught&&(r.caught=!0,__ZSt18uncaught_exceptionv.uncaught_exception--),r&&(r.rethrown=!1),EXCEPTIONS.caught.push(e),EXCEPTIONS.addRef(EXCEPTIONS.deAdjust(e)),e}function _pthread_once(e,r){_pthread_once.seen||(_pthread_once.seen={}),e in _pthread_once.seen||(Module.dynCall_v(r),_pthread_once.seen[e]=1)}function _emscripten_memcpy_big(e,r,i){return HEAPU8.set(HEAPU8.subarray(r,r+i),e),e}var SYSCALLS={varargs:0,get:function(e){return SYSCALLS.varargs+=4,HEAP32[SYSCALLS.varargs-4>>2]},getStr:function(){return Pointer_stringify(SYSCALLS.get())},get64:function(){var e=SYSCALLS.get(),r=SYSCALLS.get();return assert(0<=e?0===r:-1===r),e},getZero:function(){assert(0===SYSCALLS.get())}};function ___syscall6(e,r){SYSCALLS.varargs=r;try{var i=SYSCALLS.getStreamFromFD();return FS.close(i),0}catch(e){return"undefined"!=typeof FS&&e instanceof FS.ErrnoError||abort(e),-e.errno}}var cttz_i8=allocate([8,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,6,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,7,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,6,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0],"i8",ALLOC_STATIC),PTHREAD_SPECIFIC={};function _pthread_getspecific(e){return PTHREAD_SPECIFIC[e]||0}function ___setErrNo(e){return Module.___errno_location&&(HEAP32[Module.___errno_location()>>2]=e),e}var PTHREAD_SPECIFIC_NEXT_KEY=1,ERRNO_CODES={EPERM:1,ENOENT:2,ESRCH:3,EINTR:4,EIO:5,ENXIO:6,E2BIG:7,ENOEXEC:8,EBADF:9,ECHILD:10,EAGAIN:11,EWOULDBLOCK:11,ENOMEM:12,EACCES:13,EFAULT:14,ENOTBLK:15,EBUSY:16,EEXIST:17,EXDEV:18,ENODEV:19,ENOTDIR:20,EISDIR:21,EINVAL:22,ENFILE:23,EMFILE:24,ENOTTY:25,ETXTBSY:26,EFBIG:27,ENOSPC:28,ESPIPE:29,EROFS:30,EMLINK:31,EPIPE:32,EDOM:33,ERANGE:34,ENOMSG:42,EIDRM:43,ECHRNG:44,EL2NSYNC:45,EL3HLT:46,EL3RST:47,ELNRNG:48,EUNATCH:49,ENOCSI:50,EL2HLT:51,EDEADLK:35,ENOLCK:37,EBADE:52,EBADR:53,EXFULL:54,ENOANO:55,EBADRQC:56,EBADSLT:57,EDEADLOCK:35,EBFONT:59,ENOSTR:60,ENODATA:61,ETIME:62,ENOSR:63,ENONET:64,ENOPKG:65,EREMOTE:66,ENOLINK:67,EADV:68,ESRMNT:69,ECOMM:70,EPROTO:71,EMULTIHOP:72,EDOTDOT:73,EBADMSG:74,ENOTUNIQ:76,EBADFD:77,EREMCHG:78,ELIBACC:79,ELIBBAD:80,ELIBSCN:81,ELIBMAX:82,ELIBEXEC:83,ENOSYS:38,ENOTEMPTY:39,ENAMETOOLONG:36,ELOOP:40,EOPNOTSUPP:95,EPFNOSUPPORT:96,ECONNRESET:104,ENOBUFS:105,EAFNOSUPPORT:97,EPROTOTYPE:91,ENOTSOCK:88,ENOPROTOOPT:92,ESHUTDOWN:108,ECONNREFUSED:111,EADDRINUSE:98,ECONNABORTED:103,ENETUNREACH:101,ENETDOWN:100,ETIMEDOUT:110,EHOSTDOWN:112,EHOSTUNREACH:113,EINPROGRESS:115,EALREADY:114,EDESTADDRREQ:89,EMSGSIZE:90,EPROTONOSUPPORT:93,ESOCKTNOSUPPORT:94,EADDRNOTAVAIL:99,ENETRESET:102,EISCONN:106,ENOTCONN:107,ETOOMANYREFS:109,EUSERS:87,EDQUOT:122,ESTALE:116,ENOTSUP:95,ENOMEDIUM:123,EILSEQ:84,EOVERFLOW:75,ECANCELED:125,ENOTRECOVERABLE:131,EOWNERDEAD:130,ESTRPIPE:86};function _pthread_key_create(e,r){return 0==e?ERRNO_CODES.EINVAL:(HEAP32[e>>2]=PTHREAD_SPECIFIC_NEXT_KEY,PTHREAD_SPECIFIC[PTHREAD_SPECIFIC_NEXT_KEY]=0,PTHREAD_SPECIFIC_NEXT_KEY++,0)}function ___resumeException(e){throw EXCEPTIONS.last||(EXCEPTIONS.last=e),e+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch."}function ___cxa_find_matching_catch(){var e=EXCEPTIONS.last;if(!e)return 0|(Runtime.setTempRet0(0),0);var r=EXCEPTIONS.infos[e],i=r.type;if(!i)return 0|(Runtime.setTempRet0(0),e);var n=Array.prototype.slice.call(arguments);Module.___cxa_is_pointer_type(i);___cxa_find_matching_catch.buffer||(___cxa_find_matching_catch.buffer=_malloc(4)),HEAP32[___cxa_find_matching_catch.buffer>>2]=e,e=___cxa_find_matching_catch.buffer;for(var t=0;t<n.length;t++)if(n[t]&&Module.___cxa_can_catch(n[t],i,e))return e=HEAP32[e>>2],r.adjusted=e,0|(Runtime.setTempRet0(n[t]),e);return e=HEAP32[e>>2],0|(Runtime.setTempRet0(i),e)}function ___gxx_personality_v0(){}function _pthread_setspecific(e,r){return e in PTHREAD_SPECIFIC?(PTHREAD_SPECIFIC[e]=r,0):ERRNO_CODES.EINVAL}function ___syscall140(e,r){SYSCALLS.varargs=r;try{var i=SYSCALLS.getStreamFromFD(),n=(SYSCALLS.get(),SYSCALLS.get()),t=SYSCALLS.get(),o=SYSCALLS.get(),a=n;return FS.llseek(i,a,o),HEAP32[t>>2]=i.position,i.getdents&&0===a&&0===o&&(i.getdents=null),0}catch(e){return"undefined"!=typeof FS&&e instanceof FS.ErrnoError||abort(e),-e.errno}}function ___syscall146(e,r){SYSCALLS.varargs=r;try{var i=SYSCALLS.get(),n=SYSCALLS.get(),t=SYSCALLS.get(),o=0;___syscall146.buffer||(___syscall146.buffers=[null,[],[]],___syscall146.printChar=function(e,r){var i=___syscall146.buffers[e];assert(i),0===r||10===r?((1===e?Module.print:Module.printErr)(UTF8ArrayToString(i,0)),i.length=0):i.push(r)});for(var a=0;a<t;a++){for(var u=HEAP32[n+8*a>>2],f=HEAP32[n+(8*a+4)>>2],l=0;l<f;l++)___syscall146.printChar(i,HEAPU8[u+l]);o+=f}return o}catch(e){return"undefined"!=typeof FS&&e instanceof FS.ErrnoError||abort(e),-e.errno}}function ___syscall54(e,r){SYSCALLS.varargs=r;try{return 0}catch(e){return"undefined"!=typeof FS&&e instanceof FS.ErrnoError||abort(e),-e.errno}}function invoke_iiii(e,r,i,n){try{return Module.dynCall_iiii(e,r,i,n)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_viiiii(e,r,i,n,t,o){try{Module.dynCall_viiiii(e,r,i,n,t,o)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_vi(e,r){try{Module.dynCall_vi(e,r)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_ii(e,r){try{return Module.dynCall_ii(e,r)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_viii(e,r,i,n){try{Module.dynCall_viii(e,r,i,n)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_v(e){try{Module.dynCall_v(e)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_viiiiii(e,r,i,n,t,o,a){try{Module.dynCall_viiiiii(e,r,i,n,t,o,a)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_viiii(e,r,i,n,t){try{Module.dynCall_viiii(e,r,i,n,t)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}__ATEXIT__.push(function(){var e=Module._fflush;e&&e(0);var r=___syscall146.printChar;if(r){var i=___syscall146.buffers;i[1].length&&r(1,10),i[2].length&&r(2,10)}}),DYNAMICTOP_PTR=allocate(1,"i32",ALLOC_STATIC),STACK_BASE=STACKTOP=Runtime.alignMemory(STATICTOP),STACK_MAX=STACK_BASE+TOTAL_STACK,DYNAMIC_BASE=Runtime.alignMemory(STACK_MAX),HEAP32[DYNAMICTOP_PTR>>2]=DYNAMIC_BASE,staticSealed=!0,Module.asmGlobalArg={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array,NaN:NaN,Infinity:1/0,byteLength:byteLength},Module.asmLibraryArg={abort:abort,assert:assert,enlargeMemory:enlargeMemory,getTotalMemory:getTotalMemory,abortOnCannotGrowMemory:abortOnCannotGrowMemory,invoke_iiii:invoke_iiii,invoke_viiiii:invoke_viiiii,invoke_vi:invoke_vi,invoke_ii:invoke_ii,invoke_viii:invoke_viii,invoke_v:invoke_v,invoke_viiiiii:invoke_viiiiii,invoke_viiii:invoke_viiii,_pthread_getspecific:_pthread_getspecific,___syscall54:___syscall54,_pthread_setspecific:_pthread_setspecific,___gxx_personality_v0:___gxx_personality_v0,___syscall6:___syscall6,___setErrNo:___setErrNo,_abort:_abort,___cxa_begin_catch:___cxa_begin_catch,_pthread_once:_pthread_once,_emscripten_memcpy_big:_emscripten_memcpy_big,_pthread_key_create:_pthread_key_create,___syscall140:___syscall140,___resumeException:___resumeException,___cxa_find_matching_catch:___cxa_find_matching_catch,___syscall146:___syscall146,__ZSt18uncaught_exceptionv:__ZSt18uncaught_exceptionv,DYNAMICTOP_PTR:DYNAMICTOP_PTR,tempDoublePtr:tempDoublePtr,ABORT:ABORT,STACKTOP:STACKTOP,STACK_MAX:STACK_MAX,cttz_i8:cttz_i8};var asm=function(e,r,i){var n=e.Int8Array,se=new n(i),t=e.Int16Array,X=new t(i),o=e.Int32Array,_e=new o(i),a=e.Uint8Array,de=new a(i),u=e.Uint16Array,Ee=new u(i),f=e.Uint32Array,l=(new f(i),e.Float32Array),c=(new l(i),e.Float64Array),O=new c(i),s=e.byteLength,_=0|r.DYNAMICTOP_PTR,d=0|r.tempDoublePtr,Me=(r.ABORT,0|r.STACKTOP),E=(r.STACK_MAX,0|r.cttz_i8),C=(e.NaN,e.Infinity,0),te=(e.Math.floor,e.Math.abs,e.Math.sqrt,e.Math.pow,e.Math.cos,e.Math.sin,e.Math.tan,e.Math.acos,e.Math.asin,e.Math.atan,e.Math.atan2,e.Math.exp,e.Math.log,e.Math.ceil,e.Math.imul),M=(e.Math.min,e.Math.max,e.Math.clz32),T=r.abort,A=(r.assert,r.enlargeMemory),b=r.getTotalMemory,h=r.abortOnCannotGrowMemory,m=(r.invoke_iiii,r.invoke_viiiii,r.invoke_vi,r.invoke_ii,r.invoke_viii,r.invoke_v,r.invoke_viiiiii,r.invoke_viiii,r._pthread_getspecific),p=r.___syscall54,v=r._pthread_setspecific,S=(r.___gxx_personality_v0,r.___syscall6),k=r.___setErrNo,y=r._abort,R=(r.___cxa_begin_catch,r._pthread_once),g=r._emscripten_memcpy_big,N=r._pthread_key_create,P=r.___syscall140,w=(r.___resumeException,r.___cxa_find_matching_catch,r.___syscall146);r.__ZSt18uncaught_exceptionv;function I(e){e|=0;var r,i=0,n=0,t=0,o=0,a=0,u=0,f=0,l=0,c=0,s=0,_=0,d=0,E=0,M=0,T=0,A=0,b=0,h=0,m=0,p=0;Me=(r=Me)+16|0,d=r;do{if(e>>>0<245){if(e=(c=e>>>0<11?16:e+11&-8)>>>3,3&(n=(_=0|_e[1144])>>>e)|0)return t=0|_e[(n=(e=4616+((i=(1&n^1)+e|0)<<1<<2)|0)+8|0)>>2],(0|e)==(0|(a=0|_e[(o=t+8|0)>>2]))?_e[1144]=_&~(1<<i):(_e[a+12>>2]=e,_e[n>>2]=a),p=i<<3,_e[t+4>>2]=3|p,_e[(p=t+p+4|0)>>2]=1|_e[p>>2],Me=r,0|(p=o);if((s=0|_e[1146])>>>0<c>>>0){if(0|n)return i=((i=n<<e&((i=2<<e)|0-i))&0-i)-1|0,o=0|_e[(e=(i=4616+((t=((n=(i>>>=u=i>>>12&16)>>>5&8)|u|(o=(i>>>=n)>>>2&4)|(e=(i>>>=o)>>>1&2)|(t=(i>>>=e)>>>1&1))+(i>>>t)|0)<<1<<2)|0)+8|0)>>2],(0|i)==(0|(n=0|_e[(u=o+8|0)>>2]))?(e=_&~(1<<t),_e[1144]=e):(_e[n+12>>2]=i,_e[e>>2]=n,e=_),a=(t<<3)-c|0,_e[o+4>>2]=3|c,_e[(t=o+c|0)+4>>2]=1|a,_e[t+a>>2]=a,0|s&&(o=0|_e[1149],n=4616+((i=s>>>3)<<1<<2)|0,e&(i=1<<i)?i=0|_e[(e=n+8|0)>>2]:(_e[1144]=e|i,e=(i=n)+8|0),_e[e>>2]=o,_e[i+12>>2]=o,_e[o+8>>2]=i,_e[o+12>>2]=n),_e[1146]=a,_e[1149]=t,Me=r,0|(p=u);if(f=0|_e[1145]){if(n=(f&0-f)-1|0,e=0|_e[4880+(((a=(n>>>=u=n>>>12&16)>>>5&8)|u|(l=(n>>>=a)>>>2&4)|(t=(n>>>=l)>>>1&2)|(e=(n>>>=t)>>>1&1))+(n>>>e)<<2)>>2],n=(-8&_e[e+4>>2])-c|0,t=0|_e[e+16+((0==(0|_e[e+16>>2])&1)<<2)>>2]){for(;n=(l=(u=(-8&_e[t+4>>2])-c|0)>>>0<n>>>0)?u:n,e=l?t:e,0!=(0|(t=0|_e[t+16+((0==(0|_e[t+16>>2])&1)<<2)>>2])););l=e,a=n}else l=e,a=n;if(l>>>0<(u=l+c|0)>>>0){o=0|_e[l+24>>2],i=0|_e[l+12>>2];do{if((0|i)==(0|l)){if(!(i=0|_e[(e=l+20|0)>>2])&&!(i=0|_e[(e=l+16|0)>>2])){n=0;break}for(;;)if(0|(t=0|_e[(n=i+20|0)>>2]))i=t,e=n;else{if(!(t=0|_e[(n=i+16|0)>>2]))break;i=t,e=n}_e[e>>2]=0,n=i}else n=0|_e[l+8>>2],_e[n+12>>2]=i,_e[i+8>>2]=n,n=i}while(0);do{if(0|o){if(i=0|_e[l+28>>2],(0|l)==(0|_e[(e=4880+(i<<2)|0)>>2])){if(!(_e[e>>2]=n)){_e[1145]=f&~(1<<i);break}}else if(!(_e[o+16+(((0|_e[o+16>>2])!=(0|l)&1)<<2)>>2]=n))break;_e[n+24>>2]=o,0|(i=0|_e[l+16>>2])&&(_e[n+16>>2]=i,_e[i+24>>2]=n),0|(i=0|_e[l+20>>2])&&(_e[n+20>>2]=i,_e[i+24>>2]=n)}}while(0);return a>>>0<16?(p=a+c|0,_e[l+4>>2]=3|p,_e[(p=l+p+4|0)>>2]=1|_e[p>>2]):(_e[l+4>>2]=3|c,_e[u+4>>2]=1|a,_e[u+a>>2]=a,0|s&&(t=0|_e[1149],n=4616+((i=s>>>3)<<1<<2)|0,_&(i=1<<i)?i=0|_e[(e=n+8|0)>>2]:(_e[1144]=_|i,e=(i=n)+8|0),_e[e>>2]=t,_e[i+12>>2]=t,_e[t+8>>2]=i,_e[t+12>>2]=n),_e[1146]=a,_e[1149]=u),Me=r,0|(p=l+8|0)}_=c}else _=c}else _=c}else if(e>>>0<=4294967231)if(c=-8&(e=e+11|0),l=0|_e[1145]){t=0-c|0,f=(e>>>=8)?16777215<c>>>0?31:c>>>((f=14-((s=((m=e<<(_=(e+1048320|0)>>>16&8))+520192|0)>>>16&4)|_|(f=((m<<=s)+245760|0)>>>16&2))+(m<<f>>>15)|0)+7|0)&1|f<<1:0,n=0|_e[4880+(f<<2)>>2];e:do{if(n)for(u=c<<(31==((e=0)|f)?0:25-(f>>>1)|0),a=0;;){if((o=(-8&_e[n+4>>2])-c|0)>>>0<t>>>0){if(!o){t=0,o=e=n,m=61;break e}e=n,t=o}if(a=0==(0|(o=0|_e[n+20>>2]))|(0|o)==(0|(n=0|_e[n+16+(u>>>31<<2)>>2]))?a:o,o=0==(0|n)){n=a,m=57;break}u<<=1&(1^o)}else e=n=0,m=57}while(0);if(57==(0|m)){if(0==(0|n)&0==(0|e)){if(!(e=l&((e=2<<f)|0-e))){_=c;break}_=(e&0-e)-1|0,n=(e=0)|_e[4880+(((a=(_>>>=u=_>>>12&16)>>>5&8)|u|(f=(_>>>=a)>>>2&4)|(s=(_>>>=f)>>>1&2)|(n=(_>>>=s)>>>1&1))+(_>>>n)<<2)>>2]}n?(o=n,m=61):(f=e,u=t)}if(61==(0|m))for(;;){if(m=0,n=(_=(n=(-8&_e[o+4>>2])-c|0)>>>0<t>>>0)?n:t,e=_?o:e,!(o=0|_e[o+16+((0==(0|_e[o+16>>2])&1)<<2)>>2])){f=e,u=n;break}t=n,m=61}if(0!=(0|f)&&u>>>0<((0|_e[1146])-c|0)>>>0){if((a=f+c|0)>>>0<=f>>>0)return Me=r,(p=0)|p;o=0|_e[f+24>>2],i=0|_e[f+12>>2];do{if((0|i)==(0|f)){if(!(i=0|_e[(e=f+20|0)>>2])&&!(i=0|_e[(e=f+16|0)>>2])){i=0;break}for(;;)if(0|(t=0|_e[(n=i+20|0)>>2]))i=t,e=n;else{if(!(t=0|_e[(n=i+16|0)>>2]))break;i=t,e=n}_e[e>>2]=0}else p=0|_e[f+8>>2],_e[p+12>>2]=i,_e[i+8>>2]=p}while(0);do{if(o){if(e=0|_e[f+28>>2],(0|f)==(0|_e[(n=4880+(e<<2)|0)>>2])){if(!(_e[n>>2]=i)){t=l&~(1<<e),_e[1145]=t;break}}else if(!(_e[o+16+(((0|_e[o+16>>2])!=(0|f)&1)<<2)>>2]=i)){t=l;break}_e[i+24>>2]=o,0|(e=0|_e[f+16>>2])&&(_e[i+16>>2]=e,_e[e+24>>2]=i),t=((e=0|_e[f+20>>2])&&(_e[i+20>>2]=e,_e[e+24>>2]=i),l)}else t=l}while(0);do{if(16<=u>>>0){if(_e[f+4>>2]=3|c,_e[a+4>>2]=1|u,i=(_e[a+u>>2]=u)>>>3,u>>>0<256){n=4616+(i<<1<<2)|0,(e=0|_e[1144])&(i=1<<i)?i=0|_e[(e=n+8|0)>>2]:(_e[1144]=e|i,e=(i=n)+8|0),_e[e>>2]=a,_e[i+12>>2]=a,_e[a+8>>2]=i,_e[a+12>>2]=n;break}if(n=4880+((i=(i=u>>>8)?16777215<u>>>0?31:u>>>((i=14-((h=((p=i<<(m=(i+1048320|0)>>>16&8))+520192|0)>>>16&4)|m|(i=((p<<=h)+245760|0)>>>16&2))+(p<<i>>>15)|0)+7|0)&1|i<<1:0)<<2)|0,_e[a+28>>2]=i,_e[(e=a+16|0)+4>>2]=0,_e[e>>2]=0,!(t&(e=1<<i))){_e[1145]=t|e,_e[n>>2]=a,_e[a+24>>2]=n,_e[a+12>>2]=a,_e[a+8>>2]=a;break}for(e=u<<(31==(0|i)?0:25-(i>>>1)|0),n=0|_e[n>>2];;){if((-8&_e[n+4>>2]|0)==(0|u)){m=97;break}if(!(i=0|_e[(t=n+16+(e>>>31<<2)|0)>>2])){m=96;break}e<<=1,n=i}if(96==(0|m)){_e[t>>2]=a,_e[a+24>>2]=n,_e[a+12>>2]=a,_e[a+8>>2]=a;break}if(97==(0|m)){p=0|_e[(m=n+8|0)>>2],_e[p+12>>2]=a,_e[m>>2]=a,_e[a+8>>2]=p,_e[a+12>>2]=n,_e[a+24>>2]=0;break}}else p=u+c|0,_e[f+4>>2]=3|p,_e[(p=f+p+4|0)>>2]=1|_e[p>>2]}while(0);return Me=r,0|(p=f+8|0)}_=c}else _=c;else _=-1}while(0);if(_>>>0<=(n=0|_e[1146])>>>0)return i=n-_|0,e=0|_e[1149],15<i>>>0?(p=e+_|0,_e[1149]=p,_e[1146]=i,_e[p+4>>2]=1|i,_e[p+i>>2]=i,_e[e+4>>2]=3|_):(_e[1146]=0,_e[1149]=0,_e[e+4>>2]=3|n,_e[(p=e+n+4|0)>>2]=1|_e[p>>2]),Me=r,0|(p=e+8|0);if(_>>>0<(u=0|_e[1147])>>>0)return h=u-_|0,_e[1147]=h,m=(p=0|_e[1150])+_|0,_e[1150]=m,_e[m+4>>2]=1|h,_e[p+4>>2]=3|_,Me=r,0|(p=p+8|0);if(f=_+48|0,(c=(a=(e=0|_e[1262]?0|_e[1264]:(_e[1264]=4096,_e[1263]=4096,_e[1265]=-1,_e[1266]=-1,_e[1267]=0,_e[1255]=0,e=-16&d^1431655768,_e[d>>2]=e,_e[1262]=e,4096))+(l=_+47|0)|0)&(o=0-e|0))>>>0<=_>>>0)return Me=r,(p=0)|p;if(0|(e=0|_e[1254])&&(d=(s=0|_e[1252])+c|0)>>>0<=s>>>0|e>>>0<d>>>0)return Me=r,(p=0)|p;e:do{if(4&_e[1255])i=0,m=133;else{n=0|_e[1150];r:do{if(n){for(t=5024;!((e=0|_e[t>>2])>>>0<=n>>>0&&(e+(0|_e[(T=t+4|0)>>2])|0)>>>0>n>>>0);){if(!(e=0|_e[t+8>>2])){m=118;break r}t=e}if((i=a-u&o)>>>0<2147483647)if((0|(e=0|ke(0|i)))==((0|_e[t>>2])+(0|_e[T>>2])|0)){if(-1!=(0|e)){u=i,a=e,m=135;break e}}else t=e,m=126;else i=0}else m=118}while(0);do{if(118==(0|m))if(-1!=(0|(n=0|ke(0)))&&(i=n,M=(i=(0==((M=(E=0|_e[1263])+-1|0)&i|0)?0:(M+i&0-E)-i|0)+c|0)+(E=0|_e[1252])|0,_>>>0<i>>>0&i>>>0<2147483647)){if(0|(T=0|_e[1254])&&M>>>0<=E>>>0|T>>>0<M>>>0){i=0;break}if((0|(e=0|ke(0|i)))==(0|n)){u=i,a=n,m=135;break e}t=e,m=126}else i=0}while(0);do{if(126==(0|m)){if(n=0-i|0,!(i>>>0<f>>>0&i>>>0<2147483647&-1!=(0|t))){if(-1==(0|t)){i=0;break}u=i,a=t,m=135;break e}if(2147483647<=(e=l-i+(e=0|_e[1264])&0-e)>>>0){u=i,a=t,m=135;break e}if(-1==(0|ke(0|e))){ke(0|n),i=0;break}u=e+i|0,a=t,m=135;break e}}while(0);_e[1255]=4|_e[1255],m=133}}while(0);if(133==(0|m)&&c>>>0<2147483647&&!(-1==(0|(h=0|ke(0|c)))|1^(b=(_+40|0)>>>0<(A=(T=0|ke(0))-h|0)>>>0)|h>>>0<T>>>0&-1!=(0|h)&-1!=(0|T)^1)&&(u=b?A:i,a=h,m=135),135==(0|m)){i=(0|_e[1252])+u|0,(_e[1252]=i)>>>0>(0|_e[1253])>>>0&&(_e[1253]=i),l=0|_e[1150];do{if(l){for(i=5024;;){if((0|a)==((e=0|_e[i>>2])+(t=0|_e[(n=i+4|0)>>2])|0)){m=145;break}if(!(o=0|_e[i+8>>2]))break;i=o}if(145==(0|m)&&0==(8&_e[i+12>>2]|0)&&l>>>0<a>>>0&e>>>0<=l>>>0){_e[n>>2]=t+u,m=l+(p=0==(7&(p=l+8|0)|0)?0:0-p&7)|0,p=(0|_e[1147])+(u-p)|0,_e[1150]=m,_e[1147]=p,_e[m+4>>2]=1|p,_e[m+p+4>>2]=40,_e[1151]=_e[1266];break}for(a>>>0<(0|_e[1148])>>>0&&(_e[1148]=a),n=a+u|0,i=5024;;){if((0|_e[i>>2])==(0|n)){m=153;break}if(!(e=0|_e[i+8>>2]))break;i=e}if(153==(0|m)&&0==(8&_e[i+12>>2]|0)){_e[i>>2]=a,_e[(s=i+4|0)>>2]=(0|_e[s>>2])+u,c=(s=a+(0==(7&(s=a+8|0)|0)?0:0-s&7)|0)+_|0,f=(i=n+(0==(7&(i=n+8|0)|0)?0:0-i&7)|0)-s-_|0,_e[s+4>>2]=3|_;do{if((0|i)!=(0|l)){if((0|i)==(0|_e[1149])){p=(0|_e[1146])+f|0,_e[1146]=p,_e[1149]=c,_e[c+4>>2]=1|p,_e[c+p>>2]=p;break}if(1==(3&(e=0|_e[i+4>>2])|0)){u=-8&e,t=e>>>3;e:do{if(e>>>0<256){if(e=0|_e[i+8>>2],(0|(n=0|_e[i+12>>2]))==(0|e)){_e[1144]=_e[1144]&~(1<<t);break}_e[e+12>>2]=n,_e[n+8>>2]=e;break}a=0|_e[i+24>>2],e=0|_e[i+12>>2];do{if((0|e)==(0|i)){if(!(e=0|_e[(n=(t=i+16|0)+4|0)>>2])){if(!(e=0|_e[t>>2])){e=0;break}n=t}for(;;)if(0|(o=0|_e[(t=e+20|0)>>2]))e=o,n=t;else{if(!(o=0|_e[(t=e+16|0)>>2]))break;e=o,n=t}_e[n>>2]=0}else p=0|_e[i+8>>2],_e[p+12>>2]=e,_e[e+8>>2]=p}while(0);if(!a)break;t=4880+((n=0|_e[i+28>>2])<<2)|0;do{if((0|i)==(0|_e[t>>2])){if(0|(_e[t>>2]=e))break;_e[1145]=_e[1145]&~(1<<n);break e}if(!(_e[a+16+(((0|_e[a+16>>2])!=(0|i)&1)<<2)>>2]=e))break e}while(0);if(_e[e+24>>2]=a,0|(t=0|_e[(n=i+16|0)>>2])&&(_e[e+16>>2]=t,_e[t+24>>2]=e),!(n=0|_e[n+4>>2]))break;_e[e+20>>2]=n,_e[n+24>>2]=e}while(0);i=i+u|0,o=u+f|0}else o=f;if(_e[(i=i+4|0)>>2]=-2&_e[i>>2],_e[c+4>>2]=1|o,i=(_e[c+o>>2]=o)>>>3,o>>>0<256){n=4616+(i<<1<<2)|0,(e=0|_e[1144])&(i=1<<i)?i=0|_e[(e=n+8|0)>>2]:(_e[1144]=e|i,e=(i=n)+8|0),_e[e>>2]=c,_e[i+12>>2]=c,_e[c+8>>2]=i,_e[c+12>>2]=n;break}i=o>>>8;do{if(i){if(16777215<o>>>0){i=31;break}i=o>>>((i=14-((h=((p=i<<(m=(i+1048320|0)>>>16&8))+520192|0)>>>16&4)|m|(i=((p<<=h)+245760|0)>>>16&2))+(p<<i>>>15)|0)+7|0)&1|i<<1}else i=0}while(0);if(t=4880+(i<<2)|0,_e[c+28>>2]=i,_e[(e=c+16|0)+4>>2]=0,!((e=(_e[e>>2]=0)|_e[1145])&(n=1<<i))){_e[1145]=e|n,_e[t>>2]=c,_e[c+24>>2]=t,_e[c+12>>2]=c,_e[c+8>>2]=c;break}for(e=o<<(31==(0|i)?0:25-(i>>>1)|0),n=0|_e[t>>2];;){if((-8&_e[n+4>>2]|0)==(0|o)){m=194;break}if(!(i=0|_e[(t=n+16+(e>>>31<<2)|0)>>2])){m=193;break}e<<=1,n=i}if(193==(0|m)){_e[t>>2]=c,_e[c+24>>2]=n,_e[c+12>>2]=c,_e[c+8>>2]=c;break}if(194==(0|m)){p=0|_e[(m=n+8|0)>>2],_e[p+12>>2]=c,_e[m>>2]=c,_e[c+8>>2]=p,_e[c+12>>2]=n,_e[c+24>>2]=0;break}}else p=(0|_e[1147])+f|0,_e[1147]=p,_e[1150]=c,_e[c+4>>2]=1|p}while(0);return Me=r,0|(p=s+8|0)}for(i=5024;!((e=0|_e[i>>2])>>>0<=l>>>0&&l>>>0<(p=e+(0|_e[i+4>>2])|0)>>>0);)i=0|_e[i+8>>2];for(i=(e=(e=(o=p+-47|0)+(0==(7&(e=o+8|0)|0)?0:0-e&7)|0)>>>0<(o=l+16|0)>>>0?l:e)+8|0,m=a+(n=0==(7&(n=a+8|0)|0)?0:0-n&7)|0,n=u+-40-n|0,_e[1150]=m,_e[1147]=n,_e[m+4>>2]=1|n,_e[m+n+4>>2]=40,_e[1151]=_e[1266],_e[(n=e+4|0)>>2]=27,_e[i>>2]=_e[1256],_e[i+4>>2]=_e[1257],_e[i+8>>2]=_e[1258],_e[i+12>>2]=_e[1259],_e[1256]=a,_e[1257]=u,_e[1259]=0,_e[1258]=i,i=e+24|0;_e[(i=(m=i)+4|0)>>2]=7,(m+8|0)>>>0<p>>>0;);if((0|e)!=(0|l)){if(a=e-l|0,_e[n>>2]=-2&_e[n>>2],_e[l+4>>2]=1|a,i=(_e[e>>2]=a)>>>3,a>>>0<256){n=4616+(i<<1<<2)|0,(e=0|_e[1144])&(i=1<<i)?i=0|_e[(e=n+8|0)>>2]:(_e[1144]=e|i,e=(i=n)+8|0),_e[e>>2]=l,_e[i+12>>2]=l,_e[l+8>>2]=i,_e[l+12>>2]=n;break}if(t=4880+((n=(i=a>>>8)?16777215<a>>>0?31:a>>>((n=14-((h=((p=i<<(m=(i+1048320|0)>>>16&8))+520192|0)>>>16&4)|m|(n=((p<<=h)+245760|0)>>>16&2))+(p<<n>>>15)|0)+7|0)&1|n<<1:0)<<2)|0,_e[l+28>>2]=n,_e[l+20>>2]=0,!((i=(_e[o>>2]=0)|_e[1145])&(e=1<<n))){_e[1145]=i|e,_e[t>>2]=l,_e[l+24>>2]=t,_e[l+12>>2]=l,_e[l+8>>2]=l;break}for(e=a<<(31==(0|n)?0:25-(n>>>1)|0),n=0|_e[t>>2];;){if((-8&_e[n+4>>2]|0)==(0|a)){m=216;break}if(!(i=0|_e[(t=n+16+(e>>>31<<2)|0)>>2])){m=215;break}e<<=1,n=i}if(215==(0|m)){_e[t>>2]=l,_e[l+24>>2]=n,_e[l+12>>2]=l,_e[l+8>>2]=l;break}if(216==(0|m)){p=0|_e[(m=n+8|0)>>2],_e[p+12>>2]=l,_e[m>>2]=l,_e[l+8>>2]=p,_e[l+12>>2]=n,_e[l+24>>2]=0;break}}}else{for(0==(0|(p=0|_e[1148]))|a>>>0<p>>>0&&(_e[1148]=a),_e[1256]=a,_e[1257]=u,_e[1259]=0,_e[1153]=_e[1262],_e[1152]=-1,i=0;_e[(p=4616+(i<<1<<2)|0)+12>>2]=p,_e[p+8>>2]=p,32!=(0|(i=i+1|0)););m=a+(p=0==(7&(p=a+8|0)|0)?0:0-p&7)|0,p=u+-40-p|0,_e[1150]=m,_e[1147]=p,_e[m+4>>2]=1|p,_e[m+p+4>>2]=40,_e[1151]=_e[1266]}}while(0);if(_>>>0<(i=0|_e[1147])>>>0)return h=i-_|0,_e[1147]=h,m=(p=0|_e[1150])+_|0,_e[1150]=m,_e[m+4>>2]=1|h,_e[p+4>>2]=3|_,Me=r,0|(p=p+8|0)}return p=0|er(),_e[p>>2]=12,Me=r,(p=0)|p}function L(e,r,i,n,t,o){e|=0,r=+r,i|=0,n|=0,t|=0,o|=0;var a,u=0,f=0,l=0,c=0,s=0,_=0,d=0,E=0,M=0,T=0,A=0,b=0,h=0,m=0,p=0,v=0,S=0,k=0,y=0,R=0,g=0,O=0;Me=(a=Me)+560|0,l=a+8|0,g=O=(A=a)+524|0,R=(c=a+512|0)+12|(_e[A>>2]=0),Ue(r),S=(0|C)<0?(r=-r,k=1,2087):(k=0!=(2049&t|0)&1,0==(2048&t|0)?0==(1&t|0)?2088:2093:2090),Ue(r),y=2146435072&C;do{if(y>>>0<2146435072|2146435072==(0|y)&!1){if((u=0!=(E=2*+$e(r,A)))&&(_e[A>>2]=(0|_e[A>>2])-1),97==(0|(h=32|o))){d=0==(0|(M=32&o))?S:S+9|0,_=2|k,u=12-n|0;do{if(!(11<n>>>0|0==(0|u))){for(r=8;r*=16,0!=(0|(u=u+-1|0)););if(45==(0|se[d>>0])){r=-(r+(-E-r));break}r=E+r-r;break}r=E}while(0);for((0|(u=0|Ae(u=(0|(f=0|_e[A>>2]))<0?0-f|0:f,((0|u)<0)<<31>>31,R)))==(0|R)&&(se[(u=c+11|0)>>0]=48),se[u+-1>>0]=43+(f>>31&2),se[(s=u+-2|0)>>0]=o+15,c=(0|n)<1,l=0==(8&t|0),u=O;y=~~r,f=u+1|0,se[u>>0]=de[2122+y>>0]|M,r=16*(r-+(0|y)),u=1!=(f-g|0)||l&c&0==r?f:(se[f>>0]=46,u+2|0),0!=r;);y=u-g|0,pe(e,32,i,u=(g=R-s|0)+_+(R=0!=(0|n)&(y+-2|0)<(0|n)?n+2|0:y)|0,t),Ye(e,d,_),pe(e,48,i,u,65536^t),Ye(e,O,y),pe(e,48,R-y|0,0,0),Ye(e,s,g),pe(e,32,i,u,8192^t);break}for(f=(0|n)<0?6:n,u?(u=(0|_e[A>>2])-28|0,_e[A>>2]=u,r=268435456*E):(r=E,u=0|_e[A>>2]),l=y=(0|u)<0?l:l+288|0;p=~~r>>>0,_e[l>>2]=p,l=l+4|0,0!=(r=1e9*(r-+(p>>>0))););if(0<(0|u))for(c=y,_=l;;){if(s=(0|u)<29?u:29,c>>>0<=(u=_+-4|0)>>>0){for(l=0;b=0|Ne(0|(m=0|He(0|(m=0|Ie(0|_e[u>>2],0,0|s)),0|C,0|l,0)),0|(p=C),1e9,0),_e[u>>2]=b,l=0|We(0|m,0|p,1e9,0),c>>>0<=(u=u+-4|0)>>>0;);l&&(_e[(c=c+-4|0)>>2]=l)}for(l=_;!(l>>>0<=c>>>0||0|_e[(u=l+-4|0)>>2]);)l=u;if(u=(0|_e[A>>2])-s|0,!(0<(0|(_e[A>>2]=u))))break;_=l}else c=y;if((0|u)<0){n=1+((f+25|0)/9|0)|0,T=102==(0|h);do{if(M=(0|(M=0-u|0))<9?M:9,c>>>0<l>>>0){for(s=(1<<M)-1|0,_=1e9>>>M,d=0,u=c;p=0|_e[u>>2],_e[u>>2]=(p>>>M)+d,d=0|te(p&s,_),(u=u+4|0)>>>0<l>>>0;);u=0==(0|_e[c>>2])?c+4|0:c,u=d?(_e[l>>2]=d,c=u,l+4|0):(c=u,l)}else c=0==(0|_e[c>>2])?c+4|0:c,u=l;l=(0|n)<(u-(l=T?y:c)>>2|0)?l+(n<<2)|0:u,u=(0|_e[A>>2])+M|0,_e[A>>2]=u}while((0|u)<0);u=c,n=l}else u=c,n=l;if(p=y,u>>>0<n>>>0){if(l=9*(p-u>>2)|0,10<=(s=0|_e[u>>2])>>>0)for(c=10;l=l+1|0,(c=10*c|0)>>>0<=s>>>0;);}else l=0;if((0|(c=f-(102!=(0|h)?l:0)+(((b=0!=(0|f))&(T=103==(0|h)))<<31>>31)|0))<((9*(n-p>>2)|0)-9|0)){if(M=y+4+(((0|(c=c+9216|0))/9|0)-1024<<2)|0,(0|(c=1+((0|c)%9|0)|0))<9)for(s=10;s=10*s|0,9!=(0|(c=c+1|0)););else s=10;if((c=(M+4|0)==(0|n))&0==(0|(d=((_=0|_e[M>>2])>>>0)%(s>>>0)|0)))c=M;else if(E=0==(1&((_>>>0)/(s>>>0)|0)|0)?9007199254740992:9007199254740994,r=d>>>0<(m=(0|s)/2|0)>>>0?.5:c&(0|d)==(0|m)?1:1.5,k&&(r=(m=45==(0|se[S>>0]))?-r:r,E=m?-E:E),c=_-d|0,_e[M>>2]=c,E+r!=E){if(m=c+s|0,999999999<(_e[M>>2]=m)>>>0)for(l=M;(c=l+-4|0)>>>(_e[l>>2]=0)<u>>>0&&(_e[(u=u+-4|0)>>2]=0),m=1+(0|_e[c>>2])|0,999999999<(_e[c>>2]=m)>>>0;)l=c;else c=M;if(l=9*(p-u>>2)|0,10<=(_=0|_e[u>>2])>>>0)for(s=10;l=l+1|0,(s=10*s|0)>>>0<=_>>>0;);}else c=M;c=(c=c+4|0)>>>0<n>>>0?c:n,m=u}else c=n,m=u;for(h=c;;){if(h>>>0<=m>>>0){A=0;break}if(0|_e[(u=h+-4|0)>>2]){A=1;break}h=u}n=0-l|0;do{if(T){if(f=(0|l)<(0|(u=(1&(1^b))+f|0))&-5<(0|l)?(s=o+-1|0,u+-1-l|0):(s=o+-2|0,u+-1|0),!(u=8&t)){if(A&&0!=(0|(v=0|_e[h+-4>>2])))if((v>>>0)%10|0)c=0;else for(c=0,u=10;c=c+1|0,!(0|(v>>>0)%((u=10*u|0)>>>0)););else c=9;if(u=(9*(h-p>>2)|0)-9|0,102==(32|s)){f=(0|f)<(0|(M=0<(0|(M=u-c|0))?M:0))?f:M,M=0;break}f=(0|f)<(0|(M=0<(0|(M=u+l-c|0))?M:0))?f:M,M=0;break}M=u}else s=o,M=8&t}while(0);if(_=0!=(0|(T=f|M))&1,d=102==(32|s))u=(b=0)<(0|l)?l:0;else{if(((c=R)-(u=0|Ae(u=(0|l)<0?n:l,((0|u)<0)<<31>>31,R))|0)<2)for(;se[(u=u+-1|0)>>0]=48,(c-u|0)<2;);se[u+-1>>0]=43+(l>>31&2),se[(u=u+-2|0)>>0]=s,u=c-(b=u)|0}if(pe(e,32,i,u=k+1+f+_+u|0,t),Ye(e,S,k),pe(e,48,i,u,65536^t),d){_=M=O+9|0,d=O+8|0,c=s=y>>>0<m>>>0?y:m;do{if(l=0|Ae(0|_e[c>>2],0,M),(0|c)==(0|s))(0|l)==(0|M)&&(se[d>>0]=48,l=d);else if(O>>>0<l>>>0)for(ae(0|O,48,l-g|0);O>>>0<(l=l+-1|0)>>>0;);Ye(e,l,_-l|0),c=c+4|0}while(c>>>0<=y>>>0);if(0|T&&Ye(e,2138,1),c>>>0<h>>>0&0<(0|f))for(;;){if(O>>>0<(l=0|Ae(0|_e[c>>2],0,M))>>>0)for(ae(0|O,48,l-g|0);O>>>0<(l=l+-1|0)>>>0;);if(Ye(e,l,(0|f)<9?f:9),l=f+-9|0,!((c=c+4|0)>>>0<h>>>0&9<(0|f))){f=l;break}f=l}pe(e,48,f+9|0,9,0)}else{if(T=A?h:m+4|0,-1<(0|f)){M=0==(0|M),n=A=O+9|0,_=0-g|0,d=O+8|0,s=m;do{(0|(l=0|Ae(0|_e[s>>2],0,A)))==(0|A)&&(se[d>>0]=48,l=d);do{if((0|s)==(0|m)){if(c=l+1|0,Ye(e,l,1),M&(0|f)<1){l=c;break}Ye(e,2138,1),l=c}else{if(l>>>0<=O>>>0)break;for(ae(0|O,48,l+_|0);O>>>0<(l=l+-1|0)>>>0;);}}while(0);Ye(e,l,(0|(g=n-l|0))<(0|f)?g:f),f=f-g|0,s=s+4|0}while(s>>>0<T>>>0&-1<(0|f))}pe(e,48,f+18|0,18,0),Ye(e,b,R-b|0)}pe(e,32,i,u,8192^t)}else O=0!=(32&o|0),pe(e,32,i,u=k+3|0,-65537&t),Ye(e,S,k),Ye(e,r!=r|!1?O?2114:2118:O?2106:2110,3),pe(e,32,i,u,8192^t)}while(0);return Me=a,0|((0|u)<(0|i)?i:u)}function D(e,r,i,n,t){e|=0,r|=0,i|=0,n|=0,t|=0;var o,a,u,f,l,c,s,_,d,E=0,M=0,T=0,A=0,b=0,h=0,m=0,p=0,v=0,S=0,k=0,y=0,R=0,g=0;Me=(d=Me)+64|0,g=(c=d)+24|0,s=d+8|0,_=d+20|0,_e[(l=d+16|0)>>2]=r,o=0!=(0|e),u=a=g+40|0,g=g+39|0,f=s+4|0,h=E=M=0;e:for(;;){do{if(-1<(0|E)){if((2147483647-E|0)<(0|M)){E=0|er(),_e[E>>2]=75,E=-1;break}E=M+E|0;break}}while(0);if(!((M=0|se[r>>0])<<24>>24)){R=87;break}T=r;r:for(;;){switch(M<<24>>24){case 37:M=T,R=9;break r;case 0:M=T;break r}y=T+1|0,_e[l>>2]=y,M=0|se[y>>0],T=y}r:do{if(9==(0|R))for(;;){if(37!=((R=0)|se[T+1>>0]))break r;if(M=M+1|0,T=T+2|0,_e[l>>2]=T,37!=(0|se[T>>0]))break;R=9}}while(0);if(M=M-r|0,o&&Ye(e,r,M),0|M)r=T;else{(M=(0|se[(A=T+1|0)>>0])-48|0)>>>0<10?(k=(y=36==(0|se[T+2>>0]))?M:-1,h=y?1:h,A=y?T+3|0:A):k=-1,_e[l>>2]=A,T=((M=0|se[A>>0])<<24>>24)-32|0;r:do{if(T>>>0<32)for(b=0,m=M;;){if(!(75913&(M=1<<T))){M=m;break r}if(b|=M,A=A+1|0,_e[l>>2]=A,32<=(T=((M=0|se[A>>0])<<24>>24)-32|0)>>>0)break;m=M}else b=0}while(0);if(M<<24>>24==42){if((M=(0|se[(T=A+1|0)>>0])-48|0)>>>0<10&&36==(0|se[A+2>>0]))_e[t+(M<<2)>>2]=10,M=0|_e[n+((0|se[T>>0])-48<<3)>>2],h=1,A=A+3|0;else{if(0|h){E=-1;break}A=(h=o?(h=3+(0|_e[i>>2])&-4,M=0|_e[h>>2],_e[i>>2]=h+4,0):M=0,T)}_e[l>>2]=A,M=(y=(0|M)<0)?0-M|0:M,b=y?8192|b:b}else{if((0|(M=0|ye(l)))<0){E=-1;break}A=0|_e[l>>2]}do{if(46==(0|se[A>>0])){if(42!=(0|se[A+1>>0])){_e[l>>2]=A+1,T=0|ye(l),A=0|_e[l>>2];break}if((T=(0|se[(m=A+2|0)>>0])-48|0)>>>0<10&&36==(0|se[A+3>>0])){_e[t+(T<<2)>>2]=10,T=0|_e[n+((0|se[m>>0])-48<<3)>>2],A=A+4|0,_e[l>>2]=A;break}if(0|h){E=-1;break e}o?(y=3+(0|_e[i>>2])&-4,T=0|_e[y>>2],_e[i>>2]=y+4):T=0,A=_e[l>>2]=m}else T=-1}while(0);for(S=0;;){if(57<((0|se[A>>0])-65|0)>>>0){E=-1;break e}if(y=A+1|0,_e[l>>2]=y,!(((p=255&(m=0|se[(0|se[A>>0])-65+(1606+(58*S|0))>>0]))+-1|0)>>>0<8))break;S=p,A=y}if(!(m<<24>>24)){E=-1;break}v=-1<(0|k);do{if(m<<24>>24==19){if(v){E=-1;break e}R=49}else{if(v){_e[t+(k<<2)>>2]=p,k=0|_e[(v=n+(k<<3)|0)+4>>2],_e[(R=c)>>2]=_e[v>>2],_e[R+4>>2]=k,R=49;break}if(!o){E=0;break e}Y(c,p,i)}}while(0);if(49!=(0|R)||(R=0,o)){A=0!=(0|S)&3==(15&(A=0|se[A>>0])|0)?-33&A:A,v=-65537&b,k=0==(8192&b|0)?b:v;r:do{switch(0|A){case 110:switch((255&S)<<24>>24){case 0:case 1:_e[_e[c>>2]>>2]=E,M=0,r=y;continue e;case 2:M=0|_e[c>>2],_e[M>>2]=E,_e[M+4>>2]=((0|E)<0)<<31>>31,M=0,r=y;continue e;case 3:X[_e[c>>2]>>1]=E,M=0,r=y;continue e;case 4:se[_e[c>>2]>>0]=E,M=0,r=y;continue e;case 6:_e[_e[c>>2]>>2]=E,M=0,r=y;continue e;case 7:M=0|_e[c>>2],_e[M>>2]=E,_e[M+4>>2]=((0|E)<0)<<31>>31,M=0,r=y;continue e;default:M=0,r=y;continue e}case 112:A=120,T=8<T>>>0?T:8,r=8|k,R=61;break;case 88:case 120:r=k,R=61;break;case 111:m=2070,T=(b=0)==(8&k|0)|(0|(v=u-(p=0|Ce(r=0|_e[(A=c)>>2],A=0|_e[A+4>>2],a))|0))<(0|T)?T:v+1|0,v=k,R=67;break;case 105:case 100:if(r=0|_e[(A=c)>>2],(0|(A=0|_e[A+4>>2]))<0){r=0|Fe(0,0,0|r,0|A),A=C,_e[(b=c)>>2]=r,_e[b+4>>2]=A,b=1,m=2070,R=66;break r}b=0!=(2049&k|0)&1,m=0==(2048&k|0)?0==(1&k|0)?2070:2072:2071,R=66;break r;case 117:m=2070,r=(b=0)|_e[(A=c)>>2],A=0|_e[A+4>>2],R=66;break;case 99:se[g>>0]=_e[c>>2],r=g,b=0,m=2070,p=a,A=1,T=v;break;case 109:A=0|er(),A=0|Xe(0|_e[A>>2]),R=71;break;case 115:A=0|(A=0|_e[c>>2])?A:2080,R=71;break;case 67:_e[s>>2]=_e[c>>2],_e[f>>2]=0,p=-1,A=_e[c>>2]=s,R=75;break;case 83:r=0|_e[c>>2],R=T?(p=T,A=r,75):(pe(e,32,M,0,k),r=0,84);break;case 65:case 71:case 70:case 69:case 97:case 103:case 102:case 101:M=0|L(e,+O[c>>3],M,T,k,A),r=y;continue e;default:b=0,m=2070,p=a,A=T,T=k}}while(0);r:do{if(61==(0|R))p=0|Re(S=0|_e[(k=c)>>2],k=0|_e[k+4>>2],a,32&A),b=(m=0==(8&r|0)|0==(0|S)&0==(0|k))?0:2,m=m?2070:2070+(A>>4)|0,v=r,r=S,A=k,R=67;else if(66==(0|R))p=0|Ae(r,A,a),v=k,R=67;else if(71==(0|R))b=R=0,m=2070,p=(S=0==(0|(k=0|ee(r=A,0,T))))?A+T|0:k,A=S?T:k-A|0,T=v;else if(75==(0|R)){for(m=A,T=r=R=0;(b=0|_e[m>>2])&&!((0|(T=0|Ke(_,b)))<0|(p-r|0)>>>0<T>>>0)&&(r=T+r|0)>>>0<p>>>0;)m=m+4|0;if((0|T)<0){E=-1;break e}if(pe(e,32,M,r,k),r)for(b=0;;){if(!(T=0|_e[A>>2])){R=84;break r}if((0|r)<(0|(b=(T=0|Ke(_,T))+b|0))){R=84;break r}if(Ye(e,_,T),r>>>0<=b>>>0){R=84;break}A=A+4|0}else r=0,R=84}}while(0);if(67==(0|R))k=(R=0)!=(0|T)|(A=0!=(0|r)|0!=(0|A)),A=u-p+(1&(1^A))|0,r=k?p:a,p=a,A=k?(0|A)<(0|T)?T:A:T,T=-1<(0|T)?-65537&v:v;else if(84==(0|R)){R=0,pe(e,32,M,r,8192^k),M=(0|r)<(0|M)?M:r,r=y;continue}pe(e,32,M=(0|M)<(0|(k=(v=(0|A)<(0|(S=p-r|0))?S:A)+b|0))?k:M,k,T),Ye(e,m,b),pe(e,48,M,k,65536^T),pe(e,48,v,S,0),Ye(e,r,S),pe(e,32,M,k,8192^T),r=y}else M=0,r=y}}e:do{if(87==(0|R)&&!e)if(h){for(E=1;r=0|_e[t+(E<<2)>>2];)if(Y(n+(E<<3)|0,r,i),10<=(0|(E=E+1|0))){E=1;break e}for(;;){if(0|_e[t+(E<<2)>>2]){E=-1;break e}if(10<=(0|(E=E+1|0))){E=1;break}}}else E=0}while(0);return Me=d,0|E}function K(e,r){r|=0;var i,n,t,o,a,u,f,l,c,s,_,d,E,M,T,A=0,b=0,h=0,m=0,p=0,v=0,S=0,k=0,y=0,R=0;if(Me=(T=Me)+704|0,E=T+144|0,d=T+128|0,_=T+112|0,s=T+96|0,c=T+80|0,l=T+64|0,f=T+48|0,M=T+32|0,i=T+16|0,t=(v=T)+184|0,R=T+160|0,!(o=0|function(e,r){e|=0;var i=0,n=0,t=0,o=0,a=0,u=0,f=0,l=0;if(Me=(l=Me)+528|0,o=(a=l)+16|0,!(r|=0))return Me=l,(f=0)|f;if(r>>>0<=16)return f=0|$(e,r),Me=l,0|f;if(u=0|$(e,r+-16|0),(0|(r=0|_e[(f=e+20|0)>>2]))<16)for(n=e+4|0,t=e+8|0,i=e+16|0;e=(0|(e=0|_e[n>>2]))==(0|_e[t>>2])?0:(_e[n>>2]=e+1,0|de[e>>0]),r=r+8|0,33<=(0|(_e[f>>2]=r))&&(_e[a>>2]=866,_e[a+4>>2]=3208,_e[a+8>>2]=1366,we(o,812,a),me(o),r=0|_e[f>>2]),e=e<<32-r|_e[i>>2],_e[i>>2]=e,(0|r)<16;);else e=0|_e[(i=e=e+16|0)>>2];return _e[i>>2]=e<<16,_e[f>>2]=r+-16,Me=l,0|(f=e>>>16|u<<16)}(e|=0,14)))return function(e){var r=0,i=0,n=0,t=0,o=0,a=0;Me=(a=Me)+544|0,o=a+16|0,t=(n=a)+32|0,(_e[(e|=0)>>2]=0)|(i=0|_e[(r=e+4|0)>>2])&&(7&i?(_e[n>>2]=866,_e[n+4>>2]=2506,_e[n+8>>2]=1232,we(t,812,n),me(t)):le(i,0,0,1,0),_e[r>>2]=0,_e[e+8>>2]=0,_e[e+12>>2]=0);if(se[e+16>>0]=0,!(r=0|_e[(e=e+20|0)>>2]))return Me=a;q(r),7&r?(_e[o>>2]=866,_e[o+4>>2]=2506,_e[o+8>>2]=1232,we(t,812,o),me(t)):le(r,0,0,1,0);_e[e>>2]=0,Me=a}(r),Me=T,0|(R=1);if(a=r+4|0,(0|(A=0|_e[(u=r+8|0)>>2]))!=(0|o)){if(A>>>0<=o>>>0){do{if((0|_e[r+12>>2])>>>0<o>>>0){if(0|V(a,o,(A+1|0)==(0|o),1,0)){A=0|_e[u>>2];break}return se[r+16>>0]=1,Me=T,(R=0)|R}}while(0);ae((0|_e[a>>2])+A|0,0,o-A|0)}_e[u>>2]=o}if(ae(0|_e[a>>2],0,0|o),(0|(A=0|_e[(n=e+20|0)>>2]))<5)for(m=e+4|0,p=e+8|0,h=e+16|0;b=(0|(b=0|_e[m>>2]))==(0|_e[p>>2])?0:(_e[m>>2]=b+1,0|de[b>>0]),A=A+8|0,33<=(0|(_e[n>>2]=A))&&(_e[v>>2]=866,_e[v+4>>2]=3208,_e[v+8>>2]=1366,we(t,812,v),me(t),A=0|_e[n>>2]),b=b<<32-A|_e[h>>2],_e[h>>2]=b,(0|A)<5;);else b=0|_e[(h=b=e+16|0)>>2];if(k=b>>>27,_e[h>>2]=b<<5,_e[n>>2]=A+-5,20<(k+-1|0)>>>0)return Me=T,(R=0)|R;_e[R+20>>2]=0,_e[R>>2]=0,_e[R+4>>2]=0,_e[R+8>>2]=0,_e[R+12>>2]=0,A=R+4|(se[R+16>>0]=0),b=R+8|0;e:do{if(0|V(A,21,0,1,0)){m=0|_e[b>>2],ae((S=0|_e[A>>2])+m|0,0,21-m|0),_e[b>>2]=21,m=e+4|0,p=e+8|0,v=e+16|0,h=0;do{if((0|(A=0|_e[n>>2]))<3)for(;b=(0|(b=0|_e[m>>2]))==(0|_e[p>>2])?0:(_e[m>>2]=b+1,0|de[b>>0]),A=A+8|0,33<=(0|(_e[n>>2]=A))&&(_e[i>>2]=866,_e[i+4>>2]=3208,_e[i+8>>2]=1366,we(t,812,i),me(t),A=0|_e[n>>2]),b=b<<32-A|_e[v>>2],_e[v>>2]=b,(0|A)<3;);else b=0|_e[v>>2];_e[v>>2]=b<<3,_e[n>>2]=A+-3,se[S+(0|de[1327+h>>0])>>0]=b>>>29,h=h+1|0}while((0|h)!=(0|k));if(0|Z(R)){v=e+4|0,S=e+8|0,k=e+16|0,A=0;r:do{p=o-A|0,h=0|Te(e,R);i:do{if(h>>>0<17)(0|_e[u>>2])>>>0<=A>>>0&&(_e[M>>2]=866,_e[M+4>>2]=910,_e[M+8>>2]=1497,we(t,812,M),me(t)),se[(0|_e[a>>2])+A>>0]=h,A=A+1|0;else switch(0|h){case 17:if((0|(b=0|_e[n>>2]))<3)for(;h=(0|(h=0|_e[v>>2]))==(0|_e[S>>2])?0:(_e[v>>2]=h+1,0|de[h>>0]),b=b+8|0,33<=(0|(_e[n>>2]=b))&&(_e[f>>2]=866,_e[f+4>>2]=3208,_e[f+8>>2]=1366,we(t,812,f),me(t),b=0|_e[n>>2]),h=h<<32-b|_e[k>>2],_e[k>>2]=h,(0|b)<3;);else h=0|_e[k>>2];if(_e[k>>2]=h<<3,_e[n>>2]=b+-3,b=p>>>0<(h=3+(h>>>29)|0)>>>0){A=0;break e}A=(b?0:h)+A|0;break i;case 18:if((0|(b=0|_e[n>>2]))<7)for(;h=(0|(h=0|_e[v>>2]))==(0|_e[S>>2])?0:(_e[v>>2]=h+1,0|de[h>>0]),b=b+8|0,33<=(0|(_e[n>>2]=b))&&(_e[l>>2]=866,_e[l+4>>2]=3208,_e[l+8>>2]=1366,we(t,812,l),me(t),b=0|_e[n>>2]),h=h<<32-b|_e[k>>2],_e[k>>2]=h,(0|b)<7;);else h=0|_e[k>>2];if(_e[k>>2]=h<<7,_e[n>>2]=b+-7,b=p>>>0<(h=11+(h>>>25)|0)>>>0){A=0;break e}A=(b?0:h)+A|0;break i;default:if(2<=(h+-19|0)>>>0){y=81;break r}if(b=0|_e[n>>2],19==(0|h)){if((0|b)<2)for(h=b;m=(0|(b=0|_e[v>>2]))==(0|_e[S>>2])?0:(_e[v>>2]=b+1,0|de[b>>0]),b=h+8|0,33<=(0|(_e[n>>2]=b))&&(_e[c>>2]=866,_e[c+4>>2]=3208,_e[c+8>>2]=1366,we(t,812,c),me(t),b=0|_e[n>>2]),h=m<<32-b|_e[k>>2],_e[k>>2]=h,(0|b)<2;)h=b;else h=0|_e[k>>2];_e[k>>2]=h<<2,h>>>=30,m=3,b=b+-2|0}else{if((0|b)<6)for(;h=(0|(h=0|_e[v>>2]))==(0|_e[S>>2])?0:(_e[v>>2]=h+1,0|de[h>>0]),b=b+8|0,33<=(0|(_e[n>>2]=b))&&(_e[s>>2]=866,_e[s+4>>2]=3208,_e[s+8>>2]=1366,we(t,812,s),me(t),b=0|_e[n>>2]),h=h<<32-b|_e[k>>2],_e[k>>2]=h,(0|b)<6;);else h=0|_e[k>>2];_e[k>>2]=h<<6,h>>>=26,m=7,b=b+-6|0}if(_e[n>>2]=b,0==(0|A)|p>>>0<(h=h+m|0)>>>0){A=0;break e}if(b=A+-1|0,(0|_e[u>>2])>>>0<=b>>>0&&(_e[_>>2]=866,_e[_+4>>2]=910,_e[_+8>>2]=1497,we(t,812,_),me(t)),!((m=0|se[(0|_e[a>>2])+b>>0])<<24>>24)){A=0;break e}if((b=h+A|0)>>>0<=A>>>0)break i;for(;(0|_e[u>>2])>>>0<=A>>>0&&(_e[d>>2]=866,_e[d+4>>2]=910,_e[d+8>>2]=1497,we(t,812,d),me(t)),se[(0|_e[a>>2])+A>>0]=m,(0|(A=A+1|0))!=(0|b););A=b}}while(0)}while(A>>>0<o>>>0);if(81==(0|y)){_e[E>>2]=866,_e[E+4>>2]=3149,_e[E+8>>2]=1348,we(t,812,E),me(t),A=0;break}A=(0|o)==(0|A)?0|Z(r):0}else A=0}else se[R+16>>0]=1,A=0}while(0);return ne(R),Me=T,0|(R=A)}function F(e,r,i,n){i|=0;var t,o,a,u,f,l,c=0,s=0,_=0,d=0,E=0,M=0,T=0,A=0,b=0,h=0,m=0,p=0,v=0,S=0,k=0,y=0,R=0,g=0,O=0,C=0,N=0,P=0;if(Me=(l=Me)+880|0,N=l+144|0,f=l+128|0,u=l+112|0,a=l+96|0,O=l+80|0,S=l+64|0,p=l+48|0,v=l+32|0,A=l+16|0,t=(T=l)+360|0,o=l+296|0,P=l+224|0,m=l+156|0,0==(0|(r|=0))|11<(n|=0)>>>0)return Me=l,(P=0)|P;for(_e[(e|=0)>>2]=r,s=(c=P)+68|0;(0|(c=c+4|(_e[c>>2]=0)))<(0|s););for(c=0;s=P+((255&(C=0|se[i+c>>0]))<<2)|0,C<<24>>24&&(_e[s>>2]=1+(0|_e[s>>2])),(0|(c=c+1|0))!=(0|r););for(d=_=s=0,E=-1,M=1;(c=0|_e[P+(M<<2)>>2])?(s=c+(_e[o+((b=M+-1|0)<<2)>>2]=s)|0,C=16-M|0,_e[e+28+(b<<2)>>2]=1+(s+-1<<C|(1<<C)-1),_e[e+96+(b<<2)>>2]=_,b=c+(_e[m+(M<<2)>>2]=_)|0,d=M>>>0<d>>>0?d:M,E=E>>>0<M>>>0?E:M):(_e[e+28+(M+-1<<2)>>2]=0,b=_),17!=(0|(M=M+1|0));)s<<=1,_=b;_e[e+4>>2]=b,s=e+172|0;do{if(b>>>0>(0|_e[s>>2])>>>0){c=(c=b+-1|0)&b?(c|=c>>>16,c|=c>>>8,c|=c>>>4,r>>>0<(c=1+((c|=c>>>2)>>>1|c)|0)>>>0?r:c):b,_e[s>>2]=c,c=0|_e[(_=e+176|0)>>2];do{if(0|c){if(C=0|_e[c+-4>>2],c=c+-8|0,0!=(0|C)&&(0|C)==(0|~_e[c>>2])||(_e[T>>2]=866,_e[T+4>>2]=651,_e[T+8>>2]=1579,we(t,812,T),me(t)),7&c){_e[A>>2]=866,_e[A+4>>2]=2506,_e[A+8>>2]=1232,we(t,812,A),me(t);break}le(c,0,0,1,0);break}}while(0);if(s=0|oe(8+((c=0|(c=0|_e[s>>2])?c:1)<<1)|0,0)){_e[s+4>>2]=c,_e[s>>2]=~c,_e[_>>2]=s+8,h=24;break}n=_e[_>>2]=0;break}h=24}while(0);e:do{if(24==(0|h)){for(se[(C=e+24|0)>>0]=E,se[e+25>>0]=d,_=e+176|0,s=0;c=255&(g=0|se[i+s>>0]),g<<24>>24&&(0|_e[P+(c<<2)>>2]||(_e[v>>2]=866,_e[v+4>>2]=2276,_e[v+8>>2]=977,we(t,812,v),me(t)),c=0|_e[(g=m+(c<<2)|0)>>2],_e[g>>2]=c+1,b>>>0<=c>>>0&&(_e[p>>2]=866,_e[p+4>>2]=2280,_e[p+8>>2]=990,we(t,812,p),me(t)),X[(0|_e[_>>2])+(c<<1)>>1]=s),(0|(s=s+1|0))!=(0|r););if(R=(0|de[C>>0])>>>0<n>>>0?n:0,y=0!=(0|(_e[(g=e+8|0)>>2]=R))){k=1<<R,c=e+164|0;do{if(k>>>0>(0|_e[c>>2])>>>0){_e[c>>2]=k,c=0|_e[(_=e+168|0)>>2];do{if(0|c){if(v=0|_e[c+-4>>2],c=c+-8|0,0!=(0|v)&&(0|v)==(0|~_e[c>>2])||(_e[S>>2]=866,_e[S+4>>2]=651,_e[S+8>>2]=1579,we(t,812,S),me(t)),7&c){_e[O>>2]=866,_e[O+4>>2]=2506,_e[O+8>>2]=1232,we(t,812,O),me(t);break}le(c,0,0,1,0);break}}while(0);if(s=0|oe((c=k<<2)+8|0,0)){O=s+8|0,_e[s+4>>2]=k,_e[s>>2]=~k,s=_e[_>>2]=O;break}n=_e[_>>2]=0;break e}c=k<<2,s=0|_e[(_=s=e+168|0)>>2]}while(0);ae(0|s,-1,0|c),p=e+176|0,m=1;do{if(0|_e[P+(m<<2)>>2]&&(S=1<<(v=R-m|0),s=0|_e[o+((c=m+-1|0)<<2)>>2],16<=c>>>0&&(_e[a>>2]=866,_e[a+4>>2]=1960,_e[a+8>>2]=1453,we(t,812,a),me(t)),s>>>0<=(r=0==(0|(r=0|_e[e+28+(c<<2)>>2]))?-1:(r+-1|0)>>>(16-m|0))>>>0)){b=(0|_e[e+96+(c<<2)>>2])-s|0,h=m<<16;do{for(c=0|Ee[(0|_e[p>>2])+(b+s<<1)>>1],(0|de[i+c>>0])!=(0|m)&&(_e[u>>2]=866,_e[u+4>>2]=2322,_e[u+8>>2]=1019,we(t,812,u),me(t)),A=s<<v,M=c|h,E=0;k>>>0<=(T=E+A|0)>>>0&&(_e[f>>2]=866,_e[f+4>>2]=2328,_e[f+8>>2]=1053,we(t,812,f),me(t)),c=0|_e[_>>2],-1!=(0|_e[c+(T<<2)>>2])&&(_e[N>>2]=866,_e[N+4>>2]=2330,_e[N+8>>2]=1076,we(t,812,N),me(t),c=0|_e[_>>2]),_e[c+(T<<2)>>2]=M,(E=E+1|0)>>>0<S>>>0;);s=s+1|0}while(s>>>0<=r>>>0)}m=m+1|0}while(m>>>0<=R>>>0)}_e[(c=e+96|0)>>2]=(0|_e[c>>2])-(0|_e[o>>2]),_e[(c=e+100|0)>>2]=(0|_e[c>>2])-(0|_e[o+4>>2]),_e[(c=e+104|0)>>2]=(0|_e[c>>2])-(0|_e[o+8>>2]),_e[(c=e+108|0)>>2]=(0|_e[c>>2])-(0|_e[o+12>>2]),_e[(c=e+112|0)>>2]=(0|_e[c>>2])-(0|_e[o+16>>2]),_e[(c=e+116|0)>>2]=(0|_e[c>>2])-(0|_e[o+20>>2]),_e[(c=e+120|0)>>2]=(0|_e[c>>2])-(0|_e[o+24>>2]),_e[(c=e+124|0)>>2]=(0|_e[c>>2])-(0|_e[o+28>>2]),_e[(c=e+128|0)>>2]=(0|_e[c>>2])-(0|_e[o+32>>2]),_e[(c=e+132|0)>>2]=(0|_e[c>>2])-(0|_e[o+36>>2]),_e[(c=e+136|0)>>2]=(0|_e[c>>2])-(0|_e[o+40>>2]),_e[(c=e+140|0)>>2]=(0|_e[c>>2])-(0|_e[o+44>>2]),_e[(c=e+144|0)>>2]=(0|_e[c>>2])-(0|_e[o+48>>2]),_e[(c=e+148|0)>>2]=(0|_e[c>>2])-(0|_e[o+52>>2]),_e[(c=e+152|0)>>2]=(0|_e[c>>2])-(0|_e[o+56>>2]),_e[(c=e+156|0)>>2]=(0|_e[c>>2])-(0|_e[o+60>>2]),_e[(c=e+16|0)>>2]=0,_e[(s=e+20|0)>>2]=de[C>>0];r:do{if(y){do{if(!n)break r;n=(N=n)+-1|0}while(!(0|_e[P+(N<<2)>>2]));if(_e[c>>2]=_e[e+28+(n<<2)>>2],n=R+1|0,(_e[s>>2]=n)>>>0<=d>>>0){for(;!(0|_e[P+(n<<2)>>2]);)if(d>>>0<(n=n+1|0)>>>0)break r;_e[s>>2]=n}}}while(0);_e[e+92>>2]=-1,_e[e+160>>2]=1048575,_e[e+12>>2]=32-(0|_e[g>>2]),n=1}}while(0);return Me=l,0|(P=n)}function U(e){var r=0,i=0,n=0,t=0,o=0,a=0,u=0,f=0;if(e|=0){i=e+-8|0,t=0|_e[1148],f=i+(r=-8&(e=0|_e[e+-4>>2]))|0;do{if(1&e)a=u=i;else{if(n=0|_e[i>>2],!(3&e))return;if(o=n+r|0,(a=i+(0-n)|0)>>>0<t>>>0)return;if((0|a)==(0|_e[1149])){if(3==(3&(r=0|_e[(e=f+4|0)>>2])|0))return _e[1146]=o,_e[e>>2]=-2&r,_e[a+4>>2]=1|o,void(_e[a+o>>2]=o);u=a,r=o;break}if(i=n>>>3,n>>>0<256){if(e=0|_e[a+8>>2],(0|(r=0|_e[a+12>>2]))==(0|e)){_e[1144]=_e[1144]&~(1<<i),u=a,r=o;break}_e[e+12>>2]=r,_e[r+8>>2]=e,u=a,r=o;break}t=0|_e[a+24>>2],e=0|_e[a+12>>2];do{if((0|e)==(0|a)){if(!(e=0|_e[(r=(i=a+16|0)+4|0)>>2])){if(!(e=0|_e[i>>2])){e=0;break}r=i}for(;;)if(0|(n=0|_e[(i=e+20|0)>>2]))e=n,r=i;else{if(!(n=0|_e[(i=e+16|0)>>2]))break;e=n,r=i}_e[r>>2]=0}else u=0|_e[a+8>>2],_e[u+12>>2]=e,_e[e+8>>2]=u}while(0);if(t){if(r=0|_e[a+28>>2],(0|a)==(0|_e[(i=4880+(r<<2)|0)>>2])){if(!(_e[i>>2]=e)){_e[1145]=_e[1145]&~(1<<r),u=a,r=o;break}}else if(!(_e[t+16+(((0|_e[t+16>>2])!=(0|a)&1)<<2)>>2]=e)){u=a,r=o;break}_e[e+24>>2]=t,0|(i=0|_e[(r=a+16|0)>>2])&&(_e[e+16>>2]=i,_e[i+24>>2]=e),r=(u=((r=0|_e[r+4>>2])&&(_e[e+20>>2]=r,_e[r+24>>2]=e),a),o)}else u=a,r=o}}while(0);if(!(f>>>0<=a>>>0)&&1&(n=0|_e[(e=f+4|0)>>2])){if(2&n)_e[e>>2]=-2&n,_e[u+4>>2]=1|r,t=_e[a+r>>2]=r;else{if(e=0|_e[1149],(0|f)==(0|_e[1150])){if(f=(0|_e[1147])+r|0,_e[1147]=f,_e[1150]=u,_e[u+4>>2]=1|f,(0|u)!=(0|e))return;return _e[1149]=0,void(_e[1146]=0)}if((0|f)==(0|e))return f=(0|_e[1146])+r|0,_e[1146]=f,_e[1149]=a,_e[u+4>>2]=1|f,void(_e[a+f>>2]=f);t=(-8&n)+r|0,i=n>>>3;do{if(n>>>0<256){if(r=0|_e[f+8>>2],(0|(e=0|_e[f+12>>2]))==(0|r)){_e[1144]=_e[1144]&~(1<<i);break}_e[r+12>>2]=e,_e[e+8>>2]=r;break}o=0|_e[f+24>>2],e=0|_e[f+12>>2];do{if((0|e)==(0|f)){if(!(e=0|_e[(r=(i=f+16|0)+4|0)>>2])){if(!(e=0|_e[i>>2])){i=0;break}r=i}for(;;)if(0|(n=0|_e[(i=e+20|0)>>2]))e=n,r=i;else{if(!(n=0|_e[(i=e+16|0)>>2]))break;e=n,r=i}_e[r>>2]=0,i=e}else i=0|_e[f+8>>2],_e[i+12>>2]=e,_e[e+8>>2]=i,i=e}while(0);if(0|o){if(e=0|_e[f+28>>2],(0|f)==(0|_e[(r=4880+(e<<2)|0)>>2])){if(!(_e[r>>2]=i)){_e[1145]=_e[1145]&~(1<<e);break}}else if(!(_e[o+16+(((0|_e[o+16>>2])!=(0|f)&1)<<2)>>2]=i))break;_e[i+24>>2]=o,0|(r=0|_e[(e=f+16|0)>>2])&&(_e[i+16>>2]=r,_e[r+24>>2]=i),0|(e=0|_e[e+4>>2])&&(_e[i+20>>2]=e,_e[e+24>>2]=i)}}while(0);if(_e[u+4>>2]=1|t,_e[a+t>>2]=t,(0|u)==(0|_e[1149]))return void(_e[1146]=t)}if(e=t>>>3,t>>>0<256)return i=4616+(e<<1<<2)|0,(r=0|_e[1144])&(e=1<<e)?e=0|_e[(r=i+8|0)>>2]:(_e[1144]=r|e,r=(e=i)+8|0),_e[r>>2]=u,_e[e+12>>2]=u,_e[u+8>>2]=e,void(_e[u+12>>2]=i);n=4880+((e=(e=t>>>8)?16777215<t>>>0?31:t>>>((e=14-((o=((f=e<<(a=(e+1048320|0)>>>16&8))+520192|0)>>>16&4)|a|(e=((f<<=o)+245760|0)>>>16&2))+(f<<e>>>15)|0)+7|0)&1|e<<1:0)<<2)|0,_e[u+28>>2]=e,_e[u+20>>2]=0,r=(_e[u+16>>2]=0)|_e[1145],i=1<<e;do{if(r&i){for(r=t<<(31==(0|e)?0:25-(e>>>1)|0),i=0|_e[n>>2];;){if((-8&_e[i+4>>2]|0)==(0|t)){e=73;break}if(!(e=0|_e[(n=i+16+(r>>>31<<2)|0)>>2])){e=72;break}r<<=1,i=e}if(72==(0|e)){_e[n>>2]=u,_e[u+24>>2]=i,_e[u+12>>2]=u,_e[u+8>>2]=u;break}if(73==(0|e)){f=0|_e[(a=i+8|0)>>2],_e[f+12>>2]=u,_e[a>>2]=u,_e[u+8>>2]=f,_e[u+12>>2]=i,_e[u+24>>2]=0;break}}else _e[1145]=r|i,_e[n>>2]=u,_e[u+24>>2]=n,_e[u+12>>2]=u,_e[u+8>>2]=u}while(0);if(f=(0|_e[1152])-1|0,!(_e[1152]=f)){for(e=5032;e=0|_e[e>>2];)e=e+8|0;_e[1152]=-1}}}}function H(e,r){var i=0,n=0,t=0,o=0,a=0,u=0,f=0;f=(e|=0)+(r|=0)|0,i=0|_e[e+4>>2];do{if(1&i)u=e,i=r;else{if(n=0|_e[e>>2],!(3&i))return;if(a=n+r|0,(0|(o=e+(0-n)|0))==(0|_e[1149])){if(3==(3&(i=0|_e[(e=f+4|0)>>2])|0))return _e[1146]=a,_e[e>>2]=-2&i,_e[o+4>>2]=1|a,void(_e[o+a>>2]=a);u=o,i=a;break}if(r=n>>>3,n>>>0<256){if(e=0|_e[o+8>>2],(0|(i=0|_e[o+12>>2]))==(0|e)){_e[1144]=_e[1144]&~(1<<r),u=o,i=a;break}_e[e+12>>2]=i,_e[i+8>>2]=e,u=o,i=a;break}t=0|_e[o+24>>2],e=0|_e[o+12>>2];do{if((0|e)==(0|o)){if(!(e=0|_e[(i=(r=o+16|0)+4|0)>>2])){if(!(e=0|_e[r>>2])){e=0;break}i=r}for(;;)if(0|(n=0|_e[(r=e+20|0)>>2]))e=n,i=r;else{if(!(n=0|_e[(r=e+16|0)>>2]))break;e=n,i=r}_e[i>>2]=0}else u=0|_e[o+8>>2],_e[u+12>>2]=e,_e[e+8>>2]=u}while(0);if(t){if(i=0|_e[o+28>>2],(0|o)==(0|_e[(r=4880+(i<<2)|0)>>2])){if(!(_e[r>>2]=e)){_e[1145]=_e[1145]&~(1<<i),u=o,i=a;break}}else if(!(_e[t+16+(((0|_e[t+16>>2])!=(0|o)&1)<<2)>>2]=e)){u=o,i=a;break}_e[e+24>>2]=t,0|(r=0|_e[(i=o+16|0)>>2])&&(_e[e+16>>2]=r,_e[r+24>>2]=e),i=(u=((i=0|_e[i+4>>2])&&(_e[e+20>>2]=i,_e[i+24>>2]=e),o),a)}else u=o,i=a}}while(0);if(2&(n=0|_e[(e=f+4|0)>>2]))_e[e>>2]=-2&n,_e[u+4>>2]=1|i,_e[u+i>>2]=i;else{if(e=0|_e[1149],(0|f)==(0|_e[1150])){if(f=(0|_e[1147])+i|0,_e[1147]=f,_e[1150]=u,_e[u+4>>2]=1|f,(0|u)!=(0|e))return;return _e[1149]=0,void(_e[1146]=0)}if((0|f)==(0|e))return f=(0|_e[1146])+i|0,_e[1146]=f,_e[1149]=u,_e[u+4>>2]=1|f,void(_e[u+f>>2]=f);o=(-8&n)+i|0,r=n>>>3;do{if(n>>>0<256){if(i=0|_e[f+8>>2],(0|(e=0|_e[f+12>>2]))==(0|i)){_e[1144]=_e[1144]&~(1<<r);break}_e[i+12>>2]=e,_e[e+8>>2]=i;break}t=0|_e[f+24>>2],e=0|_e[f+12>>2];do{if((0|e)==(0|f)){if(!(e=0|_e[(i=(r=f+16|0)+4|0)>>2])){if(!(e=0|_e[r>>2])){r=0;break}i=r}for(;;)if(0|(n=0|_e[(r=e+20|0)>>2]))e=n,i=r;else{if(!(n=0|_e[(r=e+16|0)>>2]))break;e=n,i=r}_e[i>>2]=0,r=e}else r=0|_e[f+8>>2],_e[r+12>>2]=e,_e[e+8>>2]=r,r=e}while(0);if(0|t){if(e=0|_e[f+28>>2],(0|f)==(0|_e[(i=4880+(e<<2)|0)>>2])){if(!(_e[i>>2]=r)){_e[1145]=_e[1145]&~(1<<e);break}}else if(!(_e[t+16+(((0|_e[t+16>>2])!=(0|f)&1)<<2)>>2]=r))break;_e[r+24>>2]=t,0|(i=0|_e[(e=f+16|0)>>2])&&(_e[r+16>>2]=i,_e[i+24>>2]=r),0|(e=0|_e[e+4>>2])&&(_e[r+20>>2]=e,_e[e+24>>2]=r)}}while(0);if(_e[u+4>>2]=1|o,_e[u+o>>2]=o,(0|u)==(0|_e[1149]))return void(_e[1146]=o);i=o}if(e=i>>>3,i>>>0<256)return r=4616+(e<<1<<2)|0,(i=0|_e[1144])&(e=1<<e)?e=0|_e[(i=r+8|0)>>2]:(_e[1144]=i|e,i=(e=r)+8|0),_e[i>>2]=u,_e[e+12>>2]=u,_e[u+8>>2]=e,void(_e[u+12>>2]=r);if(t=4880+((e=(e=i>>>8)?16777215<i>>>0?31:i>>>((e=14-((o=((f=e<<(a=(e+1048320|0)>>>16&8))+520192|0)>>>16&4)|a|(e=((f<<=o)+245760|0)>>>16&2))+(f<<e>>>15)|0)+7|0)&1|e<<1:0)<<2)|0,_e[u+28>>2]=e,_e[u+20>>2]=0,!((r=(_e[u+16>>2]=0)|_e[1145])&(n=1<<e)))return _e[1145]=r|n,_e[t>>2]=u,_e[u+24>>2]=t,_e[u+12>>2]=u,void(_e[u+8>>2]=u);for(r=i<<(31==(0|e)?0:25-(e>>>1)|0),n=0|_e[t>>2];;){if((-8&_e[n+4>>2]|0)==(0|i)){e=69;break}if(!(e=0|_e[(t=n+16+(r>>>31<<2)|0)>>2])){e=68;break}r<<=1,n=e}return 68==(0|e)?(_e[t>>2]=u,_e[u+24>>2]=n,_e[u+12>>2]=u,void(_e[u+8>>2]=u)):69==(0|e)?(f=0|_e[(a=n+8|0)>>2],_e[f+12>>2]=u,_e[a>>2]=u,_e[u+8>>2]=f,_e[u+12>>2]=n,void(_e[u+24>>2]=0)):void 0}function x(e,r,i,n,t){t|=0;var o=0,a=0,u=0,f=0,l=0,c=0,s=0,_=0,d=0,E=0;if(c=e|=0,a=i|=0,u=_=n|=0,!(l=f=r|=0))return o=0!=(0|t),u?(o&&(_e[t>>2]=0|e,_e[t+4>>2]=0&r),(t=_=0)|(C=_,t)):(o&&(_e[t>>2]=(c>>>0)%(a>>>0),_e[t+4>>2]=0),(_=0)|(C=_,t=(c>>>0)/(a>>>0)>>>0));o=0==(0|u);do{if(a){if(!o){if((o=(0|M(0|u))-(0|M(0|l))|0)>>>0<=31){e=c>>>((a=s=o+1|0)>>>0)&(r=o-31>>31)|l<<(u=31-o|0),r&=l>>>(s>>>0),o=0,u=c<<u;break}return t&&(_e[t>>2]=0|e,_e[t+4>>2]=f|0&r),(t=_=0)|(C=_,t)}if((o=a-1|0)&a|0){e=(s=32-(u=33+(0|M(0|a))-(0|M(0|l))|0)|0)-1>>31&l>>>((d=u-32|0)>>>0)|(l<<s|c>>>((a=u)>>>0))&(r=d>>31),r&=l>>>(u>>>0),o=c<<(E=64-u|0)&(f=s>>31),u=(l<<E|c>>>(d>>>0))&f|c<<s&u-33>>31;break}return 0|t&&(_e[t>>2]=o&c,_e[t+4>>2]=0),1==(0|a)?0|(C=d=f|0&r,E=0|e):(E=0|ge(0|a),0|(C=d=l>>>(E>>>0)|0,E=l<<32-E|c>>>(E>>>0)|0))}if(o)return 0|t&&(_e[t>>2]=(l>>>0)%(a>>>0),_e[t+4>>2]=0),(d=0)|(C=d,E=(l>>>0)/(a>>>0)>>>0);if(!c)return 0|t&&(_e[t>>2]=0,_e[t+4>>2]=(l>>>0)%(u>>>0)),(d=0)|(C=d,E=(l>>>0)/(u>>>0)>>>0);if(!((o=u-1|0)&u))return 0|t&&(_e[t>>2]=0|e,_e[t+4>>2]=o&l|0&r),E=l>>>(((d=0)|ge(0|u))>>>0),0|(C=d,E);if((o=(0|M(0|u))-(0|M(0|l))|0)>>>0<=30){e=l<<(u=31-o|0)|c>>>((a=r=o+1|0)>>>0),r=l>>>(r>>>0),o=0,u=c<<u;break}return t&&(_e[t>>2]=0|e,_e[t+4>>2]=f|0&r),(E=d=0)|(C=d,E)}while(0);if(a){for(l=0|He(0|(s=0|i),0|(c=_|0&n),-1,-1),i=C,f=u,u=0;f=o>>>31|(n=f)<<1,o=u|o<<1,Fe(0|l,0|i,0|(n=e<<1|n>>>31|0),0|(_=e>>>31|r<<1|0)),u=1&(d=(E=C)>>31|((0|E)<0?-1:0)<<1),e=0|Fe(0|n,0|_,d&s|0,(((0|E)<0?-1:0)>>31|((0|E)<0?-1:0)<<1)&c|0),r=C,0!=(0|(a=a-1|0)););l=f,f=0}else l=u,u=f=0;return(a=0)|t&&(_e[t>>2]=e,_e[t+4>>2]=r),0|(C=d=(0|o)>>>31|(l|a)<<1|0&(a<<1|o>>>31)|f,E=-2&(o<<1|0)|u)}function Te(e,r){e|=0;var i,n,t,o,a,u,f,l,c=0,s=0,_=0,d=0,E=0,M=0;Me=(l=Me)+576|0,t=l+48|0,a=l+32|0,o=l+16|0,f=(n=l)+64|0,u=0|_e[(r|=0)+20>>2],(0|(i=0|_e[(M=e+20|0)>>2]))<24?(s=(c=0|_e[(E=e+4|0)>>2])>>>0<(_=0|_e[e+8>>2])>>>0,(0|i)<16?(s?(d=(0|de[c>>0])<<8,c=c+1|0):d=0,c>>>0<_>>>0?(_=0|de[c>>0],c=c+1|0):_=0,_e[E>>2]=c,_e[M>>2]=i+16,s=16,c=_|d):(c=s?(_e[E>>2]=c+1,0|de[c>>0]):0,_e[M>>2]=i+8,s=24),_=_e[(E=e+16|0)>>2]|c<<s-i,_e[E>>2]=_):_=0|_e[(E=_=e+16|0)>>2],d=1+(_>>>16)|0;do{if(!(d>>>0<=(0|_e[u+16>>2])>>>0)){for(s=0|_e[u+20>>2];d>>>0>(0|_e[u+28+((c=s+-1|0)<<2)>>2])>>>0;)s=s+1|0;if((c=(_>>>(32-s|0))+(0|_e[u+96+(c<<2)>>2])|0)>>>0<(0|_e[r>>2])>>>0){c=0|Ee[(0|_e[u+176>>2])+(c<<1)>>1];break}return _e[t>>2]=866,_e[t+4>>2]=3275,_e[t+8>>2]=1348,we(f,812,t),me(f),Me=l,(M=0)|M}-1==(0|(s=0|_e[(0|_e[u+168>>2])+(_>>>(32-(0|_e[u+8>>2])|0)<<2)>>2]))&&(_e[n>>2]=866,_e[n+4>>2]=3253,_e[n+8>>2]=1393,we(f,812,n),me(f)),c=65535&s,s>>>=16,(0|_e[r+8>>2])>>>0<=c>>>0&&(_e[o>>2]=866,_e[o+4>>2]=909,_e[o+8>>2]=1497,we(f,812,o),me(f)),(0|de[(0|_e[r+4>>2])+c>>0])!=(0|s)&&(_e[a>>2]=866,_e[a+4>>2]=3257,_e[a+8>>2]=1410,we(f,812,a),me(f))}while(0);return _e[E>>2]=_e[E>>2]<<s,_e[M>>2]=(0|_e[M>>2])-s,Me=l,0|(M=c)}function B(e){var r,i,n,t,o,a=0,u=0,f=0;if(Me=(o=Me)+576|0,f=o+48|0,n=o+32|0,i=o+16|0,t=(r=o)+64|0,(_e[(e|=0)>>2]=0)|(u=0|_e[(a=e+284|0)>>2])&&(7&u?(_e[r>>2]=866,_e[r+4>>2]=2506,_e[r+8>>2]=1232,we(t,812,r),me(t)):le(u,0,0,1,0),_e[a>>2]=0,_e[e+288>>2]=0,_e[e+292>>2]=0),(se[e+296>>0]=0)|(u=0|_e[(a=e+268|0)>>2])&&(7&u?(_e[i>>2]=866,_e[i+4>>2]=2506,_e[i+8>>2]=1232,we(t,812,i),me(t)):le(u,0,0,1,0),_e[a>>2]=0,_e[e+272>>2]=0,_e[e+276>>2]=0),(se[e+280>>0]=0)|(u=0|_e[(a=e+252|0)>>2])&&(7&u?(_e[n>>2]=866,_e[n+4>>2]=2506,_e[n+8>>2]=1232,we(t,812,n),me(t)):le(u,0,0,1,0),_e[a>>2]=0,_e[e+256>>2]=0,_e[e+260>>2]=0),!(u=(se[e+264>>0]=0)|_e[(a=e+236|0)>>2]))return ne(f=e+212|(se[(f=e+248|0)>>0]=0)),ne(f=e+188|0),ne(f=e+164|0),ne(f=e+140|0),ne(f=e+116|0),void(Me=o);7&u?(_e[f>>2]=866,_e[f+4>>2]=2506,_e[f+8>>2]=1232,we(t,812,f),me(t)):le(u,0,0,1,0),_e[a>>2]=0,_e[e+240>>2]=0,_e[e+244>>2]=0,ne(f=e+212|(se[(f=e+248|0)>>0]=0)),ne(f=e+188|0),ne(f=e+164|0),ne(f=e+140|0),ne(f=e+116|0),Me=o}function Y(e,r,i){e|=0,r|=0,i|=0;var n=0,t=0,o=0;e:do{if(r>>>0<=20){switch(0|r){case 9:n=3+(0|_e[i>>2])&-4,r=0|_e[n>>2],_e[i>>2]=n+4,_e[e>>2]=r;break e;case 10:n=3+(0|_e[i>>2])&-4,r=0|_e[n>>2],_e[i>>2]=n+4,_e[(n=e)>>2]=r,_e[n+4>>2]=((0|r)<0)<<31>>31;break e;case 11:n=3+(0|_e[i>>2])&-4,r=0|_e[n>>2],_e[i>>2]=n+4,_e[(n=e)>>2]=r,_e[n+4>>2]=0;break e;case 12:n=7+(0|_e[i>>2])&-8,t=0|_e[(r=n)>>2],r=0|_e[r+4>>2],_e[i>>2]=n+8,_e[(n=e)>>2]=t,_e[n+4>>2]=r;break e;case 13:t=3+(0|_e[i>>2])&-4,n=0|_e[t>>2],_e[i>>2]=t+4,n=(65535&n)<<16>>16,_e[(t=e)>>2]=n,_e[t+4>>2]=((0|n)<0)<<31>>31;break e;case 14:t=3+(0|_e[i>>2])&-4,n=0|_e[t>>2],_e[i>>2]=t+4,_e[(t=e)>>2]=65535&n,_e[t+4>>2]=0;break e;case 15:t=3+(0|_e[i>>2])&-4,n=0|_e[t>>2],_e[i>>2]=t+4,n=(255&n)<<24>>24,_e[(t=e)>>2]=n,_e[t+4>>2]=((0|n)<0)<<31>>31;break e;case 16:t=3+(0|_e[i>>2])&-4,n=0|_e[t>>2],_e[i>>2]=t+4,_e[(t=e)>>2]=255&n,_e[t+4>>2]=0;break e;case 17:case 18:t=7+(0|_e[i>>2])&-8,o=+O[t>>3],_e[i>>2]=t+8,O[e>>3]=o;break e;default:break e}}}while(0)}function V(e,r,i,n,t){r|=0,i|=0,n|=0,t|=0;var o,a,u,f,l,c,s=0,_=0,d=0,E=0;if(Me=(c=Me)+576|0,f=c+48|0,o=c+32|0,_=c+16|0,u=(s=c)+64|0,l=c+60|0,E=(e|=0)+8|0,(0|_e[(a=e+4|0)>>2])>>>0>(0|_e[E>>2])>>>0&&(_e[s>>2]=866,_e[s+4>>2]=2123,_e[s+8>>2]=845,we(u,812,s),me(u)),(2147418112/(n>>>0)|0)>>>0<=r>>>0&&(_e[_>>2]=866,_e[_+4>>2]=2124,_e[_+8>>2]=885,we(u,812,_),me(u)),r>>>0<=(s=0|_e[E>>2])>>>0)return Me=c,0|(E=1);if(9==(0|(i=i&&0!=((d=r+-1|0)&r|0)?(r=d>>>16|d,r|=r>>>8,r|=r>>>4,(r=1+((r|=r>>>2)>>>1|r)|0)?9:(r=0,10)):9))&&r>>>0<=s>>>0&&(i=10),10==(0|i)&&(_e[o>>2]=866,_e[o+4>>2]=2133,_e[o+8>>2]=933,we(u,812,o),me(u)),d=0|te(r,n),t)if(_=0|oe(d,l)){lr[0&t](_,0|_e[e>>2],0|_e[a>>2]),s=0|_e[e>>2];do{if(0|s){if(7&s){_e[f>>2]=866,_e[f+4>>2]=2506,_e[f+8>>2]=1232,we(u,812,f),me(u);break}le(s,0,0,1,0);break}}while(0);_e[e>>2]=_,i=20}else r=0;else(s=0|function(e,r,i,n){r|=0,i|=0,n|=0;var t=0,o=0,a=0,u=0,f=0,l=0;if(Me=(l=Me)+560|0,f=l+32|0,o=l+16|0,a=(t=l)+48|0,u=l+44|0,7&(e|=0)|0)return _e[t>>2]=866,_e[t+4>>2]=2506,_e[t+8>>2]=1210,we(a,812,t),me(a),Me=l,(f=0)|f;if(2147418112<r>>>0)return _e[o>>2]=866,_e[o+4>>2]=2506,_e[o+8>>2]=1103,we(a,812,o),me(a),Me=l,(f=0)|f;_e[u>>2]=r,e=0|le(e,r,u,n,0),0|i&&(_e[i>>2]=_e[u>>2]);7&e|0&&(_e[f>>2]=866,_e[f+4>>2]=2558,_e[f+8>>2]=1156,we(a,812,f),me(a));return Me=l,0|(f=e)}(0|_e[e>>2],d,l,1))?(_e[e>>2]=s,i=20):r=0;return 20==(0|i)&&(d>>>0<(s=0|_e[l>>2])>>>0&&(r=(s>>>0)/(n>>>0)|0),_e[E>>2]=r,r=1),Me=c,0|(E=r)}function G(e,r,i,n,t,o,a){r|=0,i|=0,n|=0,t|=0,o|=0,a|=0;var u,f=0,l=0,c=0;if(c=0|_e[(e|=0)+88>>2],f=((1<(f=(de[c+12>>0]<<8|de[c+13>>0])>>>a)>>>0?f:1)+3|0)>>>2,l=((1<(l=(de[c+14>>0]<<8|de[c+15>>0])>>>a)>>>0?l:1)+3|0)>>>2,a=0|se[(c=c+18|0)>>0],a=0|te(f,a<<24>>24==0|a<<24>>24==9?8:16),o){if(!(0==(3&o|0)&a>>>0<=o>>>0))return(t=0)|t;a=o}if((0|te(a,l))>>>0>t>>>0)return(t=0)|t;if(o=(f+1|0)>>>1,u=(l+1|0)>>>1,!i)return(t=0)|t;switch(_e[e+92>>2]=r,_e[e+96>>2]=r,_e[e+104>>2]=i,_e[e+100>>2]=r+i,_e[e+108>>2]=0,(_e[e+112>>2]=0)|se[c>>0]){case 0:if(!(0|function(e,r,i,n,t,o,a,u){r|=0,i|=0,n|=0,t|=0,o|=0,a|=0,u|=0;var f,l,c,s,_,d,E,M,T,A,b,h,m,p,v,S,k,y,R,g,O,C,N,P,w,I,L,D,F,U,H,x,B=0,Y=0,X=0,K=0,V=0,G=0,W=0,z=0,j=0,J=0,Z=0,q=0,Q=0,$=0,ee=0,re=0,ie=0,ne=0;if(Me=(x=Me)+656|0,U=x+112|0,D=x+96|0,L=x+80|0,I=x+64|0,w=x+48|0,H=x+32|0,F=x+16|0,C=(P=x)+144|0,N=x+128|0,p=0|_e[(m=240+(e|=0)|0)>>2],S=0|_e[(v=e+256|0)>>2],k=255&(re=0|se[17+(0|_e[e+88>>2])>>0]),!(re<<24>>24))return Me=x,1;R=0==(0|u),O=(g=a+-1|0)<<4,re=u+-1|0,E=0!=(1&o|0),M=n<<1,T=e+92|0,A=e+116|0,b=e+140|0,h=e+236|0,d=0!=(1&t|0),_=e+188|0,f=e+252|0,l=1+(y=n>>>2)|0,c=y+2|0,s=y+3|0,i=o=ee=0,t=1;do{if(!R)for(Q=0|_e[r+(ee<<2)>>2],$=0;;){if(Y=0==(0|(Z=1&$)),J=(Z<<5^32)-16|0,Z=(Z<<1^2)-1|0,q=E&(e=(0|$)==(0|re)),(0|(B=Y?0:g))!=(0|(j=Y?a:-1)))for(z=E&e^1,W=Y?Q:Q+O|0;;){for(1==(0|t)&&(t=512|Te(T,A)),G=7&t,t>>>=3,Y=0|de[1539+G>>0],e=0;i=(V=(K=(X=(0|Te(T,b))+i|0)-p|0)>>31)&X|K&~V,(0|_e[m>>2])>>>0<=i>>>0&&(_e[P>>2]=866,_e[P+4>>2]=910,_e[P+8>>2]=1497,we(C,812,P),me(C)),_e[N+(e<<2)>>2]=_e[(0|_e[h>>2])+(i<<2)>>2],(e=e+1|0)>>>0<Y>>>0;);if(q|(V=d&(0|B)==(0|g))){K=0;do{e=W+(0|te(K,n))|0,X=0==(0|K)|z,Y=K<<1,o=(o=(ie=(ne=(0|Te(T,_))+o|0)-S|0)>>31)&ne|ie&~o;do{if(V){if(!X){o=(o=(ne=(ie=(0|Te(T,_))+o|0)-S|0)>>31)&ie|ne&~o;break}_e[e>>2]=_e[N+((0|de[1547+(G<<2)+Y>>0])<<2)>>2],(0|_e[v>>2])>>>0<=o>>>0&&(_e[D>>2]=866,_e[D+4>>2]=910,_e[D+8>>2]=1497,we(C,812,D),me(C)),_e[e+4>>2]=_e[(0|_e[f>>2])+(o<<2)>>2],o=(o=(ne=(ie=(0|Te(T,_))+o|0)-S|0)>>31)&ie|ne&~o}else X&&(_e[e>>2]=_e[N+((0|de[1547+(G<<2)+Y>>0])<<2)>>2],(0|_e[v>>2])>>>0<=o>>>0&&(_e[L>>2]=866,_e[L+4>>2]=910,_e[L+8>>2]=1497,we(C,812,L),me(C)),_e[e+4>>2]=_e[(0|_e[f>>2])+(o<<2)>>2]),e=e+8|0,o=(o=(ne=(ie=(0|Te(T,_))+o|0)-S|0)>>31)&ie|ne&~o,X&&(_e[e>>2]=_e[N+((0|de[1547+(G<<2)+(1|Y)>>0])<<2)>>2],(0|_e[v>>2])>>>0<=o>>>0&&(_e[U>>2]=866,_e[U+4>>2]=910,_e[U+8>>2]=1497,we(C,812,U),me(C)),_e[e+4>>2]=_e[(0|_e[f>>2])+(o<<2)>>2])}while(0);K=K+1|0}while(2!=(0|K))}else _e[W>>2]=_e[N+((0|de[1547+(G<<2)>>0])<<2)>>2],o=(o=(ne=(ie=(0|Te(T,_))+o|0)-S|0)>>31)&ie|ne&~o,(0|_e[v>>2])>>>0<=o>>>0&&(_e[F>>2]=866,_e[F+4>>2]=910,_e[F+8>>2]=1497,we(C,812,F),me(C)),_e[W+4>>2]=_e[(0|_e[f>>2])+(o<<2)>>2],_e[W+8>>2]=_e[N+((0|de[1547+(G<<2)+1>>0])<<2)>>2],o=(o=(ne=(ie=(0|Te(T,_))+o|0)-S|0)>>31)&ie|ne&~o,(0|_e[v>>2])>>>0<=o>>>0&&(_e[H>>2]=866,_e[H+4>>2]=910,_e[H+8>>2]=1497,we(C,812,H),me(C)),_e[W+12>>2]=_e[(0|_e[f>>2])+(o<<2)>>2],_e[W+(y<<2)>>2]=_e[N+((0|de[1547+(G<<2)+2>>0])<<2)>>2],o=(o=(ne=(ie=(0|Te(T,_))+o|0)-S|0)>>31)&ie|ne&~o,(0|_e[v>>2])>>>0<=o>>>0&&(_e[w>>2]=866,_e[w+4>>2]=910,_e[w+8>>2]=1497,we(C,812,w),me(C)),_e[W+(l<<2)>>2]=_e[(0|_e[f>>2])+(o<<2)>>2],_e[W+(c<<2)>>2]=_e[N+((0|de[1547+(G<<2)+3>>0])<<2)>>2],o=(o=(ne=(ie=(0|Te(T,_))+o|0)-S|0)>>31)&ie|ne&~o,(0|_e[v>>2])>>>0<=o>>>0&&(_e[I>>2]=866,_e[I+4>>2]=910,_e[I+8>>2]=1497,we(C,812,I),me(C)),_e[W+(s<<2)>>2]=_e[(0|_e[f>>2])+(o<<2)>>2];if((0|(B=Z+B|0))==(0|j))break;W=W+J|0}if((0|($=$+1|0))==(0|u))break;Q=Q+M|0}ee=ee+1|0}while((0|ee)!=(0|k));return Me=x,1}(e,n,t,a,f,l,o,u)))return(t=0)|t;break;case 4:case 6:case 5:case 3:case 2:if(!(0|function(e,r,i,n,t,o,a,u){r|=0,i|=0,n|=0,t|=0,o|=0,a|=0,u|=0;var f,l,c,s,_,d,E,M,T,A,b,h,m,p,v,S,k,y,R,g,O,C,N,P,w,I,L,D,F,U,H,x,B,Y,X,K=0,V=0,G=0,W=0,z=0,j=0,J=0,Z=0,q=0,Q=0,$=0,ee=0,re=0,ie=0,ne=0,te=0,oe=0,ae=0,ue=0,fe=0,le=0,ce=0;if(Me=(X=Me)+640|0,x=X+80|0,H=X+64|0,U=X+48|0,Y=X+32|0,B=X+16|0,L=(F=X)+128|0,D=X+112|0,d=X+96|0,M=0|_e[(E=240+(e|=0)|0)>>2],A=0|_e[(T=e+256|0)>>2],h=0|_e[(b=e+272|0)>>2],ce=0|_e[e+88>>2],m=(0|de[ce+63>>0])<<8|0|de[ce+64>>0],p=255&(ce=0|se[ce+17>>0]),!(ce<<24>>24))return Me=X,1;v=0==(0|u),k=(S=a+-1|0)<<5,y=u+-1|0,R=n<<1,g=e+92|0,O=e+116|0,C=e+164|0,N=e+268|0,P=e+140|0,w=e+236|0,I=e+212|0,ce=e+188|0,_=0==(1&t|0),s=0==(1&o|0),l=e+288|0,c=e+284|0,f=e+252|0,i=t=o=e=le=0,K=1;do{if(!v)for(ue=0|_e[r+(le<<2)>>2],fe=0;;){if(G=0==(0|(ae=1&fe)),oe=(ae<<6^64)-32|0,ae=(ae<<1^2)-1|0,(0|(V=G?0:S))!=(0|(ne=G?a:-1)))for(te=s|(0|fe)!=(0|y),ie=G?ue:ue+k|0;;){for(1==(0|K)&&(K=512|Te(g,O)),re=7&K,K>>>=3,W=0|de[1539+re>>0],G=0;o=(ee=($=(Q=(0|Te(g,C))+o|0)-h|0)>>31)&Q|$&~ee,(0|_e[b>>2])>>>0<=o>>>0&&(_e[F>>2]=866,_e[F+4>>2]=910,_e[F+8>>2]=1497,we(L,812,F),me(L)),_e[d+(G<<2)>>2]=Ee[(0|_e[N>>2])+(o<<1)>>1],(G=G+1|0)>>>0<W>>>0;);for(G=0;i=(ee=($=(Q=(0|Te(g,P))+i|0)-M|0)>>31)&Q|$&~ee,(0|_e[E>>2])>>>0<=i>>>0&&(_e[B>>2]=866,_e[B+4>>2]=910,_e[B+8>>2]=1497,we(L,812,B),me(L)),_e[D+(G<<2)>>2]=_e[(0|_e[w>>2])+(i<<2)>>2],(G=G+1|0)>>>0<W>>>0;);for(ee=_|(0|V)!=(0|S),Q=0,$=ie;;){if(J=te|0==(0|Q),Z=Q<<1,ee)for(z=0,j=$;e=(e=(W=(q=(0|Te(g,I))+e|0)-m|0)>>31)&q|W&~e,t=(t=(q=(W=(0|Te(g,ce))+t|0)-A|0)>>31)&W|q&~t,J&&(G=0|de[z+Z+(1547+(re<<2))>>0],W=3*e|0,(0|_e[l>>2])>>>0<=W>>>0&&(_e[Y>>2]=866,_e[Y+4>>2]=910,_e[Y+8>>2]=1497,we(L,812,Y),me(L)),q=(0|_e[c>>2])+(W<<1)|0,_e[j>>2]=(0|Ee[q>>1])<<16|_e[d+(G<<2)>>2],_e[j+4>>2]=(0|Ee[q+4>>1])<<16|0|Ee[q+2>>1],_e[j+8>>2]=_e[D+(G<<2)>>2],(0|_e[T>>2])>>>0<=t>>>0&&(_e[U>>2]=866,_e[U+4>>2]=910,_e[U+8>>2]=1497,we(L,812,U),me(L)),_e[j+12>>2]=_e[(0|_e[f>>2])+(t<<2)>>2]),2!=(0|(z=z+1|0));)j=j+16|0;else for(q=1^J,J=1547+(re<<2)+Z|0,z=0,j=$;e=(e=(W=(Z=(0|Te(g,I))+e|0)-m|0)>>31)&Z|W&~e,t=(t=(Z=(W=(0|Te(g,ce))+t|0)-A|0)>>31)&W|Z&~t,0!=(0|z)|q||(G=0|de[J>>0],W=3*e|0,(0|_e[l>>2])>>>0<=W>>>0&&(_e[H>>2]=866,_e[H+4>>2]=910,_e[H+8>>2]=1497,we(L,812,H),me(L)),Z=(0|_e[c>>2])+(W<<1)|0,_e[j>>2]=(0|Ee[Z>>1])<<16|_e[d+(G<<2)>>2],_e[j+4>>2]=(0|Ee[Z+4>>1])<<16|0|Ee[Z+2>>1],_e[j+8>>2]=_e[D+(G<<2)>>2],(0|_e[T>>2])>>>0<=t>>>0&&(_e[x>>2]=866,_e[x+4>>2]=910,_e[x+8>>2]=1497,we(L,812,x),me(L)),_e[j+12>>2]=_e[(0|_e[f>>2])+(t<<2)>>2]),2!=(0|(z=z+1|0));)j=j+16|0;if(2==(0|(Q=Q+1|0)))break;$=$+n|0}if((0|(V=ae+V|0))==(0|ne))break;ie=ie+oe|0}if((0|(fe=fe+1|0))==(0|u))break;ue=ue+R|0}le=le+1|0}while((0|le)!=(0|p));return Me=X,1}(e,n,t,a,f,l,o,u)))return(t=0)|t;break;case 9:if(!(0|function(e,r,i,n,t,o,a,u){r|=0,i|=0,n|=0,t|=0,o|=0,a|=0,u|=0;var f,l,c,s,_,d,E,M,T,A,b,h,m,p,v,S,k,y,R,g,O,C,N,P,w=0,I=0,L=0,D=0,F=0,U=0,H=0,x=0,B=0,Y=0,X=0,K=0,V=0,G=0,W=0,z=0,j=0,J=0;if(Me=(P=Me)+592|0,O=P+48|0,N=P+32|0,C=P+16|0,y=(g=P)+80|0,R=P+64|0,d=0|_e[(_=272+(e|=0)|0)>>2],J=0|_e[e+88>>2],E=(0|de[J+63>>0])<<8|0|de[J+64>>0],M=255&(J=0|se[J+17>>0]),!(J<<24>>24))return Me=P,1;T=0==(0|u),b=(A=a+-1|0)<<4,h=u+-1|0,m=n<<1,p=e+92|0,v=e+116|0,S=e+164|0,k=e+268|0,J=e+212|0,s=0==(1&t|0),c=0==(1&o|0),l=e+288|0,f=e+284|0,i=t=j=0,o=1;do{if(!T)for(W=0|_e[r+(j<<2)>>2],z=0;;){if(w=0==(0|(G=1&z)),V=(G<<5^32)-16|0,G=(G<<1^2)-1|0,(0|(e=w?0:A))!=(0|(X=w?a:-1)))for(K=c|(0|z)!=(0|h),Y=w?W:W+b|0;;){for(1==(0|o)&&(o=512|Te(p,v)),B=7&o,o>>>=3,I=0|de[1539+B>>0],w=0;i=(x=(H=(U=(0|Te(p,S))+i|0)-d|0)>>31)&U|H&~x,(0|_e[_>>2])>>>0<=i>>>0&&(_e[g>>2]=866,_e[g+4>>2]=910,_e[g+8>>2]=1497,we(y,812,g),me(y)),_e[R+(w<<2)>>2]=Ee[(0|_e[k>>2])+(i<<1)>>1],(w=w+1|0)>>>0<I>>>0;);for(x=s|(0|e)!=(0|A),U=0,H=Y;F=K|0==(0|U),I=U<<1,D=(D=(L=(w=(0|Te(p,J))+t|0)-E|0)>>31)&w|L&~D,x?(F&&(t=0|de[1547+(B<<2)+I>>0],w=3*D|0,(0|_e[l>>2])>>>0<=w>>>0&&(_e[C>>2]=866,_e[C+4>>2]=910,_e[C+8>>2]=1497,we(y,812,C),me(y)),L=(0|_e[f>>2])+(w<<1)|0,_e[H>>2]=(0|Ee[L>>1])<<16|_e[R+(t<<2)>>2],_e[H+4>>2]=(0|Ee[L+4>>1])<<16|0|Ee[L+2>>1]),L=H+8|0,t=(t=(D=(w=(0|Te(p,J))+D|0)-E|0)>>31)&w|D&~t,F&&(w=0|de[1547+(B<<2)+(1|I)>>0],I=3*t|0,(0|_e[l>>2])>>>0<=I>>>0&&(_e[O>>2]=866,_e[O+4>>2]=910,_e[O+8>>2]=1497,we(y,812,O),me(y)),F=(0|_e[f>>2])+(I<<1)|0,_e[L>>2]=(0|Ee[F>>1])<<16|_e[R+(w<<2)>>2],_e[H+12>>2]=(0|Ee[F+4>>1])<<16|0|Ee[F+2>>1])):(F&&(t=0|de[1547+(B<<2)+I>>0],w=3*D|0,(0|_e[l>>2])>>>0<=w>>>0&&(_e[N>>2]=866,_e[N+4>>2]=910,_e[N+8>>2]=1497,we(y,812,N),me(y)),F=(0|_e[f>>2])+(w<<1)|0,_e[H>>2]=(0|Ee[F>>1])<<16|_e[R+(t<<2)>>2],_e[H+4>>2]=(0|Ee[F+4>>1])<<16|0|Ee[F+2>>1]),t=(t=(F=(D=(0|Te(p,J))+D|0)-E|0)>>31)&D|F&~t),2!=(0|(U=U+1|0));)H=H+n|0;if((0|(e=G+e|0))==(0|X))break;Y=Y+V|0}if((0|(z=z+1|0))==(0|u))break;W=W+m|0}j=j+1|0}while((0|j)!=(0|M));return Me=P,1}(e,n,t,a,f,l,o,u)))return(t=0)|t;break;case 8:case 7:if(!(0|function(e,r,i,n,t,o,a,u){r|=0,i|=0,n|=0,t|=0,o|=0,a|=0,u|=0;var f,l,c,s,_,d,E,M,T,A,b,h,m,p,v,S,k,y,R,g,O,C,N,P,w,I,L,D=0,F=0,U=0,H=0,x=0,B=0,Y=0,X=0,K=0,V=0,G=0,W=0,z=0,j=0,J=0,Z=0,q=0,Q=0,$=0,ee=0,re=0,ie=0,ne=0,te=0,oe=0;if(Me=(L=Me)+640|0,P=L+80|0,N=L+64|0,C=L+48|0,I=L+32|0,w=L+16|0,R=(O=L)+128|0,g=L+112|0,_=L+96|0,E=0|_e[(d=272+(e|=0)|0)>>2],oe=0|_e[e+88>>2],M=(0|de[oe+63>>0])<<8|0|de[oe+64>>0],T=255&(oe=0|se[oe+17>>0]),!(oe<<24>>24))return Me=L,1;A=0==(0|u),h=(b=a+-1|0)<<5,m=u+-1|0,p=n<<1,v=e+92|0,S=e+116|0,k=e+164|0,y=e+268|0,oe=e+212|0,s=0==(1&t|0),c=0==(1&o|0),l=e+288|0,f=e+284|0,i=t=o=e=te=0,D=1;do{if(!A)for(ie=0|_e[r+(te<<2)>>2],ne=0;;){if(U=0==(0|(re=1&ne)),ee=(re<<6^64)-32|0,re=(re<<1^2)-1|0,(0|(F=U?0:b))!=(0|(Q=U?a:-1)))for($=c|(0|ne)!=(0|m),q=U?ie:ie+h|0;;){for(1==(0|D)&&(D=512|Te(v,S)),Z=7&D,D>>>=3,H=0|de[1539+Z>>0],U=0;i=(J=(j=(z=(0|Te(v,k))+i|0)-E|0)>>31)&z|j&~J,(0|_e[d>>2])>>>0<=i>>>0&&(_e[O>>2]=866,_e[O+4>>2]=910,_e[O+8>>2]=1497,we(R,812,O),me(R)),_e[g+(U<<2)>>2]=Ee[(0|_e[y>>2])+(i<<1)>>1],(U=U+1|0)>>>0<H>>>0;);for(U=0;o=(J=(j=(z=(0|Te(v,k))+o|0)-E|0)>>31)&z|j&~J,(0|_e[d>>2])>>>0<=o>>>0&&(_e[w>>2]=866,_e[w+4>>2]=910,_e[w+8>>2]=1497,we(R,812,w),me(R)),_e[_+(U<<2)>>2]=Ee[(0|_e[y>>2])+(o<<1)>>1],(U=U+1|0)>>>0<H>>>0;);for(J=s|(0|F)!=(0|b),z=0,j=q;;){if(V=$|0==(0|z),G=z<<1,J)for(X=0,K=j;t=(t=(Y=(W=(0|Te(v,oe))+t|0)-M|0)>>31)&W|Y&~t,e=(e=(W=(Y=(0|Te(v,oe))+e|0)-M|0)>>31)&Y|W&~e,V&&(Y=0|de[X+G+(1547+(Z<<2))>>0],H=3*t|0,(U=0|_e[l>>2])>>>0<=H>>>0&&(_e[I>>2]=866,_e[I+4>>2]=910,_e[I+8>>2]=1497,we(R,812,I),me(R),U=0|_e[l>>2]),H=(x=0|_e[f>>2])+(H<<1)|0,W=(U=(B=3*e|0)>>>0<U>>>0?x:(_e[C>>2]=866,_e[C+4>>2]=910,_e[C+8>>2]=1497,we(R,812,C),me(R),0|_e[f>>2]))+(B<<1)|0,_e[K>>2]=(0|Ee[H>>1])<<16|_e[g+(Y<<2)>>2],_e[K+4>>2]=(0|Ee[H+4>>1])<<16|0|Ee[H+2>>1],_e[K+8>>2]=(0|Ee[W>>1])<<16|_e[_+(Y<<2)>>2],_e[K+12>>2]=(0|Ee[W+4>>1])<<16|0|Ee[W+2>>1]),2!=(0|(X=X+1|0));)K=K+16|0;else for(W=1^V,V=1547+(Z<<2)+G|0,X=0,K=j;t=(t=(Y=(G=(0|Te(v,oe))+t|0)-M|0)>>31)&G|Y&~t,e=(e=(G=(Y=(0|Te(v,oe))+e|0)-M|0)>>31)&Y|G&~e,0!=(0|X)|W||(Y=0|de[V>>0],H=3*t|0,(U=0|_e[l>>2])>>>0<=H>>>0&&(_e[N>>2]=866,_e[N+4>>2]=910,_e[N+8>>2]=1497,we(R,812,N),me(R),U=0|_e[l>>2]),H=(x=0|_e[f>>2])+(H<<1)|0,G=(U=(B=3*e|0)>>>0<U>>>0?x:(_e[P>>2]=866,_e[P+4>>2]=910,_e[P+8>>2]=1497,we(R,812,P),me(R),0|_e[f>>2]))+(B<<1)|0,_e[K>>2]=(0|Ee[H>>1])<<16|_e[g+(Y<<2)>>2],_e[K+4>>2]=(0|Ee[H+4>>1])<<16|0|Ee[H+2>>1],_e[K+8>>2]=(0|Ee[G>>1])<<16|_e[_+(Y<<2)>>2],_e[K+12>>2]=(0|Ee[G+4>>1])<<16|0|Ee[G+2>>1]),2!=(0|(X=X+1|0));)K=K+16|0;if(2==(0|(z=z+1|0)))break;j=j+n|0}if((0|(F=re+F|0))==(0|Q))break;q=q+ee|0}if((0|(ne=ne+1|0))==(0|u))break;ie=ie+p|0}te=te+1|0}while((0|te)!=(0|T));return Me=L,1}(e,n,t,a,f,l,o,u)))return(t=0)|t;break;default:return(t=0)|t}return 0|(t=1)}function W(e,r,i){e|=0,r|=0;var n,t,o=0;if(8192<=(0|(i|=0)))return 0|g(0|e,0|r,0|i);if(t=0|e,n=e+i|0,(3&e)==(3&r)){for(;3&e;){if(!i)return 0|t;se[e>>0]=0|se[r>>0],e=e+1|0,r=r+1|0,i=i-1|0}for(o=(i=-4&n|0)-64|0;(0|e)<=(0|o);)_e[e>>2]=_e[r>>2],_e[e+4>>2]=_e[r+4>>2],_e[e+8>>2]=_e[r+8>>2],_e[e+12>>2]=_e[r+12>>2],_e[e+16>>2]=_e[r+16>>2],_e[e+20>>2]=_e[r+20>>2],_e[e+24>>2]=_e[r+24>>2],_e[e+28>>2]=_e[r+28>>2],_e[e+32>>2]=_e[r+32>>2],_e[e+36>>2]=_e[r+36>>2],_e[e+40>>2]=_e[r+40>>2],_e[e+44>>2]=_e[r+44>>2],_e[e+48>>2]=_e[r+48>>2],_e[e+52>>2]=_e[r+52>>2],_e[e+56>>2]=_e[r+56>>2],_e[e+60>>2]=_e[r+60>>2],e=e+64|0,r=r+64|0;for(;(0|e)<(0|i);)_e[e>>2]=_e[r>>2],e=e+4|0,r=r+4|0}else for(i=n-4|0;(0|e)<(0|i);)se[e>>0]=0|se[r>>0],se[e+1>>0]=0|se[r+1>>0],se[e+2>>0]=0|se[r+2>>0],se[e+3>>0]=0|se[r+3>>0],e=e+4|0,r=r+4|0;for(;(0|e)<(0|n);)se[e>>0]=0|se[r>>0],e=e+1|0,r=r+1|0;return 0|t}function z(e,r,i){r|=0,i|=0;var n,t,o,a,u,f=0,l=0,c=0,s=0,_=0,d=0;Me=(u=Me)+48|0,a=u+16|0,l=(c=u)+32|0,f=0|_e[(t=(e|=0)+28|0)>>2],_e[l>>2]=f,f=(0|_e[(o=e+20|0)>>2])-f|0,_e[l+4>>2]=f,_e[l+8>>2]=r,f=f+(_e[l+12>>2]=i)|0,n=e+60|0,_e[c>>2]=_e[n>>2],_e[c+4>>2]=l,_e[c+8>>2]=2,c=0|xe(0|w(146,0|c));e:do{if((0|f)!=(0|c)){for(r=2;!((0|c)<0);)if(f=f-c|0,r=((_=(d=0|_e[l+4>>2])>>>0<c>>>0)<<31>>31)+r|0,d=c-(_?d:0)|0,_e[(l=_?l+8|0:l)>>2]=(0|_e[l>>2])+d,_e[(_=l+4|0)>>2]=(0|_e[_>>2])-d,_e[a>>2]=_e[n>>2],_e[a+4>>2]=l,_e[a+8>>2]=r,(0|f)==(0|(c=0|xe(0|w(146,0|a))))){s=3;break e}_e[e+16>>2]=0,_e[t>>2]=0,_e[o>>2]=0,_e[e>>2]=32|_e[e>>2],i=2==(0|r)?0:i-(0|_e[l+4>>2])|0}else s=3}while(0);return 3==(0|s)&&(d=0|_e[e+44>>2],_e[e+16>>2]=d+(0|_e[e+48>>2]),_e[t>>2]=d,_e[o>>2]=d),Me=u,0|i}function j(e,r,i){e|=0,r|=0,i|=0;var n,t,o,a,u,f=0,l=0,c=0,s=0,_=0,d=0,E=0;for(Me=(u=Me)+224|0,n=u+120|0,a=(o=u)+136|0,l=(f=t=u+80|0)+40|0;(0|(f=f+4|(_e[f>>2]=0)))<(0|l););return _e[n>>2]=_e[i>>2],i=(0|D(0,r,n,o,t))<0?-1:(_e[e+76>>2],E=32&(i=0|_e[e>>2]),(0|se[e+74>>0])<1&&(_e[e>>2]=-33&i),0|_e[(f=e+48|0)>>2]?i=0|D(e,r,n,o,t):(c=0|_e[(l=e+44|0)>>2],_e[l>>2]=a,_e[(s=e+28|0)>>2]=a,_e[(_=e+20|0)>>2]=a,_e[f>>2]=80,_e[(d=e+16|0)>>2]=a+80,i=0|D(e,r,n,o,t),c&&(or[7&_e[e+36>>2]](e,0,0),i=0==(0|_e[_>>2])?-1:i,_e[l>>2]=c,_e[f>>2]=0,_e[d>>2]=0,_e[s>>2]=0,_e[_>>2]=0)),f=0|_e[e>>2],_e[e>>2]=f|E,0==(32&f|0)?i:-1),Me=u,0|i}function J(e,r,i,n){r|=0,i|=0,n|=0;var t,o,a,u,f,l,c,s=0,_=0;for(Me=(c=Me)+64|0,f=c,_=0|_e[(e|=0)>>2],l=e+(0|_e[_+-8>>2])|0,_=0|_e[_+-4>>2],_e[f>>2]=i,_e[f+4>>2]=e,_e[f+8>>2]=r,_e[f+12>>2]=n,r=f+20|0,n=f+24|0,t=f+28|0,o=f+32|0,a=f+40|0,u=(s=e=f+16|0)+36|0;(0|(s=s+4|(_e[s>>2]=0)))<(0|u););X[e+36>>1]=0,se[e+38>>0]=0;e:do{if(0|Ve(_,i))_e[f+48>>2]=1,sr[3&_e[20+(0|_e[_>>2])>>2]](_,f,l,l,1,0),e=1==(0|_e[n>>2])?l:0;else{switch(ar[3&_e[24+(0|_e[_>>2])>>2]](_,f,l,1,0),0|_e[f+36>>2]){case 0:e=1==(0|_e[a>>2])&1==(0|_e[t>>2])&1==(0|_e[o>>2])?0|_e[r>>2]:0;break e;case 1:break;default:e=0;break e}if(1!=(0|_e[n>>2])&&!(0==(0|_e[a>>2])&1==(0|_e[t>>2])&1==(0|_e[o>>2]))){e=0;break}e=0|_e[e>>2]}}while(0);return Me=c,0|e}function Z(e){var r,i=0,n=0,t=0,o=0,a=0,u=0,f=0;if(Me=(r=Me)+544|0,u=r+16|0,o=(i=r)+32|0,8192<=((n=0|_e[(a=(e|=0)+8|0)>>2])+-1|0)>>>0&&(_e[i>>2]=866,_e[i+4>>2]=3006,_e[i+8>>2]=1257,we(o,812,i),me(o)),_e[e>>2]=n,f=(i=0|_e[(t=e+20|0)>>2])?n:((i=0|oe(180,0))?(_e[(f=i+164|0)>>2]=0,_e[f+4>>2]=0,_e[f+8>>2]=0,_e[f+12>>2]=0):i=0,_e[t>>2]=i,0|_e[e>>2]),u=0|_e[a>>2]?f:(_e[u>>2]=866,_e[u+4>>2]=910,_e[u+8>>2]=1497,we(o,812,u),me(o),0|_e[e>>2]),o=0|_e[e+4>>2],!(16<u>>>0))return e=(e=0)|F(i,f,o,e),Me=r,0|e;for(n=u,t=0;a=t+1|0,3<n>>>0;)n>>>=1,t=a;return e=0|F(i,f,o,e=255&((e=t+2+(32!=(0|a)&1<<a>>>0<u>>>0&1)|0)>>>0<11?e:11)),Me=r,0|e}function q(e){var r,i,n,t,o,a,u=0,f=0;Me=(a=Me)+576|0,t=a+48|0,o=a+32|0,i=a+16|0,n=(r=a)+64|0,u=0|_e[(e|=0)+168>>2];do{if(0|u){if(f=0|_e[u+-4>>2],u=u+-8|0,0!=(0|f)&&(0|f)==(0|~_e[u>>2])||(_e[r>>2]=866,_e[r+4>>2]=651,_e[r+8>>2]=1579,we(n,812,r),me(n)),7&u){_e[i>>2]=866,_e[i+4>>2]=2506,_e[i+8>>2]=1232,we(n,812,i),me(n);break}le(u,0,0,1,0);break}}while(0);if(u=0|_e[e+176>>2])return f=0|_e[u+-4>>2],u=u+-8|0,0!=(0|f)&&(0|f)==(0|~_e[u>>2])||(_e[o>>2]=866,_e[o+4>>2]=651,_e[o+8>>2]=1579,we(n,812,o),me(n)),7&u?(_e[t>>2]=866,_e[t+4>>2]=2506,_e[t+8>>2]=1232,we(n,812,t),me(n)):le(u,0,0,1,0),void(Me=a);Me=a}function Q(e,r,i){var n;return 0!=(0|(e|=0))&73<(r|=0)>>>0&0!=(0|(i|=0))?40!=(0|_e[i>>2])?(i=0)|i:18552!=((0|de[e>>0])<<8|0|de[e+1>>0]|0)?(i=0)|i:((0|de[e+2>>0])<<8|0|de[e+3>>0])>>>0<74?(i=0)|i:((0|de[e+7>>0])<<16|(0|de[e+6>>0])<<24|(0|de[e+8>>0])<<8|0|de[e+9>>0])>>>0>r>>>0?(i=0)|i:(_e[i+4>>2]=(0|de[e+12>>0])<<8|0|de[e+13>>0],_e[i+8>>2]=(0|de[e+14>>0])<<8|0|de[e+15>>0],_e[i+12>>2]=de[e+16>>0],_e[i+16>>2]=de[e+17>>0],r=e+18|0,_e[(n=i+32|0)>>2]=de[r>>0],r=(_e[n+4>>2]=0)|se[r>>0],_e[i+20>>2]=r<<24>>24==0|r<<24>>24==9?8:16,_e[i+24>>2]=(0|de[e+26>>0])<<16|(0|de[e+25>>0])<<24|(0|de[e+27>>0])<<8|0|de[e+28>>0],_e[i+28>>2]=(0|de[e+30>>0])<<16|(0|de[e+29>>0])<<24|(0|de[e+31>>0])<<8|0|de[e+32>>0],0|(i=1)):(i=0)|i}function $(e,r){e|=0;var i,n,t,o=0,a=0,u=0,f=0,l=0;if(Me=(t=Me)+544|0,l=t+16|0,f=(o=t)+32|0,33<=(r|=0)>>>0&&(_e[o>>2]=866,_e[o+4>>2]=3199,_e[o+8>>2]=1350,we(f,812,o),me(f)),(0|r)<=(0|(o=0|_e[(n=e+20|0)>>2])))return f=o,l=(a=0|_e[(u=a=e+16|0)>>2])>>>(l=32-r|0),a<<=r,_e[u>>2]=a,r=f-r|0,_e[n>>2]=r,Me=t,0|l;for(a=e+4|0,u=e+8|0,i=e+16|0;e=(0|(e=0|_e[a>>2]))==(0|_e[u>>2])?0:(_e[a>>2]=e+1,0|de[e>>0]),o=o+8|0,33<=(0|(_e[n>>2]=o))&&(_e[l>>2]=866,_e[l+4>>2]=3208,_e[l+8>>2]=1366,we(f,812,l),me(f),o=0|_e[n>>2]),e=e<<32-o|_e[i>>2],_e[i>>2]=e,(0|o)<(0|r););return l=e>>>(l=32-r|0),f=e<<r,_e[i>>2]=f,r=o-r|0,_e[n>>2]=r,Me=t,0|l}function ee(e,r,i){e|=0;var n=0,t=0,o=0,a=0;o=255&(r|=0),n=0!=(0|(i|=0));e:do{if(n&0!=(3&e|0))for(t=255&r;;){if((0|se[e>>0])==t<<24>>24){a=6;break e}if(!((n=0!=(0|(i=i+-1|0)))&0!=(3&(e=e+1|0)|0))){a=5;break}}else a=5}while(0);5==(0|a)&&(n?a=6:i=0);e:do{if(6==(0|a)&&(t=255&r,(0|se[e>>0])!=t<<24>>24)){n=0|te(o,16843009);r:do{if(3<i>>>0){for(;!((-2139062144&(o=_e[e>>2]^n)^-2139062144)&o+-16843009|0);)if(e=e+4|0,(i=i+-4|0)>>>0<=3){a=11;break r}}else a=11}while(0);if(11==(0|a)&&!i){i=0;break}for(;;){if((0|se[e>>0])==t<<24>>24)break e;if(e=e+1|0,!(i=i+-1|0)){i=0;break}}}}while(0);return 0|(0|i?e:0)}function re(e,r,i,n,t){r|=0,i|=0,n|=0,t|=0;var o,a,u,f=0,l=0,c=0;return Me=(u=Me)+528|0,l=(c=u)+16|0,o=0|_e[(e|=0)+88>>2],Me=(c=((a=(0|de[o+70+(t<<2)+1>>0])<<16|(0|de[o+70+(t<<2)>>0])<<24|(0|de[o+70+(t<<2)+2>>0])<<8|0|de[o+70+(t<<2)+3>>0])>>>0<(f=(f=t+1|0)>>>0<(0|de[o+16>>0])>>>0?(0|de[o+70+(f<<2)+1>>0])<<16|(0|de[o+70+(f<<2)>>0])<<24|(0|de[o+70+(f<<2)+2>>0])<<8|0|de[o+70+(f<<2)+3>>0]:0|_e[e+8>>2])>>>0||(_e[c>>2]=866,_e[c+4>>2]=3694,_e[c+8>>2]=1508,we(l,812,c),me(l)),0|G(e,l=(l=0|_e[(l=e+4|0)>>2])+a|0,c=f-a|0,r,i,n,t)),u),0|c}function ie(e,r,i){e|=0,r|=0;var n=0,t=0,o=0,a=0,u=0;(t=0|_e[(n=(i|=0)+16|0)>>2])?o=5:0|ve(i)?n=0:(t=0|_e[n>>2],o=5);e:do{if(5==(0|o)){if((t-(n=a=0|_e[(u=i+20|0)>>2])|0)>>>0<r>>>0){n=0|or[7&_e[i+36>>2]](i,e,r);break}r:do{if(-1<(0|se[i+75>>0])){for(a=r;;){if(!a){o=0,t=e;break r}if(10==(0|se[e+(t=a+-1|0)>>0]))break;a=t}if((n=0|or[7&_e[i+36>>2]](i,e,a))>>>0<a>>>0)break e;t=e+(o=a)|0,r=r-a|0,n=0|_e[u>>2]}else o=0,t=e}while(0);W(0|n,0|t,0|r),_e[u>>2]=(0|_e[u>>2])+r,n=o+r|0}}while(0);return 0|n}function ne(e){var r,i,n=0,t=0,o=0;Me=(i=Me)+544|0,o=i+16|0,r=(t=i)+32|0,n=0|_e[(e|=0)+20>>2];do{if(0|n){if(q(n),7&n){_e[t>>2]=866,_e[t+4>>2]=2506,_e[t+8>>2]=1232,we(r,812,t),me(r);break}le(n,0,0,1,0);break}}while(0);if(!(t=0|_e[(n=e+4|0)>>2]))return se[(o=e+16|0)>>0]=0,void(Me=i);7&t?(_e[o>>2]=866,_e[o+4>>2]=2506,_e[o+8>>2]=1232,we(r,812,o),me(r)):le(t,0,0,1,0),_e[n>>2]=0,_e[e+8>>2]=0,_e[e+12>>2]=0,se[(o=e+16|0)>>0]=0,Me=i}function oe(e,r){r|=0;var i,n,t,o,a=0,u=0,f=0;return Me=(o=Me)+560|0,f=o+32|0,t=o+16|0,n=(a=o)+48|0,i=o+44|0,2147418112<(u=0|(u=(e|=0)+3&-4)?u:4)>>>0?(_e[a>>2]=866,_e[a+4>>2]=2506,_e[a+8>>2]=1103,we(n,812,a),me(n),Me=o,(f=0)|f):(e=0|le(0,_e[i>>2]=u,i,1,0),a=0|_e[i>>2],0|r&&(_e[r>>2]=a),0==(0|e)|a>>>0<u>>>0?(_e[t>>2]=866,_e[t+4>>2]=2506,_e[t+8>>2]=1129,we(n,812,t),me(n),e=0):7&e&&(_e[f>>2]=866,_e[f+4>>2]=2533,_e[f+8>>2]=1156,we(n,812,f),me(n)),Me=o,0|(f=e))}function ae(e,r,i){r|=0;var n,t=0,o=0,a=0;if(n=(e|=0)+(i|=0)|0,r&=255,67<=(0|i)){for(;3&e;)se[e>>0]=r,e=e+1|0;for(o=(t=-4&n|0)-64|0,a=r|r<<8|r<<16|r<<24;(0|e)<=(0|o);)_e[e>>2]=a,_e[e+4>>2]=a,_e[e+8>>2]=a,_e[e+12>>2]=a,_e[e+16>>2]=a,_e[e+20>>2]=a,_e[e+24>>2]=a,_e[e+28>>2]=a,_e[e+32>>2]=a,_e[e+36>>2]=a,_e[e+40>>2]=a,_e[e+44>>2]=a,_e[e+48>>2]=a,_e[e+52>>2]=a,_e[e+56>>2]=a,_e[e+60>>2]=a,e=e+64|0;for(;(0|e)<(0|t);)_e[e>>2]=a,e=e+4|0}for(;(0|e)<(0|n);)se[e>>0]=r,e=e+1|0;return n-i|0}function ue(e,r,i,n,t){e|=0,i|=0,n|=0,t|=0;var o=0,a=0,u=0,f=0;se[(r|=0)+53>>0]=1;do{if((0|_e[r+4>>2])==(0|n)){if(se[r+52>>0]=1,u=r+54|0,f=r+48|0,a=r+24|0,e=r+36|0,!(o=0|_e[(n=r+16|0)>>2])){if(_e[n>>2]=i,_e[a>>2]=t,!((_e[e>>2]=1)==(0|_e[f>>2])&1==(0|t)))break;se[u>>0]=1;break}if((0|o)!=(0|i)){_e[e>>2]=1+(0|_e[e>>2]),se[u>>0]=1;break}2==(0|(e=0|_e[a>>2]))&&(e=_e[a>>2]=t),1==(0|_e[f>>2])&1==(0|e)&&(se[u>>0]=1)}}while(0)}function fe(e,r){e|=0;var i,n,t,o=0,a=0,u=0,f=0;Me=(t=Me)+16|0,n=255&(r|=0),se[(i=t)>>0]=n,(u=0|_e[(a=e+16|0)>>2])?f=4:0|ve(e)?o=-1:(u=0|_e[a>>2],f=4);do{if(4==(0|f)){if((a=0|_e[(f=e+20|0)>>2])>>>0<u>>>0&&(0|(o=255&r))!=(0|se[e+75>>0])){_e[f>>2]=a+1,se[a>>0]=n;break}o=1==(0|or[7&_e[e+36>>2]](e,i,1))?0|de[i>>0]:-1}}while(0);return Me=t,0|o}function le(e,r,i,n,t){e|=0,r|=0,i|=0,n|=0,t|=0;do{if(e){if(!r){if(U(e),!i){r=0;break}r=_e[i>>2]=0;break}n?e=0==(0|(r=0|be(e,r)))?e:r:r=0,i&&(t=0|Pe(e),_e[i>>2]=t)}else r=0|I(r),i&&(e=r?0|Pe(r):0,_e[i>>2]=e)}while(0);return 0|r}function ce(e,r){e|=0,r|=0;var i,n,t,o,a=0,u=0;for(u=0;;){if((0|de[2140+u>>0])==(0|e)){e=2;break}if(87==(0|(a=u+1|0))){a=2228,u=87,e=5;break}u=a}if(2==(0|e)&&(u?(a=2228,e=5):a=2228),5==(0|e))for(;;){for(;a=(e=a)+1|0,0!=(0|se[e>>0]););if(!(u=u+-1|0))break;e=5}return 0|(i=a,n=0|_e[r+20>>2],0|(t=i|=0,o=n|=0,t|=0,0|(0|(o=(o|=0)?0|function(e,r,i){r|=0,i|=0;var n,t=0,o=0,a=0,u=0,f=0,l=0,c=0,s=0,_=0;n=1794895138+(0|_e[(e|=0)>>2])|0,a=0|Ge(0|_e[e+8>>2],n),t=0|Ge(0|_e[e+12>>2],n),o=0|Ge(0|_e[e+16>>2],n);e:do{if(a>>>0<r>>>2>>>0&&(_=r-(a<<2)|0,t>>>0<_>>>0&o>>>0<_>>>0)&&0==(3&(o|t)|0)){for(_=t>>>2,s=o>>>2,c=0;;){if(t=0|Ge(0|_e[e+((o=(u=(l=c+(f=a>>>1)|0)<<1)+_|0)<<2)>>2],n),!((o=0|Ge(0|_e[e+(o+1<<2)>>2],n))>>>0<r>>>0&t>>>0<(r-o|0)>>>0)){t=0;break e}if(0|se[e+(o+t)>>0]){t=0;break e}if(!(t=0|Se(i,e+o|0)))break;if(t=(0|t)<0,1==(0|a)){t=0;break e}c=t?c:l,a=t?f:a-f|0}o=0|Ge(0|_e[e+((t=u+s|0)<<2)>>2],n),t=(t=0|Ge(0|_e[e+(t+1<<2)>>2],n))>>>0<r>>>0&o>>>0<(r-t|0)>>>0&&0==(0|se[e+(t+o)>>0])?e+t|0:0}else t=0}while(0);return 0|t}(0|_e[o>>2],0|_e[o+4>>2],t):0)?o:t)))}function Ae(e,r,i){i|=0;var n=0;if(0<(r|=0)>>>0|0==(0|r)&4294967295<(e|=0)>>>0){for(;n=0|Ne(0|e,0|r,10,0),se[(i=i+-1|0)>>0]=255&n|48,e=0|We(0|(n=e),0|r,10,0),9<r>>>0|9==(0|r)&4294967295<n>>>0;)r=C;r=e}else r=e;if(r)for(;se[(i=i+-1|0)>>0]=48|(r>>>0)%10,!(r>>>0<10);)r=(r>>>0)/10|0;return 0|i}function be(e,r){r|=0;var i=0,n=0;return(e|=0)?4294967231<r>>>0?(r=0|er(),_e[r>>2]=12,(r=0)|r):0|(i=0|function(e,r){r|=0;var i,n,t=0,o=0,a=0,u=0,f=0,l=0,c=0,s=0;if(i=(e|=0)+(t=-8&(c=0|_e[(s=e+4|0)>>2]))|0,!(3&c))return r>>>0<256?(e=0)|e:(r+4|0)>>>0<=t>>>0&&(t-r|0)>>>0<=_e[1264]<<1>>>0?0|e:(e=0)|e;if(r>>>0<=t>>>0)return(t=t-r|0)>>>0<=15||(l=e+r|0,_e[s>>2]=1&c|r|2,_e[l+4>>2]=3|t,_e[(s=l+t+4|0)>>2]=1|_e[s>>2],H(l,t)),0|e;if((0|i)==(0|_e[1150]))return t=(l=(0|_e[1147])+t|0)-r|0,o=e+r|0,l>>>0<=r>>>0?(e=0)|e:(_e[s>>2]=1&c|r|2,_e[o+4>>2]=1|t,_e[1150]=o,_e[1147]=t,0|e);if((0|i)==(0|_e[1149]))return(a=(0|_e[1146])+t|0)>>>0<r>>>0?(e=0)|e:(o=1&c,15<(t=a-r|0)>>>0?(l=(c=e+r|0)+t|0,_e[s>>2]=o|r|2,_e[c+4>>2]=1|t,_e[l>>2]=t,_e[(o=l+4|0)>>2]=-2&_e[o>>2],o=c):(_e[s>>2]=o|a|2,_e[(o=e+a+4|0)>>2]=1|_e[o>>2],t=o=0),_e[1146]=t,_e[1149]=o,0|e);if(2&(o=0|_e[i+4>>2])|0)return(e=0)|e;if((n=(-8&o)+t|0)>>>0<r>>>0)return(e=0)|e;l=n-r|0,a=o>>>3;do{if(o>>>0<256){if(o=0|_e[i+8>>2],(0|(t=0|_e[i+12>>2]))==(0|o)){_e[1144]=_e[1144]&~(1<<a);break}_e[o+12>>2]=t,_e[t+8>>2]=o;break}f=0|_e[i+24>>2],t=0|_e[i+12>>2];do{if((0|t)==(0|i)){if(t=0|_e[(o=4+(a=i+16|0)|0)>>2])u=o;else{if(!(t=0|_e[a>>2])){a=0;break}u=a}for(;;)if(0|(o=0|_e[(a=t+20|0)>>2]))t=o,u=a;else{if(!(a=0|_e[(o=t+16|0)>>2]))break;t=a,u=o}_e[u>>2]=0,a=t}else a=0|_e[i+8>>2],_e[a+12>>2]=t,_e[t+8>>2]=a,a=t}while(0);if(0|f){if(t=0|_e[i+28>>2],(0|i)==(0|_e[(o=4880+(t<<2)|0)>>2])){if(!(_e[o>>2]=a)){_e[1145]=_e[1145]&~(1<<t);break}}else if(!(_e[f+16+(((0|_e[f+16>>2])!=(0|i)&1)<<2)>>2]=a))break;_e[a+24>>2]=f,0|(o=0|_e[(t=i+16|0)>>2])&&(_e[a+16>>2]=o,_e[o+24>>2]=a),0|(t=0|_e[t+4>>2])&&(_e[a+20>>2]=t,_e[t+24>>2]=a)}}while(0);return t=1&c,l>>>0<16?(_e[s>>2]=n|t|2,_e[(s=e+n+4|0)>>2]=1|_e[s>>2]):(c=e+r|0,_e[s>>2]=t|r|2,_e[c+4>>2]=3|l,_e[(s=c+l+4|0)>>2]=1|_e[s>>2],H(c,l)),0|e}(e+-8|0,r>>>0<11?16:r+11&-8))?0|(r=i+8|0):(i=0|I(r))?(W(0|i,0|e,0|((n=(-8&(n=0|_e[e+-4>>2]))-(0==(3&n|0)?8:4)|0)>>>0<r>>>0?n:r)),U(e),0|(r=i)):(r=0)|r:0|(r=0|I(r))}function he(e,r,i,n){e|=0,i|=0,n|=0;var t,o,a;t=0|_e[(e=(r|=0)+16|0)>>2],o=r+36|0,a=r+24|0;do{if(t){if((0|t)!=(0|i)){_e[o>>2]=1+(0|_e[o>>2]),_e[a>>2]=2,se[r+54>>0]=1;break}2==(0|_e[a>>2])&&(_e[a>>2]=n)}else _e[e>>2]=i,_e[a>>2]=n,_e[o>>2]=1}while(0)}function me(e){e|=0;var r,i=0,n=0;r=0|_e[119],_e[r+76>>2];do{if((0|Be(e,r))<0)e=1;else{if(10!=(0|se[r+75>>0])&&(n=0|_e[(i=r+20|0)>>2])>>>0<(0|_e[r+16>>2])>>>0){_e[i>>2]=n+1,se[n>>0]=10,e=0;break}e=(0|fe(r,10))<0}}while(0);return e<<31>>31|0}function pe(e,r,i,n,t){e|=0,r|=0;var o,a;if(Me=(a=Me)+256|0,o=a,(0|(n|=0))<(0|(i|=0))&0==(73728&(t|=0)|0)){if(ae(0|o,0|r,0|((t=i-n|0)>>>0<256?t:256)),255<t>>>0){for(r=i-n|0;Ye(e,o,256),255<(t=t+-256|0)>>>0;);t=255&r}Ye(e,o,t)}Me=a}function ve(e){var r=0,i=0;return i=0|se[(r=(e|=0)+74|0)>>0],se[r>>0]=i+255|i,0|(e=8&(r=0|_e[e>>2])?(_e[e>>2]=32|r,-1):(_e[e+8>>2]=0,i=(_e[e+4>>2]=0)|_e[e+44>>2],_e[e+28>>2]=i,_e[e+20>>2]=i,_e[e+16>>2]=i+(0|_e[e+48>>2]),0))}function Se(e,r){r|=0;var i=0,n=0;if(i=0|se[(e|=0)>>0],n=0|se[r>>0],i<<24>>24==0||i<<24>>24!=n<<24>>24)e=n;else{for(;r=r+1|0,i=0|se[(e=e+1|0)>>0],n=0|se[r>>0],i<<24>>24!=0&&i<<24>>24==n<<24>>24;);e=n}return(255&i)-(255&e)|0}function ke(e){var r,i;return 0<(0|(i=(e|=0)+15&-16|0))&(0|(e=(r=0|_e[_>>2])+i|0))<(0|r)|(0|e)<0?(h(),k(12),-1):(0|(_e[_>>2]=e))>(0|b())&&0==(0|A())?(_e[_>>2]=r,k(12),-1):0|r}function ye(e){var r=0,i=0,n=0;if(i=0|_e[(e|=0)>>2],(n=(0|se[i>>0])-48|0)>>>0<10)for(r=0;r=n+(10*r|0)|0,i=i+1|0,_e[e>>2]=i,(n=(0|se[i>>0])-48|0)>>>0<10;);else r=0;return 0|r}function Re(e,r,i,n){if(i|=0,n|=0,!(0==(0|(e|=0))&0==(0|(r|=0))))for(;se[(i=i+-1|0)>>0]=0|de[2122+(15&e)>>0]|n,!(0==(0|(e=0|Le(0|e,0|r,4)))&0==(0|(r=C))););return 0|i}function ge(e){var r=0;return(0|(r=0|se[E+(255&(e|=0))>>0]))<8?0|r:(0|(r=0|se[E+(e>>8&255)>>0]))<8?r+8|0:(0|(r=0|se[E+(e>>16&255)>>0]))<8?r+16|0:24+(0|se[E+(e>>>24)>>0])|0}function Oe(e,r,i,n){i|=0,n|=0;var t=0;(0|_e[(r|=0)+4>>2])==(0|i)&&1!=(0|_e[(t=r+28|0)>>2])&&(_e[t>>2]=n)}function Ce(e,r,i){if(i|=0,!(0==(0|(e|=0))&0==(0|(r|=0))))for(;se[(i=i+-1|0)>>0]=7&e|48,!(0==(0|(e=0|Le(0|e,0|r,3)))&0==(0|(r=C))););return 0|i}function Ne(e,r,i,n){var t,o;return Me=(o=Me)+16|0,x(e|=0,r|=0,i|=0,n|=0,t=0|o),Me=o,0|(C=0|_e[t+4>>2],0|_e[t>>2])}function Pe(e){var r=0;return(e|=0)?0|(1==(0|(e=3&(r=0|_e[e+-4>>2])))?0:(-8&r)-(0==(0|e)?8:4)|0):0}function we(e,r,i){e|=0,r|=0,i|=0;var n,t,o,a,u;return Me=(n=Me)+16|0,_e[(t=n)>>2]=i,i=0|(o=e,a=r,u=t,0|function(e,r,i,n){e|=0,r|=0,i|=0,n|=0;var t,o,a=0,u=0,f=0,l=0,c=0;for(Me=(o=Me)+128|0,a=o+124|0,f=604,t=(u=c=o)+124|0;_e[u>>2]=_e[f>>2],f=f+4|0,(0|(u=u+4|0))<(0|t););return 2147483646<(r+-1|0)>>>0?r?(r=0|er(),_e[r>>2]=75,r=-1):(e=a,r=1,l=4):l=4,4==(0|l)&&(l=(l=-2-e|0)>>>0<r>>>0?l:r,_e[c+48>>2]=l,_e[(a=c+20|0)>>2]=e,r=(_e[c+44>>2]=e)+l|0,_e[(e=c+16|0)>>2]=r,_e[c+28>>2]=r,r=0|j(c,i,n),l&&(c=0|_e[a>>2],se[c+(((0|c)==(0|_e[e>>2]))<<31>>31)>>0]=0)),Me=o,0|r}(o|=0,2147483647,a|=0,u|=0)),Me=n,0|i}function Ie(e,r,i){return e|=0,r|=0,(0|(i|=0))<32?(C=r<<i|(e&(1<<i)-1<<32-i)>>>32-i,e<<i):(C=e<<i-32,0)}function Le(e,r,i){return e|=0,r|=0,(0|(i|=0))<32?(C=r>>>i,e>>>i|(r&(1<<i)-1)<<32-i):r>>>i-32|(C=0)}function De(e,r){e|=0,r|=0;var i;Me=(i=Me)+16|0,_e[i>>2]=r,j(r=0|_e[26],e,i),function(e,r){var i,n=0,t=0,o=0,a=0;n=i=255&(e|=0),_e[76+(r|=0)>>2],a=3;do{if(3==(0|a)){if((0|n)!=(0|se[r+75>>0])&&(o=0|_e[(t=r+20|0)>>2])>>>0<(0|_e[r+16>>2])>>>0){_e[t>>2]=o+1,se[o>>0]=i;break}n=0|fe(r,e)}}while(0)}(10,r),y()}function Fe(e,r,i,n){return 0|(C=n=(r|=0)-(n|=0)-((e|=0)>>>0<(i|=0)>>>0|0)>>>0,e-i>>>0|0)}function Ue(e){e=+e;var r;return O[d>>3]=e,r=0|_e[d>>2],C=0|_e[d+4>>2],0|r}function He(e,r,i,n){return 0|(C=(r|=0)+(n|=0)+((i=(e|=0)+(i|=0)>>>0)>>>0<e>>>0|0)>>>0,0|i)}function xe(e){var r=0;return 4294963200<(e|=0)>>>0&&(r=0|er(),_e[r>>2]=0-e,e=-1),0|e}function Be(e,r){r|=0;var i,n,t,o,a,u;return i=0|function(e){var r,i=0,n=0;r=e|=0;e:do{if(3&r)for(i=r;;){if(!(0|se[e>>0])){e=i;break e}if(!(3&(i=e=e+1|0))){n=4;break}}else n=4}while(0);if(4==(0|n)){for(;!((-2139062144&(i=0|_e[e>>2])^-2139062144)&i+-16843009);)e=e+4|0;if((255&i)<<24>>24)for(;0!=(0|se[(e=e+1|0)>>0]););}return e-r|0}(e|=0),((0|(n=e,t=1,o=i,a=r,n|=0,a|=0,u=0|te(o|=0,t|=0),o=0==(0|t)?0:o,(0|(_e[a+76>>2],n=0|ie(n,u,a)))!=(0|u)&&(o=(n>>>0)/(t>>>0)|0),0|o))!=(0|i))<<31>>31|0}function Ye(e,r,i){r|=0,i|=0,32&_e[(e|=0)>>2]||ie(r,i,e)}function Xe(e){e|=0;var r;return r=188+(0|rr())|0,0|ce(e,0|_e[r>>2])}function Ke(e,r){return r|=0,0|(e=(e|=0)?0|function(e,r,i){e|=0,r|=0,i|=0;do{if(e){if(r>>>0<128){se[e>>0]=r,e=1;break}if(i=188+(0|rr())|0,!(0|_e[_e[i>>2]>>2])){if(57216==(-128&r|0)){se[e>>0]=r,e=1;break}e=0|er(),_e[e>>2]=84,e=-1;break}if(r>>>0<2048){se[e>>0]=r>>>6|192,se[e+1>>0]=63&r|128,e=2;break}if(r>>>0<55296|57344==(-8192&r|0)){se[e>>0]=r>>>12|224,se[e+1>>0]=r>>>6&63|128,se[e+2>>0]=63&r|128,e=3;break}if((r+-65536|0)>>>0<1048576){se[e>>0]=r>>>18|240,se[e+1>>0]=r>>>12&63|128,se[e+2>>0]=r>>>6&63|128,se[e+3>>0]=63&r|128,e=4;break}e=0|er(),_e[e>>2]=84,e=-1;break}e=1}while(0);return 0|e}(e,r,0):0)}function Ve(e,r,i){return(0|(e|=0))==(0|(r|=0))|0}function Ge(e,r){r|=0;var i;return i=0|ze(0|(e|=0)),0|(0==(0|r)?e:i)}function We(e,r,i,n){return 0|x(e|=0,r|=0,i|=0,n|=0,0)}function ze(e){return(255&(e|=0))<<24|(e>>8&255)<<16|(e>>16&255)<<8|e>>>24|0}function je(e,r,i,n,t,o){T(6)}function Je(e,r,i,n,t){T(1)}function Ze(e){var r;r=e|=0,U(r|=0)}function qe(e,r,i,n){T(7)}function Qe(e,r,i){return T(0),0}function $e(e,r){return+ +function e(r,i){r=+r,i|=0;var n,t,o=0;switch(O[d>>3]=r,2047&(t=0|Le(0|(o=0|_e[d>>2]),0|(n=0|_e[d+4>>2]),52))){case 0:o=0!=r?(r=+e(0x10000000000000000*r,i),(0|_e[i>>2])-64|0):0,_e[i>>2]=o;break;case 2047:break;default:_e[i>>2]=(2047&t)-1022,_e[d>>2]=o,_e[d+4>>2]=-2146435073&n|1071644672,r=+O[d>>3]}return+r}(e=+e,r|=0)}function er(){return 64+(0|rr())|0}function rr(){return 232}function ir(e){}function nr(e){T(2)}function tr(){T(5)}var or=[Qe,z,function(e,r,i){var n,t,o;return e|=0,r|=0,i|=0,Me=(t=Me)+32|0,n=(o=t)+20|0,_e[o>>2]=_e[e+60>>2],_e[o+4>>2]=0,_e[o+8>>2]=r,_e[o+12>>2]=n,_e[o+16>>2]=i,e=(0|xe(0|P(140,0|o)))<0?_e[n>>2]=-1:0|_e[n>>2],Me=t,0|e},function(e,r,i){r|=0,i|=0;var n,t=0;return Me=(n=Me)+32|0,t=n,_e[36+(e|=0)>>2]=1,0==(64&_e[e>>2]|0)&&(_e[t>>2]=_e[e+60>>2],_e[t+4>>2]=21523,_e[t+8>>2]=n+16,0|p(54,0|t))&&(se[e+75>>0]=-1),t=0|z(e,r,i),Me=n,0|t},function(e,r,i){var n,t;return r|=0,i|=0,W(0|(t=0|_e[(n=20+(e|=0)|0)>>2]),0|r,0|(e=i>>>0<(e=(0|_e[e+16>>2])-t|0)>>>0?i:e)),_e[n>>2]=(0|_e[n>>2])+e,0|i},function(e,r,i){i|=0;var n,t,o=0,a=0;if(Me=(t=Me)+64|0,n=t,0|Ve(e|=0,r|=0))r=1;else if(0!=(0|r)&&0!=(0|(a=0|J(r,32,16,0)))){for(o=52+(r=n+4|0)|0;(0|(r=r+4|(_e[r>>2]=0)))<(0|o););_e[n>>2]=a,_e[n+8>>2]=e,_e[n+12>>2]=-1,_e[n+48>>2]=1,_r[3&_e[28+(0|_e[a>>2])>>2]](a,n,0|_e[i>>2],1),r=1==(0|_e[n+24>>2])?(_e[i>>2]=_e[n+16>>2],1):0}else r=0;return Me=t,0|r},Qe,Qe],ar=[Je,function(e,r,i,n,t){e|=0,r|=0,i|=0,n|=0;var o=0;do{if(0|Ve(e,0|_e[r+8>>2]))Oe(0,r,i,n);else if(0|Ve(e,0|_e[r>>2])){if(e=r+32|0,(0|_e[r+16>>2])!=(0|i)&&(0|_e[(o=r+20|0)>>2])!=(0|i)){_e[e>>2]=n,_e[o>>2]=i,_e[(n=r+40|0)>>2]=1+(0|_e[n>>2]),1==(0|_e[r+36>>2])&&2==(0|_e[r+24>>2])&&(se[r+54>>0]=1),_e[r+44>>2]=4;break}1==(0|n)&&(_e[e>>2]=1)}}while(0)},function(e,r,i,n,t){e|=0,r|=0,i|=0,n|=0,t|=0;var o=0,a=0,u=0,f=0;do{if(0|Ve(e,0|_e[r+8>>2]))Oe(0,r,i,n);else{if(o=e+8|0,!(0|Ve(e,0|_e[r>>2]))){u=0|_e[o>>2],ar[3&_e[24+(0|_e[u>>2])>>2]](u,r,i,n,t);break}if(e=r+32|0,(0|_e[r+16>>2])!=(0|i)&&(0|_e[(a=r+20|0)>>2])!=(0|i)){if(_e[e>>2]=n,4==(0|_e[(n=r+44|0)>>2]))break;se[(e=r+52|0)>>0]=0,o=(se[(f=r+53|0)>>0]=0)|_e[o>>2],sr[3&_e[20+(0|_e[o>>2])>>2]](o,r,i,i,1,t),0|se[f>>0]?0|se[e>>0]?e=3:(e=3,u=11):(e=4,u=11),11==(0|u)&&(_e[a>>2]=i,_e[(f=r+40|0)>>2]=1+(0|_e[f>>2]),1==(0|_e[r+36>>2])&&2==(0|_e[r+24>>2])&&(se[r+54>>0]=1)),_e[n>>2]=e;break}1==(0|n)&&(_e[e>>2]=1)}}while(0)},Je],ur=[nr,ir,Ze,ir,ir,Ze,function(e){var r;Me=(r=Me)+16|0,U(e|=0),0|v(0|_e[1285],0)?De(4406,r):Me=r},nr],fr=[function(e){return T(3),0},function(e){var r,i,n;return Me=(r=Me)+16|0,i=r,e=0|(n=0|_e[60+(e|=0)>>2],0|(n|=0)),_e[i>>2]=e,e=0|xe(0|S(6,0|i)),Me=r,0|e}],lr=[function(e,r,i){T(4)}],cr=[tr,function(){var e,r,i,n=0,t=0,o=0,a=0,u=0;Me=(a=Me)+48|0,i=a+32|0,e=a+24|0,u=a+16|0,a=(r=a)+36|0,0|(n=0|(f=0,l=0,Me=(f=Me)+16|0,0|R(5136,2)?(De(4307,f),0):(l=0|m(0|_e[1285]),Me=f,0|l)))&&0|(o=0|_e[n>>2])&&(1126902528==(-256&(t=0|_e[(n=o+48|0)>>2])|0)&1129074247==(0|(n=0|_e[n+4>>2]))||(_e[e>>2]=4168,De(4118,e)),n=1126902529==(0|t)&1129074247==(0|n)?0|_e[o+44>>2]:o+80|0,_e[a>>2]=n,o=0|_e[o>>2],n=0|_e[o+4>>2],0|or[7&_e[16+(0|_e[2])>>2]](8,o,a)?(u=0|_e[a>>2],u=0|fr[1&_e[8+(0|_e[u>>2])>>2]](u),_e[r>>2]=4168,_e[r+4>>2]=n,_e[r+8>>2]=u,De(4032,r)):(_e[u>>2]=4168,_e[u+4>>2]=n,De(4077,u))),De(4156,i);var f,l},function(){var e;Me=(e=Me)+16|0,0|N(5140,6)?De(4356,e):Me=e},tr],sr=[je,function(e,r,i,n,t,o){i|=0,n|=0,t|=0,0|Ve(e|=0,0|_e[8+(r|=0)>>2])&&ue(0,r,i,n,t)},function(e,r,i,n,t,o){i|=0,n|=0,t|=0,o|=0,0|Ve(e|=0,0|_e[8+(r|=0)>>2])?ue(0,r,i,n,t):(e=0|_e[e+8>>2],sr[3&_e[20+(0|_e[e>>2])>>2]](e,r,i,n,t,o))},je],_r=[qe,function(e,r,i,n){i|=0,n|=0,0|Ve(e|=0,0|_e[8+(r|=0)>>2])&&he(0,r,i,n)},function(e,r,i,n){i|=0,n|=0,0|Ve(e|=0,0|_e[8+(r|=0)>>2])?he(0,r,i,n):(e=0|_e[e+8>>2],_r[3&_e[28+(0|_e[e>>2])>>2]](e,r,i,n))},qe];return{stackSave:function(){return 0|Me},_i64Subtract:Fe,_crn_get_bytes_per_block:function(e,r){e|=0,r|=0;var i,n,t,o=0;switch(Me=(t=Me)+576|0,n=t+40|0,i=t+56|0,_e[(o=t)>>2]=40,Q(e,r,o),e=0|_e[4+(r=o+32|0)>>2],0|_e[r>>2]){case 0:if(!e)return Me=t,0|(o=8);e=14;break;case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:e=e?14:13;break;case 9:case 10:if(!e)return Me=t,0|(o=8);e=14;break;default:e=14}return 13==(0|e)?(Me=t,0|(o=16)):14==(0|e)?(_e[n>>2]=866,_e[n+4>>2]=2672,_e[n+8>>2]=1251,we(i,812,n),me(i),Me=t,(o=0)|o):0},setThrew:function(e,r){},dynCall_viii:function(e,r,i,n){r|=0,i|=0,n|=0,lr[0&(e|=0)](0|r,0|i,0|n)},_bitshift64Lshr:Le,_bitshift64Shl:Ie,dynCall_viiii:function(e,r,i,n,t){r|=0,i|=0,n|=0,t|=0,_r[3&(e|=0)](0|r,0|i,0|n,0|t)},setTempRet0:function(e){C=e|=0},_crn_decompress:function(e,r,i,n,t,o){e|=0,r|=0,i|=0,n|=0,t|=0,o|=0;var a,u,f,l,c=0,s=0,_=0,d=0,E=0;switch(Me=(l=Me)+592|0,f=l+56|0,_=l+40|0,a=l+72|0,u=(E=l)+68|0,_e[E>>2]=40,Q(e,r,E),c=(0|_e[E+4>>2])>>>t,s=(0|_e[E+8>>2])>>>t,n=0|_e[4+(E=E+32|0)>>2],0|_e[E>>2]){case 0:n?d=14:E=8;break;case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:d=n?14:13;break;case 9:case 10:n?d=14:E=8;break;default:d=14}13==(0|d)?E=16:14==(0|d)&&(_e[_>>2]=866,_e[_+4>>2]=2672,_e[_+8>>2]=1251,we(a,812,_),me(a),E=0),_e[u>>2]=i,d=0|function(e,r){var i=0,n=0,t=0,o=0,a=0,u=0,f=0,l=0,c=0,s=0,_=0,d=0;if(Me=(_=Me)+528|0,u=(c=_)+16|0,0==(0|(e|=0))|(r|=0)>>>0<62)return Me=_,(d=0)|d;if(!(f=0|oe(300,0)))return Me=_,(d=0)|d;for(_e[f>>2]=519686845,_e[f+4>>2]=0,_e[f+8>>2]=0,l=f+88|0,i=f+136|0,n=f+160|0,t=f+184|0,o=f+208|0,a=f+232|0,_e[(s=f+252|0)>>2]=0,_e[s+4>>2]=0,_e[s+8>>2]=0,se[s+12>>0]=0,_e[(s=f+268|0)>>2]=0,_e[s+4>>2]=0,_e[s+8>>2]=0,se[s+12>>0]=0,_e[(s=f+284|0)>>2]=0,_e[s+4>>2]=0,_e[s+8>>2]=0,se[s+12>>0]=0,d=(s=l)+44|0;((_e[s>>2]=0)|(s=s+4|0))<(0|d););return se[l+44>>0]=0,_e[i>>2]=0,_e[i+4>>2]=0,_e[i+8>>2]=0,_e[i+12>>2]=0,_e[i+16>>2]=0,se[i+20>>0]=0,_e[n>>2]=0,_e[n+4>>2]=0,_e[n+8>>2]=0,_e[n+12>>2]=0,_e[n+16>>2]=0,se[n+20>>0]=0,_e[t>>2]=0,_e[t+4>>2]=0,_e[t+8>>2]=0,_e[t+12>>2]=0,_e[t+16>>2]=0,se[t+20>>0]=0,_e[o>>2]=0,_e[o+4>>2]=0,_e[o+8>>2]=0,_e[o+12>>2]=0,_e[o+16>>2]=0,se[o+20>>0]=0,_e[a>>2]=0,_e[a+4>>2]=0,_e[a+8>>2]=0,_e[a+12>>2]=0,(se[a+16>>0]=0)|function(e,r,i){e|=0;var n=0,t=0;if(!(0==(0|(r|=0))|(i|=0)>>>0<74||18552!=((0|de[r>>0])<<8|0|de[r+1>>0]|0))&&74<=((0|de[r+2>>0])<<8|0|de[r+3>>0])>>>0&&((0|de[r+7>>0])<<16|(0|de[r+6>>0])<<24|(0|de[r+8>>0])<<8|0|de[r+9>>0])>>>0<=i>>>0){if(_e[(n=e+88|0)>>2]=r,_e[e+4>>2]=r,_e[e+8>>2]=i,!(0|function(e){var r=0,i=0,n=0,t=0;if(t=92+(e|=0)|0,i=0|_e[(n=e+88|0)>>2],r=(0|_e[e+4>>2])+((0|de[i+68>>0])<<8|(0|de[i+67>>0])<<16|0|de[i+69>>0])|0,!(i=(0|de[i+65>>0])<<8|0|de[i+66>>0]))return(t=0)|t;if(_e[t>>2]=r,_e[e+96>>2]=r,_e[e+104>>2]=i,_e[e+100>>2]=r+i,_e[e+108>>2]=0,!((_e[e+112>>2]=0)|K(t,e+116|0)))return(t=0)|t;r=0|_e[n>>2];do{if((0|de[r+39>>0])<<8|0|de[r+40>>0]){if(!(0|K(t,e+140|0)))return(t=0)|t;if(0|K(t,e+188|0)){r=0|_e[n>>2];break}return(t=0)|t}if(!((0|de[r+55>>0])<<8|0|de[r+56>>0]))return(t=0)|t}while(0);if((0|de[r+55>>0])<<8|0|de[r+56>>0]|0){if(!(0|K(t,e+164|0)))return(t=0)|t;if(!(0|K(t,e+212|0)))return(t=0)|t}return 0|(t=1)}(e)))return(t=0)|t;if(r=0|_e[n>>2],(0|de[r+39>>0])<<8|0|de[r+40>>0]?0|function(e){var r=0,i=0,n=0,t=0,o=0,a=0,u=0,f=0,l=0,c=0,s=0,_=0,d=0;if(Me=(d=Me)+576|0,t=(a=d)+64|0,_=d+16|0,r=0|_e[(n=88+(e|=0)|0)>>2],s=(0|de[r+39>>0])<<8|0|de[r+40>>0],l=e+236|0,(0|(i=0|_e[(o=e+240|0)>>2]))!=(0|s)){if(i>>>0<=s>>>0){do{if((0|_e[e+244>>2])>>>0<s>>>0){if(0|V(l,s,(i+1|0)==(0|s),4,0)){r=0|_e[o>>2];break}return se[e+248>>0]=1,Me=d,(_=0)|_}r=i}while(0);ae((0|_e[l>>2])+(r<<2)|0,0,s-r<<2|0),r=0|_e[n>>2]}_e[o>>2]=s}if(c=e+92|0,i=(0|_e[e+4>>2])+((0|de[r+34>>0])<<8|(0|de[r+33>>0])<<16|0|de[r+35>>0])|0,!(r=(0|de[r+37>>0])<<8|(0|de[r+36>>0])<<16|0|de[r+38>>0]))return Me=d,(_=0)|_;if(_e[c>>2]=i,_e[e+96>>2]=i,_e[e+104>>2]=r,_e[e+100>>2]=i+r,_e[e+108>>2]=0,_e[e+112>>2]=0,u=_+20|0,_e[_>>2]=0,_e[_+4>>2]=0,_e[_+8>>2]=0,_e[_+12>>2]=0,se[_+16>>0]=0,f=_+24|0,_e[_+44>>2]=0,_e[u>>2]=0,_e[u+4>>2]=0,_e[u+8>>2]=0,_e[u+12>>2]=0,_e[u+16>>2]=0,(se[u+20>>0]=0)|K(c,_)&&0|K(c,f))if(0|_e[o>>2]||(_e[a>>2]=866,_e[a+4>>2]=910,_e[a+8>>2]=1497,we(t,812,a),me(t)),s)for(i=(u=a=0)|_e[l>>2],o=t=r=e=n=0;;){if(a=(0|Te(c,_))+a&31,o=(0|Te(c,f))+o&63,t=(0|Te(c,_))+t&31,r=(0|Te(c,_))+r|0,e=(0|Te(c,f))+e&63,n=(0|Te(c,_))+n&31,_e[i>>2]=o<<5|a<<11|t|r<<27|e<<21|n<<16,s>>>0<=(u=u+1|0)>>>0){r=1;break}i=i+4|0,r&=31}else r=1;else r=0;return ne(_+24|0),ne(_),Me=d,0|(_=r)}(e)&&0|function(e){var r,i,n,t,o,a,u=0,f=0,l=0,c=0,s=0,_=0,d=0,E=0,M=0,T=0,A=0,b=0,h=0,m=0,p=0,v=0,S=0,k=0,y=0,R=0,g=0,O=0,C=0,N=0,P=0,w=0,I=0,L=0,D=0,F=0,U=0,H=0,x=0;if(Me=(a=Me)+1008|0,s=(_=a)+496|0,y=a+472|0,n=a+276|0,t=a+80|0,o=a+16|0,f=0|_e[88+(e|=0)>>2],r=(0|de[f+47>>0])<<8|0|de[f+48>>0],i=e+92|0,u=(0|_e[e+4>>2])+((0|de[f+42>>0])<<8|(0|de[f+41>>0])<<16|0|de[f+43>>0])|0,!(f=(0|de[f+45>>0])<<8|(0|de[f+44>>0])<<16|0|de[f+46>>0]))return Me=a,(y=0)|y;if(_e[i>>2]=u,_e[e+96>>2]=u,_e[e+104>>2]=f,_e[e+100>>2]=u+f,_e[e+108>>2]=0,_e[e+112>>2]=0,_e[y+20>>2]=0,_e[y>>2]=0,_e[y+4>>2]=0,_e[y+8>>2]=0,_e[y+12>>2]=0,(se[y+16>>0]=0)|K(i,y)){for(u=0,l=f=-3;_e[n+(u<<2)>>2]=l,_e[t+(u<<2)>>2]=f,c=2<(0|l),49!=(0|(u=u+1|0));)f=(1&c)+f|0,l=c?-3:l+1|0;for(f=(u=o)+64|0;(0|(u=u+4|(_e[u>>2]=0)))<(0|f););l=e+252|0,u=0|_e[(f=e+256|0)>>2];e:do{if((0|u)==(0|r))d=13;else{if(u>>>0<=r>>>0){do{if((0|_e[e+260>>2])>>>0<r>>>0){if(0|V(l,r,(u+1|0)==(0|r),4,0)){u=0|_e[f>>2];break}se[e+264>>0]=1,u=0;break e}}while(0);ae((0|_e[l>>2])+(u<<2)|0,0,r-u<<2|0)}_e[f>>2]=r,d=13}}while(0);do{if(13==(0|d)){if(!r){_e[_>>2]=866,_e[_+4>>2]=910,_e[_+8>>2]=1497,we(s,812,_),me(s),u=1;break}for(e=o+4|0,s=o+8|0,_=o+12|0,d=o+16|0,E=o+20|0,M=o+24|0,T=o+28|0,A=o+32|0,b=o+36|0,h=o+40|0,m=o+44|0,p=o+48|0,v=o+52|0,S=o+56|0,k=o+60|0,u=(c=0)|_e[l>>2],f=0|_e[e>>2],l=0|_e[o>>2];H=0|Te(i,y),l=l+(0|_e[n+(H<<2)>>2])&3,f=f+(0|_e[t+(H<<2)>>2])&3,H=0|Te(i,y),x=(0|_e[s>>2])+(0|_e[n+(H<<2)>>2])&3,_e[s>>2]=x,H=(0|_e[_>>2])+(0|_e[t+(H<<2)>>2])&3,_e[_>>2]=H,F=0|Te(i,y),U=(0|_e[d>>2])+(0|_e[n+(F<<2)>>2])&3,_e[d>>2]=U,F=(0|_e[E>>2])+(0|_e[t+(F<<2)>>2])&3,_e[E>>2]=F,L=0|Te(i,y),D=(0|_e[M>>2])+(0|_e[n+(L<<2)>>2])&3,_e[M>>2]=D,L=(0|_e[T>>2])+(0|_e[t+(L<<2)>>2])&3,_e[T>>2]=L,w=0|Te(i,y),I=(0|_e[A>>2])+(0|_e[n+(w<<2)>>2])&3,_e[A>>2]=I,w=(0|_e[b>>2])+(0|_e[t+(w<<2)>>2])&3,_e[b>>2]=w,N=0|Te(i,y),P=(0|_e[h>>2])+(0|_e[n+(N<<2)>>2])&3,_e[h>>2]=P,N=(0|_e[m>>2])+(0|_e[t+(N<<2)>>2])&3,_e[m>>2]=N,O=0|Te(i,y),C=(0|_e[p>>2])+(0|_e[n+(O<<2)>>2])&3,_e[p>>2]=C,O=(0|_e[v>>2])+(0|_e[t+(O<<2)>>2])&3,_e[v>>2]=O,R=0|Te(i,y),g=(0|_e[S>>2])+(0|_e[n+(R<<2)>>2])&3,_e[S>>2]=g,R=(0|_e[k>>2])+(0|_e[t+(R<<2)>>2])&3,_e[k>>2]=R,_e[u>>2]=(0|de[1441+f>>0])<<2|0|de[1441+l>>0]|(0|de[1441+x>>0])<<4|(0|de[1441+H>>0])<<6|(0|de[1441+U>>0])<<8|(0|de[1441+F>>0])<<10|(0|de[1441+D>>0])<<12|(0|de[1441+L>>0])<<14|(0|de[1441+I>>0])<<16|(0|de[1441+w>>0])<<18|(0|de[1441+P>>0])<<20|(0|de[1441+N>>0])<<22|(0|de[1441+C>>0])<<24|(0|de[1441+O>>0])<<26|(0|de[1441+g>>0])<<28|(0|de[1441+R>>0])<<30,!(r>>>0<=(c=c+1|0)>>>0);)u=u+4|0;_e[o>>2]=l,_e[e>>2]=f,u=1}}while(0)}else u=0;return ne(y),Me=a,0|(x=u)}(e)&&(r=0|_e[n>>2],t=11):t=11,11==(0|t)){if(!((0|de[r+55>>0])<<8|0|de[r+56>>0]))return 0|(t=1);if(0|function(e){var r=0,i=0,n=0,t=0,o=0,a=0,u=0,f=0,l=0;if(Me=(l=Me)+560|0,n=(t=l)+40|0,f=l+16|0,i=0|_e[88+(e|=0)>>2],a=(0|de[i+55>>0])<<8|0|de[i+56>>0],u=e+92|0,r=(0|_e[e+4>>2])+((0|de[i+50>>0])<<8|(0|de[i+49>>0])<<16|0|de[i+51>>0])|0,!(i=(0|de[i+53>>0])<<8|(0|de[i+52>>0])<<16|0|de[i+54>>0]))return Me=l,(f=0)|f;_e[u>>2]=r,_e[e+96>>2]=r,_e[e+104>>2]=i,_e[e+100>>2]=r+i,_e[e+108>>2]=0,_e[e+112>>2]=0,_e[f+20>>2]=0,_e[f>>2]=0,_e[f+4>>2]=0,_e[f+8>>2]=0,_e[f+12>>2]=0,se[f+16>>0]=0;e:do{if(0|K(u,f)){if(o=e+268|0,(0|(r=0|_e[(i=e+272|0)>>2]))!=(0|a)){if(r>>>0<=a>>>0){do{if((0|_e[e+276>>2])>>>0<a>>>0){if(0|V(o,a,(r+1|0)==(0|a),2,0)){r=0|_e[i>>2];break}se[e+280>>0]=1,r=0;break e}}while(0);ae((0|_e[o>>2])+(r<<1)|0,0,a-r<<1|0)}_e[i>>2]=a}if(!a){_e[t>>2]=866,_e[t+4>>2]=910,_e[t+8>>2]=1497,we(n,812,t),me(n),r=1;break}for(r=(n=e=i=0)|_e[o>>2];;){if(o=0|Te(u,f),n=o+n&255,e=(0|Te(u,f))+e&255,X[r>>1]=e<<8|n,a>>>0<=(i=i+1|0)>>>0){r=1;break}r=r+2|0}}else r=0}while(0);return ne(f),Me=l,0|(f=r)}(e)&&0|function(e){var r,i,n,t,o,a,u=0,f=0,l=0,c=0,s=0,_=0,d=0,E=0,M=0,T=0,A=0,b=0,h=0,m=0,p=0,v=0,S=0,k=0,y=0,R=0,g=0,O=0,C=0,N=0,P=0,w=0,I=0,L=0,D=0,F=0,U=0,H=0,x=0,B=0,Y=0;if(Me=(a=Me)+2416|0,s=(_=a)+1904|0,x=a+1880|0,n=a+980|0,t=a+80|0,o=a+16|0,f=0|_e[88+(e|=0)>>2],r=(0|de[f+63>>0])<<8|0|de[f+64>>0],i=e+92|0,u=(0|_e[e+4>>2])+((0|de[f+58>>0])<<8|(0|de[f+57>>0])<<16|0|de[f+59>>0])|0,!(f=(0|de[f+61>>0])<<8|(0|de[f+60>>0])<<16|0|de[f+62>>0]))return Me=a,(x=0)|x;if(_e[i>>2]=u,_e[e+96>>2]=u,_e[e+104>>2]=f,_e[e+100>>2]=u+f,_e[e+108>>2]=0,_e[e+112>>2]=0,_e[x+20>>2]=0,_e[x>>2]=0,_e[x+4>>2]=0,_e[x+8>>2]=0,_e[x+12>>2]=0,(se[x+16>>0]=0)|K(i,x)){for(u=0,l=f=-7;_e[n+(u<<2)>>2]=l,_e[t+(u<<2)>>2]=f,c=6<(0|l),225!=(0|(u=u+1|0));)f=(1&c)+f|0,l=c?-7:l+1|0;for(f=(u=o)+64|0;(0|(u=u+4|(_e[u>>2]=0)))<(0|f););c=e+284|0,f=3*r|0,u=0|_e[(l=e+288|0)>>2];e:do{if((0|u)==(0|f))d=13;else{if(u>>>0<=f>>>0){do{if((0|_e[e+292>>2])>>>0<f>>>0){if(0|V(c,f,(u+1|0)==(0|f),2,0)){u=0|_e[l>>2];break}se[e+296>>0]=1,u=0;break e}}while(0);ae((0|_e[c>>2])+(u<<1)|0,0,f-u<<1|0)}_e[l>>2]=f,d=13}}while(0);do{if(13==(0|d)){if(!r){_e[_>>2]=866,_e[_+4>>2]=910,_e[_+8>>2]=1497,we(s,812,_),me(s),u=1;break}for(k=o+4|0,y=o+8|0,R=o+12|0,g=o+16|0,O=o+20|0,C=o+24|0,N=o+28|0,P=o+32|0,w=o+36|0,I=o+40|0,L=o+44|0,D=o+48|0,F=o+52|0,U=o+56|0,H=o+60|0,u=(S=0)|_e[c>>2],f=0|_e[o>>2],l=0|_e[k>>2],c=0|_e[y>>2],e=0|_e[R>>2],s=0|_e[g>>2],_=0|_e[O>>2],d=0|_e[C>>2],E=0|_e[N>>2],M=0|_e[P>>2],T=0|_e[w>>2],A=0|_e[I>>2],b=0|_e[L>>2],v=p=m=h=0;Y=0|Te(i,x),f=f+(0|_e[n+(Y<<2)>>2])&7,l=l+(0|_e[t+(Y<<2)>>2])&7,Y=0|Te(i,x),c=c+(0|_e[n+(Y<<2)>>2])&7,e=e+(0|_e[t+(Y<<2)>>2])&7,Y=0|Te(i,x),s=s+(0|_e[n+(Y<<2)>>2])&7,_=_+(0|_e[t+(Y<<2)>>2])&7,Y=0|Te(i,x),d=d+(0|_e[n+(Y<<2)>>2])&7,E=E+(0|_e[t+(Y<<2)>>2])&7,Y=0|Te(i,x),M=M+(0|_e[n+(Y<<2)>>2])&7,T=T+(0|_e[t+(Y<<2)>>2])&7,Y=0|Te(i,x),A=A+(0|_e[n+(Y<<2)>>2])&7,b=b+(0|_e[t+(Y<<2)>>2])&7,Y=0|Te(i,x),h=h+(0|_e[n+(Y<<2)>>2])&7,m=m+(0|_e[t+(Y<<2)>>2])&7,Y=0|Te(i,x),p=p+(0|_e[n+(Y<<2)>>2])&7,v=v+(0|_e[t+(Y<<2)>>2])&7,Y=0|de[1445+_>>0],X[u>>1]=(0|de[1445+l>>0])<<3|0|de[1445+f>>0]|(0|de[1445+c>>0])<<6|(0|de[1445+e>>0])<<9|(0|de[1445+s>>0])<<12|Y<<15,B=0|de[1445+A>>0],X[u+2>>1]=(0|de[1445+d>>0])<<2|Y>>>1|(0|de[1445+E>>0])<<5|(0|de[1445+M>>0])<<8|(0|de[1445+T>>0])<<11|B<<14,X[u+4>>1]=(0|de[1445+b>>0])<<1|B>>>2|(0|de[1445+h>>0])<<4|(0|de[1445+m>>0])<<7|(0|de[1445+p>>0])<<10|(0|de[1445+v>>0])<<13,!(r>>>0<=(S=S+1|0)>>>0);)u=u+6|0;_e[o>>2]=f,_e[k>>2]=l,_e[y>>2]=c,_e[R>>2]=e,_e[g>>2]=s,_e[O>>2]=_,_e[C>>2]=d,_e[N>>2]=E,_e[P>>2]=M,_e[w>>2]=T,_e[I>>2]=A,_e[L>>2]=b,_e[D>>2]=h,_e[F>>2]=m,_e[U>>2]=p,_e[H>>2]=v,u=1}}while(0)}else u=0;return ne(x),Me=a,0|(Y=u)}(e))return 0|(t=1)}return(t=0)|t}return _e[e+88>>2]=0,(t=0)|t}(f,e,r)?(Me=_,0|(d=f)):(B(f),7&f?(_e[c>>2]=866,_e[c+4>>2]=2506,_e[c+8>>2]=1232,we(u,812,c),me(u)):le(f,0,0,1,0),Me=_,(d=0)|d)}(e,r),r=o+t|0;do{if(t>>>0<r>>>0){if(!d){for(n=i;n=n+(0|te(0|te((c+3|0)>>>2,E),(s+3|0)>>>2))|0,(0|(t=t+1|0))!=(0|r);)s>>>=1,c>>>=1;_e[u>>2]=n;break}for(e=s,n=i;s=0|te((c+3|0)>>>2,E),15<t>>>0|(_=0|te(s,(e+3|0)>>>2))>>>0<8||519686845!=(0|_e[d>>2])||(re(d,u,_,s,t),n=0|_e[u>>2]),n=n+_|0,_e[u>>2]=n,(0|(t=t+1|0))!=(0|r);)e>>>=1,c>>>=1}}while(0);if(d){if(519686845==(0|_e[d>>2]))return B(d),7&d?(_e[f>>2]=866,_e[f+4>>2]=2506,_e[f+8>>2]=1232,we(a,812,f),me(a)):le(d,0,0,1,0),void(Me=l);Me=l}else Me=l},_memset:ae,_sbrk:ke,_memcpy:W,stackAlloc:function(e){var r;return Me=15+(Me=(r=Me)+(e|=0)|0)&-16,0|r},_crn_get_height:function(e,r){var i,n;return e|=0,r|=0,Me=(n=Me)+48|0,_e[(i=n)>>2]=40,Q(e,r,i),Me=n,0|_e[i+8>>2]},dynCall_vi:function(e,r){r|=0,ur[7&(e|=0)](0|r)},getTempRet0:function(){return 0|C},_crn_get_levels:function(e,r){var i,n;return e|=0,r|=0,Me=(n=Me)+48|0,_e[(i=n)>>2]=40,Q(e,r,i),Me=n,0|_e[i+12>>2]},_crn_get_uncompressed_size:function(e,r,i){e|=0,r|=0,i|=0;var n,t,o,a,u=0,f=0;switch(Me=(a=Me)+576|0,o=a+40|0,t=a+56|0,_e[(f=a)>>2]=40,Q(e,r,f),n=(3+((0|_e[f+4>>2])>>>i)|0)>>>2,r=(3+((0|_e[f+8>>2])>>>i)|0)>>>2,e=0|_e[4+(i=f+32|0)>>2],0|_e[i>>2]){case 0:e?u=14:e=8;break;case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:u=e?14:13;break;case 9:case 10:e?u=14:e=8;break;default:u=14}return 13==(0|u)?e=16:14==(0|u)&&(_e[o>>2]=866,_e[o+4>>2]=2672,_e[o+8>>2]=1251,we(t,812,o),me(t),e=0),f=0|te(0|te(r,n),e),Me=a,0|f},_i64Add:He,dynCall_iiii:function(e,r,i,n){return r|=0,i|=0,n|=0,0|or[7&(e|=0)](0|r,0|i,0|n)},_emscripten_get_global_libc:function(){return 5072},dynCall_ii:function(e,r){return r|=0,0|fr[1&(e|=0)](0|r)},___udivdi3:We,_llvm_bswap_i32:ze,dynCall_viiiii:function(e,r,i,n,t,o){r|=0,i|=0,n|=0,t|=0,o|=0,ar[3&(e|=0)](0|r,0|i,0|n,0|t,0|o)},___cxa_can_catch:function(e,r,i){var n,t;return e|=0,r|=0,i|=0,Me=(t=Me)+16|0,_e[(n=t)>>2]=_e[i>>2],(e=0|or[7&_e[16+(0|_e[e>>2])>>2]](e,r,n))&&(_e[i>>2]=_e[n>>2]),Me=t,1&e|0},_free:U,runPostSets:function(){},dynCall_viiiiii:function(e,r,i,n,t,o,a){r|=0,i|=0,n|=0,t|=0,o|=0,a|=0,sr[3&(e|=0)](0|r,0|i,0|n,0|t,0|o,0|a)},establishStackSpace:function(e,r){Me=e|=0,r|=0},___uremdi3:Ne,___cxa_is_pointer_type:function(e){return 1&(e=(e|=0)?0!=(0|J(e,32,88,0)):0)|0},stackRestore:function(e){Me=e|=0},_malloc:I,_emscripten_replace_memory:function(e){return!(16777215&s(e)||s(e)<=16777215||2147483648<s(e)||(se=new n(e),X=new t(e),_e=new o(e),de=new a(e),Ee=new u(e),new f(e),new l(e),O=new c(e),i=e,0))},dynCall_v:function(e){cr[3&(e|=0)]()},_crn_get_width:function(e,r){var i,n;return e|=0,r|=0,Me=(n=Me)+48|0,_e[(i=n)>>2]=40,Q(e,r,i),Me=n,0|_e[i+4>>2]},_crn_get_dxt_format:function(e,r){var i,n;return e|=0,r|=0,Me=(n=Me)+48|0,_e[(i=n)>>2]=40,Q(e,r,i),Me=n,0|_e[i+32>>2]}}}(Module.asmGlobalArg,Module.asmLibraryArg,buffer),stackSave=Module.stackSave=asm.stackSave,getTempRet0=Module.getTempRet0=asm.getTempRet0,_memset=Module._memset=asm._memset,setThrew=Module.setThrew=asm.setThrew,_bitshift64Lshr=Module._bitshift64Lshr=asm._bitshift64Lshr,_bitshift64Shl=Module._bitshift64Shl=asm._bitshift64Shl,setTempRet0=Module.setTempRet0=asm.setTempRet0,_crn_decompress=Module._crn_decompress=asm._crn_decompress,_crn_get_bytes_per_block=Module._crn_get_bytes_per_block=asm._crn_get_bytes_per_block,_sbrk=Module._sbrk=asm._sbrk,_memcpy=Module._memcpy=asm._memcpy,stackAlloc=Module.stackAlloc=asm.stackAlloc,_crn_get_height=Module._crn_get_height=asm._crn_get_height,_i64Subtract=Module._i64Subtract=asm._i64Subtract,_crn_get_levels=Module._crn_get_levels=asm._crn_get_levels,_crn_get_uncompressed_size=Module._crn_get_uncompressed_size=asm._crn_get_uncompressed_size,_i64Add=Module._i64Add=asm._i64Add,_emscripten_get_global_libc=Module._emscripten_get_global_libc=asm._emscripten_get_global_libc,___udivdi3=Module.___udivdi3=asm.___udivdi3,_llvm_bswap_i32=Module._llvm_bswap_i32=asm._llvm_bswap_i32,___cxa_can_catch=Module.___cxa_can_catch=asm.___cxa_can_catch,_free=Module._free=asm._free,runPostSets=Module.runPostSets=asm.runPostSets,establishStackSpace=Module.establishStackSpace=asm.establishStackSpace,___uremdi3=Module.___uremdi3=asm.___uremdi3,___cxa_is_pointer_type=Module.___cxa_is_pointer_type=asm.___cxa_is_pointer_type,stackRestore=Module.stackRestore=asm.stackRestore,_malloc=Module._malloc=asm._malloc,_emscripten_replace_memory=Module._emscripten_replace_memory=asm._emscripten_replace_memory,_crn_get_width=Module._crn_get_width=asm._crn_get_width,_crn_get_dxt_format=Module._crn_get_dxt_format=asm._crn_get_dxt_format,dynCall_iiii=Module.dynCall_iiii=asm.dynCall_iiii,dynCall_viiiii=Module.dynCall_viiiii=asm.dynCall_viiiii,dynCall_vi=Module.dynCall_vi=asm.dynCall_vi,dynCall_ii=Module.dynCall_ii=asm.dynCall_ii,dynCall_viii=Module.dynCall_viii=asm.dynCall_viii,dynCall_v=Module.dynCall_v=asm.dynCall_v,dynCall_viiiiii=Module.dynCall_viiiiii=asm.dynCall_viiiiii,dynCall_viiii=Module.dynCall_viiii=asm.dynCall_viiii,initialStackTop;function ExitStatus(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function run(e){function r(){Module.calledRun||(Module.calledRun=!0,ABORT||(ensureInitRuntime(),preMain(),Module.onRuntimeInitialized&&Module.onRuntimeInitialized(),Module._main&&shouldRunNow&&Module.callMain(e),postRun()))}e=e||Module.arguments,0<runDependencies||(preRun(),0<runDependencies||Module.calledRun||(Module.setStatus?(Module.setStatus("Running..."),setTimeout(function(){setTimeout(function(){Module.setStatus("")},1),r()},1)):r()))}function exit(e,r){r&&Module.noExitRuntime||(Module.noExitRuntime||(ABORT=!0,STACKTOP=initialStackTop,exitRuntime(),Module.onExit&&Module.onExit(e)),ENVIRONMENT_IS_NODE&&process.exit(e),Module.quit(e,new ExitStatus(e)))}Runtime.stackAlloc=Module.stackAlloc,Runtime.stackSave=Module.stackSave,Runtime.stackRestore=Module.stackRestore,Runtime.establishStackSpace=Module.establishStackSpace,Runtime.setTempRet0=Module.setTempRet0,Runtime.getTempRet0=Module.getTempRet0,Module.asm=asm,ExitStatus.prototype=new Error,ExitStatus.prototype.constructor=ExitStatus,dependenciesFulfilled=function e(){Module.calledRun||run(),Module.calledRun||(dependenciesFulfilled=e)},Module.callMain=Module.callMain=function(e){e=e||[],ensureInitRuntime();var r=e.length+1;function i(){for(var e=0;e<3;e++)n.push(0)}var n=[allocate(intArrayFromString(Module.thisProgram),"i8",ALLOC_NORMAL)];i();for(var t=0;t<r-1;t+=1)n.push(allocate(intArrayFromString(e[t]),"i8",ALLOC_NORMAL)),i();n.push(0),n=allocate(n,"i32",ALLOC_NORMAL);try{exit(Module._main(r,n,0),!0)}catch(e){if(e instanceof ExitStatus)return;if("SimulateInfiniteLoop"==e)return void(Module.noExitRuntime=!0);var o=e;e&&"object"==typeof e&&e.stack&&(o=[e,e.stack]),Module.printErr("exception thrown: "+o),Module.quit(1,e)}},Module.run=Module.run=run,Module.exit=Module.exit=exit;var abortDecorators=[];function abort(r){Module.onAbort&&Module.onAbort(r),r=void 0!==r?(Module.print(r),Module.printErr(r),JSON.stringify(r)):"",ABORT=!0;var i="abort("+r+") at "+stackTrace()+"\nIf this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.";throw abortDecorators&&abortDecorators.forEach(function(e){i=e(i,r)}),i}if(Module.abort=Module.abort=abort,Module.preInit)for("function"==typeof Module.preInit&&(Module.preInit=[Module.preInit]);0<Module.preInit.length;)Module.preInit.pop()();var shouldRunNow=!0;Module.noInitialRun&&(shouldRunNow=!1),Module.noExitRuntime=!0,run();var crunch=Module,CRN_FORMAT={cCRNFmtInvalid:-1,cCRNFmtDXT1:0,cCRNFmtDXT3:1,cCRNFmtDXT5:2},DXT_FORMAT_MAP={},dst,dxtData;DXT_FORMAT_MAP[CRN_FORMAT.cCRNFmtDXT1]=PixelFormat.PixelFormat.RGB_DXT1,DXT_FORMAT_MAP[CRN_FORMAT.cCRNFmtDXT3]=PixelFormat.PixelFormat.RGBA_DXT3,DXT_FORMAT_MAP[CRN_FORMAT.cCRNFmtDXT5]=PixelFormat.PixelFormat.RGBA_DXT5;var cachedDstSize=0;function arrayBufferCopy(e,r,i,n){var t,o=i/4,a=n%4,u=new Uint32Array(e.buffer,0,(n-a)/4),f=new Uint32Array(r.buffer);for(t=0;t<u.length;t++)f[o+t]=u[t];for(t=n-a;t<n;t++)r[i+t]=e[t]}function transcodeCRNToDXT(e,r){var i=e.byteLength,n=new Uint8Array(e),t=crunch._malloc(i);arrayBufferCopy(n,crunch.HEAPU8,t,i);var o=crunch._crn_get_dxt_format(t,i),a=DXT_FORMAT_MAP[o];if(!when.defined(a))throw new RuntimeError.RuntimeError("Unsupported compressed format.");var u,f=crunch._crn_get_levels(t,i),l=crunch._crn_get_width(t,i),c=crunch._crn_get_height(t,i),s=0;for(u=0;u<f;++u)s+=PixelFormat.PixelFormat.compressedTextureSizeInBytes(a,l>>u,c>>u);cachedDstSize<s&&(when.defined(dst)&&crunch._free(dst),dst=crunch._malloc(s),dxtData=new Uint8Array(crunch.HEAPU8.buffer,dst,s),cachedDstSize=s),crunch._crn_decompress(t,i,dst,s,0,f),crunch._free(t);var _=PixelFormat.PixelFormat.compressedTextureSizeInBytes(a,l,c),d=dxtData.subarray(0,_),E=new Uint8Array(_);return E.set(d,0),r.push(E.buffer),new CompressedTextureBuffer.CompressedTextureBuffer(a,l,c,E)}var transcodeCRNToDXTprevious=createTaskProcessorWorker(transcodeCRNToDXT);return transcodeCRNToDXTprevious});