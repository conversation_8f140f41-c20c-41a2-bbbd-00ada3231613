<template>
  <div class="chart-container">
    <select v-model="selectType" @change="switchData" class="selector">
      <option
        v-for="item in dateOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value">
      </option>
    </select>
    <div ref="chart" :style="{ width: '100%', height: '50vh' }"></div>
  </div>

</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'DetailsCharts',
  props: {
    text:String,
    Yname:String,
    pathUrl:String,
  },
  data() {
    return {
      chart: null,
      chartOptions: {
          title: {
            text: this.text,
          },
          tooltip: {},
          xAxis: {
            data: []
          },
          yAxis: {},
          series: [
            {
            
              name: this.Yname,
              type: 'line',
              data: [],
              smooth: true,
            },
          ],
          legend: {
            orient: 'horizontal',
            left: 'center'
          },
        },
      selectType:1,
      dateOptions:[
        {
          label:'按日',
          value:2
        },
        {
          label:'按月',
          value:1
        },
        {
          label:'按年',
          value:0
        }
      ],
    };
  },
  mounted() {
    this.initChart();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      window.removeEventListener('resize', this.handleResize);
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart);
      this.chart.setOption(this.chartOptions);
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    async getChartData() {
      let res = await this.$http.get(
        `${this.pathUrl}?sectionId=${this.$store.state.tree.sectionId||''}&keyword=${this.selectType}`
      );
      this.chartOptions.xAxis.data = res?.result.exceptionTotalStat.map((item)=>item.date)||[]
      this.chartOptions.series[0].data = res?.result.exceptionTotalStat.map((item)=>item.num?item.num:(item.totalChargeCapacity?item.totalChargeCapacity:item.totalDischargeCapacity))||[]
      this.chart.setOption(this.chartOptions);
    },
    switchData(){
      this.getChartData()
    }
  },
  watch: {
    // options: {
    //   handler(newOptions) {
    //     this.chartOptions = {
    //       ...this.chartOptions,
    //       ...newOptions
    //     }//更新chartOptions
    //     if (this.chart) {
    //       this.chart.setOption(this.chartOptions);
    //     }
    //   },
    //   deep: true
    // },
    "$store.state.tree.sectionId": {
      handler: function () {
        this.getChartData();
      },
      deep: true,
      immediate: true,
    },
  }
};
</script>

<style lang="scss" scoped>
/* 你可以在这里添加一些样式，例如设置图表容器的最小高度和宽度 */
.chart-container{
  display: flex;
  flex-direction: column;
  // justify-content: flex-end;
  // position: absolute;
  .selector{
    position: relative;
    width: 7%;
    display: flex;
    border: 1px solid #d0d0d0;
    border-radius: 5px;
    padding:1vh;
    left: 80%;
    /* height: 2vh; */
  }
}

</style>