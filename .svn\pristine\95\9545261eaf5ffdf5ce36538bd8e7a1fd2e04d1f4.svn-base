<template>
  <div class="headerContainer pull-container">
    <div class="flex-wrap pull-container">
      <div class="logo-con">
        <img src="@/assets/images/bg.png" alt="" />
        <svg-icon icon-class="logo" class="icon-logo" />
        <span class="logo-title">新能源管控平台</span>
      </div>
      <nav class="nav-content">
        <el-menu :default-active="activeIndex" class="nav_list" mode="horizontal" background-color="#4570e7"
          text-color="#fff">
          <el-menu-item index="2">
            <router-link to="/devices/monitor">
              <svg-icon icon-class="s-device" class="icon" />
              <span>施工装备监控</span>
            </router-link>
          </el-menu-item>
          <el-menu-item index="1">
            <router-link to="/devices/statistics">
              <svg-icon icon-class="s-statics" class="icon" />
              <span>综合统计</span>
            </router-link>
          </el-menu-item>
          <el-menu-item index="3">
            <router-link to="/devices/manage">
              <svg-icon icon-class="m_device" class="icon" />
              <span>设备台账管理</span>
            </router-link>
          </el-menu-item>
          <el-menu-item index="4">
            <router-link to="/devices/powerstationmanage">
              <svg-icon icon-class="charge" class="icon" />
              <span>充换电站管理</span>
            </router-link>
          </el-menu-item>
          <el-menu-item index="5">
            <router-link to="/devices/maintenancemanage">
              <svg-icon icon-class="people" class="icon" />
              <span>维保管理</span>
            </router-link>
          </el-menu-item>
          <el-menu-item index="6">
            <router-link to="/devices/errorwarning">
              <svg-icon icon-class="message" class="icon" />
              <span>设备故障列表</span>
            </router-link>
          </el-menu-item>
        </el-menu>
      </nav>
      <div class="header-right flex-wrap">
        <!-- <svg-icon icon-class="people" class="icon" />
        <span class="text"> {{ userName }}用户 </span> -->
        <div id="full-wrap-bt">
          <svg-icon icon-class="big-full" class="icon" />
          <span class="text"> 大屏 </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeIndex: '1',
      userName: 'admin',
    };
  },
  mounted() { },
  // 方法集合
  methods: {
    handleClick() {
      console.log('我被点击了');
    }
  },
};
</script>

<style lang="scss" scoped>
.pull-container {
  background: #4570e7;
  height: 63px;

  .logo-con {
    position: relative;
    height: 100%;
    width: 40%;

    img {
      height: 100%;
    }

    .icon-logo {
      position: absolute;
      left: 37px;
      top: 14px;
      width: 50px;
      height: 38px;
    }

    .logo-title {
      position: absolute;
      color: #ffffff;
      width: 90%;
      left: 85px;
      top: 20px;
      font-size: 20px;
      text-align: left;
      font-family: '微软雅黑', sans-serif;
      font-weight: 400;
      font-style: normal;
    }
  }

  .nav-content {
    height: 100%;
    width: 33%;
    margin-left: 0px;

    .nav_list {
      display: flex;
      height: 100%;
      justify-content: center;

      .icon {
        width: 18px;
        height: 15px;
        margin-top: 5px;
        margin-right: 5px;
      }

      .nav-li {
        color: rgba(255, 255, 255, 0.996078431372549);
        font-size: 15px;
        display: flex;
        padding: 21px 15px;
        flex-shrink: 0;
        white-space: nowrap !important;

        &:hover {
          background: #3599EE;
          cursor: pointer;
        }

        &:active {
          background: #3599EE;
          cursor: pointer;
        }

        justify-content: center;
        align-items: center;

        img {
          width: 14px;
          height: 14px;
          margin-right: 4px;
        }
      }

      li .router-link-active {
        cursor: pointer;
      }
    }
  }

  .header-right {
    position: relative;
    padding: 19px 15px;
    color: #ffffff;
    font-size: 14px;
    height: 100%;
    margin-left: auto;

    .text {
      margin-top: -3px;
      margin-left: 3px;
      margin-right: 5px;
      font-family: '微软雅黑', sans-serif;
      font-weight: 400;
      font-style: normal;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
    }

    .icon {
      width: 18px;
      height: 18px;
    }
  }

  #full-wrap-bt {
    cursor: pointer;
  }
}

.headerContainer {
  width: 100% !important;

  .header-right .el-input__inner {
    color: #fff;
  }

  .el-menu.el-menu--horizontal {
    border: none;

    .el-menu-item {
      height: 63px;
      font-family: '微软雅黑', sans-serif;
      font-weight: 400;
      font-style: normal;
      font-size: 16px;
    }

    ::v-deep .el-submenu__title {
      height: 63px;
      line-height: 63px;
      font-family: '微软雅黑', sans-serif;
      font-weight: 400;
      font-style: normal;
      font-size: 16px;
    }

  }

  ::v-deep .el-submenu__title i {
    color: transparent;
  }
}

.icon-list {
  width: 18px !important;
  height: 15px !important;
  margin-right: 10px;
}
</style>


<style lang="scss">
// 控制二级菜单的颜色背景
.el-menu--horizontal {
  .el-menu--popup-bottom-start {
    background-color: #fff !important;

    .menu-item {
      background-color: #fff !important;
      color: #7F86A3 !important;

      &:hover {
        background-color: #EDF4FE !important;
      }
    }
  }
}

// 控制点击后的颜色背景
.el-menu--horizontal>.el-menu-item.is-active {
  border-bottom: 2px solid #4570e7 !important;
  color: #fff !important;
}

// 控制点击二级菜单后以及菜单颜色异常
.el-menu--horizontal>.el-submenu.is-active .el-submenu__title {
  border-bottom: 2px solid #4570e7 !important;
  color: #fff !important;
}
</style>
