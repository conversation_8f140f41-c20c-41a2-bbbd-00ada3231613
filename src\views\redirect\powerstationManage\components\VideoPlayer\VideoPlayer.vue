<template>
    <div class="video-player">
      <video ref="video" class="video-js vjs-default-skin" ></video>
    </div>
  </template>
  
  <script>
  // 引入 Video.js
  import videojs from 'video.js';
  
  export default {
    name: 'VideoPlayer',
    props: {
      sources: {
        type: Array,
        required: true
      },
      options: {
        type: Object,
        default: () => ({})
      }
    },
    mounted() {
      // 初始化 Video.js 播放器
      const player = videojs(this.$refs.video, this.options, () => {
        // 播放器准备就绪后的回调
        // console.log('播放器已就绪', player);
      });
  
      // 设置视频源
    //   if (this.sources.length > 0) {
    //     player.src(this.sources);
    //   }
      player.src(this.sources);
      // player.controlBar.addChild('retryButton', {
      //   label: '重试',
      //   click: function() {
      //       // 重试加载视频的逻辑
      //       }
      //   });
    },
    beforeDestroy() {
      // 组件销毁前释放播放器资源
      if (this.$refs.video) {
        const player = videojs(this.$refs.video);
        player.dispose();
      }
    }
  };
  </script>
  
  <style lang="scss" scoped>
  /* 可以添加一些自定义样式 */
  .video-player{
    position: relative;
    width: 100%;
    height: 100%;
  }
    .video-js{
        width: 100%;
        height: 100%;
    }

    .video-js video {
        width: 100%;
        height: 100%;
        object-fit: fill; /* 或者使用 contain, cover 等值 */
    }
    .video-js .vjs-default-skin .vjs-big-play-button {
        font-size: 2em;
        line-height: 2em;
        height: 2em;
        width: 2em;
        margin-top: -1em;
        margin-left: -1em;
    }
  
  </style>