/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./GeometryAttributes-aacecde6","./IndexDatatype-9435b55f","./arrayFill-9766fb2e","./GeometryOffsetAttribute-999fc023","./VertexFormat-fe4db402"],function(e,ue,d,le,fe,ce,de,pe,Ce,ye,he,ve,_e,Ae,p){var be=new fe.Cartesian3,xe=new fe.Cartesian3,we=new fe.Cartesian3,ke=new fe.Cartesian3,Pe=new fe.Cartesian3,l=new fe.Cartesian3(1,1,1),Fe=Math.cos,ge=Math.sin;function C(e){e=ue.defaultValue(e,ue.defaultValue.EMPTY_OBJECT);var t=ue.defaultValue(e.radii,l),a=ue.defaultValue(e.innerRadii,t),r=ue.defaultValue(e.minimumClock,0),i=ue.defaultValue(e.maximumClock,le.CesiumMath.TWO_PI),n=ue.defaultValue(e.minimumCone,0),o=ue.defaultValue(e.maximumCone,le.CesiumMath.PI),m=Math.round(ue.defaultValue(e.stackPartitions,64)),s=Math.round(ue.defaultValue(e.slicePartitions,64)),u=ue.defaultValue(e.vertexFormat,p.VertexFormat.DEFAULT);if(s<3)throw new d.DeveloperError("options.slicePartitions cannot be less than three.");if(m<3)throw new d.DeveloperError("options.stackPartitions cannot be less than three.");this._radii=fe.Cartesian3.clone(t),this._innerRadii=fe.Cartesian3.clone(a),this._minimumClock=r,this._maximumClock=i,this._minimumCone=n,this._maximumCone=o,this._stackPartitions=m,this._slicePartitions=s,this._vertexFormat=p.VertexFormat.clone(u),this._offsetAttribute=e.offsetAttribute,this._workerName="createEllipsoidGeometry"}C.packedLength=2*fe.Cartesian3.packedLength+p.VertexFormat.packedLength+7,C.pack=function(e,t,a){if(!ue.defined(e))throw new d.DeveloperError("value is required");if(!ue.defined(t))throw new d.DeveloperError("array is required");return a=ue.defaultValue(a,0),fe.Cartesian3.pack(e._radii,t,a),a+=fe.Cartesian3.packedLength,fe.Cartesian3.pack(e._innerRadii,t,a),a+=fe.Cartesian3.packedLength,p.VertexFormat.pack(e._vertexFormat,t,a),a+=p.VertexFormat.packedLength,t[a++]=e._minimumClock,t[a++]=e._maximumClock,t[a++]=e._minimumCone,t[a++]=e._maximumCone,t[a++]=e._stackPartitions,t[a++]=e._slicePartitions,t[a]=ue.defaultValue(e._offsetAttribute,-1),t};var t,y=new fe.Cartesian3,h=new fe.Cartesian3,v=new p.VertexFormat,_={radii:y,innerRadii:h,vertexFormat:v,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,offsetAttribute:void 0};C.unpack=function(e,t,a){if(!ue.defined(e))throw new d.DeveloperError("array is required");t=ue.defaultValue(t,0);var r=fe.Cartesian3.unpack(e,t,y);t+=fe.Cartesian3.packedLength;var i=fe.Cartesian3.unpack(e,t,h);t+=fe.Cartesian3.packedLength;var n=p.VertexFormat.unpack(e,t,v);t+=p.VertexFormat.packedLength;var o=e[t++],m=e[t++],s=e[t++],u=e[t++],l=e[t++],f=e[t++],c=e[t];return ue.defined(a)?(a._radii=fe.Cartesian3.clone(r,a._radii),a._innerRadii=fe.Cartesian3.clone(i,a._innerRadii),a._vertexFormat=p.VertexFormat.clone(n,a._vertexFormat),a._minimumClock=o,a._maximumClock=m,a._minimumCone=s,a._maximumCone=u,a._stackPartitions=l,a._slicePartitions=f,a._offsetAttribute=-1===c?void 0:c,a):(_.minimumClock=o,_.maximumClock=m,_.minimumCone=s,_.maximumCone=u,_.stackPartitions=l,_.slicePartitions=f,_.offsetAttribute=-1===c?void 0:c,new C(_))},C.createGeometry=function(e){var t=e._radii;if(!(t.x<=0||t.y<=0||t.z<=0)){var a=e._innerRadii;if(!(a.x<=0||a.y<=0||a.z<=0)){var r,i,n=e._minimumClock,o=e._maximumClock,m=e._minimumCone,s=e._maximumCone,u=e._vertexFormat,l=e._slicePartitions+1,f=e._stackPartitions+1;(l=Math.round(l*Math.abs(o-n)/le.CesiumMath.TWO_PI))<2&&(l=2),(f=Math.round(f*Math.abs(s-m)/le.CesiumMath.PI))<2&&(f=2);var c=0,d=[m],p=[n];for(r=0;r<f;r++)d.push(m+r*(s-m)/(f-1));for(d.push(s),i=0;i<l;i++)p.push(n+i*(o-n)/(l-1));p.push(o);var C=d.length,y=p.length,h=0,v=1,_=a.x!==t.x||a.y!==t.y||a.z!==t.z,A=!1,b=!1,x=!1;_&&(v=2,0<m&&(A=!0,h+=l-1),s<Math.PI&&(b=!0,h+=l-1),(o-n)%le.CesiumMath.TWO_PI?(x=!0,h+=2*(f-1)+1):h+=1);var w=y*C*v,k=new Float64Array(3*w),P=_e.arrayFill(new Array(w),!1),F=_e.arrayFill(new Array(w),!1),g=l*f*v,V=6*(g+h+1-(l+f)*v),D=ve.IndexDatatype.createTypedArray(g,V),M=u.normal?new Float32Array(3*w):void 0,T=u.tangent?new Float32Array(3*w):void 0,E=u.bitangent?new Float32Array(3*w):void 0,G=u.st?new Float32Array(2*w):void 0,L=new Array(C),O=new Array(C);for(r=0;r<C;r++)L[r]=ge(d[r]),O[r]=Fe(d[r]);var I=new Array(y),z=new Array(y);for(i=0;i<y;i++)z[i]=Fe(p[i]),I[i]=ge(p[i]);for(r=0;r<C;r++)for(i=0;i<y;i++)k[c++]=t.x*L[r]*z[i],k[c++]=t.y*L[r]*I[i],k[c++]=t.z*O[r];var N,R,S,U,B=w/2;if(_)for(r=0;r<C;r++)for(i=0;i<y;i++)k[c++]=a.x*L[r]*z[i],k[c++]=a.y*L[r]*I[i],k[c++]=a.z*O[r],P[B]=!0,0<r&&r!==C-1&&0!==i&&i!==y-1&&(F[B]=!0),B++;for(c=0,r=1;r<C-2;r++)for(N=r*y,R=(r+1)*y,i=1;i<y-2;i++)D[c++]=R+i,D[c++]=R+i+1,D[c++]=N+i+1,D[c++]=R+i,D[c++]=N+i+1,D[c++]=N+i;if(_){var W=C*y;for(r=1;r<C-2;r++)for(N=W+r*y,R=W+(r+1)*y,i=1;i<y-2;i++)D[c++]=R+i,D[c++]=N+i,D[c++]=N+i+1,D[c++]=R+i,D[c++]=N+i+1,D[c++]=R+i+1}if(_){if(A)for(U=C*y,r=1;r<y-2;r++)D[c++]=r,D[c++]=r+1,D[c++]=U+r+1,D[c++]=r,D[c++]=U+r+1,D[c++]=U+r;if(b)for(S=C*y-y,U=C*y*v-y,r=1;r<y-2;r++)D[c++]=S+r+1,D[c++]=S+r,D[c++]=U+r,D[c++]=S+r+1,D[c++]=U+r,D[c++]=U+r+1}if(x){for(r=1;r<C-2;r++)U=y*C+y*r,S=y*r,D[c++]=U,D[c++]=S+y,D[c++]=S,D[c++]=U,D[c++]=U+y,D[c++]=S+y;for(r=1;r<C-2;r++)U=y*C+y*(r+1)-1,S=y*(r+1)-1,D[c++]=S+y,D[c++]=U,D[c++]=S,D[c++]=S+y,D[c++]=U+y,D[c++]=U}var q=new he.GeometryAttributes;u.position&&(q.position=new Ce.GeometryAttribute({componentDatatype:pe.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:k}));var Y,J=0,X=0,Z=0,j=0,H=w/2,K=ce.Ellipsoid.fromCartesian3(t),Q=ce.Ellipsoid.fromCartesian3(a);if(u.st||u.normal||u.tangent||u.bitangent){for(r=0;r<w;r++){Y=P[r]?Q:K;var $=fe.Cartesian3.fromArray(k,3*r,be),ee=Y.geodeticSurfaceNormal($,xe);if(F[r]&&fe.Cartesian3.negate(ee,ee),u.st){var te=ce.Cartesian2.negate(ee,Pe);G[J++]=Math.atan2(te.y,te.x)/le.CesiumMath.TWO_PI+.5,G[J++]=Math.asin(ee.z)/Math.PI+.5}if(u.normal&&(M[X++]=ee.x,M[X++]=ee.y,M[X++]=ee.z),u.tangent||u.bitangent){var ae,re=we,ie=0;if(P[r]&&(ie=H),ae=!A&&ie<=r&&r<ie+2*y?fe.Cartesian3.UNIT_X:fe.Cartesian3.UNIT_Z,fe.Cartesian3.cross(ae,ee,re),fe.Cartesian3.normalize(re,re),u.tangent&&(T[Z++]=re.x,T[Z++]=re.y,T[Z++]=re.z),u.bitangent){var ne=fe.Cartesian3.cross(ee,re,ke);fe.Cartesian3.normalize(ne,ne),E[j++]=ne.x,E[j++]=ne.y,E[j++]=ne.z}}}u.st&&(q.st=new Ce.GeometryAttribute({componentDatatype:pe.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:G})),u.normal&&(q.normal=new Ce.GeometryAttribute({componentDatatype:pe.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:M})),u.tangent&&(q.tangent=new Ce.GeometryAttribute({componentDatatype:pe.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:T})),u.bitangent&&(q.bitangent=new Ce.GeometryAttribute({componentDatatype:pe.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E}))}if(ue.defined(e._offsetAttribute)){var oe=k.length,me=new Uint8Array(oe/3),se=e._offsetAttribute===Ae.GeometryOffsetAttribute.NONE?0:1;_e.arrayFill(me,se),q.applyOffset=new Ce.GeometryAttribute({componentDatatype:pe.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:me})}return new Ce.Geometry({attributes:q,indices:D,primitiveType:ye.PrimitiveType.TRIANGLES,boundingSphere:de.BoundingSphere.fromEllipsoid(K),offsetAttribute:e._offsetAttribute})}}},C.getUnitEllipsoid=function(){return ue.defined(t)||(t=C.createGeometry(new C({radii:new fe.Cartesian3(1,1,1),vertexFormat:p.VertexFormat.POSITION_ONLY}))),t},e.EllipsoidGeometry=C});