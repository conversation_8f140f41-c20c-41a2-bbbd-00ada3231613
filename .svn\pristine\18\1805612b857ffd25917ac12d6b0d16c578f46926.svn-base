<template>
    <el-dialog title="导入" :visible.sync="importvisible" width="25%" :before-close="handleClose" class="popup-dialog">
        <div class="dialog-part">
            <el-upload class="upload-demo" drag action="/api/equip/ledger/importExcel" multiple :limit="1" :before-upload="handleBeforeUpload">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip">只能上传设备台账管理表，且只能上传一个文件</div>
            </el-upload>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="onClose" type="primary">关闭</el-button>
        </span>
    </el-dialog>
</template>

<script>
import store from "@/store";
export default {
    name: 'ImportDialog',
    props: ['importvisible'],
    components: {
    },
    data() {
        return {

        }
    },

    mounted() {

    },

    methods: {
        onClose() {
            this.$emit('onClose');
        },

        handleClose() {
            this.$confirm('确认关闭？')
                .then(_ => {
                    this.$emit('onClose')
                })
                .catch(_ => { });
        },
        handleBeforeUpload(file) {
        // 创建一个新的 FormData 对象
        const formData = new FormData();
        // 将文件添加到 FormData 对象中
        formData.append('file', file);

        // 创建一个新的 XMLHttpRequest 对象
        const xhr = new XMLHttpRequest();
        // 设置请求 URL
        xhr.open('POST', '/api/equip/ledger/importExcel', true);
        // 设置自定义请求头
        
        xhr.setRequestHeader('crbimuid', store.state.user.userInfo.id);

        // 处理上传进度、成功和错误等事件
        xhr.upload.onprogress = (event) => {
            if (event.lengthComputable) {
            const percent = (event.loaded / event.total) * 100;
            console.log(`上传进度: ${percent.toFixed(2)}%`);
            }
        };

        xhr.onload = () => {
            if (xhr.status === 200) {
            console.log('文件上传成功');
            } else {
            console.error('文件上传失败');
            }
        };

        xhr.onerror = () => {
            console.error('文件上传出错');
        };

        // 发送请求
        xhr.send(formData);

        // 返回 false 以阻止 el-upload 组件的默认上传行为
        return false;
        }
    },
}
</script>

<style lang="scss" scoped>
.popup-dialog {
    .dialog-part {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    ::v-deep .el-dialog__header {
        background-color: #4575E7;

        .el-dialog__title {
            color: #fff;
        }

        .el-dialog__close {
            color: #fff;
        }
    }
}
</style>