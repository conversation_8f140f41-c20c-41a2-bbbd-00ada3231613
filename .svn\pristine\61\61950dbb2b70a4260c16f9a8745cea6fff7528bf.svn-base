import Vue from "vue";
import App from "./App.vue";
import router from "./router";
Vue.config.productionTip = false;
import store from "./store";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
// 调整 element-ui为中文
import locale from "element-ui/lib/locale/lang/zh-CN";
Vue.use(ElementUI, {
  locale
});
import "@/styles/index.scss";
// 引入echarts
import * as echarts from "echarts";
// 将echarts注册到Vue组件的原型对象中去
Vue.prototype.$echarts = echarts;
Vue.use(echarts);

// 注册全局组件
import Components from "./components/index";
Vue.use(Components);

// vue使用JSx
import VueJsx from 'vue-jsx';
Vue.use(VueJsx);

import {
  msgSuccess,
  msgError,
  msgWarn
} from "./utils/messageUtils"
import {
  Message
} from "element-ui";
Vue.prototype.$message = Message
Vue.prototype.$msgSuccess = msgSuccess
Vue.prototype.$msgError = msgError
Vue.prototype.$msgWarn = msgWarn

// http 请求
import http from './utils/request'
Vue.prototype.$http = http

// 高德地图
import AMap from "vue-amap";
Vue.use(AMap);
AMap.initAMapApiLoader({
  key: "4fc7ce81d4874d80381897f5ddce6e81",
  plugin: [
    "AMap.Autocomplete",
    "AMap.PlaceSearch",
    "AMap.Scale",
    "AMap.OverView",
    "AMap.ToolBar",
    "AMap.MapType",
    "AMap.PolyEditor",
    "AMap.CircleEditor",
  ],
  // 默认高德 sdk 版本为 1.4.4
  v: "1.4.4",
});

// svg图标的设置
import "@/icons";
import svgIcon from "./icons/Svg.vue";
Vue.component("svg-icon", svgIcon);
//xml数据处理插件
import x2js from "x2js";
Vue.prototype.$x2js = new x2js(); //全局方法挂载
new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount("#app");
