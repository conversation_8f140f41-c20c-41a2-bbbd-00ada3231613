<template>
    <div class="warn-container">
        <!-- <div class="warn-top">
            <div class="warn-item warn-left" @click="exceptionNumberDialogVisible = true">
                <img src="@/assets/images/warn-total.png" alt="异常总数" class="img-bg">
                <div class="box-info">
                    <div class="info-item info-title">异常总数</div>
                    <div class="info-item info-num">
                        <el-tooltip :content="(warnInfo.exceptionTotal || 0) + ''" placement="top">
                            <span class="num-v">{{ warnInfo.exceptionTotal || 0 }}</span>
                        </el-tooltip>
                        <span>次</span>
                    </div>
                </div>
            </div>
            <el-dialog :visible.sync="exceptionNumberDialogVisible" title="异常数量详情图" width="85%" class="details-dialog">
                <DetailsChart text="异常数量" Yname="异常数量" pathUrl="/equip/build/exceptionTotalStat"></DetailsChart>
            </el-dialog>
            <div class="warn-item warn-right" @click="exceptionDetailsDialogVisible = true">
                <img src="@/assets/images/warn-device.png" alt="异常机械" class="img-bg">
                <div class="box-info">
                    <div class="info-item info-title">异常机械</div>
                    <div class="info-item info-num">
                        <el-tooltip :content="(warnInfo.exceptionMachinery || 0) + ''" placement="top">
                            <span class="num-v">{{ warnInfo.exceptionMachinery || 0 }}</span>
                        </el-tooltip>
                        <span>台</span>
                    </div>
                </div>
            </div>
            <el-dialog :visible.sync="exceptionDetailsDialogVisible" title="异常设备详情" width="85%" class="details-dialog">
                <ExceptionTable urlpath="/equip/build/exceptionMachinery" :isclosed="!exceptionDetailsDialogVisible"></ExceptionTable>
            </el-dialog>
        </div> -->
        <div class="warn-bottom ">
            <el-tooltip :content="'最近三天内没有上传数据的设备'" placement="top" class="alert-info">
                <div class="warning-item" @click="offlineDetailsDialogVisible=true">
                    <div class="warn-box"></div>
                    <!-- <svg-icon icon-class="warn-info" class="img-bg" /> -->
                    <div class="img-bg"></div>
                    <div class="item-info">
                        <div class="info-title">机械离线报警</div>
                        <div class="info-total">
                            {{ warnInfo.machineryOfflineAlarm || 0 }}
                            <span>台</span>
                        </div>
                    </div>
                </div>
            </el-tooltip>
            <el-dialog :visible.sync="offlineDetailsDialogVisible" title="设备离线报警详情" width="85%" class="details-dialog">
                <ExceptionTable urlpath="/equip/build/machineryOfflineAlarm" :isclosed="!offlineDetailsDialogVisible"></ExceptionTable>
            </el-dialog>

            <el-tooltip :content="'目前尚未维修完毕的设备'" placement="top" class="alert-info">
                <div class="warning-item" data-tooltip="目前尚未维修完毕的设备"  @click="faultDetailsDialogVisible=true">
                    <div class="warn-box"></div>
                    <!-- <svg-icon icon-class="warn-info" class="img-bg" /> -->
                    <div class="img-bg"></div>
                    <div class="item-info" >
                        <div class="info-title">机械故障报警</div>
                        <div class="info-total">
                            {{ warnInfo.machineryFaultAlarm || 0 }}
                            <span>台</span>

                        </div>
                    </div>
                </div>
            </el-tooltip>

            <el-dialog :visible.sync="faultDetailsDialogVisible" title="设备故障报警详情" width="85%" class="details-dialog">
                <ExceptionTable urlpath="/equip/build/machineryFaultAlarm" :isclosed="!faultDetailsDialogVisible"></ExceptionTable>
            </el-dialog>

            <el-tooltip :content="'SOC小于等于30%'" placement="top" class="alert-info">
                <div class="warning-item machine-alert-info" data-tooltip="SOC小于等于30%" @click="lowpowerDetailsDialogVisible=true">
                    <div class="warn-box"></div>
                    <!-- <svg-icon icon-class="warn-info" class="img-bg" /> -->
                    <div class="img-bg"></div>
                    <div class="item-info" >
                        <div class="info-title">低电量报警</div>
                        <div class="info-total">
                            {{ warnInfo.lowBatteryAlarm || 0 }}
                            <span>台</span>
                        </div>
                    </div>
                </div>
            </el-tooltip>

            <el-dialog :visible.sync="lowpowerDetailsDialogVisible" title="设备低电量报警详情" width="85%" class="details-dialog">
                <ExceptionTable urlpath="/equip/build/lowBatteryAlarm" :isclosed="!lowpowerDetailsDialogVisible"></ExceptionTable>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import ExceptionTable from "../Table/ExceptionTable.vue"
import DetailsChart from "../Chart/DetailsChart.vue"
export default {
    name: 'WarnInfo',
    components: {
        ExceptionTable,
        DetailsChart,
    },
    props: [],
    data() {
        return {
            warnInfo: {},
            exceptionDetailsDialogVisible: false,
            exceptionNumberDialogVisible: false,
            offlineDetailsDialogVisible: false,
            faultDetailsDialogVisible: false,
            lowpowerDetailsDialogVisible: false,
        }
    },
    mounted() {

    },

    watch: {
        "$store.state.tree.sectionId": {
            handler: function () {
                this.getWarnInfo();
            },
            deep: true,
            immediate: true,
        },
    },

    methods: {
        async getWarnInfo() {
            let res = await this.$http.get(`/equip/build/alarm?sectionId=${this.$store.state.tree.sectionId||''}`);
            this.warnInfo = res?.result || {}
        }
    }
}
</script>

<style lang="scss" scoped>
.warn-container {
    // width: 370px;
    width: 100%;
    height: 50%;
    // margin: 0 15px;

    // .warn-top {
    //     width: 100%;
    //     height: 40%;
    //     margin-top: 10px;
    //     background-color: #fff;
    //     display: flex;
    //     flex-direction: row;
    //     flex-wrap: nowrap;

    //     .warn-item {
    //         width: 165px;
    //         height: 80px;
    //         position: relative;
    //         cursor: pointer;

    //         .img-bg {
    //             width: 100%;
    //             height: 100%;
    //             background-size: contain;
    //         }

    //         .box-info {
    //             width: 80px;
    //             height: 60px;
    //             position: absolute;
    //             left: 15px;
    //             top: 10px;

    //             .info-item {
    //                 width: 80px;
    //                 height: 30px;
    //                 line-height: 30px;
    //                 text-align: left;
    //                 font-family: '微软雅黑', sans-serif;
    //                 font-weight: 400;
    //                 font-style: normal;
    //                 font-size: 16px;
    //                 color: #3A3F5D;
    //                 overflow: hidden;
    //                 white-space: nowrap;
    //                 text-overflow: ellipsis;
    //             }

    //             .info-num {
    //                 font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
    //                 font-weight: 700;
    //                 color: #3D7AF0;
    //                 text-align: left;

    //                 .num-v {
    //                     cursor: pointer;
    //                 }
    //             }
    //         }
    //     }

    //     .warn-right {
    //         margin-left: 25px;
    //     }
    // }

    .warn-bottom {
        width: 100%;
        height: 100%;

        .warning-item {
            width: 100%;
            height: 24%;
            margin-top: 2%;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            position: relative;
            cursor: pointer;

            .machine-alert-info:hover::after {
                content: attr(data-tooltip); /* 要显示的文字 */
                position: absolute; /* 绝对定位 */
                top: 100%; /* 定位在元素下方 */
                left: 50%; /* 水平居中 */
                transform: translateX(-50%); /* 调整水平位置 */
                background-color: black; /* 背景颜色 */
                color: white; /* 文字颜色 */
                padding: 5px; /* 内边距 */
                border-radius: 4px; /* 圆角 */
                white-space: nowrap; /* 防止文字换行 */
                z-index: 1; /* 确保文字显示在最上层 */
                opacity: 1;
            }


            .img-bg {
                width: calc(100% - 10px);
                // width: 100%;
                height: 100%;
                background-image: linear-gradient(to right, #FAC6B4, #FEF7F0);
                border-bottom: #E34D65 solid 1px;
                // background-size: contain;
            }

            .warn-box {
                width: 3px;
                height: 100%;
                
                background: linear-gradient(to bottom, #E34D65, #F49F38);
            }

            .item-info {
                position: absolute;
                width: calc(100% - 3px);
                height: 100%;
                top: 0px;
                left: 3px;
                // line-height: 30px;
                display: flex;
                flex-direction: row;
                flex-wrap: nowrap;
                padding: 0 20px;

                .info-title {
                    width: 50%;
                    height: 100%;
                    text-align: left;
                    font-family: '微软雅黑', sans-serif;
                    font-weight: 400;
                    font-style: normal;
                    font-size: 16px;
                    color: #3A3F5D;
                }

                .info-total {
                    width: 50%;
                    height: 100%;
                    text-align: center;
                    font-family: '微软雅黑', sans-serif;
                    font-weight: 400;
                    font-style: normal;
                    font-size: 16px;
                    color: #3D7AF0;
                    position: relative;

                    .total-warn {
                        position: absolute;
                        width: 50px;
                        height: 100%;
                        top: 0;
                        right: 0px;
                        color: #fff;
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        .alert-message {
                            width: 30px;
                            height: 30px;
                            background-size: contain;
                            animation: blink-warning 1s infinite;
                            margin-top: -5px;
                            position: relative;
                        }

                        // .alert-info:hover::after {
                        //     content: "此处为实际故障设备数量"; /* 要显示的文字 */
                        //     position: absolute; /* 绝对定位 */
                        //     top: 100%; /* 定位在元素下方 */
                        //     left: 50%; /* 水平居中 */
                        //     transform: translateX(-50%); /* 调整水平位置 */
                        //     background-color: black; /* 背景颜色 */
                        //     color: white; /* 文字颜色 */
                        //     padding: 5px; /* 内边距 */
                        //     border-radius: 4px; /* 圆角 */
                        //     white-space: nowrap; /* 防止文字换行 */
                        //     z-index: 1; /* 确保文字显示在最上层 */
                        // }

                        .alert-info {
                            position: absolute;
                            width: 15px;
                            height: 15px;
                            line-height: 20px;
                            left: 18px;
                            top: 8px;
                            font-weight: 600;
                            font-size: 12px;

                            .num-v {
                                cursor: pointer;
                            }
                        }


                        @keyframes blink-warning {
                            0% {
                                opacity: 0;
                            }

                            50% {
                                opacity: 1;
                            }

                            100% {
                                opacity: 0;
                            }
                        }
                    }
                }

                


            }
        }

        
    }

    

}
</style>