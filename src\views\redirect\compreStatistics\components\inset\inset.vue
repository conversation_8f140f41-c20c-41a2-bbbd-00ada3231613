<template>
  <div id="inset-container">
    <div class="center-top">
      <div class="center-item">
        <svg-icon icon-class="inset-g" class="img-bg" />
        <div class="item-title">采集设备接入数量</div>
      </div>
      <div class="center-item">
        <svg-icon icon-class="inset-b" class="img-bg" />
        <div class="item-title">系统传输接入数量</div>
      </div>
    </div>
    <div class="center-bottom">
      <div class="bottom-item">
        <span class="item-num">{{ this.insetInfo.inset_num }}</span>
        <span>个</span>
      </div>
      <div class="bottom-item">
        <span class="item-num">{{ this.insetInfo.inset_c_num }}</span>
        <span>个</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "inset",
  props: [],
  data() {
    return {
      insetInfo: {},
    };
  },
  mounted() {},
  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.getInsetInfo();
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    async getInsetInfo() {
      let resp = await this.$http.get(
        `/equip/build/accessSituation?sectionId=${this.$store.state.tree.sectionId||''}`
      );
      this.insetInfo = {
        inset_num: resp.result?.totalCountBycollecterId || 0,
        inset_c_num: resp.result?.totalCountByfactoryNum || 0,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
#inset-container {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  border: 1px solid #b3caff;

  .center-top {
    width: 100%;
    height: 39px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .center-item {
      width: 50%;
      height: 100%;
      position: relative;

      .img-bg {
        width: 100%;
        height: 100%;
        background-size: contain;
      }

      .item-title {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0px;
        top: 0px;
        line-height: 39px;
        text-align: center;
        font-size: 16px;
        font-weight: 400;
        color: #fff;
        font-family: "微软雅黑", sans-serif;
        font-style: normal;
      }
    }
  }

  .center-bottom {
    width: 100%;
    height: 97px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .bottom-item {
      width: 50%;
      height: 100%;
      text-align: center;
      line-height: 97px;
      font-family: "优设标题黑", sans-serif;
      font-weight: 400;
      font-size: 16px;

      .item-num {
        font-size: 38px;
      }
    }
  }
}
</style>
