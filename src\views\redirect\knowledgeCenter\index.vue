<template>
  <div id="knowledge">
    <el-card class="card-content">
      <div class="content-option">
        <div class="content-title" style="margin-left: 45%">维保知识库</div>
        <router-link 
                to="/devices/maintenanceStatistics"
                type="primary"
                tag="el-button" 
                :loading="loading" 
                style="margin-top: 0.3%; margin-bottom: 0.3%; margin-left: 39%"
                class="jump-to-screen">
                返回维保统计
            </router-link>
        <!-- <el-button
          type="primary"
          style="margin-top: 0.3%; margin-bottom: 0.3%; margin-left: 39%"
          >返回维保统计</el-button
        > -->
      </div>
      <div class="content-option">
        <div class="container-left">
          <div class="knowledge-tree-container">
            <el-scrollbar>
              <el-tree
                :data="treeData"
                :props="defaultProps"
                node-key="id"
                highlight-current
                style="height: 75vh"
                @node-click="handleNodeClick"
              >
              </el-tree>
            </el-scrollbar>
          </div>
        </div>
        <errRecords :tableData="filteredTableData" style="margin-left: 2%" />
      </div>
    </el-card>
  </div>
</template>

<script>
import tableUtils from "@/mixins/tableUtils";
//import addErrReport from "./components/addErrReport.vue";
import { exportPlan } from "@/api/baseApi/common";
import { originalData, errorReport } from "./staticData";
//import warnRecords from "./components/warnRecords.vue";
import errRecords from "./components/knowledgeRecords.vue";
export default {
  components: { errRecords },
  name: "knowledge",
  mixins: [tableUtils],
  //   components: { AddLedgerDialog, ImportDialog },
  data() {
    return {
      originalData,
      filteredTableData: originalData.slice(),
      treeData: [
        {
          label: "充电挖掘机",
          children: [
            { label: "充电挖掘机 - XX4348", index: 1 },
            { label: "充电挖掘机 - XX1012", index: 6 },
            { label: "充电挖掘机 - XX5433", index: 13 },
            { label: "充电挖掘机 - XX7865", index: 25 },
          ],
        },
        {
          label: "换电挖掘机",
          children: [
            { label: "换电挖掘机 - XX2349", index: 3 },
            { label: "换电挖掘机 - XX1112", index: 12 },
            { label: "换电挖掘机 - XX4562", index: 17 },
            { label: "换电挖掘机 - XX8567", index: 29 },
            { label: "换电挖掘机 - XX1290", index: 35 },
          ],
        },
        {
          label: "充电装载机",
          children: [
            { label: "充电装载机 - XX4348", index: 2 },
            { label: "充电装载机 - XX1023", index: 10 },
            { label: "充电装载机 - XX6532", index: 19 },
            { label: "充电装载机 - XX3341", index: 28 },
          ],
        },
        {
          label: "换电装载机",
          children: [
            { label: "换电装载机 - XX4987", index: 4 },
            { label: "换电装载机 - XX7890", index: 15 },
            { label: "换电装载机 - XX5612", index: 24 },
            { label: "换电装载机 - XX2222", index: 32 },
          ],
        },
        {
          label: "充电自卸车",
          children: [
            { label: "充电自卸车 - XX1348", index: 5 },
            { label: "充电自卸车 - XX3478", index: 8 },
            { label: "充电自卸车 - XX6543", index: 18 },
            { label: "充电自卸车 - XX9988", index: 33 },
          ],
        },
        {
          label: "换电自卸车",
          children: [
            { label: "换电自卸车 - XX5674", index: 7 },
            { label: "换电自卸车 - XX7431", index: 11 },
            { label: "换电自卸车 - XX3210", index: 26 },
            { label: "换电自卸车 - XX5555", index: 37 },
          ],
        },
        {
          label: "充电混泥土车",
          children: [
            { label: "充电混泥土车 - XX8772", index: 9 },
            { label: "充电混泥土车 - XX6531", index: 14 },
            { label: "充电混泥土车 - XX9833", index: 23 },
            { label: "充电混泥土车 - XX7788", index: 31 },
          ],
        },
        {
          label: "换电混泥土车",
          children: [
            { label: "智能混泥土车 - XX9982", index: 16 },
            { label: "高效混泥土车 - XX3345", index: 20 },
            { label: "自动化混泥土车 - XX7789", index: 30 },
            { label: "节能混泥土车 - XX4455", index: 40 },
          ],
        },
      ],
      // 配置项
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
  watch: {},
  methods: {
    created() {
      // 初始化表格数据展示
      this.filteredTableData = this.originalData.slice();
    },
    handleNodeClick(node) {
      const recordCount = Math.floor(Math.random() * 4) + 4; // 生成4-7范围的随机数

      // 创建包含1-40的随机不重复索引数组
      const indices = Array.from({ length: 40 }, (_, i) => i + 1);
      const randomIndices = [];

      while (randomIndices.length < recordCount) {
        const randomIndex = indices.splice(
          Math.floor(Math.random() * indices.length),
          1
        )[0];
        randomIndices.push(randomIndex);
      }
      if (!node.children) {
        this.filteredTableData = this.originalData.filter((item) =>
          randomIndices.includes(item.index)
        );
      }

      console.log(this.filteredTableData);
      console.log(randomIndices);
    },
  },
};
</script>

<style lang="scss" scoped>
#knowledge {
  .table-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin: 20px 0;
    padding: 10px 0;
    border-top: 2px solid #dcdfe6;
    border-bottom: 2px solid #dcdfe6;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .knowledge-tree-container {
    flex-grow: 1;
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #dcdfe6;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    background-color: #fff;
  }

  .el-tree {
    height: 100%;
    padding: 10px;
  }
  .el-scrollbar {
    height: 100%; /* 滚动条占满父容器 */
    overflow-y: auto; /* 确保有内容溢出时启用滚动条 */
  }

  .container-left {
    width: 405px;
  }
  .content-option {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    border-top: 2px solid #dcdfe6;
    border-bottom: 2px solid #dcdfe6;
    margin-bottom: 1%;
    .content-title {
      font-family: "微软雅黑 Bold", "微软雅黑 Regular", "微软雅黑", sans-serif;
      font-style: normal;
      margin-left: 50%;
      text-align: center; /* 让文字居中 */
      font-size: 32px; /* 字体较大，可以根据需要调整 */
      font-weight: bold; /* 加粗字体使其更显眼 */
      color: #333; /* 可以选择一个好看的颜色 */
      margin-bottom: 0.5%; /* 给标题增加一些顶部间距 */
    }

    .form-view {
      flex: 1;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      padding: 0px;
    }

    ::v-deep .el-input {
      width: 175px;
    }
  }

  .table-edit {
    margin-right: 10px;
  }

  .pagination-part {
    margin-top: 20px;
  }
}
.jump-to-screen {
  margin-left: 5px;
  background-color: #379de5;
  color: white;
}
</style>
