<template>
  <div id="PerceptionTable">
    <el-card class="card-content">
      <div class="content-table">
        <el-table
          ref="table"
          :data="tableData"
          style="width: 100%"
          border
          :header-cell-style="headerCellStyle"
          :cell-style="cellStyle"
        >
          <el-table-column
            type="index"
            width="50"
            label="序号"
            align="center"
          />
          <el-table-column
            prop="carNum"
            label="车牌号码"
            align="center"
            min-width="130"
          />
          <el-table-column
            label="车辆品牌"
            align="center"
            min-width="130"
            prop="carBrand"
          ></el-table-column>
          <el-table-column
            prop="vehicleIdentify"
            label="车架号"
            align="center"
            min-width="130"
          />
          <el-table-column label="管理编号" align="center" min-width="180">
            <template slot-scope="scope">
              <span class="device-code" @click="handleView(scope.row)">{{
                scope.row.managementNumber
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="机械类型" align="center" min-width="130">
            <template slot-scope="scope">
              <span v-if="scope.row.deviceType == '0'">充电挖掘机</span>
              <span v-else-if="scope.row.deviceType == '1'">换电挖掘机</span>
              <span v-else-if="scope.row.deviceType == '2'">充电装载机</span>
              <span v-else-if="scope.row.deviceType == '3'">换电装载机</span>
              <span v-else-if="scope.row.deviceType == '4'">充电自卸车</span>
              <span v-else-if="scope.row.deviceType == '5'">换电自卸车</span>
              <span v-else-if="scope.row.deviceType == '6'">充电混泥土车</span>
              <span v-else-if="scope.row.deviceType == '7'">换电混泥土车</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="walkStatus"
            label="车辆状态"
            align="center"
            min-width="130"
          />
          <el-table-column label="当前SOC" align="center" min-width="130">
            <template slot-scope="scope">{{
              scope.row.soc ? scope.row.soc + "%" : ""
            }}</template>
          </el-table-column>
          <el-table-column label="当前SOH" align="center" min-width="130">
            <template slot-scope="scope">{{
              scope.row.soc ? scope.row.soc + "%" : ""
            }}</template>
          </el-table-column>
          <el-table-column
            prop="totalWorkHours"
            label="总工作时长 / h"
            align="center"
            min-width="130"
          />
          <!--<el-table-column
            label="在线状态"
            align="center"
            min-width="130"
            prop="carStatus"
          >
          </el-table-column>-->
          <el-table-column
            prop="manufacturer"
            label="制造商"
            align="center"
            min-width="100"
          />
          <el-table-column
            prop="energyType"
            label="补能类型"
            align="center"
            min-width="130"
          />
          <el-table-column
            prop="faultLevel"
            label="故障等级"
            align="center"
            min-width="100"
          >
          </el-table-column>
        </el-table>
        <el-pagination
          class="pagination-part"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageParams.page"
          :page-sizes="pageSizes"
          :page-size="pageParams.limit"
          :layout="tableLayout"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import tableUtils from '@/mixins/tableUtils';
import { exportExcel } from '@/api/baseApi/common';
export default {
  name: "PerceptionTable",
  mixins: [tableUtils],
  props: {
    type: String,
    isclosed:Boolean,
  },
  data() {
    return {
      formInline: {},
      // type: "",
      tableData: [],
      deviceTypeData: [],
      keyword: null,
      loading: false,
      timer: null,
    };
  },

  // beforeMount() {
  //   // this.type = this.$route.query.type;
  //   this.keyword = this.type === "in" ? 2 : this.type === "out" ? 3 : 0;
  // },

  mounted() {
    this.getDeviceType();
    // console.log(this.pageParams);
    
  },

  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.timer = setTimeout(() => {
          this.requestList();
        }, 500);
      },
      deep: true,
      immediate: true,
    },
    isclosed: {
        handler: function () {
          if (this.isclosed) {
            this.tableData = [];
            this.total = 0;
          }else{
            this.requestList();
          }
        },
      },
  },

  methods: {
    async getDeviceType() {
      let device = [];
      let res = await this.$http.get(`/equip/dictData/type?dictName=机械类型`);
      res?.result.map((item) => {
        device.push({
          label: item.dictLabel,
          value: item.dictValue,
        });
      });
      this.deviceTypeData = device;
    },

    async requestList() {
      let params = {
        ...this.formInline,
        sectionId: this.$store.state.tree.sectionId||'',
      };
      // console.log(this.type);
      
      // this.keyword = this.type == 'in' ? 2 : (this.type == 'out' ? 3 : 0);
      let keyword = null;
      let keyword2 = null;
      let res = {};
      let res2 = {};
      if (this.type == 'in') {
        keyword = 2;
        // keyword2 = 4;
      }else if (this.type == 'out') {
        keyword = 3;
      } else if (this.type == 'not'){
        keyword = 1;
        keyword2 = 5;
      }else if(this.type == 'all'){
        keyword = 0;
        keyword2 = 4;
      }else if(this.type == 'plt'){
        keyword = 1;
      }else if(this.type == 'dev'){
        keyword = 0;
      }
      // console.log(this.keyword);
      
      // let res = await this.$http.post(
      //   `/equip/battery/listByQuery?keyword=${this.keyword}&page=${this.pageParams.page}&limit=${this.pageParams.limit}`,
      //   params
      // );
      // this.tableData = res.result?.records || [];
      // this.total = res.result?.total;
      if (keyword!=null) {
        res = await this.$http.post(
          `/equip/battery/listByQuery?keyword=${keyword}&page=${this.pageParams.page}&limit=${this.pageParams.limit}`,
          params
        );
      }

      if (keyword2!=null) {
        res2 = await this.$http.post(
          `/equip/battery/listByQuery?keyword=${keyword2}&page=${this.pageParams.page}&limit=${this.pageParams.limit}`,
          params
        );
      }

      this.tableData = (res.result?.records || []).concat(res2.result?.records || []);
      this.total = (res.result?.total||0) + (res2.result?.total||0);
      clearTimeout(this.timer);
    },


    handleView(row) {
      this.$router.push({
        path: "/devices/details",
        query: {
          id: row.vehicleIdentify,
        },
      });
    },

    handleReset() {
      window.history.back();
    },
  },
};
</script>

<style lang="scss" scoped>
#PerceptionTable {
  .content-option {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .content-title {
      line-height: 40px;
      font-family: "微软雅黑 Bold", "微软雅黑 Regular", "微软雅黑", sans-serif;
      font-weight: 700;
      font-style: normal;
      font-size: 18px;
      color: #3a3f5d;
      margin-right: 5%;
    }

    .form-view {
      flex: 1;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }

    ::v-deep .el-input {
      width: 175px;
    }
  }

  .pagination-part {
    margin-top: 20px;
  }

  .device-code {
    color: #4575e7;
  }

  .device-in {
    color: #60bf79;
  }

  .device-out {
    color: #f58a69;
  }
}
</style>