/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./GeometryAttributes-aacecde6","./IndexDatatype-9435b55f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./arrayFill-9766fb2e","./GeometryOffsetAttribute-999fc023","./VertexFormat-fe4db402","./arrayRemoveDuplicates-2869246d","./EllipsoidTangentPlane-9c25b2da","./EllipsoidRhumbLine-6ca4b1e6","./earcut-2.2.1-b404d9e6","./PolygonPipeline-cc78b34e","./PolylineVolumeGeometryLibrary-ac3b176f","./EllipsoidGeodesic-db2069b3","./PolylinePipeline-65700d85","./CorridorGeometryLibrary-f6551651"],function(at,p,it,ot,h,g,t,e,r,nt,st,b,a,i,o,lt,dt,n,s,x,N,D,C,l,d,u,M,c,m,f,ut){var mt=new ot.Cartesian3,ft=new ot.Cartesian3,yt=new ot.Cartesian3,ct=new ot.Cartesian3,O=new ot.Cartesian3,pt=new ot.Cartesian3,ht=new ot.Cartesian3,gt=new ot.Cartesian3;function v(t,e){for(var r=0;r<t.length;r++)t[r]=e.scaleToGeodeticSurface(t[r],t[r]);return t}function bt(t,e,r,a,i,o){var n=t.normals,s=t.tangents,l=t.bitangents,d=ot.Cartesian3.normalize(ot.Cartesian3.cross(r,e,ht),ht);o.normal&&ut.CorridorGeometryLibrary.addAttribute(n,e,a,i),o.tangent&&ut.CorridorGeometryLibrary.addAttribute(s,d,a,i),o.bitangent&&ut.CorridorGeometryLibrary.addAttribute(l,r,a,i)}function I(t,e,r){var a,i,o,n=t.positions,s=t.corners,l=t.endPositions,d=t.lefts,u=t.normals,m=new lt.GeometryAttributes,f=0,y=0,c=0;for(i=0;i<n.length;i+=2)f+=o=n[i].length-3,c+=2*o,y+=n[i+1].length-3;for(f+=3,y+=3,i=0;i<s.length;i++){a=s[i];var p=s[i].leftPositions;at.defined(p)?f+=o=p.length:y+=o=s[i].rightPositions.length,c+=o}var h,g=at.defined(l);g&&(f+=h=l[0].length-3,y+=h,c+=6*(h/=3));var b,C,v,A,_,w,T=f+y,G=new Float64Array(T),E={normals:e.normal?new Float32Array(T):void 0,tangents:e.tangent?new Float32Array(T):void 0,bitangents:e.bitangent?new Float32Array(T):void 0},V=0,F=T-1,L=mt,P=ft,x=h/2,N=dt.IndexDatatype.createTypedArray(T/3,c),D=0;if(g){w=yt,_=ct;var M=l[0];for(L=ot.Cartesian3.fromArray(u,0,L),P=ot.Cartesian3.fromArray(d,0,P),i=0;i<x;i++)w=ot.Cartesian3.fromArray(M,3*(x-1-i),w),_=ot.Cartesian3.fromArray(M,3*(x+i),_),ut.CorridorGeometryLibrary.addAttribute(G,_,V),ut.CorridorGeometryLibrary.addAttribute(G,w,void 0,F),bt(E,L,P,V,F,e),A=(C=V/3)+1,v=(b=(F-2)/3)-1,N[D++]=b,N[D++]=C,N[D++]=v,N[D++]=v,N[D++]=C,N[D++]=A,V+=3,F-=3}var O,I,k=0,S=0,R=n[k++],H=n[k++];for(G.set(R,V),G.set(H,F-H.length+1),P=ot.Cartesian3.fromArray(d,S,P),o=H.length-3,i=0;i<o;i+=3)O=r.geodeticSurfaceNormal(ot.Cartesian3.fromArray(R,i,ht),ht),I=r.geodeticSurfaceNormal(ot.Cartesian3.fromArray(H,o-i,gt),gt),bt(E,L=ot.Cartesian3.normalize(ot.Cartesian3.add(O,I,L),L),P,V,F,e),A=(C=V/3)+1,v=(b=(F-2)/3)-1,N[D++]=b,N[D++]=C,N[D++]=v,N[D++]=v,N[D++]=C,N[D++]=A,V+=3,F-=3;for(O=r.geodeticSurfaceNormal(ot.Cartesian3.fromArray(R,o,ht),ht),I=r.geodeticSurfaceNormal(ot.Cartesian3.fromArray(H,o,gt),gt),L=ot.Cartesian3.normalize(ot.Cartesian3.add(O,I,L),L),S+=3,i=0;i<s.length;i++){var z,U,B,Y=(a=s[i]).leftPositions,W=a.rightPositions,q=pt,J=yt,j=ct;if(L=ot.Cartesian3.fromArray(u,S,L),at.defined(Y)){for(bt(E,L,P,void 0,F,e),F-=3,U=A,B=v,z=0;z<Y.length/3;z++)q=ot.Cartesian3.fromArray(Y,3*z,q),N[D++]=U,N[D++]=B-z-1,N[D++]=B-z,ut.CorridorGeometryLibrary.addAttribute(G,q,void 0,F),J=ot.Cartesian3.fromArray(G,3*(B-z-1),J),j=ot.Cartesian3.fromArray(G,3*U,j),bt(E,L,P=ot.Cartesian3.normalize(ot.Cartesian3.subtract(J,j,P),P),void 0,F,e),F-=3;q=ot.Cartesian3.fromArray(G,3*U,q),J=ot.Cartesian3.subtract(ot.Cartesian3.fromArray(G,3*B,J),q,J),j=ot.Cartesian3.subtract(ot.Cartesian3.fromArray(G,3*(B-z),j),q,j),bt(E,L,P=ot.Cartesian3.normalize(ot.Cartesian3.add(J,j,P),P),V,void 0,e),V+=3}else{for(bt(E,L,P,V,void 0,e),V+=3,U=v,B=A,z=0;z<W.length/3;z++)q=ot.Cartesian3.fromArray(W,3*z,q),N[D++]=U,N[D++]=B+z,N[D++]=B+z+1,ut.CorridorGeometryLibrary.addAttribute(G,q,V),J=ot.Cartesian3.fromArray(G,3*U,J),j=ot.Cartesian3.fromArray(G,3*(B+z),j),bt(E,L,P=ot.Cartesian3.normalize(ot.Cartesian3.subtract(J,j,P),P),V,void 0,e),V+=3;q=ot.Cartesian3.fromArray(G,3*U,q),J=ot.Cartesian3.subtract(ot.Cartesian3.fromArray(G,3*(B+z),J),q,J),j=ot.Cartesian3.subtract(ot.Cartesian3.fromArray(G,3*B,j),q,j),bt(E,L,P=ot.Cartesian3.normalize(ot.Cartesian3.negate(ot.Cartesian3.add(j,J,P),P),P),void 0,F,e),F-=3}for(R=n[k++],H=n[k++],R.splice(0,3),H.splice(H.length-3,3),G.set(R,V),G.set(H,F-H.length+1),o=H.length-3,S+=3,P=ot.Cartesian3.fromArray(d,S,P),z=0;z<H.length;z+=3)O=r.geodeticSurfaceNormal(ot.Cartesian3.fromArray(R,z,ht),ht),I=r.geodeticSurfaceNormal(ot.Cartesian3.fromArray(H,o-z,gt),gt),bt(E,L=ot.Cartesian3.normalize(ot.Cartesian3.add(O,I,L),L),P,V,F,e),C=(A=V/3)-1,b=(v=(F-2)/3)+1,N[D++]=b,N[D++]=C,N[D++]=v,N[D++]=v,N[D++]=C,N[D++]=A,V+=3,F-=3;V-=3,F+=3}if(bt(E,L=ot.Cartesian3.fromArray(u,u.length-3,L),P,V,F,e),g){V+=3,F-=3,w=yt,_=ct;var K=l[1];for(i=0;i<x;i++)w=ot.Cartesian3.fromArray(K,3*(h-i-1),w),_=ot.Cartesian3.fromArray(K,3*i,_),ut.CorridorGeometryLibrary.addAttribute(G,w,void 0,F),ut.CorridorGeometryLibrary.addAttribute(G,_,V),bt(E,L,P,V,F,e),C=(A=V/3)-1,b=(v=(F-2)/3)+1,N[D++]=b,N[D++]=C,N[D++]=v,N[D++]=v,N[D++]=C,N[D++]=A,V+=3,F-=3}if(m.position=new st.GeometryAttribute({componentDatatype:nt.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:G}),e.st){var Q,X,Z=new Float32Array(T/3*2),$=0;if(g){f/=3,y/=3;var tt,et=Math.PI/(h+1);X=1/(f-h+1),Q=1/(y-h+1);var rt=h/2;for(i=rt+1;i<h+1;i++)tt=it.CesiumMath.PI_OVER_TWO+et*i,Z[$++]=Q*(1+Math.cos(tt)),Z[$++]=.5*(1+Math.sin(tt));for(i=1;i<y-h+1;i++)Z[$++]=i*Q,Z[$++]=0;for(i=h;rt<i;i--)tt=it.CesiumMath.PI_OVER_TWO-i*et,Z[$++]=1-Q*(1+Math.cos(tt)),Z[$++]=.5*(1+Math.sin(tt));for(i=rt;0<i;i--)tt=it.CesiumMath.PI_OVER_TWO-et*i,Z[$++]=1-X*(1+Math.cos(tt)),Z[$++]=.5*(1+Math.sin(tt));for(i=f-h;0<i;i--)Z[$++]=i*X,Z[$++]=1;for(i=1;i<rt+1;i++)tt=it.CesiumMath.PI_OVER_TWO+et*i,Z[$++]=X*(1+Math.cos(tt)),Z[$++]=.5*(1+Math.sin(tt))}else{for(X=1/((f/=3)-1),Q=1/((y/=3)-1),i=0;i<y;i++)Z[$++]=i*Q,Z[$++]=0;for(i=f;0<i;i--)Z[$++]=(i-1)*X,Z[$++]=1}m.st=new st.GeometryAttribute({componentDatatype:nt.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:Z})}return e.normal&&(m.normal=new st.GeometryAttribute({componentDatatype:nt.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E.normals})),e.tangent&&(m.tangent=new st.GeometryAttribute({componentDatatype:nt.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E.tangents})),e.bitangent&&(m.bitangent=new st.GeometryAttribute({componentDatatype:nt.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E.bitangents})),{attributes:m,indices:N}}function k(t,e,r){r[e++]=t[0],r[e++]=t[1],r[e++]=t[2];for(var a=3;a<t.length;a+=3){var i=t[a],o=t[a+1],n=t[a+2];r[e++]=i,r[e++]=o,r[e++]=n,r[e++]=i,r[e++]=o,r[e++]=n}return r[e++]=t[0],r[e++]=t[1],r[e++]=t[2],r}function A(t,e){var r=new D.VertexFormat({position:e.position,normal:e.normal||e.bitangent||t.shadowVolume,tangent:e.tangent,bitangent:e.normal||e.bitangent,st:e.st}),a=t.ellipsoid,i=I(ut.CorridorGeometryLibrary.computePositions(t),r,a),o=t.height,n=t.extrudedHeight,s=i.attributes,l=i.indices,d=s.position.values,u=d.length,m=new Float64Array(6*u),f=new Float64Array(u);f.set(d);var y,c=new Float64Array(4*u);c=k(d=M.PolygonPipeline.scaleToGeodeticHeight(d,o,a),0,c),c=k(f=M.PolygonPipeline.scaleToGeodeticHeight(f,n,a),2*u,c),m.set(d),m.set(f,u),m.set(c,2*u),s.position.values=m,s=function(t,e){if(!(e.normal||e.tangent||e.bitangent||e.st))return t;var r,a,i=t.position.values;(e.normal||e.bitangent)&&(r=t.normal.values,a=t.bitangent.values);var o,n=t.position.values.length/18,s=3*n,l=2*n,d=2*s;if(e.normal||e.bitangent||e.tangent){var u=e.normal?new Float32Array(6*s):void 0,m=e.tangent?new Float32Array(6*s):void 0,f=e.bitangent?new Float32Array(6*s):void 0,y=mt,c=ft,p=yt,h=ct,g=O,b=pt,C=d;for(o=0;o<s;o+=3){var v=C+d;y=ot.Cartesian3.fromArray(i,o,y),c=ot.Cartesian3.fromArray(i,o+s,c),p=ot.Cartesian3.fromArray(i,(o+3)%s,p),c=ot.Cartesian3.subtract(c,y,c),p=ot.Cartesian3.subtract(p,y,p),h=ot.Cartesian3.normalize(ot.Cartesian3.cross(c,p,h),h),e.normal&&(ut.CorridorGeometryLibrary.addAttribute(u,h,v),ut.CorridorGeometryLibrary.addAttribute(u,h,v+3),ut.CorridorGeometryLibrary.addAttribute(u,h,C),ut.CorridorGeometryLibrary.addAttribute(u,h,C+3)),(e.tangent||e.bitangent)&&(b=ot.Cartesian3.fromArray(r,o,b),e.bitangent&&(ut.CorridorGeometryLibrary.addAttribute(f,b,v),ut.CorridorGeometryLibrary.addAttribute(f,b,v+3),ut.CorridorGeometryLibrary.addAttribute(f,b,C),ut.CorridorGeometryLibrary.addAttribute(f,b,C+3)),e.tangent&&(g=ot.Cartesian3.normalize(ot.Cartesian3.cross(b,h,g),g),ut.CorridorGeometryLibrary.addAttribute(m,g,v),ut.CorridorGeometryLibrary.addAttribute(m,g,v+3),ut.CorridorGeometryLibrary.addAttribute(m,g,C),ut.CorridorGeometryLibrary.addAttribute(m,g,C+3))),C+=6}if(e.normal){for(u.set(r),o=0;o<s;o+=3)u[o+s]=-r[o],u[o+s+1]=-r[o+1],u[o+s+2]=-r[o+2];t.normal.values=u}else t.normal=void 0;if(e.bitangent?(f.set(a),f.set(a,s),t.bitangent.values=f):t.bitangent=void 0,e.tangent){var A=t.tangent.values;m.set(A),m.set(A,s),t.tangent.values=m}}if(e.st){var _=t.st.values,w=new Float32Array(6*l);w.set(_),w.set(_,l);for(var T=2*l,G=0;G<2;G++){for(w[T++]=_[0],w[T++]=_[1],o=2;o<l;o+=2){var E=_[o],V=_[o+1];w[T++]=E,w[T++]=V,w[T++]=E,w[T++]=V}w[T++]=_[0],w[T++]=_[1]}t.st.values=w}return t}(s,e);var p=u/3;if(t.shadowVolume){var h=s.normal.values;u=h.length;var g=new Float32Array(6*u);for(y=0;y<u;y++)h[y]=-h[y];g.set(h,u),g=k(h,4*u,g),s.extrudeDirection=new st.GeometryAttribute({componentDatatype:nt.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:g}),e.normal||(s.normal=void 0)}if(at.defined(t.offsetAttribute)){var b=new Uint8Array(6*p);if(t.offsetAttribute===N.GeometryOffsetAttribute.TOP)b=x.arrayFill(b,1,0,p),b=x.arrayFill(b,1,2*p,4*p);else{var C=t.offsetAttribute===N.GeometryOffsetAttribute.NONE?0:1;b=x.arrayFill(b,C)}s.applyOffset=new st.GeometryAttribute({componentDatatype:nt.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:b})}var v=l.length,A=p+p,_=dt.IndexDatatype.createTypedArray(m.length/3,2*v+3*A);_.set(l);var w,T,G,E,V=v;for(y=0;y<v;y+=3){var F=l[y],L=l[y+1],P=l[y+2];_[V++]=P+p,_[V++]=L+p,_[V++]=F+p}for(y=0;y<A;y+=2)G=(w=y+A)+1,E=(T=w+A)+1,_[V++]=w,_[V++]=T,_[V++]=G,_[V++]=G,_[V++]=T,_[V++]=E;return{attributes:s,indices:_}}var _=new ot.Cartesian3,w=new ot.Cartesian3,T=new ot.Cartographic;function G(t,e,r,a,i,o){var n=ot.Cartesian3.subtract(e,t,_);ot.Cartesian3.normalize(n,n);var s=r.geodeticSurfaceNormal(t,w),l=ot.Cartesian3.cross(n,s,_);ot.Cartesian3.multiplyByScalar(l,a,l);var d=i.latitude,u=i.longitude,m=o.latitude,f=o.longitude;ot.Cartesian3.add(t,l,w),r.cartesianToCartographic(w,T);var y=T.latitude,c=T.longitude;d=Math.min(d,y),u=Math.min(u,c),m=Math.max(m,y),f=Math.max(f,c),ot.Cartesian3.subtract(t,l,w),r.cartesianToCartographic(w,T),y=T.latitude,c=T.longitude,d=Math.min(d,y),u=Math.min(u,c),m=Math.max(m,y),f=Math.max(f,c),i.latitude=d,i.longitude=u,o.latitude=m,o.longitude=f}var E=new ot.Cartesian3,V=new ot.Cartesian3,F=new ot.Cartographic,L=new ot.Cartographic;function y(t,e,r,a,i){t=v(t,e);var o=C.arrayRemoveDuplicates(t,ot.Cartesian3.equalsEpsilon),n=o.length;if(n<2||r<=0)return new h.Rectangle;var s,l,d=.5*r;if(F.latitude=Number.POSITIVE_INFINITY,F.longitude=Number.POSITIVE_INFINITY,L.latitude=Number.NEGATIVE_INFINITY,L.longitude=Number.NEGATIVE_INFINITY,a===c.CornerType.ROUNDED){var u=o[0];ot.Cartesian3.subtract(u,o[1],E),ot.Cartesian3.normalize(E,E),ot.Cartesian3.multiplyByScalar(E,d,E),ot.Cartesian3.add(u,E,V),e.cartesianToCartographic(V,T),s=T.latitude,l=T.longitude,F.latitude=Math.min(F.latitude,s),F.longitude=Math.min(F.longitude,l),L.latitude=Math.max(L.latitude,s),L.longitude=Math.max(L.longitude,l)}for(var m=0;m<n-1;++m)G(o[m],o[m+1],e,d,F,L);var f=o[n-1];ot.Cartesian3.subtract(f,o[n-2],E),ot.Cartesian3.normalize(E,E),ot.Cartesian3.multiplyByScalar(E,d,E),ot.Cartesian3.add(f,E,V),G(f,V,e,d,F,L),a===c.CornerType.ROUNDED&&(e.cartesianToCartographic(V,T),s=T.latitude,l=T.longitude,F.latitude=Math.min(F.latitude,s),F.longitude=Math.min(F.longitude,l),L.latitude=Math.max(L.latitude,s),L.longitude=Math.max(L.longitude,l));var y=at.defined(i)?i:new h.Rectangle;return y.north=L.latitude,y.south=F.latitude,y.east=L.longitude,y.west=F.longitude,y}function P(t){var e=(t=at.defaultValue(t,at.defaultValue.EMPTY_OBJECT)).positions,r=t.width;p.Check.defined("options.positions",e),p.Check.defined("options.width",r);var a=at.defaultValue(t.height,0),i=at.defaultValue(t.extrudedHeight,a);this._positions=e,this._ellipsoid=h.Ellipsoid.clone(at.defaultValue(t.ellipsoid,h.Ellipsoid.WGS84)),this._vertexFormat=D.VertexFormat.clone(at.defaultValue(t.vertexFormat,D.VertexFormat.DEFAULT)),this._width=r,this._height=Math.max(a,i),this._extrudedHeight=Math.min(a,i),this._cornerType=at.defaultValue(t.cornerType,c.CornerType.ROUNDED),this._granularity=at.defaultValue(t.granularity,it.CesiumMath.RADIANS_PER_DEGREE),this._shadowVolume=at.defaultValue(t.shadowVolume,!1),this._workerName="createCorridorGeometry",this._offsetAttribute=t.offsetAttribute,this._rectangle=void 0,this.packedLength=1+e.length*ot.Cartesian3.packedLength+h.Ellipsoid.packedLength+D.VertexFormat.packedLength+7}P.pack=function(t,e,r){p.Check.defined("value",t),p.Check.defined("array",e),r=at.defaultValue(r,0);var a=t._positions,i=a.length;e[r++]=i;for(var o=0;o<i;++o,r+=ot.Cartesian3.packedLength)ot.Cartesian3.pack(a[o],e,r);return h.Ellipsoid.pack(t._ellipsoid,e,r),r+=h.Ellipsoid.packedLength,D.VertexFormat.pack(t._vertexFormat,e,r),r+=D.VertexFormat.packedLength,e[r++]=t._width,e[r++]=t._height,e[r++]=t._extrudedHeight,e[r++]=t._cornerType,e[r++]=t._granularity,e[r++]=t._shadowVolume?1:0,e[r]=at.defaultValue(t._offsetAttribute,-1),e};var S=h.Ellipsoid.clone(h.Ellipsoid.UNIT_SPHERE),R=new D.VertexFormat,H={positions:void 0,ellipsoid:S,vertexFormat:R,width:void 0,height:void 0,extrudedHeight:void 0,cornerType:void 0,granularity:void 0,shadowVolume:void 0,offsetAttribute:void 0};return P.unpack=function(t,e,r){p.Check.defined("array",t),e=at.defaultValue(e,0);for(var a=t[e++],i=new Array(a),o=0;o<a;++o,e+=ot.Cartesian3.packedLength)i[o]=ot.Cartesian3.unpack(t,e);var n=h.Ellipsoid.unpack(t,e,S);e+=h.Ellipsoid.packedLength;var s=D.VertexFormat.unpack(t,e,R);e+=D.VertexFormat.packedLength;var l=t[e++],d=t[e++],u=t[e++],m=t[e++],f=t[e++],y=1===t[e++],c=t[e];return at.defined(r)?(r._positions=i,r._ellipsoid=h.Ellipsoid.clone(n,r._ellipsoid),r._vertexFormat=D.VertexFormat.clone(s,r._vertexFormat),r._width=l,r._height=d,r._extrudedHeight=u,r._cornerType=m,r._granularity=f,r._shadowVolume=y,r._offsetAttribute=-1===c?void 0:c,r):(H.positions=i,H.width=l,H.height=d,H.extrudedHeight=u,H.cornerType=m,H.granularity=f,H.shadowVolume=y,H.offsetAttribute=-1===c?void 0:c,new P(H))},P.computeRectangle=function(t,e){var r=(t=at.defaultValue(t,at.defaultValue.EMPTY_OBJECT)).positions,a=t.width;return p.Check.defined("options.positions",r),p.Check.defined("options.width",a),y(r,at.defaultValue(t.ellipsoid,h.Ellipsoid.WGS84),a,at.defaultValue(t.cornerType,c.CornerType.ROUNDED),e)},P.createGeometry=function(t){var e=t._positions,r=t._width,a=t._ellipsoid;e=v(e,a);var i=C.arrayRemoveDuplicates(e,ot.Cartesian3.equalsEpsilon);if(!(i.length<2||r<=0)){var o,n=t._height,s=t._extrudedHeight,l=!it.CesiumMath.equalsEpsilon(n,s,0,it.CesiumMath.EPSILON2),d=t._vertexFormat,u={ellipsoid:a,positions:i,width:r,cornerType:t._cornerType,granularity:t._granularity,saveAttributes:!0};if(l)u.height=n,u.extrudedHeight=s,u.shadowVolume=t._shadowVolume,u.offsetAttribute=t._offsetAttribute,o=A(u,d);else if((o=I(ut.CorridorGeometryLibrary.computePositions(u),d,a)).attributes.position.values=M.PolygonPipeline.scaleToGeodeticHeight(o.attributes.position.values,n,a),at.defined(t._offsetAttribute)){var m=t._offsetAttribute===N.GeometryOffsetAttribute.NONE?0:1,f=o.attributes.position.values.length,y=new Uint8Array(f/3);x.arrayFill(y,m),o.attributes.applyOffset=new st.GeometryAttribute({componentDatatype:nt.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:y})}var c=o.attributes,p=g.BoundingSphere.fromVertices(c.position.values,void 0,3);return d.position||(o.attributes.position.values=void 0),new st.Geometry({attributes:c,indices:o.indices,primitiveType:b.PrimitiveType.TRIANGLES,boundingSphere:p,offsetAttribute:t._offsetAttribute})}},P.createShadowVolume=function(t,e,r){var a=t._granularity,i=t._ellipsoid,o=e(a,i),n=r(a,i);return new P({positions:t._positions,width:t._width,cornerType:t._cornerType,ellipsoid:i,granularity:a,extrudedHeight:o,height:n,vertexFormat:D.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(P.prototype,{rectangle:{get:function(){return at.defined(this._rectangle)||(this._rectangle=y(this._positions,this._ellipsoid,this._width,this._cornerType)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return[0,0,0,1,1,0]}}}),function(t,e){return at.defined(e)&&(t=P.unpack(t,e)),t._ellipsoid=h.Ellipsoid.clone(t._ellipsoid),P.createGeometry(t)}});