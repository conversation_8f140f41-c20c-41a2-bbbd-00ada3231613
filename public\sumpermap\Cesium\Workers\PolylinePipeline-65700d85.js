/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./EllipsoidRhumbLine-6ca4b1e6","./EllipsoidGeodesic-db2069b3"],function(a,w,P,T,y,b,v,m,A,E,e){var S={numberOfPoints:function(a,e,r){var i=y.Cartesian3.distance(a,e);return Math.ceil(i/r)},numberOfPointsRhumbLine:function(a,e,r){var i=Math.pow(a.longitude-e.longitude,2)+Math.pow(a.latitude-e.latitude,2);return Math.ceil(Math.sqrt(i/(r*r)))}},o=new y.Cartographic;S.extractHeights=function(a,e){for(var r=a.length,i=new Array(r),n=0;n<r;n++){var t=a[n];i[n]=e.cartesianToCartographic(t,o).height}return i};var M=new v.Matrix4,R=new y.Cartesian3,D=new y.Cartesian3,x=new A.Plane(y.Cartesian3.UNIT_X,0),G=new y.Cartesian3,I=new A.Plane(y.Cartesian3.UNIT_X,0),N=new y.Cartesian3,k=new y.Cartesian3,V=[];function L(a,e,r){var i,n=V;if(n.length=a,e===r){for(i=0;i<a;i++)n[i]=e;return n}var t=(r-e)/a;for(i=0;i<a;i++){var o=e+i*t;n[i]=o}return n}var _=new y.Cartographic,B=new y.Cartographic,O=new y.Cartesian3,U=new y.Cartesian3,q=new y.Cartesian3,z=new e.EllipsoidGeodesic,X=new E.EllipsoidRhumbLine;function W(a,e,r,i,n,t,o,s,c){var l=i.scaleToGeodeticSurface(a,U),u=i.scaleToGeodeticSurface(e,q),h=S.numberOfPoints(a,e,r),f=i.cartesianToCartographic(l,_),d=i.cartesianToCartographic(u,B),p=L(h,n,t);0<c&&(p=function(a,e){var r=V;r.length=a;for(var i=0;i<a;i++)r[i]=e*Math.sin(Math.PI*i/a);return r}(h,c)),z.setEndPoints(f,d);var g=z.surfaceDistance/h,C=s;f.height=n;var v=i.cartographicToCartesian(f,O);y.Cartesian3.pack(v,o,C),C+=3;for(var m=1;m<h;m++){var w=z.interpolateUsingSurfaceDistance(m*g,B);w.height=p[m],v=i.cartographicToCartesian(w,O),y.Cartesian3.pack(v,o,C),C+=3}return C}function Y(a,e,r,i,n,t,o,s){var c=i.scaleToGeodeticSurface(a,U),l=i.scaleToGeodeticSurface(e,q),u=i.cartesianToCartographic(c,_),h=i.cartesianToCartographic(l,B),f=S.numberOfPointsRhumbLine(u,h,r),d=L(f,n,t);X.ellipsoid.equals(i)||(X=new E.EllipsoidRhumbLine(void 0,void 0,i)),X.setEndPoints(u,h);var p=X.surfaceDistance/f,g=s;u.height=n;var C=i.cartographicToCartesian(u,O);y.Cartesian3.pack(C,o,g),g+=3;for(var v=1;v<f;v++){var m=X.interpolateUsingSurfaceDistance(v*p,B);m.height=d[v],C=i.cartographicToCartesian(m,O),y.Cartesian3.pack(C,o,g),g+=3}return g}S.wrapLongitude=function(a,e){var r=[],i=[];if(w.defined(a)&&0<a.length){e=w.defaultValue(e,v.Matrix4.IDENTITY);var n=v.Matrix4.inverseTransformation(e,M),t=v.Matrix4.multiplyByPoint(n,y.Cartesian3.ZERO,R),o=y.Cartesian3.normalize(v.Matrix4.multiplyByPointAsVector(n,y.Cartesian3.UNIT_Y,D),D),s=A.Plane.fromPointNormal(t,o,x),c=y.Cartesian3.normalize(v.Matrix4.multiplyByPointAsVector(n,y.Cartesian3.UNIT_X,G),G),l=A.Plane.fromPointNormal(t,c,I),u=1;r.push(y.Cartesian3.clone(a[0]));for(var h=r[0],f=a.length,d=1;d<f;++d){var p=a[d];if(A.Plane.getPointDistance(l,h)<0||A.Plane.getPointDistance(l,p)<0){var g=m.IntersectionTests.lineSegmentPlane(h,p,s,N);if(w.defined(g)){var C=y.Cartesian3.multiplyByScalar(o,5e-9,k);A.Plane.getPointDistance(s,h)<0&&y.Cartesian3.negate(C,C),r.push(y.Cartesian3.add(g,C,new y.Cartesian3)),i.push(u+1),y.Cartesian3.negate(C,C),r.push(y.Cartesian3.add(g,C,new y.Cartesian3)),u=1}}r.push(y.Cartesian3.clone(a[d])),u++,h=p}i.push(u)}return{positions:r,lengths:i}},S.generateArc=function(a){w.defined(a)||(a={});var e=a.positions;if(!w.defined(e))throw new P.DeveloperError("options.positions is required.");var r=e.length,i=w.defaultValue(a.ellipsoid,b.Ellipsoid.WGS84),n=w.defaultValue(a.height,0),t=Array.isArray(n);if(r<1)return[];if(1===r){var o=i.scaleToGeodeticSurface(e[0],U);if(0!==(n=t?n[0]:n)){var s=i.geodeticSurfaceNormal(o,O);y.Cartesian3.multiplyByScalar(s,n,s),y.Cartesian3.add(o,s,o)}return[o.x,o.y,o.z]}var c=a.minDistance;if(!w.defined(c)){var l=w.defaultValue(a.granularity,T.CesiumMath.RADIANS_PER_DEGREE);c=T.CesiumMath.chordLength(l,i.maximumRadius)}var u,h=0;for(u=0;u<r-1;u++)h+=S.numberOfPoints(e[u],e[u+1],c);var f=a.hMax,d=3*(h+1),p=new Array(d),g=0;for(u=0;u<r-1;u++){g=W(e[u],e[u+1],c,i,t?n[u]:n,t?n[u+1]:n,p,g,f)}V.length=0;var C=e[r-1],v=i.cartesianToCartographic(C,_);v.height=t?n[r-1]:n;var m=i.cartographicToCartesian(v,O);return y.Cartesian3.pack(m,p,d-3),p};var H=new y.Cartographic,Z=new y.Cartographic;S.generateRhumbArc=function(a){w.defined(a)||(a={});var e=a.positions;if(!w.defined(e))throw new P.DeveloperError("options.positions is required.");var r=e.length,i=w.defaultValue(a.ellipsoid,b.Ellipsoid.WGS84),n=w.defaultValue(a.height,0),t=Array.isArray(n);if(r<1)return[];if(1===r){var o=i.scaleToGeodeticSurface(e[0],U);if(0!==(n=t?n[0]:n)){var s=i.geodeticSurfaceNormal(o,O);y.Cartesian3.multiplyByScalar(s,n,s),y.Cartesian3.add(o,s,o)}return[o.x,o.y,o.z]}var c,l,u=w.defaultValue(a.granularity,T.CesiumMath.RADIANS_PER_DEGREE),h=0,f=i.cartesianToCartographic(e[0],H);for(c=0;c<r-1;c++)l=i.cartesianToCartographic(e[c+1],Z),h+=S.numberOfPointsRhumbLine(f,l,u),f=y.Cartographic.clone(l,H);var d=3*(h+1),p=new Array(d),g=0;for(c=0;c<r-1;c++){g=Y(e[c],e[c+1],u,i,t?n[c]:n,t?n[c+1]:n,p,g)}V.length=0;var C=e[r-1],v=i.cartesianToCartographic(C,_);v.height=t?n[r-1]:n;var m=i.cartographicToCartesian(v,O);return y.Cartesian3.pack(m,p,d-3),p},S.generateCartesianArc=function(a){for(var e=S.generateArc(a),r=e.length/3,i=new Array(r),n=0;n<r;n++)i[n]=y.Cartesian3.unpack(e,3*n);return i},S.generateCartesianRhumbArc=function(a){for(var e=S.generateRhumbArc(a),r=e.length/3,i=new Array(r),n=0;n<r;n++)i[n]=y.Cartesian3.unpack(e,3*n);return i},a.PolylinePipeline=S});