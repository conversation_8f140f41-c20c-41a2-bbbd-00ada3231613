<template>
  <el-container id="login-content">
    <el-header class="content-header">
      <div class="header-title">
        新能源管控平台
      </div>
    </el-header>
    <el-main class="content-main">
      <div class="main-form">
        <div class="form-title">
          账号登录
        </div>
        <div class="form-content">
          <el-form :model="form" :rules="rules" ref="form" label-width="60px">
            <el-form-item label="账号" prop="account">
              <el-input type="username" v-model="form.account" prefix-icon="el-icon-user"></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input type="password" v-model="form.password" prefix-icon="el-icon-lock"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :loading="loading" class="account-btn" @click="submit">登录</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-main>
    <!-- <el-footer class="content-footer">
      <p>
        <span class="img"></span>
        版权所有：中国国家铁路集团有限公司工程管理中心
      </p>
    </el-footer> -->
  </el-container>
</template>

<script>
import JSEncrypt from 'jsencrypt';
import { login, getPublicKey } from '@/api/baseApi/user';

export default {
  name: 'Login',
  data() {
    return {
      loading: false,
      form: {
        account: '',
        password: '',
      },
      rules: {
        account: [
          { required: true, message: '请输入账号', trigger: ['blur', 'change'] },
          { min: 2, max: 15, message: '长度在2到15个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: ['blur', 'change'] },
          { min: 2, max: 20, message: '长度在2到20个字符', trigger: 'blur' }
        ]
      },
      publicKey: '',
    }
  },
  created() {
    this.getPublicKey();
  },

  methods: {
    // 获取公钥
    async getPublicKey() {
      let res = await getPublicKey();
      if (res.code == 0) {
        this.publicKey = res.result;
      }
    },

    /**
  * 加密
  * @param {String}  需要加密的参数
  */
    passwordEncryption(param) {
      // 后台给的公钥
      let encryptor = new JSEncrypt() // 新建JSEncrypt对象
      encryptor.setPublicKey(this.publicKey) // 设置公钥
      let passwordEncryp = encryptor.encrypt(param) // 对密码进行加密
      return passwordEncryp
    },

    // 登录
    // 登录
    async submit() {
      this.loading = true;
      const valid = await this.$refs.form.validate();
      if (valid) {
        let param = {
          ...this.form,
          password: this.passwordEncryption(this.form.password),
        };
        try {
          let res = await login(param);
          if (res.code == 0) {
            this.$store.dispatch('setUserInfo', res?.result || {});
            this.$store.dispatch('setUserToken', res.result?.token || null);

            // 确保用户信息已更新后再获取权限
            await this.$nextTick(); // 等待 DOM 更新

            // 根据用户id获取用户角色(权限)
            let res2 = await this.$http.get(`/equip/tree/getRoleByUserId?userId=${this.$store.state.user.userInfo.id}`);
            let permissionsType = res2.result[0]?.type || []; // 假设权限在 data.permissions 中
            console.log('登录时获取的用户权限是:', permissionsType);
            //根据permissionsType设置用户权限，目前权限为静态数组内的内容
            let userPermissions = permissionsType === 3 ? ['manage', 'monitor'] : ['admin'];
            this.$store.dispatch('setUserPermissions', userPermissions);

            console.log('Store state after dispatch:', this.$store.state.user); // 调试信息
            this.$router.push('/devices/monitor');
          } else {
            this.$message.error(res.msg);
          }
        } catch (error) {
          this.$message.error('登录失败，请稍后再试');
          console.error('登录失败:', error);
        }
      } else {
        this.$message.error('账号或密码校验失败');
      }
      this.loading = false;
    },
  }
}
</script>

<style lang="scss" scoped>
#login-content {
  width: 100vw;
  height: 100vh;
  background: url('/src/assets/login/bg.jpeg') no-repeat;
  background-size: 100% 100%;

  .content-header {
    width: 100%;
    height: 220px !important;
    padding: 0 30px;
    display: flex;
    justify-content: center;
    align-items: center;

    .header-title {
      height: 100px;
      font-size: 32px;
      color: #fff;
      line-height: 120px;
    }
  }

  .content-main {
    width: 100%;
    height: calc(100% - 270px);
    display: flex;
    justify-content: center;
    align-items: center;

    .main-form {
      margin-top: -140px;
      width: 380px;
      min-height: 350px;
      background: url('/src/assets/login/form-bg.png') no-repeat;
      background-size: 100%;
      padding: 0 20px;

      .form-title {
        width: 100%;
        height: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        font-weight: 600;
        color: #555555;
      }

      .form-content {
        width: 100%;
        height: 190px;
        margin-top: 80px;

        .account-btn {
          width: 200px;
          margin: 10px 0 0 20px;
        }
      }
    }
  }

  .content-footer {
    width: 100%;
    height: 50px !important;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;

    p {
      font-size: 16px;
    }

    .img {
      width: 40px;
      height: 40px;
      background: url('/src/assets/login/copyright.png') no-repeat;
      background-size: contain;
      display: inline-block;
      vertical-align: middle;
    }
  }

  .el-button {
    background-color: #0F6BAD;
  }
}
</style>
