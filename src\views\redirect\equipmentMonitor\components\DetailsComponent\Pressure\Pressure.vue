<template>
    <div id="pressure-wrapper"></div>
</template>

<script>
export default {
    name: 'Pressure',
    props: ["deviceInfo", "deviceId", "sectionId"],
    data() {
        return {
            time: ['2025-04-02 11:25:00', '2025-04-02 11:55:00', '2025-04-02 12:15:10', '2025-04-02 12:35:40', '2025-04-02 13:05:10', '13:55:10', '14:25:30'],
            hightData: [20, 32, 70, 34, 50, 23, 71],
            lowData: [22, 18, 19, 34, 30, 33, 31],
            preDiff: [50, 32, 20, 54, 40, 30, 41]            
        }
    },
    mounted() {
        
    },
    watch: {
        "deviceInfo.id": {
            handler(val) {
                this.getPressureInfo();
            },
            deep: true
        },
    },

    methods: {
        async getPressureInfo() {
            let time = [];
            let hight = [];
            let low = [];
            let temp = [];
            let res = await this.$http.get(`/equip/build/cellVoltageLineChart?vehicleIdentify=${this.deviceId}&sectionId=${this.sectionId||''}`);
            res?.result?.map(i=>{
                const timestamp = new Date(i.hour).getTime();
                time.push(timestamp);
                hight.push(i.maxValues);
                low.push(i.minxValues);
                temp.push(i.subtraction);
            })
            this.time = time;
            this.hightData = hight;
            this.lowData = low;
            this.preDiff = temp;
            this.setChart();
        },
        // 初始化
        setChart() {
            let _ = this;
            let myChart = _.$echarts.init(
                document.getElementById("pressure-wrapper"),
                null,
                {
                    renderer: "svg",
                }
            );
            myChart.setOption(
                {
                    color: ['#F58A69', '#60BF79', '#65C1DD'],
                    tooltip: {
                        trigger: 'axis',
                        formatter: (params) => {
                            const date = new Date(params[0].axisValue);
                            return `${date.toLocaleTimeString()}<br/>` + 
                                params.map(p => `${p.marker}${p.seriesName}: ${p.value[1]}V`).join('<br/>')
                        }
                    },
                    legend: {
                        data: ['最高单体', '最低单体', '最大压差'],
                        left: 'right'
                    },
                    grid: {
                        left: '3%',
                        right: '2%',
                        bottom: '1%',
                        top: 30,
                        containLabel: true
                    },
                    xAxis: {
                        type: 'time',
                        axisLabel: {
                            formatter: (value) => {
                                    const date = new Date(value);
                                    return date.getHours().toString().padStart(2, '0') + ':' +
                                        date.getMinutes().toString().padStart(2, '0');
                                }
                        },
                    },
                    yAxis: {
                        type: 'value',
                        name: '单体电压 / V'
                    },
                    series: [
                        {
                            name: '最高单体',
                            type: 'line',
                            data: this.hightData.map((v, i) => [this.time[i], v])
                        },
                        {
                            name: '最低单体',
                            type: 'line',
                            data: this.lowData.map((v, i) => [this.time[i], v])
                        },
                        {
                            name: '最大压差',
                            type: 'line',
                            data: this.preDiff.map((v, i) => [this.time[i], v])
                        }
                    ]
                }
            );
            window.addEventListener("resize", () => {
                myChart.resize();
            });
        },
        
    }
}
</script>

<style lang="scss" scoped>
#pressure-wrapper {
    width: 100%;
    height: 100%;
}
</style>