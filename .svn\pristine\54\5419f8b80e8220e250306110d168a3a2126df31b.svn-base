<template>
  <div id="Sgzbjk">
    <div class="container-left">
      <Card :title="titleData.mechanicalPreview" class="card-preview">
        <div class="img-box">
          <img src="@/assets/images/device-preview.png" alt="机械预览" class="img-bg">
          <div class="box-info">
            <div class="info-left">
              <p>进场总数</p>
              <p class="info-p">{{ currentDate }}</p>
            </div>
            <div class="info-right">{{ deviceInfo.presentNumber || 0 }}</div>
          </div>
        </div>
        <div class="preview-box">
          <div class="num-preview">
            <div class="preview-top">
              <div class="top-left">
                <div class="left-box kai">开</div>
                <div class="left-title">今日开工</div>
                <div class="left-total">
                  <span>{{ deviceInfo.todayStart || 0 }}</span>台
                </div>
              </div>
              <div class="top-right">
                <div class="left-box lu">场</div>
                <div class="left-title">在场数量</div>
                <div class="left-total">
                  <span>{{ deviceInfo.enterTotal || 0 }}</span>台
                </div>
              </div>
            </div>
            <div class="preview-bottom">
              <div class="top-left">
                <div class="left-box xiu">修</div>
                <div class="left-title">累计维修</div>
                <div class="left-total">
                  <span>{{ deviceInfo.totalMaintenance || 0 }}</span>次
                </div>
              </div>
              <div class="top-right">
                <div class="left-box tui">退</div>
                <div class="left-title">累计退场</div>
                <div class="left-total">
                  <span>{{ deviceInfo.totalexit || 0 }}</span>台
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
      <Card :title="titleData.mechanicalType" class="card-type card-item">
        <div class="chart-box">
          <ChartType></ChartType>
        </div>
      </Card>
      <Card :title="titleData.alarmInfo" class="card-info card-item">
        <WarnInfo></WarnInfo>
      </Card>
    </div>
    <div class="container-center">
      <Card :title="titleData.mechanicalMapPreview" class="card-preview">
        <DeviceMap></DeviceMap>
      </Card>
    </div>
    <div class="container-right">
      <Card :title="titleData.devicePreview" class="card-preview">
        <PerceptionDevice></PerceptionDevice>
      </Card>
      <Card :title="titleData.timeDate" :status="dropdownStatus" @getDropdownValue="getDropdownValue"
        class="card-type card-item">
        <WorkHour :dropdownValue="dropdownValue"></WorkHour>
      </Card>
      <Card :title="titleData.elePreview" class="card-info card-item">
        <ElectricityLevel></ElectricityLevel>
      </Card>
    </div>
  </div>
</template>

<script>
import Card from './components/Card/Card.vue';
import ChartType from './components/Chart/ChartType.vue';
import WarnInfo from './components/WarnInfo/WarnInfo.vue';
import PerceptionDevice from './components/PerceptionDevice/PerceptionDevice.vue';
import ElectricityLevel from './components/ElectricityLevel/ElectricityLevel.vue';
import WorkHour from './components/WorkHour/WorkHour.vue';
import DeviceMap from './components/DeviceMap/DeviceMap.vue';
import constDataSet from '@/utils/const';
export default {
  name: 'Sgzbjk',
  components: { Card, ChartType, WarnInfo, PerceptionDevice, ElectricityLevel, WorkHour, DeviceMap },
  data() {
    return {
      titleData: constDataSet.titleData,
      currentDate: '2024-03-08',
      deviceInfo: {},
      dropdownStatus: true,
      dropdownValue: 1,
    }
  },
  mounted() {
    var moment = require('moment');
    this.currentDate = moment().format('YYYY-MM-DD');
  },
  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.getDeviceInfo();
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    async getDeviceInfo() {
      let res = await this.$http.get(`/equip/build/machineryOutline?sectionId=${this.$store.state.tree.sectionId}`);
      this.deviceInfo = res?.result || {}
    },

    getDropdownValue(val) {
      this.dropdownValue = val === '累计' ? 1 : 0
    }
  }
}
</script>

<style lang="scss" scoped>
#Sgzbjk {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;

  .container-left {
    width: 405px;

    .card-preview {
      height: 280px;

      .img-box {
        width: 100%;
        height: 100px;
        background: linear-gradient(45deg, #085F8E, #24CAE1);
        position: relative;

        .img-bg {
          width: 100%;
          height: 100%;
          background-size: contain;
        }

        .box-info {
          position: absolute;
          top: 23px;
          right: 20px;
          width: 260px;
          height: 56px;
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          justify-content: space-between;

          .info-left {
            width: 100px;
            height: 100%;

            p {
              width: 100%;
              height: 28px;
              line-height: 28px;
              text-align: left;
              font-size: 18px;
              font-family: '思源黑体 CN', sans-serif;
              font-weight: 400;
              font-style: normal;
              color: #FFFFFF;
            }

            .info-p {
              font-size: 14px;
            }
          }

          .info-right {
            width: 120px;
            height: 100%;
            text-align: right;
            line-height: 56px;
            font-size: 28px;
            font-family: 'Courier New', Courier, monospace;
            font-weight: 600;
            color: #30E5F4;
          }
        }
      }

      .preview-box {
        width: 100%;
        height: 110px;
        border: 1px solid #eee;
        margin: 10px 0;
        padding: 15px;

        .num-preview {
          width: 100%;
          height: 100%;

          .preview-top {
            width: 100%;
            height: 30px;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
          }

          .preview-bottom {
            width: 100%;
            height: 30px;
            margin-top: 15px;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
          }

          .top-left {
            width: 50%;
            height: 100%;
            line-height: 30px;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            font-size: 14px;
          }

          .top-right {
            width: 45%;
            height: 100%;
            line-height: 30px;
            margin-left: 5%;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            font-size: 14px;
          }

          .left-box {
            width: 30px;
            height: 100%;
            text-align: center;
            color: #fff;
            border-radius: 5px;
          }

          .left-title {
            margin: 0 10px;
          }

          .left-total {
            color: #3E7FF0;
          }

          .kai {
            background-color: #76D6EA;
          }

          .lu {
            background-color: #34D4B9;
          }

          .xiu {
            background-color: #F69B6C;
          }

          .tui {
            background-color: #A7A7A7;
          }
        }
      }
    }

    .card-type {
      height: 325px;

      .chart-box {
        width: 385px;
        height: 265px;
      }
    }

    .card-info {
      height: 315px;
    }
  }

  .container-center {
    height: 960px;
    flex: 1;
    margin: 0 10px;

    .card-preview {
      width: 100%;
      height: 100%;
    }
  }

  .container-right {
    width: 390px;

    .card-preview {
      height: 280px;
    }

    .card-type {
      height: 325px;
    }

    .card-info {
      height: 315px;
    }
  }

  .card-item {
    margin-top: 20px;
  }
}
</style>