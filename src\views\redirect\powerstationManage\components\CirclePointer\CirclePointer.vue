<template>
    <div class="circle-container">
        <div class="circle-pointer">
            <img :src="src" class="circle-img"/>
            <div class="pointer" :style="pointerStyle"></div>
        </div>
        <div class="text">{{ desc }}</div>
        <div class="value">{{ `${currentPointer?.toFixed(2)} `+quantifier }}</div>
    </div>

  </template>
  
  <script>

  export default {
    props:{
        currentPointer:Number,
        pointerScale:Number,
        src:String,
        quantifier:String,
        desc:String,
    },
    data() {
      return {
      };
    },
    computed: {
      pointerStyle() {
        const angle = this.currentPointer * (240 / this.pointerScale) - 120;
        return {
            'transform': `rotate(${angle}deg) translate(0, -50%)`,
        };
      },
    },
    methods: {
      setPointer(newPointer) {
        this.currentPointer = newPointer;
      },
    },
  };
  </script>
  
<style lang="scss" scoped>
.circle-container{
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-items: center;
    width: 100%;
    padding:10%;
    border: 1px solid #00A7E9;
    border-radius: 5px;
    background-image: linear-gradient(to bottom, #F3F6FF, white);
    .circle-pointer {
        display: flex;
        width:100%;
        position: relative;

        .circle-img{
            width: 100%;
            
        }
        @keyframes rotatePointer {
            from {
                transform: rotate(-120deg) translate(0, -50%);
            }
            to {
                transform: rotate('calc(this.currentPointer * (240 / this.pointerScale) - 120)'deg) translate(0, -50%);
            }
        }
        
        .pointer {
            width: 5px;
            height: 40%;
            background-image: linear-gradient(to bottom, #0098EC, #00C8E2);
            position: absolute;
            top: 38%;
            left: 50%;
            border-radius: 2px;
            animation: rotatePointer 0.5s ease-in-out;
        }
    }
    .text{
        font-family: '思源黑体 CN', sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: 14px;
        color: #3A3F5D;
    }
    .value{
        font-family: '优设标题黑', sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: 22px;
        color: #3A3F5D;
    }
}

</style>