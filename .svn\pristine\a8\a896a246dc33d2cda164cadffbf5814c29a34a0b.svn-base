import Vue from "vue";
import Vuex from "vuex";
import tagsView from "./modules/tagsView";
import settings from "./modules/settings";
import user from "./modules/user";
import tree from "./modules/tree";
import createPersistedState from "vuex-persistedstate";

Vue.use(Vuex);

const store = new Vuex.Store({
  // 持久化
  plugins: [createPersistedState()],
  modules: {
    tagsView,
    settings,
    user,
    tree
  },
});
export default store;
