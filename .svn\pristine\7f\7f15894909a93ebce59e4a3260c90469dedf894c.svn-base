import axios from "axios";

// 创建一个axios实例
const service = axios.create({
  // baseURL: "http://101.207.130.161:8071/",
  baseURL: "http://10.48.252.83:8001/",
  // timeout: 5000,
});
// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // if (store.getters.token) {
    //   config.headers["x-Token"] = getToken();
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use((response) => {
  //   if (response.headers.token) {
  //     sessionStorage.setItem("token", response.headers.token);
  //   }
  //自定义设置后台返回code对应的响应方式
  if (response.status === 200) {
    return response.data;
  } else {
    Promise.reject();
  }
});
export default service;
