{"name": "xnyzb", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "prod": "vue-cli-service serve --mode production"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "axios": "^0.27.2", "chart.js": "^2.9.4", "core-js": "^3.8.3", "echarts": "^4.9.0", "element-ui": "^2.15.10", "file-saver": "^2.0.5", "jquery": "^3.7.1", "js-cookie": "^2.2.0", "jsencrypt": "^3.3.2", "moment": "^2.30.1", "ol": "^9.1.0", "path-browserify": "^1.0.1", "postcss-px-to-viewport": "^1.1.1", "sass": "^1.26.8", "sass-loader": "^8.0.2", "screenfull": "^5.1.0", "video.js": "^8.17.3", "vue": "^2.6.14", "vue-amap": "^0.5.10", "vue-jsx": "^0.2.9", "vue-router": "^3.5.1", "vue-seamless-scroll": "^1.1.23", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0", "x2js": "^3.4.4", "xlsx": "^0.14.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@types/chart.js": "^2.9.41", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^6.1.0", "less-loader": "^8.0.0", "lint-staged": "^11.1.2", "script-loader": "^0.7.2", "svg-sprite-loader": "^6.0.11", "vue-template-compiler": "^2.6.14"}, "gitHooks": {"pre-commit": "lint-staged"}}