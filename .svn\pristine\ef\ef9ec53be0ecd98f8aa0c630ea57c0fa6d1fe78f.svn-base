<template>
  <div class="card-container">
    <div class="header">
      <div class="header-title">{{ title }}</div>
    </div>
    <div class="card-body">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: "card",
  props: ["title", "status"],
  data() {
    return {};
  },
  mounted() {},

  methods: {},
};
</script>

<style lang="scss" scoped>
.card-container {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border: 1px solid #6299c7;

  .header {
    width: 100%;
    height: 39px;
    background: linear-gradient(45deg, #d0e4f9, #fbfdff);
    border-bottom: 1px solid #6299c7;
    line-height: 39px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;

    .header-title {
      width: 150px;
      height: 100%;
      margin-left: 20px;
      font-family: "微软雅黑 Bold", "微软雅黑 Regular", "微软雅黑", sans-serif;
      font-weight: 700;
      font-style: normal;
      font-size: 16px;
      color: #3a3f5d;
    }
  }

  .card-body {
    padding: 10px;
    min-height: 100px;
  }
}
</style>
