/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240"],function(e,R,S,N){function A(e,t,n){this.x=R.defaultValue(e,0),this.y=R.defaultValue(t,0),this.z=R.defaultValue(n,0)}A.fromSpherical=function(e,t){S.Check.typeOf.object("spherical",e),R.defined(t)||(t=new A);var n=e.clock,r=e.cone,a=R.defaultValue(e.magnitude,1),i=a*Math.sin(r);return t.x=i*Math.cos(n),t.y=i*Math.sin(n),t.z=a*Math.cos(r),t},A.fromElements=function(e,t,n,r){return R.defined(r)?(r.x=e,r.y=t,r.z=n,r):new A(e,t,n)},A.fromCartesian4=A.clone=function(e,t){if(R.defined(e))return R.defined(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t):new A(e.x,e.y,e.z)},A.packedLength=3,A.pack=function(e,t,n){return S.Check.typeOf.object("value",e),S.Check.defined("array",t),n=R.defaultValue(n,0),t[n++]=e.x,t[n++]=e.y,t[n]=e.z,t},A.unpack=function(e,t,n){return S.Check.defined("array",e),t=R.defaultValue(t,0),R.defined(n)||(n=new A),n.x=e[t++],n.y=e[t++],n.z=e[t],n},A.packArray=function(e,t){S.Check.defined("array",e);var n=e.length;R.defined(t)?t.length=3*n:t=new Array(3*n);for(var r=0;r<n;++r)A.pack(e[r],t,3*r);return t},A.unpackArray=function(e,t){if(S.Check.defined("array",e),S.Check.typeOf.number.greaterThanOrEquals("array.length",e.length,3),e.length%3!=0)throw new S.DeveloperError("array length must be a multiple of 3.");var n=e.length;R.defined(t)?t.length=n/3:t=new Array(n/3);for(var r=0;r<n;r+=3){var a=r/3;t[a]=A.unpack(e,r,t[a])}return t},A.fromArray=A.unpack,A.maximumComponent=function(e){return S.Check.typeOf.object("cartesian",e),Math.max(e.x,e.y,e.z)},A.minimumComponent=function(e){return S.Check.typeOf.object("cartesian",e),Math.min(e.x,e.y,e.z)},A.minimumByComponent=function(e,t,n){return S.Check.typeOf.object("first",e),S.Check.typeOf.object("second",t),S.Check.typeOf.object("result",n),n.x=Math.min(e.x,t.x),n.y=Math.min(e.y,t.y),n.z=Math.min(e.z,t.z),n},A.maximumByComponent=function(e,t,n){return S.Check.typeOf.object("first",e),S.Check.typeOf.object("second",t),S.Check.typeOf.object("result",n),n.x=Math.max(e.x,t.x),n.y=Math.max(e.y,t.y),n.z=Math.max(e.z,t.z),n},A.magnitudeSquared=function(e){return S.Check.typeOf.object("cartesian",e),e.x*e.x+e.y*e.y+e.z*e.z},A.magnitude=function(e){return Math.sqrt(A.magnitudeSquared(e))};var n=new A;A.distance=function(e,t){return S.Check.typeOf.object("left",e),S.Check.typeOf.object("right",t),A.subtract(e,t,n),A.magnitude(n)},A.distanceSquared=function(e,t){return S.Check.typeOf.object("left",e),S.Check.typeOf.object("right",t),A.subtract(e,t,n),A.magnitudeSquared(n)},A.normalize=function(e,t){S.Check.typeOf.object("cartesian",e),S.Check.typeOf.object("result",t);var n=A.magnitude(e);if(t.x=e.x/n,t.y=e.y/n,t.z=e.z/n,isNaN(t.x)||isNaN(t.y)||isNaN(t.z))throw new S.DeveloperError("normalized result is not a number");return t},A.dot=function(e,t){return S.Check.typeOf.object("left",e),S.Check.typeOf.object("right",t),e.x*t.x+e.y*t.y+e.z*t.z},A.multiplyComponents=function(e,t,n){return S.Check.typeOf.object("left",e),S.Check.typeOf.object("right",t),S.Check.typeOf.object("result",n),n.x=e.x*t.x,n.y=e.y*t.y,n.z=e.z*t.z,n},A.divideComponents=function(e,t,n){return S.Check.typeOf.object("left",e),S.Check.typeOf.object("right",t),S.Check.typeOf.object("result",n),n.x=e.x/t.x,n.y=e.y/t.y,n.z=e.z/t.z,n},A.add=function(e,t,n){return S.Check.typeOf.object("left",e),S.Check.typeOf.object("right",t),S.Check.typeOf.object("result",n),n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n},A.subtract=function(e,t,n){return S.Check.typeOf.object("left",e),S.Check.typeOf.object("right",t),S.Check.typeOf.object("result",n),n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n},A.multiplyByScalar=function(e,t,n){return S.Check.typeOf.object("cartesian",e),S.Check.typeOf.number("scalar",t),S.Check.typeOf.object("result",n),n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n},A.divideByScalar=function(e,t,n){return S.Check.typeOf.object("cartesian",e),S.Check.typeOf.number("scalar",t),S.Check.typeOf.object("result",n),n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n},A.negate=function(e,t){return S.Check.typeOf.object("cartesian",e),S.Check.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t},A.abs=function(e,t){return S.Check.typeOf.object("cartesian",e),S.Check.typeOf.object("result",t),t.x=Math.abs(e.x),t.y=Math.abs(e.y),t.z=Math.abs(e.z),t};var a=new A;A.lerp=function(e,t,n,r){return S.Check.typeOf.object("start",e),S.Check.typeOf.object("end",t),S.Check.typeOf.number("t",n),S.Check.typeOf.object("result",r),A.multiplyByScalar(t,n,a),r=A.multiplyByScalar(e,1-n,r),A.add(a,r,r)};var i=new A,o=new A;A.angleBetween=function(e,t){S.Check.typeOf.object("left",e),S.Check.typeOf.object("right",t),A.normalize(e,i),A.normalize(t,o);var n=A.dot(i,o),r=A.magnitude(A.cross(i,o,i));return Math.atan2(r,n)};var r=new A;A.mostOrthogonalAxis=function(e,t){S.Check.typeOf.object("cartesian",e),S.Check.typeOf.object("result",t);var n=A.normalize(e,r);return A.abs(n,n),t=n.x<=n.y?n.x<=n.z?A.clone(A.UNIT_X,t):A.clone(A.UNIT_Z,t):n.y<=n.z?A.clone(A.UNIT_Y,t):A.clone(A.UNIT_Z,t)},A.projectVector=function(e,t,n){S.Check.defined("a",e),S.Check.defined("b",t),S.Check.defined("result",n);var r=A.dot(e,t)/A.dot(t,t);return A.multiplyByScalar(t,r,n)},A.equals=function(e,t){return e===t||R.defined(e)&&R.defined(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z},A.equalsArray=function(e,t,n){return e.x===t[n]&&e.y===t[n+1]&&e.z===t[n+2]},A.equalsEpsilon=function(e,t,n,r){return e===t||R.defined(e)&&R.defined(t)&&N.CesiumMath.equalsEpsilon(e.x,t.x,n,r)&&N.CesiumMath.equalsEpsilon(e.y,t.y,n,r)&&N.CesiumMath.equalsEpsilon(e.z,t.z,n,r)},A.cross=function(e,t,n){S.Check.typeOf.object("left",e),S.Check.typeOf.object("right",t),S.Check.typeOf.object("result",n);var r=e.x,a=e.y,i=e.z,o=t.x,u=t.y,c=t.z,f=a*c-i*u,d=i*o-r*c,h=r*u-a*o;return n.x=f,n.y=d,n.z=h,n},A.midpoint=function(e,t,n){return S.Check.typeOf.object("left",e),S.Check.typeOf.object("right",t),S.Check.typeOf.object("result",n),n.x=.5*(e.x+t.x),n.y=.5*(e.y+t.y),n.z=.5*(e.z+t.z),n},A.fromDegrees=function(e,t,n,r,a){return S.Check.typeOf.number("longitude",e),S.Check.typeOf.number("latitude",t),e=N.CesiumMath.toRadians(e),t=N.CesiumMath.toRadians(t),A.fromRadians(e,t,n,r,a)};var c=new A,f=new A,d=new A(40680631590769,40680631590769,40408299984661.445),h=new A(40680631590769,40680631590769,40680631590769);A.fromRadians=function(e,t,n,r,a){S.Check.typeOf.number("longitude",e),S.Check.typeOf.number("latitude",t),n=R.defaultValue(n,0);var i=R.defined(r)?r.radiiSquared:h;N.CesiumMath.equalsEpsilon(N.CesiumMath.Radius,6356752.314245179,N.CesiumMath.EPSILON10)&&(i=R.defined(r)?r.radiiSquared:d);var o=Math.cos(t);c.x=o*Math.cos(e),c.y=o*Math.sin(e),c.z=Math.sin(t),c=A.normalize(c,c),A.multiplyComponents(i,c,f);var u=Math.sqrt(A.dot(c,f));return f=A.divideByScalar(f,u,f),c=A.multiplyByScalar(c,n,c),R.defined(a)||(a=new A),A.add(f,c,a)},A.fromDegreesArray=function(e,t,n){if(S.Check.defined("coordinates",e),e.length<2||e.length%2!=0)throw new S.DeveloperError("the number of coordinates must be a multiple of 2 and at least 2");var r=e.length;R.defined(n)?n.length=r/2:n=new Array(r/2);for(var a=0;a<r;a+=2){var i=e[a],o=e[a+1],u=a/2;n[u]=A.fromDegrees(i,o,0,t,n[u])}return n},A.fromRadiansArray=function(e,t,n){if(S.Check.defined("coordinates",e),e.length<2||e.length%2!=0)throw new S.DeveloperError("the number of coordinates must be a multiple of 2 and at least 2");var r=e.length;R.defined(n)?n.length=r/2:n=new Array(r/2);for(var a=0;a<r;a+=2){var i=e[a],o=e[a+1],u=a/2;n[u]=A.fromRadians(i,o,0,t,n[u])}return n},A.fromDegreesArrayHeights=function(e,t,n){if(S.Check.defined("coordinates",e),e.length<3||e.length%3!=0)throw new S.DeveloperError("the number of coordinates must be a multiple of 3 and at least 3");var r=e.length;R.defined(n)?n.length=r/3:n=new Array(r/3);for(var a=0;a<r;a+=3){var i=e[a],o=e[a+1],u=e[a+2],c=a/3;n[c]=A.fromDegrees(i,o,u,t,n[c])}return n},A.fromRadiansArrayHeights=function(e,t,n){if(S.Check.defined("coordinates",e),e.length<3||e.length%3!=0)throw new S.DeveloperError("the number of coordinates must be a multiple of 3 and at least 3");var r=e.length;R.defined(n)?n.length=r/3:n=new Array(r/3);for(var a=0;a<r;a+=3){var i=e[a],o=e[a+1],u=e[a+2],c=a/3;n[c]=A.fromRadians(i,o,u,t,n[c])}return n},A.ZERO=Object.freeze(new A(0,0,0)),A.UNIT_X=Object.freeze(new A(1,0,0)),A.UNIT_Y=Object.freeze(new A(0,1,0)),A.UNIT_Z=Object.freeze(new A(0,0,1)),A.UNIT_XYZ=Object.freeze(new A(1,1,1)),A.prototype.clone=function(e){return A.clone(this,e)},A.prototype.equals=function(e){return A.equals(this,e)},A.prototype.equalsEpsilon=function(e,t,n){return A.equalsEpsilon(this,e,t,n)},A.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+")"},A.globalOffset=new A(0,0,0);var D=new A,B=new A;function l(e,t,n,r,a){if(!R.defined(e))throw new S.DeveloperError("cartesian is required.");if(!R.defined(t))throw new S.DeveloperError("oneOverRadii is required.");if(!R.defined(n))throw new S.DeveloperError("oneOverRadiiSquared is required.");if(!R.defined(r))throw new S.DeveloperError("centerToleranceSquared is required.");var i=e.x,o=e.y,u=e.z,c=t.x,f=t.y,d=t.z,h=i*i*c*c,l=o*o*f*f,s=u*u*d*d,y=h+l+s,p=Math.sqrt(1/y),m=A.multiplyByScalar(e,p,D);if(y<r)return isFinite(p)?A.clone(m,a):void 0;var C=n.x,b=n.y,g=n.z,k=B;k.x=m.x*C*2,k.y=m.y*b*2,k.z=m.z*g*2;var O,z,x,w,v,j,M,q=(1-p)*A.magnitude(e)/(.5*A.magnitude(k)),E=0;do{E=(O=h*(v=(z=1/(1+(q-=E)*C))*z)+l*(j=(x=1/(1+q*b))*x)+s*(M=(w=1/(1+q*g))*w)-1)/(-2*(h*(v*z)*C+l*(j*x)*b+s*(M*w)*g))}while(Math.abs(O)>N.CesiumMath.EPSILON12);return R.defined(a)?(a.x=i*z,a.y=o*x,a.z=u*w,a):new A(i*z,o*x,u*w)}function s(e,t,n){this.longitude=R.defaultValue(e,0),this.latitude=R.defaultValue(t,0),this.height=R.defaultValue(n,0)}s.fromRadians=function(e,t,n,r){return S.Check.typeOf.number("longitude",e),S.Check.typeOf.number("latitude",t),n=R.defaultValue(n,0),R.defined(r)?(r.longitude=e,r.latitude=t,r.height=n,r):new s(e,t,n)},s.fromDegrees=function(e,t,n,r){return S.Check.typeOf.number("longitude",e),S.Check.typeOf.number("latitude",t),e=N.CesiumMath.toRadians(e),t=N.CesiumMath.toRadians(t),s.fromRadians(e,t,n,r)};var y=new A,p=new A,m=new A,C=new A(1/6378137,1/6378137,1/6356752.314245179),b=new A(1/6378137,1/6378137,1/6378137),g=new A(1/40680631590769,1/40680631590769,1/40408299984661.445),k=new A(1/40680631590769,1/40680631590769,1/40680631590769),O=N.CesiumMath.EPSILON1;s.fromCartesian=function(e,t,n){var r=R.defined(t)?t.oneOverRadii:b,a=R.defined(t)?t.oneOverRadiiSquared:k,i=R.defined(t)?t._centerToleranceSquared:O;N.CesiumMath.equalsEpsilon(N.CesiumMath.Radius,6356752.314245179,N.CesiumMath.EPSILON10)&&(r=R.defined(t)?t.oneOverRadii:C,a=R.defined(t)?t.oneOverRadiiSquared:g);var o=l(e,r,a,i,p);if(R.defined(o)){var u=A.multiplyComponents(o,a,y);u=A.normalize(u,u);var c=A.subtract(e,o,m),f=Math.atan2(u.y,u.x),d=Math.asin(u.z),h=N.CesiumMath.sign(A.dot(c,e))*A.magnitude(c);return R.defined(n)?(n.longitude=f,n.latitude=d,n.height=h,n):new s(f,d,h)}},s.toCartesian=function(e,t,n){return S.Check.defined("cartographic",e),A.fromRadians(e.longitude,e.latitude,e.height,t,n)},s.sphericalDistance=function(e,t,n,r){if(S.Check.defined("longitudeA",e),S.Check.defined("longitudeB",n),S.Check.defined("latitudeA",t),S.Check.defined("latitudeB",r),e===n&&t===r)return 0;var a=N.CesiumMath.toRadians(t),i=N.CesiumMath.toRadians(r),o=N.CesiumMath.toRadians(e),u=N.CesiumMath.toRadians(n),c=o*o+a*a,f=u*u+i*i,d=(c+f-((o-u)*(o-u)+(a-i)*(a-i)))/(2*Math.sqrt(c)*Math.sqrt(f));return d=N.CesiumMath.clamp(d,-1,1),Math.acos(d)*N.CesiumMath.Radius},s.clone=function(e,t){if(R.defined(e))return R.defined(t)?(t.longitude=e.longitude,t.latitude=e.latitude,t.height=e.height,t):new s(e.longitude,e.latitude,e.height)},s.equals=function(e,t){return e===t||R.defined(e)&&R.defined(t)&&e.longitude===t.longitude&&e.latitude===t.latitude&&e.height===t.height},s.equalsEpsilon=function(e,t,n){return S.Check.typeOf.number("epsilon",n),e===t||R.defined(e)&&R.defined(t)&&Math.abs(e.longitude-t.longitude)<=n&&Math.abs(e.latitude-t.latitude)<=n&&Math.abs(e.height-t.height)<=n},s.ZERO=Object.freeze(new s(0,0,0)),s.prototype.clone=function(e){return s.clone(this,e)},s.prototype.equals=function(e){return s.equals(this,e)},s.prototype.equalsEpsilon=function(e,t){return s.equalsEpsilon(this,e,t)},s.prototype.toString=function(){return"("+this.longitude+", "+this.latitude+", "+this.height+")"},e.Cartesian3=A,e.Cartographic=s,e.scaleToGeodeticSurface=l});