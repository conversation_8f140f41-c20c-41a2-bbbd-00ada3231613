<template>
  <div style="height: 100%; width: 100%">
    <iframe
      :src="iframeUrl"
      frameborder="0"
      style="width: 100%; height: 100%"
    ></iframe>
  </div>
</template>

<script>
import { JSEncrypt } from "jsencrypt";
export default {
  name: "SpecEquipManage",
  mounted() {
    this.account = this.encodeData("cznq", this.publicKey);
    this.password = this.encodeData("Nq2487980d.", this.publicKey);
    console.log(this.account);
    console.log(this.password);
    console.log(
      `https://iot.crchi.com:9000/tunnelGreen/oauthIOT?usraccountnumber=${encodeURIComponent(
        this.account
      )}&usrtokenstr=${encodeURIComponent(this.password)}`
    );
    const url = `https://iot.crchi.com:9000/tunnelGreen/oauthIOT?usraccountnumber=${encodeURIComponent(
      this.account
    )}&usrtokenstr=${encodeURIComponent(this.password)}`;
    window.open(url, "_blank");
  },

  data() {
    return {
      publicKey:
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQClJ9OoelNqzUe09yZlSEl1P4dK7ty9SAI1ZQeEHCFngJOL916iGZKNfq5zHldHh43qWZ7ccVIiJHuMnIP7XEmDQQjPBUNsSyQKKbUxhhi692QR0COG7yIOxkD+MueIPz6+kBgtH/4Ap9wzvCO9lIu4v3AWRForEnz00StQEHNiVQIDAQAB",
      account: "",
      password: "",
      iframeUrl: `https://iot.crchi.com:9000/tunnelGreen/oauthIOT?usraccountnumber=${encodeURIComponent(
        this.account
      )}&usrtokenstr=${encodeURIComponent(this.password)}`,
    };
  },
  methods: {
    encodeData(value, publicKey) {
      const encrypt = new JSEncrypt();
      encrypt.setPublicKey(publicKey);
      return encrypt.encrypt(value);
    },
  },
};
</script>

<style scoped>
html,
body,
#app,
.layout,
.main {
  height: 100%;
  margin: 0;
}
</style>
