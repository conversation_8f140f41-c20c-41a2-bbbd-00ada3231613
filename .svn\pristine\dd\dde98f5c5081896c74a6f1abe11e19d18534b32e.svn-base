<template>
  <el-dialog
    :title="titleStr"
    :visible.sync="dialogVisible"
    width="55%"
    :before-close="handleClose"
    class="popup-dialog"
  >
    <div class="dialog-part">
      <div class="item">
        <el-form ref="form" :model="formData" label-width="100px">
          <el-form-item label="车辆品牌">
            <el-input
              v-model="formData.carBrand"
              placeholder="请输入车辆品牌"
            ></el-input>
          </el-form-item>

          <el-form-item label="车牌号">
            <el-input
              v-model="formData.carNum"
              placeholder="请输入车牌号"
            ></el-input>
          </el-form-item>
          <el-form-item label="采集终端ID">
            <el-input
              v-model="formData.collecterId"
              placeholder="请输入采集终端ID"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="item item-right">
        <el-form ref="form" :model="formData" label-width="100px">
          <el-form-item label="出场编号">
            <el-input
              v-model="formData.factoryNum"
              placeholder="请输入出场编号"
            ></el-input>
          </el-form-item>
          <el-form-item label="故障码">
            <el-input
              v-model="formData.faultCode"
              placeholder="请输入故障码"
            ></el-input>
          </el-form-item>
          <el-form-item label="故障说明">
            <el-input
              v-model="formData.faultDescription"
              placeholder="请输入故障说明"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="item item-right">
        <el-form ref="form" :model="formData" label-width="100px">
          <el-form-item label="处置人">
            <el-input
              v-model="formData.operator"
              placeholder="请输入处置人"
            ></el-input>
          </el-form-item>

          <!-- <el-form-item label="补能类型">
            <el-select
              v-model="formData.energyType"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(item, index) in energyTypeData"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="机械类型">
            <el-select
              v-model="formData.deviceType"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(item, index) in deviceTypeData"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
        </el-form>
      </div>
    </div>
    <el-form ref="form" :model="formData" label-width="100px">
      <!-- <el-form-item label="上传附件">
        <el-upload
          :limit="1"
          :action="upload_url"
          :on-remove="handleRemoved"
          :file-list="fileList1"
          :on-success="handleSucceed"
        >
          <el-button size="small" type="primary">点击上传</el-button>
          <div slot="tip" class="el-upload__tip">
            支持扩展名：.xls,.xlsx,.doc,.docx,.pdf,.jpg,.jpeg,.png,.git
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="申报人">
        <el-input
          v-model="formData.operationPerson"
          placeholder="请输入申报人"
        ></el-input>
      </el-form-item> -->
      <el-form-item label="备注">
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入内容"
          v-model="formData.remark"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click.native.prevent="submit">保存</el-button>
      <el-button @click="onClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import FormUtils from "@/mixins/formUtils";
export default {
  name: "addErrReport",
  mixins: [FormUtils],
  props: ["curData", "dialogVisible"],
  components: {},
  data() {
    return {
      formData: {},
      fileList: [],
      fileList1: [],
      deviceTypeData: [],
      dynamicTypeData: [
        {
          label: "纯电",
          value: "纯电",
        },
        {
          label: "混动",
          value: "混动",
        },
        {
          label: "拖电",
          value: "拖电",
        },
        {
          label: "氢燃料",
          value: "氢燃料",
        },
      ],
      energyTypeData: [
        {
          label: "充电",
          value: "充电",
        },
        {
          label: "换电",
          value: "换电",
        },
      ],
    };
  },
  computed: {
    titleStr() {
      return this.curData.type == "1" ? "设备故障申报" : "编辑设备台账";
    },
  },

  created() {
    let arr = this.curData.data.annxArray
      ? JSON.parse(this.curData.data.annxArray)
      : [];
    if (this.curData.type != "1" && arr) {
      this.fileList = arr.map((item) => {
        return { id: item.id, name: item.name };
      });
      this.fileList1 = arr.map((item) => {
        return {
          name: item.name,
          url: `/equip/fileinfo/download?id=${item.id}`,
        };
      });
    }
  },

  mounted() {
    this.formData = this.curData.data;
    // this.getDeviceType();
  },

  methods: {
    onClose() {
      this.$emit("onClose");
    },

    // async getDeviceType() {
    //   let device = [];
    //   let res = await this.$http.get(`/equip/dictData/type?dictName=机械类型`);
    //   res?.result.map((item) => {
    //     device.push({
    //       label: item.dictLabel,
    //       value: item.dictValue,
    //     });
    //   });
    //   this.deviceTypeData = device;
    // },

    async submit() {
      if (this.$store.state.tree.sectionId){
        let url =
        this.curData.type === 1
          ? "/equip/fault/insert"
          : "/equip/fault/saveOrUpdate";
        let params = {
          ...this.formData,
          annxArray:
            this.fileList.length > 0 ? JSON.stringify(this.fileList) : "",
          sectionId: this.$store.state.tree.sectionId,
        };
        const res = await this.$http.post(url, params);
        this.$emit("onSave");
        this.$msgSuccess(res.msg);
      }else{
        this.$msgError("请先选择一个工点");
      }
      
    },

    handleClose() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("onClose");
        })
        .catch((_) => {});
    },

    handleRemoved(file, files) {
      this.handleRemove(file, files, "fileList");
    },

    handleSucceed(file, files) {
      this.handleSuccess(file, files, [], "fileList");
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-dialog {
  .dialog-part {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .item {
      flex: 1;
    }

    .item-right {
      margin-left: 10px;
    }
  }

  ::v-deep .el-dialog__header {
    background-color: #4575e7;

    .el-dialog__title {
      color: #fff;
    }

    .el-dialog__close {
      color: #fff;
    }
  }

  ::v-deep .el-date-editor {
    width: 100%;
  }

  ::v-deep .el-select {
    width: 100%;
  }

  ::v-deep .el-input {
    width: 175px;
  }
}
</style>
