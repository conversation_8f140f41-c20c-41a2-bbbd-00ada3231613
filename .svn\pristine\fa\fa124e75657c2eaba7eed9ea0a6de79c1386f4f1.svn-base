/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c"],function(t,m,p,f,C,c){function P(t,i,e){if(0===t)return i*e;var a=t*t,n=a*a,s=n*a,h=s*a,u=h*a,r=u*a,d=e;return i*((1-a/4-3*n/64-5*s/256-175*h/16384-441*u/65536-4851*r/1048576)*d-(3*a/8+3*n/32+45*s/1024+105*h/4096+2205*u/131072+6237*r/524288)*Math.sin(2*d)+(15*n/256+45*s/1024+525*h/16384+1575*u/65536+155925*r/8388608)*Math.sin(4*d)-(35*s/3072+175*h/12288+3675*u/262144+13475*r/1048576)*Math.sin(6*d)+(315*h/131072+2205*u/524288+43659*r/8388608)*Math.sin(8*d)-(693*u/1310720+6237*r/5242880)*Math.sin(10*d)+1001*r/8388608*Math.sin(12*d))}function v(t,i){if(0===t)return Math.log(Math.tan(.5*(f.CesiumMath.PI_OVER_TWO+i)));var e=t*Math.sin(i);return Math.log(Math.tan(.5*(f.CesiumMath.PI_OVER_TWO+i)))-t/2*Math.log((1+e)/(1-e))}var O=new C.Cartesian3,E=new C.Cartesian3;function n(t,i,e,a){var n=C.Cartesian3.normalize(a.cartographicToCartesian(i,E),O),s=C.Cartesian3.normalize(a.cartographicToCartesian(e,E),E);p.Check.typeOf.number.greaterThanOrEquals("value",Math.abs(Math.abs(C.Cartesian3.angleBetween(n,s))-Math.PI),.0125);var h,u,r,d,o,l,c,M=a.maximumRadius,m=a.minimumRadius,_=M*M,g=m*m;t._ellipticitySquared=(_-g)/_,t._ellipticity=Math.sqrt(t._ellipticitySquared),t._start=C.Cartographic.clone(i,t._start),t._start.height=0,t._end=C.Cartographic.clone(e,t._end),t._end.height=0,t._heading=(h=t,u=i.longitude,r=i.latitude,d=e.longitude,o=e.latitude,l=v(h._ellipticity,r),c=v(h._ellipticity,o),Math.atan2(f.CesiumMath.negativePiToPi(d-u),c-l)),t._distance=function(t,i,e,a,n,s,h){var u=t._heading,r=s-a,d=0;if(f.CesiumMath.equalsEpsilon(Math.abs(u),f.CesiumMath.PI_OVER_TWO,f.CesiumMath.EPSILON8))if(i===e)d=i*Math.cos(n)*f.CesiumMath.negativePiToPi(r);else{var o=Math.sin(n);d=i*Math.cos(n)*f.CesiumMath.negativePiToPi(r)/Math.sqrt(1-t._ellipticitySquared*o*o)}else{var l=P(t._ellipticity,i,n);d=(P(t._ellipticity,i,h)-l)/Math.cos(u)}return Math.abs(d)}(t,a.maximumRadius,a.minimumRadius,i.longitude,i.latitude,e.longitude,e.latitude)}function M(t,i,e,a,n,s){var h,u,r,d=n*n;if(Math.abs(f.CesiumMath.PI_OVER_TWO-Math.abs(i))>f.CesiumMath.EPSILON8){u=function(t,i,e){var a=t/e;if(0===i)return a;var n=a*a,s=n*a,h=s*a,u=i*i,r=u*u,d=r*u,o=d*u,l=o*u,c=l*u,M=Math.sin(2*a),m=Math.cos(2*a),_=Math.sin(4*a),g=Math.cos(4*a),p=Math.sin(6*a),f=Math.cos(6*a),C=Math.sin(8*a),P=Math.cos(8*a),v=Math.sin(10*a);return a+a*u/4+7*a*r/64+15*a*d/256+579*a*o/16384+1515*a*l/65536+16837*a*c/1048576+(3*a*r/16+45*a*d/256-a*(32*n-561)*o/4096-a*(232*n-1677)*l/16384+a*(399985-90560*n+512*h)*c/5242880)*m+(21*a*d/256+483*a*o/4096-a*(224*n-1969)*l/16384-a*(33152*n-112599)*c/1048576)*g+(151*a*o/4096+4681*a*l/65536+1479*a*c/16384-453*s*c/32768)*f+(1097*a*l/65536+42783*a*c/1048576)*P+8011*a*c/1048576*Math.cos(10*a)+(3*u/8+3*r/16+213*d/2048-3*n*d/64+255*o/4096-33*n*o/512+20861*l/524288-33*n*l/512+h*l/1024+28273*c/1048576-471*n*c/8192+9*h*c/4096)*M+(21*r/256+21*d/256+533*o/8192-21*n*o/512+197*l/4096-315*n*l/4096+584039*c/16777216-12517*n*c/131072+7*h*c/2048)*_+(151*d/6144+151*o/4096+5019*l/131072-453*n*l/16384+26965*c/786432-8607*n*c/131072)*p+(1097*o/131072+1097*l/65536+225797*c/10485760-1097*n*c/65536)*C+(8011*l/2621440+8011*c/1048576)*v+293393*c/251658240*Math.sin(12*a)}(P(n,a,t.latitude)+e*Math.cos(i),n,a);var o=v(n,t.latitude),l=v(n,u);r=Math.tan(i)*(l-o),h=f.CesiumMath.negativePiToPi(t.longitude+r)}else{var c;if(u=t.latitude,0===n)c=a*Math.cos(t.latitude);else{var M=Math.sin(t.latitude);c=a*Math.cos(t.latitude)/Math.sqrt(1-d*M*M)}r=e/c,h=0<i?f.CesiumMath.negativePiToPi(t.longitude+r):f.CesiumMath.negativePiToPi(t.longitude-r)}return m.defined(s)?(s.longitude=h,s.latitude=u,s.height=0,s):new C.Cartographic(h,u,0)}function _(t,i,e){var a=m.defaultValue(e,c.Ellipsoid.WGS84);this._ellipsoid=a,this._start=new C.Cartographic,this._end=new C.Cartographic,this._heading=void 0,this._distance=void 0,this._ellipticity=void 0,this._ellipticitySquared=void 0,m.defined(t)&&m.defined(i)&&n(this,t,i,a)}Object.defineProperties(_.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},surfaceDistance:{get:function(){return p.Check.defined("distance",this._distance),this._distance}},start:{get:function(){return this._start}},end:{get:function(){return this._end}},heading:{get:function(){return p.Check.defined("distance",this._distance),this._heading}}}),_.fromStartHeadingDistance=function(t,i,e,a,n){p.Check.defined("start",t),p.Check.defined("heading",i),p.Check.defined("distance",e),p.Check.typeOf.number.greaterThan("distance",e,0);var s=m.defaultValue(a,c.Ellipsoid.WGS84),h=s.maximumRadius,u=s.minimumRadius,r=h*h,d=u*u,o=Math.sqrt((r-d)/r),l=M(t,i=f.CesiumMath.negativePiToPi(i),e,s.maximumRadius,o);return!m.defined(n)||m.defined(a)&&!a.equals(n.ellipsoid)?new _(t,l,s):(n.setEndPoints(t,l),n)},_.prototype.setEndPoints=function(t,i){p.Check.defined("start",t),p.Check.defined("end",i),n(this,t,i,this._ellipsoid)},_.prototype.interpolateUsingFraction=function(t,i){return this.interpolateUsingSurfaceDistance(t*this._distance,i)},_.prototype.interpolateUsingSurfaceDistance=function(t,i){if(p.Check.typeOf.number("distance",t),!m.defined(this._distance)||0===this._distance)throw new p.DeveloperError("EllipsoidRhumbLine must have distinct start and end set.");return M(this._start,this._heading,t,this._ellipsoid.maximumRadius,this._ellipticity,i)},_.prototype.findIntersectionWithLongitude=function(t,i){if(p.Check.typeOf.number("intersectionLongitude",t),!m.defined(this._distance)||0===this._distance)throw new p.DeveloperError("EllipsoidRhumbLine must have distinct start and end set.");var e=this._ellipticity,a=this._heading,n=Math.abs(a),s=this._start;if(t=f.CesiumMath.negativePiToPi(t),f.CesiumMath.equalsEpsilon(Math.abs(t),Math.PI,f.CesiumMath.EPSILON14)&&(t=f.CesiumMath.sign(s.longitude)*Math.PI),m.defined(i)||(i=new C.Cartographic),Math.abs(f.CesiumMath.PI_OVER_TWO-n)<=f.CesiumMath.EPSILON8)return i.longitude=t,i.latitude=s.latitude,i.height=0,i;if(f.CesiumMath.equalsEpsilon(Math.abs(f.CesiumMath.PI_OVER_TWO-n),f.CesiumMath.PI_OVER_TWO,f.CesiumMath.EPSILON8)){if(f.CesiumMath.equalsEpsilon(t,s.longitude,f.CesiumMath.EPSILON12))return;return i.longitude=t,i.latitude=f.CesiumMath.PI_OVER_TWO*f.CesiumMath.sign(f.CesiumMath.PI_OVER_TWO-a),i.height=0,i}var h,u=s.latitude,r=e*Math.sin(u),d=Math.tan(.5*(f.CesiumMath.PI_OVER_TWO+u))*Math.exp((t-s.longitude)/Math.tan(a)),o=(1+r)/(1-r),l=s.latitude;do{h=l;var c=e*Math.sin(h),M=(1+c)/(1-c);l=2*Math.atan(d*Math.pow(M/o,e/2))-f.CesiumMath.PI_OVER_TWO}while(!f.CesiumMath.equalsEpsilon(l,h,f.CesiumMath.EPSILON12));return i.longitude=t,i.latitude=l,i.height=0,i},_.prototype.findIntersectionWithLatitude=function(t,i){if(p.Check.typeOf.number("intersectionLatitude",t),!m.defined(this._distance)||0===this._distance)throw new p.DeveloperError("EllipsoidRhumbLine must have distinct start and end set.");var e=this._ellipticity,a=this._heading,n=this._start;if(!f.CesiumMath.equalsEpsilon(Math.abs(a),f.CesiumMath.PI_OVER_TWO,f.CesiumMath.EPSILON8)){var s=v(e,n.latitude),h=v(e,t),u=Math.tan(a)*(h-s),r=f.CesiumMath.negativePiToPi(n.longitude+u);return m.defined(i)?(i.longitude=r,i.latitude=t,i.height=0,i):new C.Cartographic(r,t,0)}},t.EllipsoidRhumbLine=_});