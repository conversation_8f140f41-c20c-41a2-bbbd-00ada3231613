/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281"],function(e,o,a){var r=function(e){null==e&&(e=(new Date).getTime()),this.N=624,this.M=397,this.MATRIX_A=**********,this.UPPER_MASK=**********,this.LOWER_MASK=**********,this.mt=new Array(this.N),this.mti=this.N+1,this.init_genrand(e)};r.prototype.init_genrand=function(e){for(this.mt[0]=e>>>0,this.mti=1;this.mti<this.N;this.mti++){e=this.mt[this.mti-1]^this.mt[this.mti-1]>>>30;this.mt[this.mti]=(***********((**********&e)>>>16)<<16)+***********(65535&e)+this.mti,this.mt[this.mti]>>>=0}},r.prototype.genrand_int32=function(){var e,r=new Array(0,this.MATRIX_A);if(this.mti>=this.N){var t;for(this.mti==this.N+1&&this.init_genrand(5489),t=0;t<this.N-this.M;t++)e=this.mt[t]&this.UPPER_MASK|this.mt[t+1]&this.LOWER_MASK,this.mt[t]=this.mt[t+this.M]^e>>>1^r[1&e];for(;t<this.N-1;t++)e=this.mt[t]&this.UPPER_MASK|this.mt[t+1]&this.LOWER_MASK,this.mt[t]=this.mt[t+(this.M-this.N)]^e>>>1^r[1&e];e=this.mt[this.N-1]&this.UPPER_MASK|this.mt[0]&this.LOWER_MASK,this.mt[this.N-1]=this.mt[this.M-1]^e>>>1^r[1&e],this.mti=0}return e=this.mt[this.mti++],e^=e>>>11,e^=e<<7&2636928640,e^=e<<15&4022730752,(e^=e>>>18)>>>0},r.prototype.random=function(){return this.genrand_int32()*(1/4294967296)};var h={Radius:6378137,EPSILON1:.1,EPSILON2:.01,EPSILON3:.001,EPSILON4:1e-4,EPSILON5:1e-5,EPSILON6:1e-6,EPSILON7:1e-7,EPSILON8:1e-8,EPSILON9:1e-9,EPSILON10:1e-10,EPSILON11:1e-11,EPSILON12:1e-12,EPSILON13:1e-13,EPSILON14:1e-14,EPSILON15:1e-15,EPSILON16:1e-16,EPSILON17:1e-17,EPSILON18:1e-18,EPSILON19:1e-19,EPSILON20:1e-20,EPSILON21:1e-21,GRAVITATIONALPARAMETER:3986004418e5,SOLAR_RADIUS:6955e5,LUNAR_RADIUS:1737400,SIXTY_FOUR_KILOBYTES:65536};h.sign=o.defaultValue(Math.sign,function(e){return 0===(e=+e)||e!=e?e:0<e?1:-1}),h.signNotZero=function(e){return e<0?-1:1},h.toSNorm=function(e,r){return r=o.defaultValue(r,255),Math.round((.5*h.clamp(e,-1,1)+.5)*r)},h.fromSNorm=function(e,r){return r=o.defaultValue(r,255),h.clamp(e,0,r)/r*2-1},h.normalize=function(e,r,t){return 0===(t=Math.max(t-r,0))?0:h.clamp((e-r)/t,0,1)},h.sinh=o.defaultValue(Math.sinh,function(e){return(Math.exp(e)-Math.exp(-e))/2}),h.cosh=o.defaultValue(Math.cosh,function(e){return(Math.exp(e)+Math.exp(-e))/2}),h.lerp=function(e,r,t){return(1-t)*e+t*r},h.PI=Math.PI,h.ONE_OVER_PI=1/Math.PI,h.PI_OVER_TWO=Math.PI/2,h.PI_OVER_THREE=Math.PI/3,h.PI_OVER_FOUR=Math.PI/4,h.PI_OVER_SIX=Math.PI/6,h.THREE_PI_OVER_TWO=3*Math.PI/2,h.TWO_PI=2*Math.PI,h.ONE_OVER_TWO_PI=1/(2*Math.PI),h.RADIANS_PER_DEGREE=Math.PI/180,h.DEGREES_PER_RADIAN=180/Math.PI,h.RADIANS_PER_ARCSECOND=h.RADIANS_PER_DEGREE/3600,h.toRadians=function(e){if(!o.defined(e))throw new a.DeveloperError("degrees is required.");return e*h.RADIANS_PER_DEGREE},h.toDegrees=function(e){if(!o.defined(e))throw new a.DeveloperError("radians is required.");return e*h.DEGREES_PER_RADIAN},h.convertLongitudeRange=function(e){if(!o.defined(e))throw new a.DeveloperError("angle is required.");var r=h.TWO_PI,t=e-Math.floor(e/r)*r;return t<-Math.PI?t+r:t>=Math.PI?t-r:t},h.clampToLatitudeRange=function(e){if(!o.defined(e))throw new a.DeveloperError("angle is required.");return h.clamp(e,-1*h.PI_OVER_TWO,h.PI_OVER_TWO)},h.negativePiToPi=function(e){if(!o.defined(e))throw new a.DeveloperError("angle is required.");return h.zeroToTwoPi(e+h.PI)-h.PI},h.zeroToTwoPi=function(e){if(!o.defined(e))throw new a.DeveloperError("angle is required.");var r=h.mod(e,h.TWO_PI);return Math.abs(r)<h.EPSILON14&&Math.abs(e)>h.EPSILON14?h.TWO_PI:r},h.mod=function(e,r){if(!o.defined(e))throw new a.DeveloperError("m is required.");if(!o.defined(r))throw new a.DeveloperError("n is required.");return(e%r+r)%r},h.equalsEpsilon=function(e,r,t,i){if(!o.defined(e))throw new a.DeveloperError("left is required.");if(!o.defined(r))throw new a.DeveloperError("right is required.");if(!o.defined(t))throw new a.DeveloperError("relativeEpsilon is required.");i=o.defaultValue(i,t);var n=Math.abs(e-r);return n<=i||n<=t*Math.max(Math.abs(e),Math.abs(r))},h.lessThan=function(e,r,t){if(!o.defined(e))throw new a.DeveloperError("first is required.");if(!o.defined(r))throw new a.DeveloperError("second is required.");if(!o.defined(t))throw new a.DeveloperError("relativeEpsilon is required.");return e-r<-t},h.lessThanOrEquals=function(e,r,t){if(!o.defined(e))throw new a.DeveloperError("first is required.");if(!o.defined(r))throw new a.DeveloperError("second is required.");if(!o.defined(t))throw new a.DeveloperError("relativeEpsilon is required.");return e-r<t},h.greaterThan=function(e,r,t){if(!o.defined(e))throw new a.DeveloperError("first is required.");if(!o.defined(r))throw new a.DeveloperError("second is required.");if(!o.defined(t))throw new a.DeveloperError("relativeEpsilon is required.");return t<e-r},h.greaterThanOrEquals=function(e,r,t){if(!o.defined(e))throw new a.DeveloperError("first is required.");if(!o.defined(r))throw new a.DeveloperError("second is required.");if(!o.defined(t))throw new a.DeveloperError("relativeEpsilon is required.");return-t<e-r};var u=[1];h.factorial=function(e){if("number"!=typeof e||e<0)throw new a.DeveloperError("A number greater than or equal to 0 is required.");var r=u.length;if(r<=e)for(var t=u[r-1],i=r;i<=e;i++){var n=t*i;u.push(n),t=n}return u[e]},h.incrementWrap=function(e,r,t){if(t=o.defaultValue(t,0),!o.defined(e))throw new a.DeveloperError("n is required.");if(r<=t)throw new a.DeveloperError("maximumValue must be greater than minimumValue.");return r<++e&&(e=t),e},h.isPowerOfTwo=function(e){if("number"!=typeof e||e<0)throw new a.DeveloperError("A number greater than or equal to 0 is required.");return 0!==e&&0==(e&e-1)},h.nextPowerOfTwo=function(e){if("number"!=typeof e||e<0)throw new a.DeveloperError("A number greater than or equal to 0 is required.");return--e,e|=e>>1,e|=e>>2,e|=e>>4,e|=e>>8,e|=e>>16,++e},h.clamp=function(e,r,t){if(!o.defined(e))throw new a.DeveloperError("value is required");if(!o.defined(r))throw new a.DeveloperError("min is required.");if(!o.defined(t))throw new a.DeveloperError("max is required.");return e<r?r:t<e?t:e};var t=new r;h.setRandomNumberSeed=function(e){if(!o.defined(e))throw new a.DeveloperError("seed is required.");t=new r(e)},h.nextRandomNumber=function(){return t.random()},h.randomBetween=function(e,r){return h.nextRandomNumber()*(r-e)+e},h.acosClamped=function(e){if(!o.defined(e))throw new a.DeveloperError("value is required.");return Math.acos(h.clamp(e,-1,1))},h.asinClamped=function(e){if(!o.defined(e))throw new a.DeveloperError("value is required.");return Math.asin(h.clamp(e,-1,1))},h.chordLength=function(e,r){if(!o.defined(e))throw new a.DeveloperError("angle is required.");if(!o.defined(r))throw new a.DeveloperError("radius is required.");return 2*r*Math.sin(.5*e)},h.logBase=function(e,r){if(!o.defined(e))throw new a.DeveloperError("number is required.");if(!o.defined(r))throw new a.DeveloperError("base is required.");return Math.log(e)/Math.log(r)},h.cbrt=o.defaultValue(Math.cbrt,function(e){var r=Math.pow(Math.abs(e),1/3);return e<0?-r:r}),h.log2=o.defaultValue(Math.log2,function(e){return Math.log(e)*Math.LOG2E}),h.fog=function(e,r){var t=e*r;return 1-Math.exp(-t*t)},h.fastApproximateAtan=function(e){return a.Check.typeOf.number("x",e),e*(-.1784*Math.abs(e)-.0663*e*e+1.0301)},h.fastApproximateAtan2=function(e,r){var t,i;a.Check.typeOf.number("x",e),a.Check.typeOf.number("y",r);var n=Math.abs(e);t=Math.abs(r),i=Math.max(n,t);var o=(t=Math.min(n,t))/i;if(isNaN(o))throw new a.DeveloperError("either x or y must be nonzero");return n=h.fastApproximateAtan(o),n=Math.abs(r)>Math.abs(e)?h.PI_OVER_TWO-n:n,n=e<0?h.PI-n:n,n=r<0?-n:n},e.CesiumMath=h});