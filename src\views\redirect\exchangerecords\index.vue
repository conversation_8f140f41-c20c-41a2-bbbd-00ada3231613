<template>
    <div id="Hdjl">
        <el-card class="card-content">
            <div class="content-option">
                <div class="content-title">换电记录</div>
                <el-form :inline="true" :model="formInline" ref="formInline" class="form-view">
                    <el-form-item label="车牌号">
                        <el-input v-model="formInline.carNo" clearable
                        prefix-icon="el-icon-search"></el-input>
                    </el-form-item>
                    <el-form-item label="车架号">
                        <el-input v-model="formInline.carVin" clearable
                        prefix-icon="el-icon-search"></el-input>
                    </el-form-item>
                    <el-form-item label="开始时间">
                        <el-date-picker
                        v-model="startTime"
                        type="datetime"
                        placeholder="选择开始日期"
                        clearable
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="结束时间">
                        <el-date-picker
                        v-model="endTime"
                        type="datetime"
                        placeholder="选择结束日期"
                        clearable
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item class="button-container">
                        <el-button type="primary" @click="handleQuery()">查询</el-button>
                        <el-button @click="handelExport()" :loading="loading">导出</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="content-table">
                <el-table ref="table" :data="tableData" style="width: 100%"  border :header-cell-style="headerCellStyle" :cell-style="cellStyle">
                    <el-table-column type="index" width="50" label="序号" align="center" />
                    <el-table-column prop="carNo" label="车牌号" align="center" width="100" />
                    <el-table-column prop="carVin" label="车架号" align="center" width="80" />
                    <el-table-column prop="carBodyNo" label="车身编号" align="center" width="90" />
                    <el-table-column prop="startTime" label="开始时间" align="center" width="170" />
                    <el-table-column prop="endTime" label="结束时间" align="center" width="170" />
                    <el-table-column prop="replaceTime" label="换电时间(秒)" align="center" width="120" />
                    <el-table-column prop="lowBatterySoc" label="乏电电池SOC(%)" align="center" width="140" />
                    <el-table-column prop="lowBatteryId" label="乏电电池编号" align="center" width="120" />
                    <el-table-column prop="lowBatteryNo" label="乏电电池架号" align="center" width="120" />
                    <el-table-column prop="fullBatterySoc" label="满电电池SOC(%)" align="center" width="140" />
                    <el-table-column prop="fullBatteryId" label="满电电池编号" align="center" width="120" />
                    <el-table-column prop="fullBatteryNo" label="满电电池架号" align="center" width="120" />
                    <el-table-column prop="remark" label="备注" align="center" width="160" />
                </el-table>
                <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
                :total="total">
                </el-pagination>
            </div>
        </el-card>
    </div>
</template>
<script>
import tableUtils from '@/mixins/tableUtils';
import { exportChangeElectricityRecordExcel } from '@/api/baseApi/common';
import { formattedTime } from '@/utils/timer';
export default{
    name:'Hdjl',
    mixins: [tableUtils],
    components: {},
    mounted(){
        this.getRecodes();
    },
    computed: {
        treeInfo() {
            return this.$store.state.tree.sectionId;
        }
    },
    watch:{
        treeInfo(){
            // console.log(this.treeInfo);
            this.getRecodes()
        }
    },
    data(){
        return{
            formInline: {},
            loading:false,
            startTime:'',
            endTime:'',
            tableData:[
                {
                    chargingNo:'#1-1',
                    plateNo:'川ADD123',
                    frameNo:'LJ1EEKRP9J',
                    carNo:'#00001',
                    startTime:'2024-3-28 15:45:29',
                    endTime:'2024-3-28 15:45:29',
                    duration:'80',
                    emptySOC:'20%',
                    emptyBatteryNo:'DC00001',
                    emptyBatteryFrameNo:'DC00001',
                    fullSOC:'80%',
                    fullBatteryNo:'DC00001',
                    fullBatteryFrameNo:'DC00001',
                    endInfo:''
                },
                {
                    chargingNo:'#1-1',
                    plateNo:'川ADD123',
                    frameNo:'LJ1EEKRP9J',
                    carNo:'#00001',
                    startTime:'2024-3-28 15:45:29',
                    endTime:'2024-3-28 15:45:29',
                    duration:'80',
                    emptySOC:'20%',
                    emptyBatteryNo:'DC00001',
                    emptyBatteryFrameNo:'DC00001',
                    fullSOC:'80%',
                    fullBatteryNo:'DC00001',
                    fullBatteryFrameNo:'DC00001',
                    endInfo:''
                },
                
            ],
            pageParams:{
                page: 1,
                limit: 20,
            },
            total:0,
        }
    },
    methods:{
        handleSizeChange(val){
            this.pageParams = {
                ...this.pageParams,
                limit: val
            }
            this.handleQuery()
        },
        handleCurrentChange(val){
            this.pageParams = {
                ...this.pageParams,
                page: val
            }
            this.handleQuery()
        },
        async handelExport(){
            this.loading = true;
            let params = {
                ...this.formInline,
                startTime:formattedTime(this.startTime),
                endTime:formattedTime(this.endTime),

                // ...this.requestLoad,
                sectionId: this.treeInfo
            };
            console.log(params);
            let res = await exportChangeElectricityRecordExcel(params)
            var list = new Blob([res], {
                type: "application/vnd.ms-excel;charset=utf-8",
            });
            var downloadUrl = window.URL.createObjectURL(list);
            var anchor = document.createElement("a");
            anchor.href = downloadUrl;
            anchor.download = '换电记录表.xlsx';
            anchor.click();
            window.URL.revokeObjectURL(list);
            this.loading = false;
        },
        handleQuery(){
            this.getRecodes()
        },
        async getRecodes(){
            let params = {
                ...this.formInline,
                startTime:formattedTime(this.startTime),
                endTime:formattedTime(this.endTime),
                // ...this.requestLoad,
                sectionId: this.treeInfo
            };
            let res = await this.$http.post(`/equip/changeElectricityRecord/page?limit=${this.pageParams.limit}&page=${this.pageParams.page}`, params)
            this.tableData = res.result?.records;
            this.total = res.result?.total;

            console.log(res);
        },
    }
}
</script>

<style lang="scss" scoped>
#Hdjl {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width:100%;
    // height: 100%;
    .card-content{
        .content-option {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            width:100%;

            .content-title {
                line-height: 40px;
                font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
                font-weight: 700;
                font-style: normal;
                font-size: 18px;
                color: #3A3F5D;
                margin-right: 5%;
            }

            .form-view {
                display: flex;
                align-items: flex-end;
                // float: right;
                // width: 80%;
                // flex-direction: row;
                // flex-wrap: wrap;
                .button-container{
                    min-width:150px;
                }
            }
        }
    }

}
</style>