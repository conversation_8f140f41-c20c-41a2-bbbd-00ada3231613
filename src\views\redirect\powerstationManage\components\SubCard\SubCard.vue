<template>
    <div class="subcard-container">
        <div class="header">
            <div class="header-title">{{ title }}</div>
            <div class="header-right">
                <svg-icon v-if="icon" class="svg-icon" :icon-class="icon"></svg-icon>
                <span v-if="subtitle"  class="subtitle">{{ subtitle }}</span>
            </div>
        </div>
        <div class="subcard-body">
            <slot />
        </div>
    </div>
</template>

<script>

export default {
    name: 'Card',
    props: ['title', 'subtitle', 'icon'],
    data() {
        return {
        }
    },
    mounted() {
    },

    methods: {
    }
}

</script>

<style lang="scss" scoped>
.subcard-container {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border: 1px solid #6299C7 !important;
    margin: 0.5%;
    display: inline-block !important;
    // flex-direction: column;
    // flex-wrap: wrap !important;

    .header {
        width: 100%;
        height: 30px;
        background-color: #E6ECF9;
        border-bottom: 1px solid #6299C7;
        line-height: 30px;
        display: flex;
        flex-direction: row;
        // flex-wrap: nowrap;
        justify-content: space-between;
        padding-left: 10px;

        .header-title {
            font-family: '思源黑体 CN Bold', '思源黑体 CN Regular', '思源黑体 CN', sans-serif;
            font-weight: 700;
            font-style: normal;
            font-size: 14px;
            color: #3A3F5D;
            text-align: left;
            text-wrap: nowrap;
            @media (max-width: 1500px){
                font-size: 12px;
            }
        }
        .header-right{
            padding-right: 10px;
            white-space: nowrap; /* 使内容不换行 */
            overflow: hidden;
            .svg-icon{
                width: 22px !important;
                height: 15px !important;
            }
            .subtitle{
                font-size: 14px;
                // text-align: left;
                // margin-right: 1%;
                font-family: '微软雅黑', sans-serif;
                font-weight: 400;
                font-style: normal;
                color: #3A3F5D;
                @media (max-width: 1500px){
                    font-size: 12px;
                }
                
                
                // margin-left: 2%;
            }
        }
    }

    .subcard-body {
        height: calc(100% - 30px);
        width: 100%;
        padding: 5px;
        min-height: 80px;
    }
}
</style>