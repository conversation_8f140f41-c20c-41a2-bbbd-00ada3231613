export default {
  data() {
    return {
      pageParams: {
        page: 1,
        limit: 20,
      },
      pageSizes: [10, 20, 30, 40],
      tableLayout: 'total, sizes, prev, pager, next, jumper',
      total: 0,
      tableData: [],
    }
  },
  computed: {
    headerCellStyle() {
      return {
        background: '#EBF4FE',
        color: '#606266',
        borderColor: '#C9DBF4',
      }
    },

    cellStyle() {
      return {
        borderColor: '#C9DBF4'
      }
    },
  },

  methods: {
    onClosePopup(refresh, key) {
      this[`${key || 'dialogVisible'}`] = false
      if (refresh) {
        this.requestList && this.requestList()
      }
    },

    // 重置
    resetForm() {
      this.formInline = {

      };
      this.requestList();
    },

    // 查询
    handleQuery() {
      this.pageParams = {
        ...this.pageParams,
        page: 1
      }
      this.requestList && this.requestList();
    },

    // 页面限制改变
    handleSizeChange(val) {
      this.pageParams = {
        ...this.pageParams,
        limit: val
      }
      this.requestList();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pageParams = {
        ...this.pageParams,
        page: val
      }
      this.requestList();
    },

    handleDelete(id, url) {
      this.$http.post(`${url}?id=${id}`).then(res => {
        if (!res) {
          return
        }
        this.$msgSuccess(res.msg);
        this.requestList()
      })
    },

  },
}
