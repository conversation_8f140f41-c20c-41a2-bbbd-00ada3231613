import Vue from "vue";
import Vuex from "vuex";
import tagsView from "./modules/tagsView";
import settings from "./modules/settings";
import user from "./modules/user";
import tree from "./modules/tree";
import createPersistedState from "vuex-persistedstate";

Vue.use(Vuex);

const store = new Vuex.Store({
  // 持久化
  plugins: [createPersistedState({
    paths: ['user'] // 指定持久化 user 模块
  })],
  modules: {
    tagsView,
    settings,
    user,
    tree
  },
});
export default store;
