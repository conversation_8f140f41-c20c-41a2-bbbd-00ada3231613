{"accessors": [{"bufferView": 2, "componentType": 5126, "count": 288, "max": [0.02812303602695465, 0.19820211827754974, 0.22145164012908936], "min": [-0.028123054653406143, -0.19820211827754974, -0.13417571783065796], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3456, "componentType": 5126, "count": 288, "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 288, "type": "VEC2"}, {"bufferView": 0, "componentType": 5125, "count": 648, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6912, "componentType": 5126, "count": 2282, "max": [0.0957942008972168, 0.08721746504306793, 1.8326746225357056], "min": [-0.09610742330551147, -0.1398775279521942, 1.5201963186264038], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 34296, "componentType": 5126, "count": 2282, "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2304, "componentType": 5126, "count": 2282, "type": "VEC2"}, {"bufferView": 4, "componentType": 5123, "count": 2282, "type": "VEC4"}, {"bufferView": 3, "componentType": 5126, "count": 2282, "type": "VEC4"}, {"bufferView": 0, "byteOffset": 2592, "componentType": 5125, "count": 12282, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 61680, "componentType": 5126, "count": 2401, "max": [0.20507173240184784, 0.12753254175186157, 1.098662257194519], "min": [-0.20507338643074036, -0.1475410908460617, 0.09549660235643387], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 90492, "componentType": 5126, "count": 2401, "type": "VEC3"}, {"bufferView": 3, "byteOffset": 36512, "componentType": 5126, "count": 2401, "type": "VEC4"}, {"bufferView": 1, "byteOffset": 20560, "componentType": 5126, "count": 2401, "type": "VEC2"}, {"bufferView": 4, "byteOffset": 18256, "componentType": 5123, "count": 2401, "type": "VEC4"}, {"bufferView": 3, "byteOffset": 74928, "componentType": 5126, "count": 2401, "type": "VEC4"}, {"bufferView": 0, "byteOffset": 51720, "componentType": 5125, "count": 12219, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 119304, "componentType": 5126, "count": 2571, "max": [0.4140944480895996, 0.12913605570793152, 1.615830659866333], "min": [-0.4141703248023987, -0.14196182787418365, 1.0276144742965698], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 150156, "componentType": 5126, "count": 2571, "type": "VEC3"}, {"bufferView": 3, "byteOffset": 113344, "componentType": 5126, "count": 2571, "type": "VEC4"}, {"bufferView": 1, "byteOffset": 39768, "componentType": 5126, "count": 2571, "type": "VEC2"}, {"bufferView": 4, "byteOffset": 37464, "componentType": 5123, "count": 2571, "type": "VEC4"}, {"bufferView": 3, "byteOffset": 154480, "componentType": 5126, "count": 2571, "type": "VEC4"}, {"bufferView": 0, "byteOffset": 100596, "componentType": 5125, "count": 13797, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 181008, "componentType": 5126, "count": 120, "max": [-0.013288690708577633, -0.09070298820734024, 1.7427678108215332], "min": [-0.0525328703224659, -0.11028489470481873, 1.7033462524414062], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 182448, "componentType": 5126, "count": 120, "type": "VEC3"}, {"bufferView": 1, "byteOffset": 60336, "componentType": 5126, "count": 120, "type": "VEC2"}, {"bufferView": 4, "byteOffset": 58032, "componentType": 5123, "count": 120, "type": "VEC4"}, {"bufferView": 3, "byteOffset": 195616, "componentType": 5126, "count": 120, "type": "VEC4"}, {"bufferView": 0, "byteOffset": 155784, "componentType": 5125, "count": 618, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 183888, "componentType": 5126, "count": 970, "max": [0.08127328008413315, 0.0076716891489923, 1.7350800037384033], "min": [-0.08127350360155106, -0.12752661108970642, 1.687604546546936], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 195528, "componentType": 5126, "count": 970, "type": "VEC3"}, {"bufferView": 1, "byteOffset": 61296, "componentType": 5126, "count": 970, "type": "VEC2"}, {"bufferView": 4, "byteOffset": 58992, "componentType": 5123, "count": 970, "type": "VEC4"}, {"bufferView": 3, "byteOffset": 197536, "componentType": 5126, "count": 970, "type": "VEC4"}, {"bufferView": 0, "byteOffset": 158256, "componentType": 5125, "count": 2892, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 207168, "componentType": 5126, "count": 84, "max": [0.028552936390042305, -0.0531608946621418, 1.6715738773345947], "min": [-0.02855316735804081, -0.12130891531705856, 1.6358819007873535], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 208176, "componentType": 5126, "count": 84, "type": "VEC3"}, {"bufferView": 1, "byteOffset": 69056, "componentType": 5126, "count": 84, "type": "VEC2"}, {"bufferView": 4, "byteOffset": 66752, "componentType": 5123, "count": 84, "type": "VEC4"}, {"bufferView": 3, "byteOffset": 213056, "componentType": 5126, "count": 84, "type": "VEC4"}, {"bufferView": 0, "byteOffset": 169824, "componentType": 5125, "count": 300, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 209184, "componentType": 5126, "count": 970, "max": [0.2195514440536499, 0.11533472687005997, 0.1764206737279892], "min": [-0.21955320239067078, -0.19973576068878174, 0.0011999111156910658], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 220824, "componentType": 5126, "count": 970, "type": "VEC3"}, {"bufferView": 3, "byteOffset": 214400, "componentType": 5126, "count": 970, "type": "VEC4"}, {"bufferView": 1, "byteOffset": 69728, "componentType": 5126, "count": 970, "type": "VEC2"}, {"bufferView": 4, "byteOffset": 67424, "componentType": 5123, "count": 970, "type": "VEC4"}, {"bufferView": 3, "byteOffset": 229920, "componentType": 5126, "count": 970, "type": "VEC4"}, {"bufferView": 0, "byteOffset": 171024, "componentType": 5125, "count": 4650, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 232464, "componentType": 5126, "count": 120, "max": [0.05241195112466812, -0.09070294350385666, 1.7427279949188232], "min": [0.013011124916374683, -0.11028484255075455, 1.7033874988555908], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 233904, "componentType": 5126, "count": 120, "type": "VEC3"}, {"bufferView": 1, "byteOffset": 77488, "componentType": 5126, "count": 120, "type": "VEC2"}, {"bufferView": 4, "byteOffset": 75184, "componentType": 5123, "count": 120, "type": "VEC4"}, {"bufferView": 3, "byteOffset": 245440, "componentType": 5126, "count": 120, "type": "VEC4"}, {"bufferView": 0, "byteOffset": 189624, "componentType": 5125, "count": 618, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 235344, "componentType": 5126, "count": 756, "max": [0.10085504502058029, 0.08899738639593124, 1.8456664085388184], "min": [-0.09734336286783218, -0.13789856433868408, 1.6472235918045044], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 244416, "componentType": 5126, "count": 756, "type": "VEC3"}, {"bufferView": 3, "byteOffset": 247360, "componentType": 5126, "count": 756, "type": "VEC4"}, {"bufferView": 1, "byteOffset": 78448, "componentType": 5126, "count": 756, "type": "VEC2"}, {"bufferView": 4, "byteOffset": 76144, "componentType": 5123, "count": 756, "type": "VEC4"}, {"bufferView": 3, "byteOffset": 259456, "componentType": 5126, "count": 756, "type": "VEC4"}, {"bufferView": 0, "byteOffset": 192096, "componentType": 5125, "count": 4086, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 253488, "componentType": 5126, "count": 1022, "max": [0.5308413505554199, 0.07798825949430466, 1.2235171794891357], "min": [-0.5308485627174377, -0.14855925738811493, 0.8881329298019409], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 265752, "componentType": 5126, "count": 1022, "type": "VEC3"}, {"bufferView": 3, "byteOffset": 271552, "componentType": 5126, "count": 1022, "type": "VEC4"}, {"bufferView": 1, "byteOffset": 84496, "componentType": 5126, "count": 1022, "type": "VEC2"}, {"bufferView": 4, "byteOffset": 82192, "componentType": 5123, "count": 1022, "type": "VEC4"}, {"bufferView": 3, "byteOffset": 287904, "componentType": 5126, "count": 1022, "type": "VEC4"}, {"bufferView": 0, "byteOffset": 208440, "componentType": 5125, "count": 4878, "type": "SCALAR"}, {"bufferView": 5, "componentType": 5126, "count": 6, "type": "MAT4"}, {"bufferView": 5, "byteOffset": 384, "componentType": 5126, "count": 10, "type": "MAT4"}, {"bufferView": 5, "byteOffset": 1024, "componentType": 5126, "count": 14, "type": "MAT4"}, {"bufferView": 5, "byteOffset": 1920, "componentType": 5126, "count": 1, "type": "MAT4"}, {"bufferView": 5, "byteOffset": 1984, "componentType": 5126, "count": 2, "type": "MAT4"}, {"bufferView": 5, "byteOffset": 2112, "componentType": 5126, "count": 2, "type": "MAT4"}, {"bufferView": 5, "byteOffset": 2240, "componentType": 5126, "count": 6, "type": "MAT4"}, {"bufferView": 5, "byteOffset": 2624, "componentType": 5126, "count": 1, "type": "MAT4"}, {"bufferView": 5, "byteOffset": 2688, "componentType": 5126, "count": 2, "type": "MAT4"}, {"bufferView": 5, "byteOffset": 2816, "componentType": 5126, "count": 36, "type": "MAT4"}, {"bufferView": 8, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 128, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 384, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 144, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 272, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 432, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 400, "componentType": 5126, "count": 12, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 816, "componentType": 5126, "count": 12, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 448, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 512, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 576, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 960, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 704, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 1344, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 720, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 1024, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 848, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 1392, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 976, "componentType": 5126, "count": 6, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 1776, "componentType": 5126, "count": 6, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 1000, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 1536, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 1128, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 1848, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 1256, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 2232, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 1272, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 2048, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 1400, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 2280, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 1528, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 2664, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 1544, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 2560, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 1672, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 2712, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 1800, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 3096, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 1816, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 3072, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 1944, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 3144, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 2072, "componentType": 5126, "count": 6, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 3528, "componentType": 5126, "count": 6, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 2096, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 3584, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 2224, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 3600, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 2352, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 3984, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 2384, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 4096, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 2512, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 4080, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 2640, "componentType": 5126, "count": 10, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 4464, "componentType": 5126, "count": 10, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 2680, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 4608, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 2808, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 4584, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 2936, "componentType": 5126, "count": 6, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 4968, "componentType": 5126, "count": 6, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 2960, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 5120, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 3088, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 5040, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 3216, "componentType": 5126, "count": 6, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 5424, "componentType": 5126, "count": 6, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 3240, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 5632, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 3368, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 5496, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 3496, "componentType": 5126, "count": 10, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 5880, "componentType": 5126, "count": 10, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 3536, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 6144, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 3664, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 6000, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 3792, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 6384, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 3808, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 6656, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 3936, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 6432, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 4064, "componentType": 5126, "count": 6, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 6816, "componentType": 5126, "count": 6, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 4088, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 7168, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 4216, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 6888, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 4344, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 7272, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 4360, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 7680, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 4488, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 7320, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 4616, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 7704, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 4632, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 8192, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 4760, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 7752, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 4888, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 8136, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 4904, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 8704, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 5032, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 8184, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 5160, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 8568, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 5176, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 9216, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 5304, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 8616, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 5432, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 9000, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 5464, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 9728, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 5592, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 9096, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 5720, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 9480, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 5752, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 10240, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 5880, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 9576, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 6008, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 9960, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 6024, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 10752, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 6152, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 10008, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 6280, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 10392, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 6296, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 11264, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 6424, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 10440, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 6552, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 10824, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 6568, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 11776, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 6696, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 10872, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 6824, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 11256, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 6840, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 12288, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 6968, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 11304, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 7096, "componentType": 5126, "count": 14, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 11688, "componentType": 5126, "count": 14, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 7152, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 12800, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 7280, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 11856, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 7408, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 12240, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 7424, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 13312, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 7552, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 12288, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 7680, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 12672, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 7696, "componentType": 5126, "count": 30, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 13824, "componentType": 5126, "count": 30, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 7816, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 12720, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 7944, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 13104, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 7976, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 14304, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 8104, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 13200, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 8232, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 13584, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 8248, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 14816, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 8376, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 13632, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 8504, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 14016, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 8520, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 15328, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 8648, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 14064, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 8776, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 14448, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 8792, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 15840, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 8920, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 14496, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 9048, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 16352, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 9176, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 14880, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 9304, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 15264, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 9320, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 16864, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 9448, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 15312, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 9576, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 15696, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 9592, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 17376, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 9720, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 15744, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 9848, "componentType": 5126, "count": 6, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 16128, "componentType": 5126, "count": 6, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 9872, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 17888, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 10000, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 16200, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 10128, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 16584, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 10160, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 18400, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 10288, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 16680, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 10416, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 17064, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 10448, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 18912, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 10576, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 17160, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 10704, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 17544, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 10720, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 19424, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 10848, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 17592, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 10976, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 17976, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 11008, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 19936, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 11136, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 18072, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 11264, "componentType": 5126, "count": 12, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 18456, "componentType": 5126, "count": 12, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 11312, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 20448, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 11440, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 18600, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 11568, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 18984, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 11584, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 20960, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 11712, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 19032, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 11840, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 19416, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 11856, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 21472, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 11984, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 19464, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 12112, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 19848, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 12128, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 21984, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 12256, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 19896, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 12384, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 20280, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 12400, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 22496, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 12528, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 20328, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 12656, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 20712, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 12672, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 23008, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 12800, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 20760, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 12928, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 21144, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 12960, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 23520, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 13088, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 21240, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 13216, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 21624, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 13248, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 24032, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 13376, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 21720, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 13504, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 22104, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 13536, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 24544, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 13664, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 22200, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 13792, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 22584, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 13808, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 25056, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 13936, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 22632, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 14064, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 23016, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 14080, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 25568, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 14208, "componentType": 5126, "count": 12, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 23064, "componentType": 5126, "count": 12, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 14256, "componentType": 5126, "count": 10, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 23208, "componentType": 5126, "count": 10, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 14296, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 26080, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 14424, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 23328, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 14552, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 26592, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 14680, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 23712, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 14808, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 24096, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 14840, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 27104, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 14968, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 24192, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 15096, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 24576, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 15112, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 27616, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 15240, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 24624, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 15368, "componentType": 5126, "count": 6, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 25008, "componentType": 5126, "count": 6, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 15392, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 28128, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 15520, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 25080, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 15648, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 25464, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 15680, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 28640, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 15808, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 25560, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 15936, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 25944, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 15952, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 29152, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 16080, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 25992, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 16208, "componentType": 5126, "count": 6, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 26376, "componentType": 5126, "count": 6, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 16232, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 29664, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 16360, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 26448, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 16488, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 26832, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 16520, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 30176, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 16648, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 26928, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 16776, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 30688, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 16904, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 27312, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 17032, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 27696, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 17048, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 31200, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 17176, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 27744, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 17304, "componentType": 5126, "count": 6, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 28128, "componentType": 5126, "count": 6, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 17328, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 31712, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 17456, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 28200, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 17584, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 28584, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 17600, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 32224, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 17728, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 28632, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 17856, "componentType": 5126, "count": 8, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 29016, "componentType": 5126, "count": 8, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 17888, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 32736, "componentType": 5126, "count": 32, "type": "VEC4"}, {"bufferView": 8, "byteOffset": 18016, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 29112, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 18144, "componentType": 5126, "count": 4, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 6, "byteOffset": 29496, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 18160, "componentType": 5126, "count": 32, "max": [1.0333333015441895], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 33248, "componentType": 5126, "count": 32, "type": "VEC4"}], "animations": [{"channels": [{"sampler": 0, "target": {"node": 12, "path": "translation"}}, {"sampler": 1, "target": {"node": 12, "path": "scale"}}, {"sampler": 2, "target": {"node": 12, "path": "rotation"}}, {"sampler": 3, "target": {"node": 11, "path": "translation"}}, {"sampler": 4, "target": {"node": 11, "path": "scale"}}, {"sampler": 5, "target": {"node": 11, "path": "rotation"}}, {"sampler": 6, "target": {"node": 10, "path": "translation"}}, {"sampler": 7, "target": {"node": 10, "path": "scale"}}, {"sampler": 8, "target": {"node": 10, "path": "rotation"}}, {"sampler": 9, "target": {"node": 21, "path": "translation"}}, {"sampler": 10, "target": {"node": 21, "path": "scale"}}, {"sampler": 11, "target": {"node": 21, "path": "rotation"}}, {"sampler": 12, "target": {"node": 20, "path": "translation"}}, {"sampler": 13, "target": {"node": 20, "path": "scale"}}, {"sampler": 14, "target": {"node": 20, "path": "rotation"}}, {"sampler": 15, "target": {"node": 19, "path": "translation"}}, {"sampler": 16, "target": {"node": 19, "path": "scale"}}, {"sampler": 17, "target": {"node": 19, "path": "rotation"}}, {"sampler": 18, "target": {"node": 18, "path": "translation"}}, {"sampler": 19, "target": {"node": 18, "path": "scale"}}, {"sampler": 20, "target": {"node": 18, "path": "rotation"}}, {"sampler": 21, "target": {"node": 26, "path": "translation"}}, {"sampler": 22, "target": {"node": 26, "path": "scale"}}, {"sampler": 23, "target": {"node": 26, "path": "rotation"}}, {"sampler": 24, "target": {"node": 25, "path": "translation"}}, {"sampler": 25, "target": {"node": 25, "path": "scale"}}, {"sampler": 26, "target": {"node": 25, "path": "rotation"}}, {"sampler": 27, "target": {"node": 24, "path": "translation"}}, {"sampler": 28, "target": {"node": 24, "path": "scale"}}, {"sampler": 29, "target": {"node": 24, "path": "rotation"}}, {"sampler": 30, "target": {"node": 23, "path": "translation"}}, {"sampler": 31, "target": {"node": 23, "path": "scale"}}, {"sampler": 32, "target": {"node": 23, "path": "rotation"}}, {"sampler": 33, "target": {"node": 31, "path": "translation"}}, {"sampler": 34, "target": {"node": 31, "path": "scale"}}, {"sampler": 35, "target": {"node": 31, "path": "rotation"}}, {"sampler": 36, "target": {"node": 30, "path": "translation"}}, {"sampler": 37, "target": {"node": 30, "path": "scale"}}, {"sampler": 38, "target": {"node": 30, "path": "rotation"}}, {"sampler": 39, "target": {"node": 29, "path": "translation"}}, {"sampler": 40, "target": {"node": 29, "path": "scale"}}, {"sampler": 41, "target": {"node": 29, "path": "rotation"}}, {"sampler": 42, "target": {"node": 28, "path": "translation"}}, {"sampler": 43, "target": {"node": 28, "path": "scale"}}, {"sampler": 44, "target": {"node": 28, "path": "rotation"}}, {"sampler": 45, "target": {"node": 36, "path": "translation"}}, {"sampler": 46, "target": {"node": 36, "path": "scale"}}, {"sampler": 47, "target": {"node": 36, "path": "rotation"}}, {"sampler": 48, "target": {"node": 35, "path": "translation"}}, {"sampler": 49, "target": {"node": 35, "path": "scale"}}, {"sampler": 50, "target": {"node": 35, "path": "rotation"}}, {"sampler": 51, "target": {"node": 34, "path": "translation"}}, {"sampler": 52, "target": {"node": 34, "path": "scale"}}, {"sampler": 53, "target": {"node": 34, "path": "rotation"}}, {"sampler": 54, "target": {"node": 33, "path": "translation"}}, {"sampler": 55, "target": {"node": 33, "path": "scale"}}, {"sampler": 56, "target": {"node": 33, "path": "rotation"}}, {"sampler": 57, "target": {"node": 41, "path": "translation"}}, {"sampler": 58, "target": {"node": 41, "path": "scale"}}, {"sampler": 59, "target": {"node": 41, "path": "rotation"}}, {"sampler": 60, "target": {"node": 40, "path": "translation"}}, {"sampler": 61, "target": {"node": 40, "path": "scale"}}, {"sampler": 62, "target": {"node": 40, "path": "rotation"}}, {"sampler": 63, "target": {"node": 39, "path": "translation"}}, {"sampler": 64, "target": {"node": 39, "path": "scale"}}, {"sampler": 65, "target": {"node": 39, "path": "rotation"}}, {"sampler": 66, "target": {"node": 38, "path": "translation"}}, {"sampler": 67, "target": {"node": 38, "path": "scale"}}, {"sampler": 68, "target": {"node": 38, "path": "rotation"}}, {"sampler": 69, "target": {"node": 17, "path": "translation"}}, {"sampler": 70, "target": {"node": 17, "path": "scale"}}, {"sampler": 71, "target": {"node": 17, "path": "rotation"}}, {"sampler": 72, "target": {"node": 16, "path": "translation"}}, {"sampler": 73, "target": {"node": 16, "path": "scale"}}, {"sampler": 74, "target": {"node": 16, "path": "rotation"}}, {"sampler": 75, "target": {"node": 15, "path": "translation"}}, {"sampler": 76, "target": {"node": 15, "path": "scale"}}, {"sampler": 77, "target": {"node": 15, "path": "rotation"}}, {"sampler": 78, "target": {"node": 14, "path": "translation"}}, {"sampler": 79, "target": {"node": 14, "path": "scale"}}, {"sampler": 80, "target": {"node": 14, "path": "rotation"}}, {"sampler": 81, "target": {"node": 72, "path": "translation"}}, {"sampler": 82, "target": {"node": 72, "path": "scale"}}, {"sampler": 83, "target": {"node": 72, "path": "rotation"}}, {"sampler": 84, "target": {"node": 50, "path": "translation"}}, {"sampler": 85, "target": {"node": 50, "path": "scale"}}, {"sampler": 86, "target": {"node": 50, "path": "rotation"}}, {"sampler": 87, "target": {"node": 49, "path": "translation"}}, {"sampler": 88, "target": {"node": 49, "path": "scale"}}, {"sampler": 89, "target": {"node": 49, "path": "rotation"}}, {"sampler": 90, "target": {"node": 48, "path": "translation"}}, {"sampler": 91, "target": {"node": 48, "path": "scale"}}, {"sampler": 92, "target": {"node": 48, "path": "rotation"}}, {"sampler": 93, "target": {"node": 47, "path": "translation"}}, {"sampler": 94, "target": {"node": 47, "path": "scale"}}, {"sampler": 95, "target": {"node": 47, "path": "rotation"}}, {"sampler": 96, "target": {"node": 55, "path": "translation"}}, {"sampler": 97, "target": {"node": 55, "path": "rotation"}}, {"sampler": 98, "target": {"node": 54, "path": "translation"}}, {"sampler": 99, "target": {"node": 54, "path": "scale"}}, {"sampler": 100, "target": {"node": 54, "path": "rotation"}}, {"sampler": 101, "target": {"node": 53, "path": "translation"}}, {"sampler": 102, "target": {"node": 53, "path": "scale"}}, {"sampler": 103, "target": {"node": 53, "path": "rotation"}}, {"sampler": 104, "target": {"node": 52, "path": "translation"}}, {"sampler": 105, "target": {"node": 52, "path": "scale"}}, {"sampler": 106, "target": {"node": 52, "path": "rotation"}}, {"sampler": 107, "target": {"node": 60, "path": "translation"}}, {"sampler": 108, "target": {"node": 60, "path": "scale"}}, {"sampler": 109, "target": {"node": 60, "path": "rotation"}}, {"sampler": 110, "target": {"node": 59, "path": "translation"}}, {"sampler": 111, "target": {"node": 59, "path": "scale"}}, {"sampler": 112, "target": {"node": 59, "path": "rotation"}}, {"sampler": 113, "target": {"node": 58, "path": "translation"}}, {"sampler": 114, "target": {"node": 58, "path": "scale"}}, {"sampler": 115, "target": {"node": 58, "path": "rotation"}}, {"sampler": 116, "target": {"node": 57, "path": "translation"}}, {"sampler": 117, "target": {"node": 57, "path": "scale"}}, {"sampler": 118, "target": {"node": 57, "path": "rotation"}}, {"sampler": 119, "target": {"node": 65, "path": "translation"}}, {"sampler": 120, "target": {"node": 65, "path": "scale"}}, {"sampler": 121, "target": {"node": 65, "path": "rotation"}}, {"sampler": 122, "target": {"node": 64, "path": "translation"}}, {"sampler": 123, "target": {"node": 64, "path": "scale"}}, {"sampler": 124, "target": {"node": 64, "path": "rotation"}}, {"sampler": 125, "target": {"node": 63, "path": "translation"}}, {"sampler": 126, "target": {"node": 63, "path": "scale"}}, {"sampler": 127, "target": {"node": 63, "path": "rotation"}}, {"sampler": 128, "target": {"node": 62, "path": "translation"}}, {"sampler": 129, "target": {"node": 62, "path": "scale"}}, {"sampler": 130, "target": {"node": 62, "path": "rotation"}}, {"sampler": 131, "target": {"node": 70, "path": "translation"}}, {"sampler": 132, "target": {"node": 70, "path": "scale"}}, {"sampler": 133, "target": {"node": 70, "path": "rotation"}}, {"sampler": 134, "target": {"node": 69, "path": "translation"}}, {"sampler": 135, "target": {"node": 69, "path": "scale"}}, {"sampler": 136, "target": {"node": 69, "path": "rotation"}}, {"sampler": 137, "target": {"node": 68, "path": "translation"}}, {"sampler": 138, "target": {"node": 68, "path": "scale"}}, {"sampler": 139, "target": {"node": 68, "path": "rotation"}}, {"sampler": 140, "target": {"node": 67, "path": "translation"}}, {"sampler": 141, "target": {"node": 67, "path": "scale"}}, {"sampler": 142, "target": {"node": 67, "path": "rotation"}}, {"sampler": 143, "target": {"node": 46, "path": "translation"}}, {"sampler": 144, "target": {"node": 46, "path": "scale"}}, {"sampler": 145, "target": {"node": 46, "path": "rotation"}}, {"sampler": 146, "target": {"node": 45, "path": "translation"}}, {"sampler": 147, "target": {"node": 45, "path": "scale"}}, {"sampler": 148, "target": {"node": 45, "path": "rotation"}}, {"sampler": 149, "target": {"node": 44, "path": "translation"}}, {"sampler": 150, "target": {"node": 44, "path": "scale"}}, {"sampler": 151, "target": {"node": 44, "path": "rotation"}}, {"sampler": 152, "target": {"node": 43, "path": "translation"}}, {"sampler": 153, "target": {"node": 43, "path": "scale"}}, {"sampler": 154, "target": {"node": 43, "path": "rotation"}}, {"sampler": 155, "target": {"node": 9, "path": "translation"}}, {"sampler": 156, "target": {"node": 9, "path": "rotation"}}, {"sampler": 157, "target": {"node": 8, "path": "translation"}}, {"sampler": 158, "target": {"node": 8, "path": "scale"}}, {"sampler": 159, "target": {"node": 8, "path": "rotation"}}, {"sampler": 160, "target": {"node": 7, "path": "translation"}}, {"sampler": 161, "target": {"node": 7, "path": "scale"}}, {"sampler": 162, "target": {"node": 7, "path": "rotation"}}, {"sampler": 163, "target": {"node": 78, "path": "translation"}}, {"sampler": 164, "target": {"node": 78, "path": "scale"}}, {"sampler": 165, "target": {"node": 78, "path": "rotation"}}, {"sampler": 166, "target": {"node": 77, "path": "translation"}}, {"sampler": 167, "target": {"node": 77, "path": "scale"}}, {"sampler": 168, "target": {"node": 77, "path": "rotation"}}, {"sampler": 169, "target": {"node": 76, "path": "translation"}}, {"sampler": 170, "target": {"node": 76, "path": "scale"}}, {"sampler": 171, "target": {"node": 76, "path": "rotation"}}, {"sampler": 172, "target": {"node": 75, "path": "translation"}}, {"sampler": 173, "target": {"node": 75, "path": "scale"}}, {"sampler": 174, "target": {"node": 75, "path": "rotation"}}, {"sampler": 175, "target": {"node": 74, "path": "translation"}}, {"sampler": 176, "target": {"node": 74, "path": "scale"}}, {"sampler": 177, "target": {"node": 74, "path": "rotation"}}, {"sampler": 178, "target": {"node": 84, "path": "translation"}}, {"sampler": 179, "target": {"node": 84, "path": "rotation"}}, {"sampler": 180, "target": {"node": 83, "path": "translation"}}, {"sampler": 181, "target": {"node": 83, "path": "scale"}}, {"sampler": 182, "target": {"node": 83, "path": "rotation"}}, {"sampler": 183, "target": {"node": 82, "path": "translation"}}, {"sampler": 184, "target": {"node": 82, "path": "scale"}}, {"sampler": 185, "target": {"node": 82, "path": "rotation"}}, {"sampler": 186, "target": {"node": 81, "path": "translation"}}, {"sampler": 187, "target": {"node": 81, "path": "scale"}}, {"sampler": 188, "target": {"node": 81, "path": "rotation"}}, {"sampler": 189, "target": {"node": 80, "path": "translation"}}, {"sampler": 190, "target": {"node": 80, "path": "scale"}}, {"sampler": 191, "target": {"node": 80, "path": "rotation"}}, {"sampler": 192, "target": {"node": 6, "path": "translation"}}, {"sampler": 193, "target": {"node": 6, "path": "scale"}}, {"sampler": 194, "target": {"node": 6, "path": "rotation"}}], "name": "walk with case", "samplers": [{"input": 79, "interpolation": "LINEAR", "output": 80}, {"input": 81, "interpolation": "LINEAR", "output": 82}, {"input": 83, "interpolation": "LINEAR", "output": 84}, {"input": 85, "interpolation": "LINEAR", "output": 86}, {"input": 87, "interpolation": "LINEAR", "output": 88}, {"input": 89, "interpolation": "LINEAR", "output": 90}, {"input": 91, "interpolation": "LINEAR", "output": 92}, {"input": 93, "interpolation": "LINEAR", "output": 94}, {"input": 95, "interpolation": "LINEAR", "output": 96}, {"input": 97, "interpolation": "LINEAR", "output": 98}, {"input": 99, "interpolation": "LINEAR", "output": 100}, {"input": 101, "interpolation": "LINEAR", "output": 102}, {"input": 103, "interpolation": "LINEAR", "output": 104}, {"input": 105, "interpolation": "LINEAR", "output": 106}, {"input": 107, "interpolation": "LINEAR", "output": 108}, {"input": 109, "interpolation": "LINEAR", "output": 110}, {"input": 111, "interpolation": "LINEAR", "output": 112}, {"input": 113, "interpolation": "LINEAR", "output": 114}, {"input": 115, "interpolation": "LINEAR", "output": 116}, {"input": 117, "interpolation": "LINEAR", "output": 118}, {"input": 119, "interpolation": "LINEAR", "output": 120}, {"input": 121, "interpolation": "LINEAR", "output": 122}, {"input": 123, "interpolation": "LINEAR", "output": 124}, {"input": 125, "interpolation": "LINEAR", "output": 126}, {"input": 127, "interpolation": "LINEAR", "output": 128}, {"input": 129, "interpolation": "LINEAR", "output": 130}, {"input": 131, "interpolation": "LINEAR", "output": 132}, {"input": 133, "interpolation": "LINEAR", "output": 134}, {"input": 135, "interpolation": "LINEAR", "output": 136}, {"input": 137, "interpolation": "LINEAR", "output": 138}, {"input": 139, "interpolation": "LINEAR", "output": 140}, {"input": 141, "interpolation": "LINEAR", "output": 142}, {"input": 143, "interpolation": "LINEAR", "output": 144}, {"input": 145, "interpolation": "LINEAR", "output": 146}, {"input": 147, "interpolation": "LINEAR", "output": 148}, {"input": 149, "interpolation": "LINEAR", "output": 150}, {"input": 151, "interpolation": "LINEAR", "output": 152}, {"input": 153, "interpolation": "LINEAR", "output": 154}, {"input": 155, "interpolation": "LINEAR", "output": 156}, {"input": 157, "interpolation": "LINEAR", "output": 158}, {"input": 159, "interpolation": "LINEAR", "output": 160}, {"input": 161, "interpolation": "LINEAR", "output": 162}, {"input": 163, "interpolation": "LINEAR", "output": 164}, {"input": 165, "interpolation": "LINEAR", "output": 166}, {"input": 167, "interpolation": "LINEAR", "output": 168}, {"input": 169, "interpolation": "LINEAR", "output": 170}, {"input": 171, "interpolation": "LINEAR", "output": 172}, {"input": 173, "interpolation": "LINEAR", "output": 174}, {"input": 175, "interpolation": "LINEAR", "output": 176}, {"input": 177, "interpolation": "LINEAR", "output": 178}, {"input": 179, "interpolation": "LINEAR", "output": 180}, {"input": 181, "interpolation": "LINEAR", "output": 182}, {"input": 183, "interpolation": "LINEAR", "output": 184}, {"input": 185, "interpolation": "LINEAR", "output": 186}, {"input": 187, "interpolation": "LINEAR", "output": 188}, {"input": 189, "interpolation": "LINEAR", "output": 190}, {"input": 191, "interpolation": "LINEAR", "output": 192}, {"input": 193, "interpolation": "LINEAR", "output": 194}, {"input": 195, "interpolation": "LINEAR", "output": 196}, {"input": 197, "interpolation": "LINEAR", "output": 198}, {"input": 199, "interpolation": "LINEAR", "output": 200}, {"input": 201, "interpolation": "LINEAR", "output": 202}, {"input": 203, "interpolation": "LINEAR", "output": 204}, {"input": 205, "interpolation": "LINEAR", "output": 206}, {"input": 207, "interpolation": "LINEAR", "output": 208}, {"input": 209, "interpolation": "LINEAR", "output": 210}, {"input": 211, "interpolation": "LINEAR", "output": 212}, {"input": 213, "interpolation": "LINEAR", "output": 214}, {"input": 215, "interpolation": "LINEAR", "output": 216}, {"input": 217, "interpolation": "LINEAR", "output": 218}, {"input": 219, "interpolation": "LINEAR", "output": 220}, {"input": 221, "interpolation": "LINEAR", "output": 222}, {"input": 223, "interpolation": "LINEAR", "output": 224}, {"input": 225, "interpolation": "LINEAR", "output": 226}, {"input": 227, "interpolation": "LINEAR", "output": 228}, {"input": 229, "interpolation": "LINEAR", "output": 230}, {"input": 231, "interpolation": "LINEAR", "output": 232}, {"input": 233, "interpolation": "LINEAR", "output": 234}, {"input": 235, "interpolation": "LINEAR", "output": 236}, {"input": 237, "interpolation": "LINEAR", "output": 238}, {"input": 239, "interpolation": "LINEAR", "output": 240}, {"input": 241, "interpolation": "LINEAR", "output": 242}, {"input": 243, "interpolation": "LINEAR", "output": 244}, {"input": 245, "interpolation": "LINEAR", "output": 246}, {"input": 247, "interpolation": "LINEAR", "output": 248}, {"input": 249, "interpolation": "LINEAR", "output": 250}, {"input": 251, "interpolation": "LINEAR", "output": 252}, {"input": 253, "interpolation": "LINEAR", "output": 254}, {"input": 255, "interpolation": "LINEAR", "output": 256}, {"input": 257, "interpolation": "LINEAR", "output": 258}, {"input": 259, "interpolation": "LINEAR", "output": 260}, {"input": 261, "interpolation": "LINEAR", "output": 262}, {"input": 263, "interpolation": "LINEAR", "output": 264}, {"input": 265, "interpolation": "LINEAR", "output": 266}, {"input": 267, "interpolation": "LINEAR", "output": 268}, {"input": 269, "interpolation": "LINEAR", "output": 270}, {"input": 271, "interpolation": "LINEAR", "output": 272}, {"input": 273, "interpolation": "LINEAR", "output": 274}, {"input": 275, "interpolation": "LINEAR", "output": 276}, {"input": 277, "interpolation": "LINEAR", "output": 278}, {"input": 279, "interpolation": "LINEAR", "output": 280}, {"input": 281, "interpolation": "LINEAR", "output": 282}, {"input": 283, "interpolation": "LINEAR", "output": 284}, {"input": 285, "interpolation": "LINEAR", "output": 286}, {"input": 287, "interpolation": "LINEAR", "output": 288}, {"input": 289, "interpolation": "LINEAR", "output": 290}, {"input": 291, "interpolation": "LINEAR", "output": 292}, {"input": 293, "interpolation": "LINEAR", "output": 294}, {"input": 295, "interpolation": "LINEAR", "output": 296}, {"input": 297, "interpolation": "LINEAR", "output": 298}, {"input": 299, "interpolation": "LINEAR", "output": 300}, {"input": 301, "interpolation": "LINEAR", "output": 302}, {"input": 303, "interpolation": "LINEAR", "output": 304}, {"input": 305, "interpolation": "LINEAR", "output": 306}, {"input": 307, "interpolation": "LINEAR", "output": 308}, {"input": 309, "interpolation": "LINEAR", "output": 310}, {"input": 311, "interpolation": "LINEAR", "output": 312}, {"input": 313, "interpolation": "LINEAR", "output": 314}, {"input": 315, "interpolation": "LINEAR", "output": 316}, {"input": 317, "interpolation": "LINEAR", "output": 318}, {"input": 319, "interpolation": "LINEAR", "output": 320}, {"input": 321, "interpolation": "LINEAR", "output": 322}, {"input": 323, "interpolation": "LINEAR", "output": 324}, {"input": 325, "interpolation": "LINEAR", "output": 326}, {"input": 327, "interpolation": "LINEAR", "output": 328}, {"input": 329, "interpolation": "LINEAR", "output": 330}, {"input": 331, "interpolation": "LINEAR", "output": 332}, {"input": 333, "interpolation": "LINEAR", "output": 334}, {"input": 335, "interpolation": "LINEAR", "output": 336}, {"input": 337, "interpolation": "LINEAR", "output": 338}, {"input": 339, "interpolation": "LINEAR", "output": 340}, {"input": 341, "interpolation": "LINEAR", "output": 342}, {"input": 343, "interpolation": "LINEAR", "output": 344}, {"input": 345, "interpolation": "LINEAR", "output": 346}, {"input": 347, "interpolation": "LINEAR", "output": 348}, {"input": 349, "interpolation": "LINEAR", "output": 350}, {"input": 351, "interpolation": "LINEAR", "output": 352}, {"input": 353, "interpolation": "LINEAR", "output": 354}, {"input": 355, "interpolation": "LINEAR", "output": 356}, {"input": 357, "interpolation": "LINEAR", "output": 358}, {"input": 359, "interpolation": "LINEAR", "output": 360}, {"input": 361, "interpolation": "LINEAR", "output": 362}, {"input": 363, "interpolation": "LINEAR", "output": 364}, {"input": 365, "interpolation": "LINEAR", "output": 366}, {"input": 367, "interpolation": "LINEAR", "output": 368}, {"input": 369, "interpolation": "LINEAR", "output": 370}, {"input": 371, "interpolation": "LINEAR", "output": 372}, {"input": 373, "interpolation": "LINEAR", "output": 374}, {"input": 375, "interpolation": "LINEAR", "output": 376}, {"input": 377, "interpolation": "LINEAR", "output": 378}, {"input": 379, "interpolation": "LINEAR", "output": 380}, {"input": 381, "interpolation": "LINEAR", "output": 382}, {"input": 383, "interpolation": "LINEAR", "output": 384}, {"input": 385, "interpolation": "LINEAR", "output": 386}, {"input": 387, "interpolation": "LINEAR", "output": 388}, {"input": 389, "interpolation": "LINEAR", "output": 390}, {"input": 391, "interpolation": "LINEAR", "output": 392}, {"input": 393, "interpolation": "LINEAR", "output": 394}, {"input": 395, "interpolation": "LINEAR", "output": 396}, {"input": 397, "interpolation": "LINEAR", "output": 398}, {"input": 399, "interpolation": "LINEAR", "output": 400}, {"input": 401, "interpolation": "LINEAR", "output": 402}, {"input": 403, "interpolation": "LINEAR", "output": 404}, {"input": 405, "interpolation": "LINEAR", "output": 406}, {"input": 407, "interpolation": "LINEAR", "output": 408}, {"input": 409, "interpolation": "LINEAR", "output": 410}, {"input": 411, "interpolation": "LINEAR", "output": 412}, {"input": 413, "interpolation": "LINEAR", "output": 414}, {"input": 415, "interpolation": "LINEAR", "output": 416}, {"input": 417, "interpolation": "LINEAR", "output": 418}, {"input": 419, "interpolation": "LINEAR", "output": 420}, {"input": 421, "interpolation": "LINEAR", "output": 422}, {"input": 423, "interpolation": "LINEAR", "output": 424}, {"input": 425, "interpolation": "LINEAR", "output": 426}, {"input": 427, "interpolation": "LINEAR", "output": 428}, {"input": 429, "interpolation": "LINEAR", "output": 430}, {"input": 431, "interpolation": "LINEAR", "output": 432}, {"input": 433, "interpolation": "LINEAR", "output": 434}, {"input": 435, "interpolation": "LINEAR", "output": 436}, {"input": 437, "interpolation": "LINEAR", "output": 438}, {"input": 439, "interpolation": "LINEAR", "output": 440}, {"input": 441, "interpolation": "LINEAR", "output": 442}, {"input": 443, "interpolation": "LINEAR", "output": 444}, {"input": 445, "interpolation": "LINEAR", "output": 446}, {"input": 447, "interpolation": "LINEAR", "output": 448}, {"input": 449, "interpolation": "LINEAR", "output": 450}, {"input": 451, "interpolation": "LINEAR", "output": 452}, {"input": 453, "interpolation": "LINEAR", "output": 454}, {"input": 455, "interpolation": "LINEAR", "output": 456}, {"input": 457, "interpolation": "LINEAR", "output": 458}, {"input": 459, "interpolation": "LINEAR", "output": 460}, {"input": 461, "interpolation": "LINEAR", "output": 462}, {"input": 463, "interpolation": "LINEAR", "output": 464}, {"input": 465, "interpolation": "LINEAR", "output": 466}, {"input": 467, "interpolation": "LINEAR", "output": 468}]}], "asset": {"generator": "fab-model-conversion", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 227952, "target": 34963}, {"buffer": 0, "byteLength": 92672, "byteOffset": 227952, "byteStride": 8, "target": 34962}, {"buffer": 0, "byteLength": 278016, "byteOffset": 320624, "byteStride": 12, "target": 34962}, {"buffer": 0, "byteLength": 304256, "byteOffset": 598640, "byteStride": 16, "target": 34962}, {"buffer": 0, "byteLength": 90368, "byteOffset": 902896, "byteStride": 8, "target": 34962}, {"buffer": 0, "byteLength": 5120, "byteOffset": 993264}, {"buffer": 0, "byteLength": 29544, "byteOffset": 998384}, {"buffer": 0, "byteLength": 33760, "byteOffset": 1027928}, {"buffer": 0, "byteLength": 18288, "byteOffset": 1061688}], "buffers": [{"byteLength": 1079976, "uri": "scene.bin"}], "images": [{"uri": "textures/head_baseColor.jpeg"}, {"uri": "textures/bottom_baseColor.jpeg"}, {"uri": "textures/bottom_metallicRoughness.png"}, {"uri": "textures/bottom_normal.jpeg"}, {"uri": "textures/material_baseColor.jpeg"}, {"uri": "textures/material_normal.jpeg"}, {"uri": "textures/eyes_baseColor.jpeg"}, {"uri": "textures/glasses_baseColor.png"}, {"uri": "textures/teeth_baseColor.jpeg"}, {"uri": "textures/shoes_baseColor.jpeg"}, {"uri": "textures/shoes_metallicRoughness.png"}, {"uri": "textures/shoes_normal.jpeg"}, {"uri": "textures/hair_baseColor.jpeg"}, {"uri": "textures/hair_normal.png"}, {"uri": "textures/body_baseColor.jpeg"}, {"uri": "textures/body_normal.jpeg"}], "materials": [{"doubleSided": true, "name": "Material", "pbrMetallicRoughness": {"baseColorFactor": [0.0110878, 0.0110878, 0.0110878, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.9969088538639493}}, {"doubleSided": true, "name": "head", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.0, "roughnessFactor": 0.8614359353944898}}, {"doubleSided": true, "name": "bottom", "normalTexture": {"index": 3}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 1}, "metallicFactor": 0.0, "metallicRoughnessTexture": {"index": 2}}}, {"doubleSided": true, "name": "material", "normalTexture": {"index": 5}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 4}, "metallicFactor": 0.0, "roughnessFactor": 0.9050358583495263}}, {"doubleSided": true, "name": "eyes", "pbrMetallicRoughness": {"baseColorTexture": {"index": 6}, "metallicFactor": 0.0}}, {"alphaMode": "BLEND", "doubleSided": true, "name": "glasses", "pbrMetallicRoughness": {"baseColorTexture": {"index": 7}, "metallicFactor": 0.0, "roughnessFactor": 0.7593947379349493}}, {"doubleSided": true, "name": "teeth", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "shoes", "normalTexture": {"index": 11}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 9}, "metallicFactor": 0.0, "metallicRoughnessTexture": {"index": 10}}}, {"doubleSided": true, "name": "hair", "normalTexture": {"index": 13}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 12}, "metallicFactor": 0.0, "roughnessFactor": 0.8409117639992059}}, {"doubleSided": true, "name": "body", "normalTexture": {"index": 15}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 14}, "metallicFactor": 0.0, "roughnessFactor": 0.8308205978495098}}], "meshes": [{"name": "Cube_Material_0", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 0, "TEXCOORD_0": 2}, "indices": 3, "material": 0, "mode": 4}]}, {"name": "head_head_0", "primitives": [{"attributes": {"JOINTS_0": 7, "NORMAL": 5, "POSITION": 4, "TEXCOORD_0": 6, "WEIGHTS_0": 8}, "indices": 9, "material": 1, "mode": 4}]}, {"name": "bottom_bottom_0", "primitives": [{"attributes": {"JOINTS_0": 14, "NORMAL": 11, "POSITION": 10, "TANGENT": 12, "TEXCOORD_0": 13, "WEIGHTS_0": 15}, "indices": 16, "material": 2, "mode": 4}]}, {"name": "top_top_0", "primitives": [{"attributes": {"JOINTS_0": 21, "NORMAL": 18, "POSITION": 17, "TANGENT": 19, "TEXCOORD_0": 20, "WEIGHTS_0": 22}, "indices": 23, "material": 3, "mode": 4}]}, {"name": "eye_right_eyes_0", "primitives": [{"attributes": {"JOINTS_0": 27, "NORMAL": 25, "POSITION": 24, "TEXCOORD_0": 26, "WEIGHTS_0": 28}, "indices": 29, "material": 4, "mode": 4}]}, {"name": "glasses_glasses_0", "primitives": [{"attributes": {"JOINTS_0": 33, "NORMAL": 31, "POSITION": 30, "TEXCOORD_0": 32, "WEIGHTS_0": 34}, "indices": 35, "material": 5, "mode": 4}]}, {"name": "teeth_teeth_0", "primitives": [{"attributes": {"JOINTS_0": 39, "NORMAL": 37, "POSITION": 36, "TEXCOORD_0": 38, "WEIGHTS_0": 40}, "indices": 41, "material": 6, "mode": 4}]}, {"name": "shoes_shoes_0", "primitives": [{"attributes": {"JOINTS_0": 46, "NORMAL": 43, "POSITION": 42, "TANGENT": 44, "TEXCOORD_0": 45, "WEIGHTS_0": 47}, "indices": 48, "material": 7, "mode": 4}]}, {"name": "eye_left_eyes_0", "primitives": [{"attributes": {"JOINTS_0": 52, "NORMAL": 50, "POSITION": 49, "TEXCOORD_0": 51, "WEIGHTS_0": 53}, "indices": 54, "material": 4, "mode": 4}]}, {"name": "hair_hair_0", "primitives": [{"attributes": {"JOINTS_0": 59, "NORMAL": 56, "POSITION": 55, "TANGENT": 57, "TEXCOORD_0": 58, "WEIGHTS_0": 60}, "indices": 61, "material": 8, "mode": 4}]}, {"name": "body_body_0", "primitives": [{"attributes": {"JOINTS_0": 66, "NORMAL": 63, "POSITION": 62, "TANGENT": 64, "TEXCOORD_0": 65, "WEIGHTS_0": 67}, "indices": 68, "material": 9, "mode": 4}]}], "nodes": [{"children": [1, 87, 89, 91, 93, 95, 97, 99, 101, 103, 105], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 2.220446049250313e-16, -1.0, 0.0, 0.0, 1.0, 2.220446049250313e-16, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Sketchfab_model"}, {"children": [2], "matrix": [0.009999999776482582, 0.0, 0.0, 0.0, 0.0, 0.0, 0.009999999776482582, 0.0, 0.0, -0.009999999776482582, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "09e4487b46ae4e6ea31b657221c38b60.fbx"}, {"children": [3], "name": "Object_2"}, {"children": [4, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115], "name": "RootNode", "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [0.0, 0.0, 0.0]}, {"children": [5], "name": "Armature", "rotation": [8.146033536604591e-08, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [0.0, 0.0, 0.0]}, {"children": [6, 86, 88, 90, 92, 94, 96, 98, 100, 102, 104], "name": "Object_5"}, {"children": [7, 74, 80], "name": "mixamorig:Hips_01", "rotation": [-0.08164593577384949, 0.05351758748292923, 0.040573108941316605, 0.9943960905075073], "scale": [0.9999998807907104, 0.9999999403953552, 1.0], "translation": [-0.20695197582244873, 102.59954833984375, -0.03983867168426514]}, {"children": [8], "name": "mixamorig:Spine_02", "rotation": [0.08110713958740234, -0.039817243814468384, -0.03319219872355461, 0.9953564405441284], "scale": [1.0, 1.0000001192092896, 1.0], "translation": [-1.043081283569336e-06, 10.280517578125, -1.180405616760254]}, {"children": [9], "name": "mixamorig:Spine1_03", "rotation": [0.024734025821089745, -0.010581953451037407, -0.006064185872673988, 0.9996196627616882], "scale": [0.9999999403953552, 0.9999998211860657, 0.9999999403953552], "translation": [-3.725290298461914e-08, 12.072758674621582, -4.0605664253234863e-07]}, {"children": [10, 14, 43], "name": "mixamorig:Spine2_04", "rotation": [0.024822648614645004, -0.010582910850644112, -0.005719488020986319, 0.9996194839477539], "scale": [0.9999999403953552, 0.9999998807907104, 1.0], "translation": [1.1548399925231934e-07, 13.797440528869629, 8.111819624900818e-07]}, {"children": [11], "name": "mixamorig:Neck_05", "rotation": [-0.032008931040763855, -0.001989243784919381, 0.01690368354320526, 0.999342679977417], "scale": [1.0, 0.9999999403953552, 0.9999999403953552], "translation": [-1.3317912817001343e-07, 15.522093772888184, -2.1010637283325195e-06]}, {"children": [12], "name": "mixamorig:Head_06", "rotation": [0.005317806266248226, 0.03447755053639412, -0.008102381601929665, 0.9993584752082825], "scale": [0.9999998807907104, 0.9999997615814209, 0.9999999403953552], "translation": [-1.30385160446167e-07, 6.993485927581787, 3.1081202030181885]}, {"children": [13], "name": "mixamorig:HeadTop_End_07", "rotation": [-9.9316832535834e-10, 7.930794354571447e-10, 2.3283064365386963e-10, 1.0], "scale": [1.0, 0.9999999403953552, 0.9999999403953552], "translation": [-8.940696716308594e-08, 21.06243133544922, 9.360790252685547]}, {"name": "mixamorig:HeadTop_End_end_065", "rotation": [0.0, -3.469446951953614e-18, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [-2.220446049250313e-16, 23.048858642578125, 0.0]}, {"children": [15], "name": "mixamorig:LeftShoulder_08", "rotation": [0.5883970856666565, 0.40765488147735596, -0.5764008164405823, 0.39416810870170593], "scale": [0.9999999403953552, 0.9999998211860657, 0.9999998807907104], "translation": [5.518177032470703, 12.700860977172852, -0.3256809413433075]}, {"children": [16], "name": "mixamorig:LeftArm_09", "rotation": [0.4161953330039978, 0.21363306045532227, 0.02083881013095379, 0.8835768699645996], "scale": [0.9999999403953552, 0.9999998807907104, 0.9999998807907104], "translation": [3.2782554626464844e-07, 12.432161331176758, 3.576278402306343e-07]}, {"children": [17], "name": "mixamorig:LeftForeArm_010", "rotation": [-0.021800247952342033, -0.030551418662071228, 0.28668269515037537, 0.9572901129722595], "scale": [1.0, 0.9999999403953552, 1.0000001192092896], "translation": [2.86102294921875e-06, 23.891237258911133, 4.291534423828125e-06]}, {"children": [18, 23, 28, 33, 38], "name": "mixamorig:LeftHand_011", "rotation": [-0.0018773134797811508, -0.22947365045547485, -0.12111780792474747, 0.9657477736473083], "scale": [1.0, 0.9999997615814209, 0.9999999403953552], "translation": [3.039836883544922e-06, 24.82097816467285, -1.0505318641662598e-06]}, {"children": [19], "name": "mixamorig:LeftHandThumb1_012", "rotation": [0.15346182882785797, 0.05835915356874466, 0.18481482565402985, 0.9689618945121765], "scale": [1.0000001192092896, 1.0, 1.0], "translation": [-2.712315082550049, 3.3741979598999023, 1.058603048324585]}, {"children": [20], "name": "mixamorig:LeftHandThumb2_013", "rotation": [0.004672329407185316, -0.0012431697687134147, -0.11114753782749176, 0.9937921762466431], "scale": [1.000000238418579, 1.0000001192092896, 1.0], "translation": [-0.657433032989502, 3.504255771636963, -3.3974647521972656e-06]}, {"children": [21], "name": "mixamorig:LeftHandThumb3_014", "rotation": [-0.13540776073932648, 0.0025707741733640432, -0.011217287741601467, 0.9907231330871582], "scale": [0.9999999403953552, 1.0000001192092896, 0.9999997615814209], "translation": [0.08183979988098145, 3.867893695831299, -1.1920928955078125e-06]}, {"children": [22], "name": "mixamorig:LeftHandThumb4_015", "rotation": [1.853331781376255e-07, 2.337619520176304e-07, -2.561137080192566e-07, 1.0], "scale": [1.0, 1.0, 1.0000001192092896], "translation": [0.575584888458252, 3.1933984756469727, -1.7285346984863281e-06]}, {"name": "mixamorig:LeftHandThumb4_end_066", "rotation": [1.3010426069826053e-17, -6.938893903907228e-18, 9.027796614315168e-35, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [-3.552713678800501e-15, 3.2448599338531494, 0.0]}, {"children": [24], "name": "mixamorig:LeftHandIndex1_016", "rotation": [0.093904510140419, 0.003346175653859973, 0.0362187959253788, 0.994916558265686], "scale": [0.9999998807907104, 0.9999999403953552, 1.0], "translation": [-3.537043809890747, 10.697574615478516, 0.0938650518655777]}, {"children": [25], "name": "mixamorig:LeftHandIndex2_017", "rotation": [0.1336372047662735, 0.00012805893493350595, -0.02921084128320217, 0.9905996918678284], "scale": [1.0, 0.9999998807907104, 0.9999999403953552], "translation": [0.08136427402496338, 3.840723991394043, 1.087784767150879e-06]}, {"children": [26], "name": "mixamorig:LeftHandIndex3_018", "rotation": [0.1336362212896347, 0.001489373971708119, -0.014193631708621979, 0.9909276962280273], "scale": [0.9999998807907104, 1.0000001192092896, 1.0], "translation": [-0.03984564542770386, 3.6096324920654297, 1.0915100574493408e-06]}, {"children": [27], "name": "mixamorig:LeftHandIndex4_019", "rotation": [-9.487850505252027e-09, -5.122273272206712e-09, -6.28642737865448e-09, 1.0], "scale": [1.000000238418579, 0.9999999403953552, 1.0], "translation": [-0.041514426469802856, 3.1644349098205566, -2.682209014892578e-07]}, {"name": "mixamorig:LeftHandIndex4_end_067", "rotation": [-9.990923019453923e-17, -3.4662977417047614e-34, 3.469446951953614e-18, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [-1.7763568394002505e-15, 3.1647188663482666, 1.0658141036401503e-14]}, {"children": [29], "name": "mixamorig:LeftHandMiddle1_020", "rotation": [0.16164973378181458, 0.0059486739337444305, 0.02761206217110157, 0.9864438772201538], "scale": [1.0, 1.0, 1.000000238418579], "translation": [-1.2238845825195312, 10.947479248046875, 0.055167607963085175]}, {"children": [30], "name": "mixamorig:LeftHandMiddle2_021", "rotation": [0.20105628669261932, -9.731084901432041e-06, -0.032518111169338226, 0.9790397882461548], "scale": [1.0000001192092896, 1.0, 1.0000001192092896], "translation": [0.07205712795257568, 3.960819959640503, 1.3336539268493652e-06]}, {"children": [31], "name": "mixamorig:LeftHandMiddle3_022", "rotation": [0.2010602504014969, 0.0017419712385162711, -0.021418357267975807, 0.9793431162834167], "scale": [0.9999997019767761, 0.9999998807907104, 1.0], "translation": [-0.03163328766822815, 3.870354175567627, -1.5795230865478516e-06]}, {"children": [32], "name": "mixamorig:LeftHandMiddle4_023", "rotation": [7.043126970529556e-09, 8.963979070131245e-08, -6.28642737865448e-09, 1.0], "scale": [1.0000001192092896, 0.9999998211860657, 1.0], "translation": [-0.0404244065284729, 3.3243460655212402, 1.1056661605834961e-05]}, {"name": "mixamorig:LeftHandMiddle4_end_068", "rotation": [4.130810277169772e-17, 8.957266953265831e-35, 2.168404344971009e-18, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [0.0, 3.324585199356079, 7.105427357601002e-15]}, {"children": [34], "name": "mixamorig:LeftHandRing1_024", "rotation": [0.2326051890850067, 0.007198268547654152, 0.013473590835928917, 0.9724512696266174], "scale": [1.0, 1.0, 1.0000001192092896], "translation": [1.369489312171936, 10.611247062683105, -0.1551252007484436]}, {"children": [35], "name": "mixamorig:LeftHandRing2_025", "rotation": [0.2713182866573334, -6.211920117493719e-05, -0.03298480063676834, 0.9619243144989014], "scale": [1.0000001192092896, 0.9999999403953552, 0.9999998807907104], "translation": [0.022001445293426514, 3.6295650005340576, -2.630986273288727e-07]}, {"children": [36], "name": "mixamorig:LeftHandRing3_026", "rotation": [0.27132439613342285, 0.0010539268841966987, -0.026486152783036232, 0.9621228575706482], "scale": [1.0, 0.9999999403953552, 0.9999999403953552], "translation": [-0.01608431339263916, 3.4608535766601562, 5.1781535148620605e-06]}, {"children": [37], "name": "mixamorig:LeftHandRing4_027", "rotation": [-1.2631063306400847e-08, 7.366762702076812e-07, 8.987262845039368e-08, 1.0], "scale": [0.9999999403953552, 1.0, 0.9999999403953552], "translation": [-0.00591568648815155, 2.9237465858459473, -8.715316653251648e-06]}, {"name": "mixamorig:LeftHandRing4_end_069", "rotation": [2.2876665839444144e-17, 1.734723475976807e-18, -6.938893903907228e-18, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [0.0, 2.9237656593322754, 0.0]}, {"children": [39], "name": "mixamorig:LeftHandPinky1_028", "rotation": [0.26884153485298157, 0.0124521404504776, 0.004424833692610264, 0.9630937576293945], "scale": [1.0, 0.9999998807907104, 0.9999999403953552], "translation": [3.3914341926574707, 9.465256690979004, 0.18506473302841187]}, {"children": [40], "name": "mixamorig:LeftHandPinky2_029", "rotation": [0.3073912560939789, 9.88048268482089e-05, -0.025426043197512627, 0.9512434601783752], "scale": [0.9999999403953552, 1.0000001192092896, 0.9999999403953552], "translation": [-0.017263948917388916, 3.0354080200195312, 1.327134313555689e-08]}, {"children": [41], "name": "mixamorig:LeftHandPinky3_030", "rotation": [0.3073824346065521, -0.0011544423177838326, -0.0304593313485384, 0.9510977864265442], "scale": [1.0, 1.0000001192092896, 0.9999998211860657], "translation": [0.010171189904212952, 2.576512575149536, 3.9711594581604e-06]}, {"children": [42], "name": "mixamorig:LeftHandPinky4_031", "rotation": [2.1274900063872337e-08, 9.554786828402939e-08, -2.5756889954209328e-09, 1.0], "scale": [1.0, 0.9999999403953552, 1.0000001192092896], "translation": [0.007092952728271484, 2.22953200340271, 3.7029385566711426e-06]}, {"name": "mixamorig:LeftHandPinky4_end_070", "rotation": [-3.0357660829594124e-18, -1.734723475976807e-18, 3.469446951953614e-18, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [-8.881784197001252e-16, 2.2295472621917725, 0.0]}, {"children": [44], "name": "mixamorig:RightShoulder_032", "rotation": [0.6497906446456909, -0.33319932222366333, 0.5826473832130432, 0.3567526042461395], "scale": [1.0, 1.0000001192092896, 1.0000001192092896], "translation": [-5.518177509307861, 12.70047378540039, -0.3222293257713318]}, {"children": [45], "name": "mixamorig:RightArm_033", "rotation": [0.39874351024627686, -0.08987711369991302, -0.020673273131251335, 0.912413477897644], "scale": [0.9999999403953552, 1.0, 1.0], "translation": [-1.430511474609375e-06, 12.432175636291504, 2.3677945137023926e-05]}, {"children": [46], "name": "mixamorig:RightForeArm_034", "rotation": [-0.014514954760670662, 0.021482717245817184, -0.17253094911575317, 0.9846628308296204], "scale": [0.9999998807907104, 0.9999999403953552, 1.0], "translation": [-2.86102294921875e-06, 23.89581298828125, -5.960464477539062e-07]}, {"children": [47, 52, 57, 62, 67, 72], "name": "mixamorig:RightHand_035", "rotation": [-0.07102517038583755, 0.020616209134459496, 0.12027421593666077, 0.9899820685386658], "scale": [1.0, 1.0, 0.9999999403953552], "translation": [4.76837158203125e-07, 24.81507110595703, -1.8030405044555664e-06]}, {"children": [48], "name": "mixamorig:RightHandThumb1_00", "rotation": [0.1931425780057907, -0.0672530010342598, -0.16153310239315033, 0.9654428958892822], "scale": [0.9999998211860657, 0.9999999403953552, 1.0000001192092896], "translation": [2.9158639907836914, 3.5041260719299316, 1.15128493309021]}, {"children": [49], "name": "mixamorig:RightHandThumb2_036", "rotation": [-0.000711699714884162, 0.003976538311690092, 0.06655170768499374, 0.9977747797966003], "scale": [0.9999999403953552, 0.9999998807907104, 0.9999998211860657], "translation": [0.8139395713806152, 3.67229962348938, 7.152557373046875e-07]}, {"children": [50], "name": "mixamorig:RightHandThumb3_037", "rotation": [-0.13341586291790009, -0.006407852750271559, -0.032182227820158005, 0.9905167818069458], "scale": [1.0000001192092896, 1.0, 1.0], "translation": [-0.17835187911987305, 3.729379177093506, 1.1920928955078125e-06]}, {"children": [51], "name": "mixamorig:RightHandThumb4_038", "rotation": [-2.421439049271612e-08, 2.9802322387695312e-08, -4.842878098543224e-08, 1.0], "scale": [0.9999999403953552, 0.9999998211860657, 1.0], "translation": [-0.6355957984924316, 2.8894095420837402, -1.7881393432617188e-06]}, {"name": "mixamorig:RightHandThumb4_end_071", "rotation": [2.0816681711721685e-17, 2.7755575615628914e-17, -5.7777898331617076e-34, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [0.0, 2.958495616912842, 7.105427357601002e-15]}, {"children": [53], "name": "mixamorig:RightHandIndex1_039", "rotation": [0.4655839800834656, -0.01661304198205471, -0.006880799774080515, 0.8848209977149963], "scale": [0.9999999403953552, 0.9999998807907104, 0.9999998807907104], "translation": [3.7296037673950195, 10.940314292907715, 0.007778942584991455]}, {"children": [54], "name": "mixamorig:RightHandIndex2_040", "rotation": [0.760138750076294, 0.0024312210734933615, 0.07090093195438385, 0.6458763480186462], "scale": [1.0, 0.9999999403953552, 0.9999998807907104], "translation": [-0.06703376770019531, 3.8157105445861816, -3.4570693969726562e-06]}, {"children": [55], "name": "mixamorig:RightHandIndex3_041", "rotation": [0.5811095237731934, -0.005853369366377592, 0.04689924418926239, 0.8124517798423767], "scale": [1.0, 0.9999998211860657, 0.9999999403953552], "translation": [0.036490440368652344, 3.5836398601531982, 1.7881393432617188e-07]}, {"children": [56], "name": "mixamorig:RightHandIndex4_042", "rotation": [-1.303851426825986e-08, 5.122274160385132e-09, -5.587935447692871e-09, 1.0], "scale": [1.0, 1.0000001192092896, 1.0000001192092896], "translation": [0.030548095703125, 3.0748584270477295, 1.0728836059570312e-06]}, {"name": "mixamorig:RightHandIndex4_end_072", "rotation": [3.469446951953614e-17, -2.7755575615628914e-17, -2.0816681711721685e-17, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [-4.440892098500626e-16, 3.0750160217285156, -3.552713678800501e-15]}, {"children": [58], "name": "mixamorig:RightHandMiddle1_043", "rotation": [0.5232528448104858, -0.02004033327102661, -8.294146391563118e-05, 0.8519417643547058], "scale": [1.0, 0.9999999403953552, 0.9999999403953552], "translation": [1.3463246822357178, 10.749543190002441, 0.08384507894515991]}, {"children": [59], "name": "mixamorig:RightHandMiddle2_044", "rotation": [0.7264344692230225, 0.0021874697413295507, 0.06544335186481476, 0.6841091513633728], "scale": [1.0000001192092896, 0.9999999403953552, 1.0], "translation": [-0.06409776210784912, 4.146315574645996, 6.154179573059082e-06]}, {"children": [60], "name": "mixamorig:RightHandMiddle3_045", "rotation": [0.5752171874046326, -0.005042628850787878, 0.045888517051935196, 0.8166970014572144], "scale": [0.9999998807907104, 1.0000001192092896, 0.9999998807907104], "translation": [0.030823588371276855, 3.8537721633911133, -3.7550926208496094e-06]}, {"children": [61], "name": "mixamorig:RightHandMiddle4_046", "rotation": [3.725290742551124e-09, 5.308539385850963e-08, 3.539026494081554e-08, 1.0], "scale": [0.9999998807907104, 0.9999998807907104, 0.9999997615814209], "translation": [0.03326845169067383, 3.356839179992676, -2.3245811462402344e-06]}, {"name": "mixamorig:RightHandMiddle4_end_073", "rotation": [-4.5102810375396984e-17, 2.7755575615628914e-17, 1.2518544638517033e-33, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [-1.7763568394002505e-15, 3.3569867610931396, 3.552713678800501e-15]}, {"children": [63], "name": "mixamorig:RightHandRing1_047", "rotation": [0.5491124987602234, -0.021630529314279556, 0.0076146735809743404, 0.8354337811470032], "scale": [1.0, 1.0, 1.0000001192092896], "translation": [-1.366539478302002, 10.595291137695312, -0.10302817821502686]}, {"children": [64], "name": "mixamorig:RightHandRing2_048", "rotation": [0.7241536974906921, 0.0007116503547877073, 0.0643562451004982, 0.6866288781166077], "scale": [1.0, 0.9999999403953552, 0.9999998807907104], "translation": [-0.025052189826965332, 3.7124478816986084, 1.125037670135498e-06]}, {"children": [65], "name": "mixamorig:RightHandRing3_049", "rotation": [0.558406412601471, -0.0022464769426733255, 0.04618183895945549, 0.8282780051231384], "scale": [1.0, 1.0, 1.0000003576278687], "translation": [0.014943361282348633, 3.427595615386963, -5.364418029785156e-07]}, {"children": [66], "name": "mixamorig:RightHandRing4_050", "rotation": [6.51925802230835e-09, -8.381903171539307e-09, 1.0151417484394187e-07, 1.0], "scale": [0.9999998807907104, 0.9999999403953552, 1.0], "translation": [0.010108351707458496, 2.85937237739563, 8.642673492431641e-07]}, {"name": "mixamorig:RightHandRing4_end_074", "rotation": [-3.122502256758253e-17, 2.7755575615628914e-17, -1.3877787807814457e-17, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [2.6645352591003757e-15, 2.8594107627868652, 0.0]}, {"children": [68], "name": "mixamorig:RightHandPinky1_051", "rotation": [0.4021046459674835, -0.015259222127497196, -0.003582095494493842, 0.9154595732688904], "scale": [1.000000238418579, 1.0000001192092896, 0.9999999403953552], "translation": [-3.7093942165374756, 9.74341869354248, 0.05928698182106018]}, {"children": [69], "name": "mixamorig:RightHandPinky2_052", "rotation": [0.5560144782066345, -2.1485882371052867e-06, 0.04806960001587868, 0.8297814130783081], "scale": [1.0, 1.0, 1.0000001192092896], "translation": [0.0047647953033447266, 2.9564366340637207, 2.8312206268310547e-06]}, {"children": [70], "name": "mixamorig:RightHandPinky3_053", "rotation": [0.545363187789917, 0.0005992336082272232, 0.049006905406713486, 0.8367657661437988], "scale": [1.0, 0.9999999403953552, 1.0], "translation": [-0.003939628601074219, 2.438419818878174, 1.430511474609375e-05]}, {"children": [71], "name": "mixamorig:RightHandPinky4_054", "rotation": [3.725291186640334e-09, -1.1292287638298149e-07, 4.190952029858863e-09, 1.0], "scale": [0.9999999403953552, 1.0, 0.9999999403953552], "translation": [-0.0008263587951660156, 2.0251643657684326, 0.0]}, {"name": "mixamorig:RightHandPinky4_end_075", "rotation": [0.0, 5.551115123125783e-17, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [0.0, 2.0251684188842773, 0.0]}, {"children": [73], "name": "C<PERSON>", "rotation": [0.6157670021057129, -0.5360066294670105, 0.35290050506591797, 0.4571532905101776], "scale": [99.99999237060547, 99.9999771118164, 100.00000762939453], "translation": [0.6181974411010742, 28.47368621826172, 9.84930419921875]}, {"mesh": 0, "name": "Cube_Material_0"}, {"children": [75], "name": "mixamorig:LeftUpLeg_055", "rotation": [0.051539260894060135, 0.11727920919656754, 0.9916402697563171, 0.015454832464456558], "scale": [1.0000075101852417, 1.0000005960464478, 1.0000081062316895], "translation": [8.641143798828125, -5.711184024810791, -0.4409489631652832]}, {"children": [76], "name": "mixamorig:LeftLeg_056", "rotation": [-0.4410100281238556, 0.04151798039674759, -0.03716026619076729, 0.8957709074020386], "scale": [0.9999999403953552, 1.0, 1.0], "translation": [-1.1920928955078125e-06, 45.44256591796875, 1.9073486328125e-06]}, {"children": [77], "name": "mixamorig:LeftFoot_057", "rotation": [0.5173629522323608, -0.015462336130440235, 0.03804429993033409, 0.8547801375389099], "scale": [0.9999998807907104, 0.9999999403953552, 0.9999999403953552], "translation": [-4.172325134277344e-07, 41.430973052978516, 2.384185791015625e-07]}, {"children": [78], "name": "mixamorig:LeftToeBase_058", "rotation": [0.34231647849082947, 0.008310099132359028, 0.01202059630304575, 0.9394710659980774], "scale": [0.9999998807907104, 1.000000238418579, 1.000000238418579], "translation": [-1.5273690223693848e-07, 18.189678192138672, -4.76837158203125e-07]}, {"children": [79], "name": "mixamorig:Left<PERSON>oe_End_059", "rotation": [-1.0884834367175245e-08, -7.916240996053148e-09, 1.2316742470375175e-07, 1.0], "scale": [1.0, 0.9999998211860657, 1.0], "translation": [-2.905726432800293e-07, 7.590356826782227, 5.960464477539063e-08]}, {"name": "mixamorig:<PERSON>Toe_End_end_076", "rotation": [-5.551115123125783e-17, -3.469446951953614e-18, 1.3877787807814457e-17, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [-1.7763568394002505e-15, 7.590418815612793, 0.0]}, {"children": [81], "name": "mixamorig:RightUpLeg_060", "rotation": [-0.05334017053246498, -0.0152297243475914, 0.997710108757019, 0.038696277886629105], "scale": [0.999991238117218, 0.9999997019767761, 0.9999891519546509], "translation": [-8.641143798828125, -5.711188793182373, -0.19484418630599976]}, {"children": [82], "name": "mixamorig:RightLeg_061", "rotation": [-0.17271174490451813, -0.049149904400110245, 0.045557450503110886, 0.9826899170875549], "scale": [0.9999998807907104, 0.9999999403953552, 0.9999998807907104], "translation": [5.103647708892822e-07, 45.45054626464844, 1.2293457984924316e-07]}, {"children": [83], "name": "mixamorig:RightFoot_062", "rotation": [0.5767018795013428, -0.010282939299941063, -0.08910039067268372, 0.8120161890983582], "scale": [1.0000001192092896, 0.9999998807907104, 0.9999998211860657], "translation": [-3.8743019104003906e-07, 41.42987060546875, -2.086162567138672e-07]}, {"children": [84], "name": "mixamorig:RightToeBase_063", "rotation": [0.30824634432792664, 0.013692407868802547, -0.004409442655742168, 0.9511978030204773], "scale": [1.0000001192092896, 1.0, 1.0000001192092896], "translation": [-3.501772880554199e-07, 17.69644546508789, 7.450580596923828e-07]}, {"children": [85], "name": "mixamorig:RightToe_End_064", "rotation": [2.2869784288559458e-07, 3.6088743105011645e-09, 1.0424989937973805e-07, 1.0], "scale": [1.0000001192092896, 1.000000238418579, 1.000000238418579], "translation": [2.086162567138672e-07, 7.476222515106201, 7.450580596923828e-08]}, {"name": "mixamorig:RightToe_End_end_077", "rotation": [7.52316384526264e-37, 4.336808689942018e-19, -1.734723475976807e-18, 1.0], "scale": [1.0, 1.0, 1.0], "translation": [-8.881784197001252e-16, 7.476144313812256, 2.220446049250313e-16]}, {"matrix": [100.0, 1.6987321098816495e-06, 2.384188132275794e-07, 0.0, 2.384187492053199e-07, 5.564461940635146e-07, -99.99997711181905, 0.0, -1.6987321112083655e-06, 100.00000000000254, 5.564463173738729e-07, 0.0, -2.2649765014648438e-06, 2.8958955056168634e-07, -4.723492941227647e-07, 1.0], "name": "Object_86"}, {"mesh": 1, "name": "Object_87", "skin": 0}, {"matrix": [100.0, -2.9802303593734674e-07, -8.046627530219078e-07, 0.0, -8.046627533175936e-07, -9.920841073616769e-08, -100.00000000000271, 0.0, 2.9802303513906154e-07, 100.00000000000267, -9.920841313424803e-08, 0.0, 7.82310962677002e-08, 2.8958965003766934e-07, -1.068395741876671e-06, 1.0], "name": "Object_88"}, {"mesh": 2, "name": "Object_89", "skin": 1}, {"matrix": [100.0, -1.1040574620983912e-09, 1.4685115166095886e-08, 0.0, 1.4685115164342875e-08, -1.588148884455254e-07, -100.0000000000027, 0.0, 1.1040574854205701e-09, 100.00000000000269, -1.5881488844536325e-07, 0.0, -1.4296674635261297e-06, 2.895895221399769e-07, -2.3393071502120222e-07, 1.0], "name": "Object_90"}, {"mesh": 3, "name": "Object_91", "skin": 2}, {"matrix": [100.0, 0.0, 0.0, 0.0, 0.0, 1.0719373376359156e-12, -100.00000000000266, 0.0, 0.0, 100.00000000000266, 1.0719373376359156e-12, 0.0, 0.0, 2.8958947950741276e-07, 4.487864080360282e-09, 1.0], "name": "Object_92"}, {"mesh": 4, "name": "Object_93", "skin": 3}, {"matrix": [100.0, 0.0, 0.0, 0.0, 0.0, 1.0719373376359156e-12, -100.00000000000266, 0.0, 0.0, 100.00000000000266, 1.0719373376359156e-12, 0.0, 0.0, 2.8958947950741276e-07, 4.487864080360282e-09, 1.0], "name": "Object_94"}, {"mesh": 5, "name": "Object_95", "skin": 4}, {"matrix": [100.0, 0.0, 0.0, 0.0, 0.0, 1.0719373376359156e-12, -100.00000000000266, 0.0, 0.0, 100.00000000000266, 1.0719373376359156e-12, 0.0, 0.0, 2.8958947950741276e-07, 4.487864080360282e-09, 1.0], "name": "Object_96"}, {"mesh": 6, "name": "Object_97", "skin": 5}, {"matrix": [100.0, -2.9802303593734674e-07, -8.046627530219078e-07, 0.0, -8.046627533175936e-07, -9.920841073616769e-08, -100.00000000000271, 0.0, 2.9802303513906154e-07, 100.00000000000267, -9.920841313424803e-08, 0.0, 7.82310962677002e-08, 2.8958965003766934e-07, -1.068395741876671e-06, 1.0], "name": "Object_98"}, {"mesh": 7, "name": "Object_99", "skin": 6}, {"matrix": [100.0, 0.0, 0.0, 0.0, 0.0, 1.0719373376359156e-12, -100.00000000000266, 0.0, 0.0, 100.00000000000266, 1.0719373376359156e-12, 0.0, 0.0, 2.8958947950741276e-07, 4.487864080360282e-09, 1.0], "name": "Object_100"}, {"mesh": 8, "name": "Object_101", "skin": 7}, {"matrix": [100.0, 0.0, 0.0, 0.0, 0.0, 1.0719373376359156e-12, -100.00000000000266, 0.0, 0.0, 100.00000000000266, 1.0719373376359156e-12, 0.0, 0.0, 2.8958947950741276e-07, 4.487864080360282e-09, 1.0], "name": "Object_102"}, {"mesh": 9, "name": "Object_103", "skin": 8}, {"matrix": [100.0, -6.854533400734693e-07, 2.3841846742705091e-07, 0.0, 2.3841847818676653e-07, 1.5697216016389937e-06, -100.00000000000271, 0.0, 6.854533363309796e-07, 100.0000000000024, 1.5697216032732364e-06, 0.0, 1.3262033462524414e-06, 2.8958955056168634e-07, -4.723492941227647e-07, 1.0], "name": "Object_104"}, {"mesh": 10, "name": "Object_105", "skin": 9}, {"name": "head", "rotation": [-0.7071068286895752, 2.4234427797864555e-09, 5.1629887032333954e-09, 0.7071067094802856], "scale": [100.0, 99.9999771118164, 100.0], "translation": [-2.2649765014648438e-06, 1.192093463942001e-07, -4.76837158203125e-07]}, {"name": "bottom", "rotation": [-0.7071068286895752, 2.63417843093805e-09, -1.2644046920584628e-09, 0.7071067094802856], "scale": [100.0, 100.0, 100.0], "translation": [7.82310962677002e-08, -2.9802141199297694e-08, -1.1082738637924194e-06]}, {"name": "top", "rotation": [-0.7071068286895752, -5.582316900398787e-11, 4.8016299269981744e-11, 0.7071067094802856], "scale": [100.0, 100.0, 100.0], "translation": [-1.429668145647156e-06, -3.427267074584961e-06, -2.384191191140417e-07]}, {"name": "eye_right", "rotation": [-0.7071068286895752, 0.0, 0.0, 0.7071067094802856], "scale": [100.0, 100.0, 100.0], "translation": [0.0, 0.0, 0.0]}, {"name": "glasses", "rotation": [-0.7071068286895752, 0.0, 0.0, 0.7071067094802856], "scale": [100.0, 100.0, 100.0], "translation": [0.0, 0.0, 0.0]}, {"name": "teeth", "rotation": [-0.7071068286895752, 0.0, 0.0, 0.7071067094802856], "scale": [100.0, 100.0, 100.0], "translation": [0.0, 0.0, 0.0]}, {"name": "shoes", "rotation": [-0.7071068286895752, 2.63417843093805e-09, -1.2644046920584628e-09, 0.7071067094802856], "scale": [100.0, 100.0, 100.0], "translation": [7.82310962677002e-08, -2.9802141199297694e-08, -1.1082738637924194e-06]}, {"name": "eye_left", "rotation": [-0.7071068286895752, 0.0, 0.0, 0.7071067094802856], "scale": [100.0, 100.0, 100.0], "translation": [0.0, 0.0, 0.0]}, {"name": "hair", "rotation": [-0.7071068286895752, 0.0, 0.0, 0.7071067094802856], "scale": [100.0, 100.0, 100.0], "translation": [0.0, 0.0, 0.0]}, {"name": "body", "rotation": [-0.7071068286895752, 2.7922297807236873e-09, -2.423443667964875e-09, 0.7071067094802856], "scale": [100.0, 100.0, 100.0], "translation": [1.3262033462524414e-06, 2.3841860752327193e-07, -4.768370445162873e-07]}], "samplers": [{"magFilter": 9729, "minFilter": 9987, "wrapS": 10497, "wrapT": 10497}], "scene": 0, "scenes": [{"name": "Sketchfab_Scene", "nodes": [0]}], "skins": [{"inverseBindMatrices": 69, "joints": [11, 10, 9, 43, 44, 14]}, {"inverseBindMatrices": 70, "joints": [6, 7, 74, 80, 75, 81, 82, 76, 77, 83]}, {"inverseBindMatrices": 71, "joints": [8, 9, 43, 44, 14, 7, 15, 6, 74, 80, 16, 45, 10, 11]}, {"inverseBindMatrices": 72, "joints": [11]}, {"inverseBindMatrices": 73, "joints": [11, 10]}, {"inverseBindMatrices": 74, "joints": [11, 10]}, {"inverseBindMatrices": 75, "joints": [83, 82, 81, 77, 76, 75]}, {"inverseBindMatrices": 76, "joints": [11]}, {"inverseBindMatrices": 77, "joints": [11, 10]}, {"inverseBindMatrices": 78, "joints": [45, 44, 46, 67, 57, 47, 62, 52, 48, 53, 59, 58, 63, 68, 69, 54, 64, 49, 16, 15, 17, 18, 38, 33, 23, 28, 19, 24, 30, 29, 34, 39, 40, 20, 25, 35]}], "textures": [{"sampler": 0, "source": 0}, {"sampler": 0, "source": 1}, {"sampler": 0, "source": 2}, {"sampler": 0, "source": 3}, {"sampler": 0, "source": 4}, {"sampler": 0, "source": 5}, {"sampler": 0, "source": 6}, {"sampler": 0, "source": 7}, {"sampler": 0, "source": 8}, {"sampler": 0, "source": 9}, {"sampler": 0, "source": 10}, {"sampler": 0, "source": 11}, {"sampler": 0, "source": 12}, {"sampler": 0, "source": 13}, {"sampler": 0, "source": 14}, {"sampler": 0, "source": 15}]}