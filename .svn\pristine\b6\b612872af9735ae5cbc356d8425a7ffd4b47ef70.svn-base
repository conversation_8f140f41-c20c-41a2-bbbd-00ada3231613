<template>
  <div v-if="ChartData!={}"class="chart-container">
    <canvas id="pie-chart" ref="chart"></canvas>
    <div class="description">
      <div class="value">{{ ChartData.data.reduce((a, b) => a + b, 0)}}</div>
      <div class="split-line"></div>
      <div class="text">设备累计故障</div>
    </div>
  </div>
  <div v-else></div>

  </template>
  
  <script>
  import Chart from 'chart.js';
  
  export default {
    name: 'PieChart',
    props: {
      ChartData:{
        type: Object,
        required: true,
      }
    },
    data(){
      return{
        chart:null,
      }
    },
    watch:{
      ChartData:{
        handler(){
          this.handleUpdate();
        },
        deep:true
      }
    },
    mounted() {
      this.renderChart();
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
      // 销毁时移除事件监听器
      window.removeEventListener('resize', this.handleResize);
      if (this.myChart) {
        this.myChart.destroy(); // 销毁 Chart 实例
      }
    },
    methods: {
      renderChart() {
        const ctx = this.$refs.chart.getContext('2d');
        var data = {
          labels: this.ChartData.labels,
          datasets: [{
            label: '饼图实例',
            data: this.ChartData.data,
            backgroundColor: [
              '#1890FF',
              '#2FC25B',
              '#FACC14',
              '#F04864',
              '#8543E0',
              '#13C2C2'
            ],
            hoverOffset: 4
          }]
        };
        var config = {
          type: 'doughnut',
          data: data,
          options: {
            cutoutPercentage:70,
            responsive: true, // 设置图表为响应式，根据屏幕窗口变化而变化
            maintainAspectRatio: false,// 保持图表原有比例
            layout:{
              padding:{
                // right:50
              }
            },
            legend: {
              align:"center",
              position: 'right',
              fullWidth:true,
              labels: {
                boxWidth: 20,
                fontSize: 15,
              }
            }
          }
        };
        this.chart = new Chart(ctx, config);

      },
      handleUpdate(){
        // console.log(this.chart);
        this.chart.data.labels = this.ChartData.labels;
        this.chart.data.datasets[0].data = this.ChartData.data;
        this.chart.update();
      },
      handleResize() {
        if (this.myChart) {
          this.myChart.resize(); // 调整图表大小
        }
      },
    }
  };
  </script>
  
  <style lang="scss" scoped>
  .chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    /* object-fit: contain; */
    .description {
      position: absolute;
      top: 50%;
      left: 18%;
      transform: translate(-50%, -50%);
      color:black;
      font-size: 18px;
      @media (max-width: 1500px){
        font-size: 16px;
      }
      font-weight: bold;
      text-align: center;
      .value{
        font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
        font-weight: 700;
        font-size: 24px;
        @media (max-width: 1500px){
          font-size: 21px;
        }
      }
      .split-line{
        width:100%;
        height: 2px;
        background-color: grey;
      }
      .text{
        font-family: '微软雅黑', sans-serif;
        font-weight: 400;
        font-size: 14px;
        @media (max-width: 1500px){
          font-size: 12px;
        }
      }
    }
  }
  </style>