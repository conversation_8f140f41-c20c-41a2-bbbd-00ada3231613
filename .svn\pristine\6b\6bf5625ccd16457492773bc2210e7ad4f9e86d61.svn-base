<template>
  <div id="DetailsDialog">
    <div class="wrapper-top">
      <div class="wrapper-l">
        <CardItem :title="'机械概览'">
          <MechanicalOverview :deviceInfo="deviceInfo"></MechanicalOverview>
        </CardItem>
      </div>
      <div class="wrapper-c">
        <CardItem :title="'BMS电池包信息'">
          <BMS :deviceInfo="deviceInfo" />
        </CardItem>
      </div>
      <div class="wrapper-r">
        <CardItem :title="'BMS电池包信息'">
          <BMSInfo :deviceInfo="deviceInfo" />
        </CardItem>
      </div>
    </div>
    <div class="wrapper-center">
      <div class="wrapper-left">
        <CardItem :title="'单体温度趋势'">
          <Heat :deviceInfo="deviceInfo" :deviceId="deviceId" :sectionId="sectionId"/>
        </CardItem>
      </div>
      <div class="wrapper-right">
        <CardItem :title="'单体压差极值'">
          <Pressure :deviceInfo="deviceInfo" :deviceId="deviceId" :sectionId="sectionId"/>
        </CardItem>
      </div>
    </div>
    <div class="wrapper-bottom">
      <div class="wrapper-left">
        <CardItem :title="'电池曲线'">
          <Battery :deviceInfo="deviceInfo" :deviceId="deviceId" :sectionId="sectionId"/>
        </CardItem>
      </div>
      <div class="wrapper-right">
        <CardItem :title="'电池电压分布波动图'">
          <Voltage :deviceInfo="deviceInfo" :deviceId="deviceId" :sectionId="sectionId"/>
        </CardItem>
      </div>
    </div>
  </div>
</template>

<script>
import CardItem from "./components/CardItem/CardItem.vue";
import MechanicalOverview from "./components/DetailsComponent/MechanicalOverview/MechanicalOverview.vue";
import BMS from "./components/DetailsComponent/BMS/BMS.vue";
import BMSInfo from "./components/DetailsComponent/BMSInfo/BMSInfo.vue";
import Heat from "./components/DetailsComponent/Heat/Heat.vue";
import Pressure from "./components/DetailsComponent/Pressure/Pressure.vue";
import Battery from "./components/DetailsComponent/Battery/Battery.vue";
import Voltage from "./components/DetailsComponent/Voltage/Voltage.vue";
export default {
  name: "DetailsDialog",
  props:{
    id:String,
    isclosed:Boolean,
  },
  components: {
    CardItem,
    MechanicalOverview,
    BMS,
    BMSInfo,
    Heat,
    Pressure,
    Battery,
    Voltage,
  },
  data() {
    return {
      deviceId: null,
      deviceInfo: {},
      sectionId: this.$store.state.tree.sectionId||'',
    };
  },
  watch:{
    isclosed: {
        handler: function () {
          if (this.isclosed) {
            this.deviceInfo = {};
            // this.total = 0;
          }
        },
      },
      id: {
        handler: function () {
          if (this.id) {
            this.getDeviceInfo();
          }else{
            this.deviceInfo = {};
          }
        },
        
      }
  },

  // beforeMount() {
  //   this.deviceId = this.$route.query.id;
  //   this.getDeviceInfo();
  // },

  mounted() {
    this.getDeviceInfo();
  },

  methods: {
    async getDeviceInfo() {
      this.deviceId = this.id;
      let res = await this.$http.get(`/equip/battery/getDetailByVehicleIdentify?vehicleIdentify=${this.deviceId}`);
      this.deviceInfo = res?.result || {};
    },
  },
};
</script>

<style lang="scss" scoped>
#DetailsDialog {
  width: 100%;
  height: 85vh;

  .wrapper-top {
    width: 100%;
    height: 33%;
    // min-height: 250px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .wrapper-l {
      width: 28%;
      height: 100%;
    }

    .wrapper-c {
      width: 30%;
      height: 100%;
      margin-left: 1%;
    }

    .wrapper-r {
      // flex: 1;
      width: 40%;
      margin-left: 1%;
      // height: 100%;
    }
  }

  .wrapper-center {
    margin-top: 10px;
    width: 100%;
    height: 32%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    .wrapper-left {
      flex: 1;
      height: 100%;
    }

    .wrapper-right {
      flex: 1;
      margin-left: 10px;
      height: 100%;
    }
  }

  .wrapper-bottom {
    margin-top: 10px;
    width: 100%;
    height: 32%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    .wrapper-left {
      flex: 1;
      height: 100%;
    }

    .wrapper-right {
      flex: 1;
      margin-left: 10px;
      height: 100%;
    }
  }


}
</style>