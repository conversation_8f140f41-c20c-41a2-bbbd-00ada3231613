<template>
    <div id="Dcrgl">
        <Card :title="'电池热管理'" class="card-main">
            <div class="info">
                <InfoCard>
                    <template #title>
                    <h2 v-if="showTitle">{{ infoTitle }}</h2>
                    </template>
                    <template #content>
                    <div v-for="item in totalInfos" :key="item.id">
                        {{ item.key + ":" + item.text }}
                    </div>
                    </template>
                </InfoCard> 
            </div>
            <div class="content" >
                <Content>
                    <template #label v-if="switchValue">
                        <el-tab-pane  :label="'电池总览'" :name="'1'">
                            <TemperBlock :batteries="testBatterys" :isclickblock="true" @clickblock="(index)=>handleClickBlock(index)"></TemperBlock>
                        </el-tab-pane>
                    </template>
                    <template #label v-else>
                        <el-tab-pane :label="'电池电芯总览'" :name="'1'"><TemperBlock :batteries="testBatterys" :maxBlocks="50" @back="handleReturn()"></TemperBlock></el-tab-pane>
                        <el-tab-pane :label="'电池详情'" :name="'2'"></el-tab-pane>
                    </template>
                </Content>
            </div>
        </Card>
    </div>
</template>

<script>
import Card from './components/Card/Card.vue';
import InfoCard from './components/InfoCard/InfoCard.vue';
import Content from './components/Content/Content.vue';
import TemperBlock from './components/TemperBlock/TemperBlock.vue';
export default {
    name: 'Dcrgl',
    components: { Card, InfoCard, Content, TemperBlock },
    data() {
    return {
        showTitle:true,
        infoTitle:'电池热管理信息',
        items:[  
            { id: 1, text: "项目1" },
            { id: 2, text: "项目2" },
            { id: 3, text: "项目3" },
            { id: 4, text: "项目4" },
            { id: 5, text: "项目5" },
            
        ],
        totalInfos:[
            {
                key:"电池总数",
                text:100
            },
            {
                key:"温度异常电池数",
                text:10
            },
            {
                key:"温度异常电池占比",
                text:10
            },
            {
                key:"温度异常电池平均温度",
                text:30
            },
            {
                key:"温度异常电池最高温度",
                text:40
            },
            {
                key:"温度异常电池最低温度",
                text:20
            },
            {
                key:"温度异常电池最高温度电池编号",
                text:1

            }
        ],
        switchValue: true,
        testBatterys:[
            { temperature: 25 },
            { temperature: 35 },
            { temperature: 45 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 18 },
            { temperature: 25 },
            { temperature: 35 },
            { temperature: 45 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 18 },
            { temperature: 25 },
            { temperature: 35 },
            { temperature: 45 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 18 },
            { temperature: 25 },
            { temperature: 35 },
            { temperature: 45 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 18 },
            { temperature: 25 },
            { temperature: 35 },
            { temperature: 45 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 18 },
            { temperature: 25 },
            { temperature: 35 },
            { temperature: 45 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 0 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 18 },
            { temperature: 25 },
            { temperature: 35 },
            { temperature: 45 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 18 },
            { temperature: 25 },
            { temperature: 35 },
            { temperature: 45 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 18 },
            { temperature: 25 },
            { temperature: 35 },
            { temperature: 45 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 28 },
            { temperature: 42 },
            { temperature: 18 },
            { temperature: 25 },
            { temperature: 35 },
            { temperature: 45 },
            { temperature: 28 },
            { temperature: 42 },
        ],
    }
    },
    mounted() {

    },
    watch: {

    },

    methods: {
        handleClick(){
            console.log("点击了");
            // this.switchValue = !this.switchValue;
            // this.items = [ ...this.items, { id: 3, text: "项目3"}]
        },
        handleClickBlock(index){
            // console.log(index);
            this.switchValue = false;
        },
        handleReturn(){
            this.switchValue = true;
        }
    }
}
</script>

<style lang="scss" scoped>
#Dcrgl {
    display: flex;

    // width: 100vw;
    // height: 86vh;
    .card-main{
        width: 50%;
        height: 87vh;
        flex: 1;
        .info{
            width: 100%;
            height: 20%;
            // background: #000000;
            // margin-top: 10px;
            // border-radius: 10px;
        }
        .content{
            width: 100%;
            height: 78%;
            margin: 10px;
            // background: #000000;
            
            // border-radius: 10px;
            // border: 1px solid #000000;
        }
    }
}
</style>
  