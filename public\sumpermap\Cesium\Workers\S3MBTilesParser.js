/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./buildModuleUrl-392763e2","./IndexDatatype-9435b55f","./createTaskProcessorWorker","./arrayFill-9766fb2e","./BoundingRectangle-3d4f3d01","./Color-69f1845f","./pako_inflate-8ea163f9","./S3MCompressType-c0bf5136","./unzip-9ad5f9b4","./CompressedTextureBuffer-21cababf","./PixelFormat-8e0e5be1"],function(_t,E,t,at,p,nt,g,A,e,it,d,s,r,l,a,M,n,N,pt,At,ct,c,v){function ot(t,e,r){if(E.Check.defined("array",t),_t.defined(e)&&E.Check.typeOf.number("begin",e),_t.defined(r)&&E.Check.typeOf.number("end",r),"function"==typeof t.slice)return t.slice(e,r);for(var a=Array.prototype.slice.call(t,e,r),n=s.FeatureDetection.typedArrayTypes,i=n.length,o=0;o<i;++o)if(t instanceof n[o]){a=new n[o](a);break}return a}function Et(){}var _;function m(t,e,r){var a,n=t.num_points(),i=r.num_components(),o=new _.AttributeQuantizationTransform;if(o.InitFromAttribute(r)){for(var E=new Array(i),s=0;s<i;++s)E[s]=o.min_value(s);a={quantizationBits:o.quantization_bits(),minValues:E,range:o.range(),octEncoded:!1}}_.destroy(o),(o=new _.AttributeOctahedronTransform).InitFromAttribute(r)&&(a={quantizationBits:o.quantization_bits(),octEncoded:!0}),_.destroy(o);var y,f=n*i;y=_t.defined(a)?function(t,e,r,a,n){var i,o;a.quantizationBits<=8?(o=new _.DracoUInt8Array,i=new Uint8Array(n),e.GetAttributeUInt8ForAllPoints(t,r,o)):(o=new _.DracoUInt16Array,i=new Uint16Array(n),e.GetAttributeUInt16ForAllPoints(t,r,o));for(var E=0;E<n;++E)i[E]=o.GetValue(E);return _.destroy(o),i}(t,e,r,a,f):function(t,e,r,a){var n,i;switch(r.data_type()){case 1:case 11:i=new _.DracoInt8Array,n=new Int8Array(a),e.GetAttributeInt8ForAllPoints(t,r,i);break;case 2:i=new _.DracoUInt8Array,n=new Uint8Array(a),e.GetAttributeUInt8ForAllPoints(t,r,i);break;case 3:i=new _.DracoInt16Array,n=new Int16Array(a),e.GetAttributeInt16ForAllPoints(t,r,i);break;case 4:i=new _.DracoUInt16Array,n=new Uint16Array(a),e.GetAttributeUInt16ForAllPoints(t,r,i);break;case 5:case 7:i=new _.DracoInt32Array,n=new Int32Array(a),e.GetAttributeInt32ForAllPoints(t,r,i);break;case 6:case 8:i=new _.DracoUInt32Array,n=new Uint32Array(a),e.GetAttributeUInt32ForAllPoints(t,r,i);break;case 9:case 10:i=new _.DracoFloat32Array,n=new Float32Array(a),e.GetAttributeFloatForAllPoints(t,r,i)}for(var o=0;o<a;++o)n[o]=i.GetValue(o);return _.destroy(i),n}(t,e,r,f);var T=it.ComponentDatatype.fromTypedArray(y);return{array:y,data:{componentsPerAttribute:i,componentDatatype:T,byteOffset:r.byte_offset(),byteStride:it.ComponentDatatype.getSizeInBytes(T)*i,normalized:r.normalized(),quantization:a}}}function B(t,e,r,a){var n=r.vertexAttributes,i=r.attrLocation;if(r.nCompressOptions=0,_t.defined(a.posUniqueID)&&0<=a.posUniqueID){r.nCompressOptions|=At.VertexCompressOption.SVC_Vertex;var o=m(t,e,e.GetAttribute(t,a.posUniqueID)),E=o.data.componentsPerAttribute;r.verticesCount=o.array.length/E,r.vertCompressConstant=o.data.quantization.range/(1<<o.data.quantization.quantizationBits);var s=o.data.quantization.minValues;r.minVerticesValue=new g.Cartesian4(s[0],s[1],s[2],1),3<E&&(r.minVerticesValue.w=s[3]),i.aPosition=n.length,n.push({index:i.aPosition,typedArray:o.array,componentsPerAttribute:E,componentDatatype:o.data.componentDatatype,offsetInBytes:o.data.byteOffset,strideInBytes:o.data.byteStride,normalize:o.data.normalized})}if(_t.defined(a.normalUniqueID)&&0<=a.normalUniqueID){r.nCompressOptions|=At.VertexCompressOption.SVC_Normal;var y=m(t,e,e.GetAttribute(t,a.normalUniqueID)),f=y.data.quantization;r.normalRangeConstant=(1<<f.quantizationBits)-1,i.aNormal=n.length,n.push({index:i.aNormal,typedArray:y.array,componentsPerAttribute:y.data.componentsPerAttribute,componentDatatype:y.data.componentDatatype,offsetInBytes:y.data.byteOffset,strideInBytes:y.data.byteStride,normalize:y.data.normalized})}if(_t.defined(a.colorUniqueID)&&0<=a.colorUniqueID){r.nCompressOptions|=At.VertexCompressOption.SVC_VertexColor;var T=m(t,e,e.GetAttribute(t,a.colorUniqueID));i.aColor=n.length,n.push({index:i.aColor,typedArray:T.array,componentsPerAttribute:T.data.componentsPerAttribute,componentDatatype:T.data.componentDatatype,offsetInBytes:T.data.byteOffset,strideInBytes:T.data.byteStride,normalize:T.data.normalized})}for(var u=0;u<a.texCoordUniqueIDs.length;u++){r.texCoordCompressConstant=[],r.minTexCoordValue=[];var d=a.texCoordUniqueIDs[u];if(!(d<0)){var l=m(t,e,e.GetAttribute(t,d));if(_t.defined(l.data.quantization)){r.nCompressOptions|=At.VertexCompressOption.SVC_TexutreCoord,r.texCoordCompressConstant.push(l.data.quantization.range/(1<<l.data.quantization.quantizationBits));s=l.data.quantization.minValues;r.minTexCoordValue.push(new p.Cartesian2(s[0],s[1]))}var _="aTexCoord"+u;i[_]=n.length,n.push({index:i[_],typedArray:l.array,componentsPerAttribute:l.data.componentsPerAttribute,componentDatatype:l.data.componentDatatype,offsetInBytes:l.data.byteOffset,strideInBytes:l.data.byteStride,normalize:l.data.normalized})}}}Et.dracoDecodePointCloud=function(t,e,r,a,n){for(var i=new(_=t).Decoder,o=["POSITION","NORMAL","COLOR"],E=0;E<o.length;++E)i.SkipAttributeTransform(_[o[E]]);var s=new _.DecoderBuffer;if(s.Init(e,r),i.GetEncodedGeometryType(s)!==_.POINT_CLOUD)throw new A.RuntimeError("Draco geometry type must be POINT_CLOUD.");var y=new _.PointCloud,f=i.DecodeBufferToPointCloud(s,y);if(!f.ok()||0===y.ptr)throw new A.RuntimeError("Error decoding draco point cloud: "+f.error_msg());_.destroy(s),B(y,i,a,n),_.destroy(y),_.destroy(i)},Et.dracoDecodeMesh=function(t,e,r,a,n,i){for(var o=new(_=t).Decoder,E=["POSITION","NORMAL","COLOR","TEX_COORD"],s=0;s<E.length;++s)o.SkipAttributeTransform(_[E[s]]);var y=new _.DecoderBuffer;if(y.Init(e,r),o.GetEncodedGeometryType(y)!==_.TRIANGULAR_MESH)throw new A.RuntimeError("Unsupported draco mesh geometry type.");var f=new _.Mesh,T=o.DecodeBufferToMesh(y,f);if(!T.ok()||0===f.ptr)throw new A.RuntimeError("Error decoding draco mesh geometry: "+T.error_msg());_.destroy(y),B(f,o,a,i);var u=function(t,e){for(var r=t.num_points(),a=t.num_faces(),n=new _.DracoInt32Array,i=3*a,o=l.IndexDatatype.createTypedArray(r,i),E=0,s=0;s<a;++s)e.GetFaceFromMesh(t,s,n),o[E+0]=n.GetValue(0),o[E+1]=n.GetValue(1),o[E+2]=n.GetValue(2),E+=3;var y=l.IndexDatatype.UNSIGNED_SHORT;return o instanceof Uint32Array&&(y=l.IndexDatatype.UNSIGNED_INT),_.destroy(n),{typedArray:o,numberOfIndices:i,indexDataType:y}}(f,o);n.indicesTypedArray=u.typedArray,n.indicesCount=u.numberOfIndices,n.indexType=u.indexDataType,n.primitiveType=d.PrimitiveType.TRIANGLES,_.destroy(f),_.destroy(o)};var P,U,vt=Object.freeze({S3M:49,S3M4:1}),i=1,o=2,S={};S[0]=v.PixelFormat.RGB_DXT1,S[i]=v.PixelFormat.RGBA_DXT3,S[o]=v.PixelFormat.RGBA_DXT5;var b,L=0,st=!1;function yt(t,e){var r=t.data,a=r.byteLength,n=new Uint8Array(r),i=b._malloc(a);!function(t,e,r,a){var n,i=r/4,o=a%4,E=new Uint32Array(t.buffer,0,(a-o)/4),s=new Uint32Array(e.buffer);for(n=0;n<E.length;n++)s[i+n]=E[n];for(n=a-o;n<a;n++)e[r+n]=t[n]}(n,b.HEAPU8,i,a);var o=b._crn_get_dxt_format(i,a),E=S[o];if(!_t.defined(E))throw new A.RuntimeError("Unsupported compressed format.");var s,y=b._crn_get_levels(i,a),f=b._crn_get_width(i,a),T=b._crn_get_height(i,a),u=0;for(s=0;s<y;++s)u+=v.PixelFormat.compressedTextureSizeInBytes(E,f>>s,T>>s);if(L<u&&(_t.defined(P)&&b._free(P),P=b._malloc(u),U=new Uint8Array(b.HEAPU8.buffer,P,u),L=u),b._crn_decompress(i,a,P,u,0,y),b._free(i),_t.defaultValue(t.bMipMap,!1)){var d=U.slice(0,u);return e.push(d.buffer),new c.CompressedTextureBuffer(E,f,T,d)}var l=v.PixelFormat.compressedTextureSizeInBytes(E,f,T),_=U.subarray(0,l),p=new Uint8Array(l);return p.set(_,0),e.push(p.buffer),new c.CompressedTextureBuffer(E,f,T,p)}var ft,mt={SVO_HasInstSelInfo:1},Tt={SV_Unkown:0,SV_Standard:1,SV_Compressed:2,SV_DracoCompressed:3},h=new N.Color,ut="ClampGroundAndObjectLinePass",gt=!1;if(_t.defined(ct.unzip)){ct.unzip.onRuntimeInitialized=function(){gt=!0};var Bt=ct.unzip.cwrap("unzip","number",["number","number","number","number"]),Pt=ct.unzip.cwrap("freePointer",null,["number"])}function Ut(t,e,r,a,n,i){this.left=t,this.bottom=e,this.right=r,this.top=a,this.minHeight=n,this.maxHeight=i,this.width=r-t,this.length=a-e,this.height=i-n}function St(t,e,r){var a=r,n=t.getUint32(a,!0),i=a+=Uint32Array.BYTES_PER_ELEMENT,o=new Uint8Array(e,a,n);return{dataViewByteOffset:i,byteOffset:a+=n*Uint8Array.BYTES_PER_ELEMENT,buffer:o}}function bt(t,e,r,a){var n=t.getUint32(a+e,!0);a+=Uint32Array.BYTES_PER_ELEMENT;var i=r.subarray(a,a+n);return{string:At.getStringFromTypedArray(i),bytesOffset:a+=n}}function dt(t,e,r,a,n,i){var o=r,E=t.getUint16(r+a,!0);o+=Uint16Array.BYTES_PER_ELEMENT,i||(o+=Uint16Array.BYTES_PER_ELEMENT);for(var s=0;s<E;s++){var y=t.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var f=t.getUint16(o+a,!0);o+=Uint16Array.BYTES_PER_ELEMENT;t.getUint16(o+a,!0);if(o+=Uint16Array.BYTES_PER_ELEMENT,20==f||35==f);else{var T=y*f*Float32Array.BYTES_PER_ELEMENT,u=e.subarray(o,o+T);o+=T;var d="aTexCoord"+s,l=n.vertexAttributes,_=n.attrLocation;_[d]=l.length,l.push({index:_[d],typedArray:u,componentsPerAttribute:f,componentDatatype:it.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:f*Float32Array.BYTES_PER_ELEMENT,normalize:!1})}}return{bytesOffset:o}}function lt(t,e,r,a,n){var i=r,o=t.getUint16(i+a,!0);i+=Uint16Array.BYTES_PER_ELEMENT,i+=Uint16Array.BYTES_PER_ELEMENT;for(var E=n.vertexAttributes,s=n.attrLocation,y=0;y<o;y++){var f=t.getUint32(i+a,!0);i+=Uint32Array.BYTES_PER_ELEMENT;var T=t.getUint16(i+a,!0);if(i+=Uint16Array.BYTES_PER_ELEMENT,16===T){i-=Uint16Array.BYTES_PER_ELEMENT;var u=f*(T*Float32Array.BYTES_PER_ELEMENT+Uint16Array.BYTES_PER_ELEMENT),d=e.subarray(i,i+u);i+=u;var l=new Uint8Array(Float32Array.BYTES_PER_ELEMENT*T*f);n.instanceCount=f,n.instanceMode=T,n.instanceBuffer=l,n.instanceIndex=1;for(var _=Float32Array.BYTES_PER_ELEMENT*T+Uint16Array.BYTES_PER_ELEMENT,p=0;p<f;p++){var A=p*_+Uint16Array.BYTES_PER_ELEMENT,c=d.subarray(A,A+_);l.set(c,p*(_-Uint16Array.BYTES_PER_ELEMENT))}v=16*Float32Array.BYTES_PER_ELEMENT,s.uv2=E.length,E.push({index:s.uv2,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:v,instanceDivisor:1}),s.uv3=E.length,E.push({index:s.uv3,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}),s.uv4=E.length,E.push({index:s.uv4,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}),s.secondary_colour=E.length,E.push({index:s.secondary_colour,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1})}else{t.getUint16(i+a,!0);i+=Uint16Array.BYTES_PER_ELEMENT;u=f*T*Float32Array.BYTES_PER_ELEMENT;if(17===T||29===T){var v;l=e.subarray(i,i+u);n.instanceCount=f,n.instanceMode=T,n.instanceBuffer=l,n.instanceIndex=1,17===T?(v=17*Float32Array.BYTES_PER_ELEMENT,s.uv2=E.length,E.push({index:s.uv2,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:v,instanceDivisor:1}),s.uv3=E.length,E.push({index:s.uv3,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}),s.uv4=E.length,E.push({index:s.uv4,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}),s.secondary_colour=E.length,E.push({index:s.secondary_colour,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}),s.uv6=E.length,E.push({index:s.uv6,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:16*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1})):29===T&&(v=29*Float32Array.BYTES_PER_ELEMENT,s.uv1=E.length,E.push({index:s.uv1,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:v,instanceDivisor:1,byteLength:u}),s.uv2=E.length,E.push({index:s.uv2,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}),s.uv3=E.length,E.push({index:s.uv3,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}),s.uv4=E.length,E.push({index:s.uv4,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}),s.uv5=E.length,E.push({index:s.uv5,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:16*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}),s.uv6=E.length,E.push({index:s.uv6,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:20*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}),s.uv7=E.length,E.push({index:s.uv7,componentsPerAttribute:3,componentDatatype:it.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:24*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}),s.secondary_colour=E.length,E.push({index:s.secondary_colour,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:27*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}),s.uv9=E.length,E.push({index:s.uv9,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:28*Float32Array.BYTES_PER_ELEMENT,strideInBytes:v,instanceDivisor:1}))}else{var m=f*T;n.instanceBounds=new Float32Array(m);for(var g=0;g<m;g++)n.instanceBounds[g]=t.getFloat32(i+a+g*Float32Array.BYTES_PER_ELEMENT,!0)}i+=u}}return{bytesOffset:i}}function Lt(t,e,r,a,n,i){var o=a,E=e.getUint32(o+r,!0);if(n.verticesCount=E,o+=Uint32Array.BYTES_PER_ELEMENT,E<=0)return{bytesOffset:o};var s=e.getUint16(o+r,!0);o+=Uint16Array.BYTES_PER_ELEMENT;var y=e.getUint16(o+r,!0);y=s*Float32Array.BYTES_PER_ELEMENT,o+=Uint16Array.BYTES_PER_ELEMENT;var f=E*s*Float32Array.BYTES_PER_ELEMENT,T=t.subarray(o,o+f);if(o+=f,3===s&&_t.defined(i)){for(var u=new at.Cartesian3,d=new at.Cartesian3,l=new Float32Array(T.buffer,T.byteOffset,T.byteLength/4),_=new Float32Array(T.byteLength/4+E),p=l.length,A=0,c=0;A<p;A+=3,c+=4)_[c]=l[A],_[c+1]=l[A+1],_[c+2]=l[A+2],nt.Matrix4.multiplyByPoint(i,at.Cartesian3.fromElements(_[c],_[c+1],_[c+2],u),d),_[c+3]=at.Cartographic.fromCartesian(d).height;T=_,y=(s=4)*Float32Array.BYTES_PER_ELEMENT}var v=n.vertexAttributes,m=n.attrLocation;return m.aPosition=v.length,v.push({index:m.aPosition,typedArray:T,componentsPerAttribute:s,componentDatatype:it.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:y,normalize:!1}),{bytesOffset:o}}function Mt(t,e,r,a,n){var i=a,o=e.getUint32(i+r,!0);if(i+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:i};var E=e.getUint16(i+r,!0);i+=Uint16Array.BYTES_PER_ELEMENT;var s=e.getUint16(i+r,!0);i+=Uint16Array.BYTES_PER_ELEMENT;var y=o*E*Float32Array.BYTES_PER_ELEMENT,f=t.subarray(i,i+y);if(i+=y,!n.ignoreNormal){var T=n.vertexAttributes,u=n.attrLocation;u.aNormal=T.length,T.push({index:u.aNormal,typedArray:f,componentsPerAttribute:E,componentDatatype:it.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:s,normalize:!1})}return{bytesOffset:i}}function Nt(t,e,r,a,n){var i=a,o=e.getUint32(i+r,!0);i+=Uint32Array.BYTES_PER_ELEMENT;var E;n.verticesCount;if(0<o){e.getUint16(i+r,!0);i+=Uint16Array.BYTES_PER_ELEMENT,i+=2*Uint8Array.BYTES_PER_ELEMENT;var s=o*Uint8Array.BYTES_PER_ELEMENT*4;E=ot(t,i,i+s),i+=s;var y=n.vertexAttributes,f=n.attrLocation;f.aColor=y.length,y.push({index:f.aColor,typedArray:E,componentsPerAttribute:4,componentDatatype:it.ComponentDatatype.UNSIGNED_BYTE,offsetInBytes:0,strideInBytes:4,normalize:!0})}return{bytesOffset:i}}function ht(t,e,r,a,n){var i=a,o=e.getUint32(i+r,!0);if(i+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:i};e.getUint16(i+r,!0);return i+=Uint16Array.BYTES_PER_ELEMENT,i+=2*Uint8Array.BYTES_PER_ELEMENT,{bytesOffset:i+=o*Uint8Array.BYTES_PER_ELEMENT*4}}function Rt(t,e,r,a){var n=a,i=[],o=e.getUint32(n+r,!0);n+=Uint32Array.BYTES_PER_ELEMENT;for(var E=0;E<o;E++){var s={},y=e.getUint32(n+r,!0);n+=Uint32Array.BYTES_PER_ELEMENT;var f=e.getUint8(n+r,!0);n+=Uint8Array.BYTES_PER_ELEMENT;e.getUint8(n+r,!0);n+=Uint8Array.BYTES_PER_ELEMENT;var T=e.getUint8(n+r,!0);if(n+=Uint8Array.BYTES_PER_ELEMENT,n+=Uint8Array.BYTES_PER_ELEMENT,0<y){var u=0,d=null;1===f||3===f?(u=y*Uint32Array.BYTES_PER_ELEMENT,d=t.subarray(n,n+u)):(u=y*Uint16Array.BYTES_PER_ELEMENT,d=t.subarray(n,n+u),y%2!=0&&(u+=2)),s.indicesTypedArray=d,n+=u}s.indicesCount=y,s.indexType=f,s.primitiveType=T;var l=[],_=e.getUint32(n+r,!0);n+=Uint32Array.BYTES_PER_ELEMENT;for(var p=0;p<_;p++){var A=bt(e,r,t,n),c=A.string;n=A.bytesOffset,l.push(c),s.materialCode=c}if(i.push(s),0!==n%4)n+=4-n%4}return{bytesOffset:n,arrIndexPackage:i}}function xt(t,e,r,a,n,i,o){var E=a,s=e.getUint32(E+r,!0);return n.nCompressOptions=s,E+=Uint32Array.BYTES_PER_ELEMENT,E=(s&At.VertexCompressOption.SVC_Vertex)==At.VertexCompressOption.SVC_Vertex?function(t,e,r,a,n){var i=a,o=e.getUint32(i+r,!0);if(n.verticesCount=o,i+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:i};var E=e.getUint16(i+r,!0);i+=Uint16Array.BYTES_PER_ELEMENT;var s=e.getUint16(i+r,!0);s=E*Int16Array.BYTES_PER_ELEMENT,i+=Uint16Array.BYTES_PER_ELEMENT;var y=e.getFloat32(i+r,!0);i+=Float32Array.BYTES_PER_ELEMENT;var f=new g.Cartesian4;f.x=e.getFloat32(i+r,!0),i+=Float32Array.BYTES_PER_ELEMENT,f.y=e.getFloat32(i+r,!0),i+=Float32Array.BYTES_PER_ELEMENT,f.z=e.getFloat32(i+r,!0),i+=Float32Array.BYTES_PER_ELEMENT,f.w=e.getFloat32(i+r,!0),i+=Float32Array.BYTES_PER_ELEMENT,n.vertCompressConstant=y,n.minVerticesValue=f;var T=o*E*Int16Array.BYTES_PER_ELEMENT,u=t.subarray(i,i+T);i+=T;var d=n.vertexAttributes,l=n.attrLocation;return l.aPosition=d.length,d.push({index:l.aPosition,typedArray:u,componentsPerAttribute:E,componentDatatype:it.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:s,normalize:!1}),{bytesOffset:i}}(t,e,r,E,n).bytesOffset:Lt(t,e,r,E,n,o).bytesOffset,E=ht(0,e,r,E=Nt(t,e,r,E=(s&At.VertexCompressOption.SVC_Normal)==At.VertexCompressOption.SVC_Normal?function(t,e,r,a,n){var i=a,o=e.getUint32(i+r,!0);if(i+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:i};e.getUint16(i+r,!0),i+=Uint16Array.BYTES_PER_ELEMENT;var E=e.getUint16(i+r,!0);i+=Uint16Array.BYTES_PER_ELEMENT;var s=2*o*Int16Array.BYTES_PER_ELEMENT,y=t.subarray(i,i+s);if(i+=s,!n.ignoreNormal){var f=n.vertexAttributes,T=n.attrLocation;T.aNormal=f.length,f.push({index:T.aNormal,typedArray:y,componentsPerAttribute:2,componentDatatype:it.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:E,normalize:!1})}return{bytesOffset:i}}(t,e,r,E,n).bytesOffset:Mt(t,e,r,E,n).bytesOffset,n).bytesOffset).bytesOffset,E=(s&At.VertexCompressOption.SVC_TexutreCoord)==At.VertexCompressOption.SVC_TexutreCoord?function(t,e,r,a,n){n.texCoordCompressConstant=[],n.minTexCoordValue=[];var i=r,o=t.getUint16(r+a,!0);i+=Uint16Array.BYTES_PER_ELEMENT,i+=Uint16Array.BYTES_PER_ELEMENT;for(var E=0,s=0;s<o;s++){var y=t.getUint8(i+a,!0);i+=Uint8Array.BYTES_PER_ELEMENT,i+=3*Uint8Array.BYTES_PER_ELEMENT;var f=t.getUint32(i+a,!0);i+=Uint32Array.BYTES_PER_ELEMENT;var T=t.getUint16(i+a,!0);i+=Uint16Array.BYTES_PER_ELEMENT,t.getUint16(i+a,!0),i+=Uint16Array.BYTES_PER_ELEMENT;var u=t.getFloat32(i+a,!0);i+=Float32Array.BYTES_PER_ELEMENT,n.texCoordCompressConstant.push(u);var d=new g.Cartesian4;d.x=t.getFloat32(i+a,!0),i+=Float32Array.BYTES_PER_ELEMENT,d.y=t.getFloat32(i+a,!0),i+=Float32Array.BYTES_PER_ELEMENT,d.z=t.getFloat32(i+a,!0),i+=Float32Array.BYTES_PER_ELEMENT,d.w=t.getFloat32(i+a,!0),i+=Float32Array.BYTES_PER_ELEMENT,n.minTexCoordValue.push(d);var l=f*T*Int16Array.BYTES_PER_ELEMENT,_=e.subarray(i,i+l),p=(i+=l)%4;0!==p&&(i+=4-p);var A="aTexCoord"+E,c=n.vertexAttributes,v=n.attrLocation;if(v[A]=c.length,c.push({index:v[A],typedArray:_,componentsPerAttribute:T,componentDatatype:it.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:T*Int16Array.BYTES_PER_ELEMENT,normalize:!1}),y){l=f*Float32Array.BYTES_PER_ELEMENT;var m=e.subarray(i,i+l);i+=l,n.texCoordZMatrix=!0,v[A="aTexCoordZ"+E]=c.length,c.push({index:v[A],typedArray:m,componentsPerAttribute:1,componentDatatype:it.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:Float32Array.BYTES_PER_ELEMENT,normalize:!1})}E++}return{bytesOffset:i}}(e,t,E,r,n).bytesOffset:dt(e,t,E,r,n,i).bytesOffset,(s&At.VertexCompressOption.SVC_TexutreCoordIsW)==At.VertexCompressOption.SVC_TexutreCoordIsW&&(n.textureCoordIsW=!0),{bytesOffset:E=lt(e,t,E,r,n).bytesOffset}}function Yt(t,e,r,a,n,i,o,E,s,y){var f=t,T=0,u=e.getUint32(T+r,!0);T+=Uint32Array.BYTES_PER_ELEMENT,y=_t.defaultValue(y,_t.defaultValue.EMPTY_OBJECT);for(var d,l,_,p,A,c,v=void 0,m=0;m<u;m++){var g=bt(e,r,f,T),B=g.string;if(_t.defined(s)){var P=_t.defaultValue(y[B],nt.Matrix4.IDENTITY);v=new nt.Matrix4,nt.Matrix4.multiply(s,P,v)}var U=(T=g.bytesOffset)%4;0!==U&&(T+=4-U);var S=Tt.SV_Unkown;if(S=e.getUint32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT,(rt={vertexAttributes:[],attrLocation:{},instanceCount:0,instanceMode:0,instanceIndex:-1}).ignoreNormal=a.ignoreNormal,S==Tt.SV_DracoCompressed){if(2<=E){e.getInt32(T+r,!0);T+=Int32Array.BYTES_PER_ELEMENT}var b={};b.posUniqueID=e.getInt32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT,b.normalUniqueID=e.getInt32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT,b.colorUniqueID=e.getInt32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT,b.secondColorUniqueID=e.getInt32(T+r,!0),T+=Int32Array.BYTES_PER_ELEMENT;var L=e.getUint16(T+r,!0);T+=Int16Array.BYTES_PER_ELEMENT;for(var M=[],N=0;N<L;N++){var h=e.getInt32(T+r,!0);M.push(h),T+=Int32Array.BYTES_PER_ELEMENT}b.texCoordUniqueIDs=M;var R=e.getInt32(T+r,!0);T+=Int32Array.BYTES_PER_ELEMENT;var x=[],Y={};if(0<R){var I=bt(e,r,f,T),C=I.string;T=I.bytesOffset,Y.materialCode=C,x.push(Y)}var F=e.getUint32(T+r,!0),D=ot(f,T+=Int32Array.BYTES_PER_ELEMENT,T+F);0<R?Et.dracoDecodeMesh(ft,D,F,rt,Y,b):Et.dracoDecodePointCloud(ft,D,F,rt,b),T+=F,a[B]={vertexPackage:rt,arrIndexPackage:x}}else{S==Tt.SV_Standard?(c=n,void 0,T=(g={bytesOffset:lt(_=e,l=f,dt(_,l,ht(0,_,p=r,Nt(l,_,p,Mt(l,_,p,Lt(l,_,p,T,A=rt,v).bytesOffset,A).bytesOffset,A).bytesOffset).bytesOffset,p,A,c).bytesOffset,p,A).bytesOffset}).bytesOffset):S==Tt.SV_Compressed&&(T=(g=xt(f,e,r,T,rt,n,v)).bytesOffset);var O;x=(g=Rt(f,e,r,T)).arrIndexPackage;0!==(d=x).length&&d[0].materialCode===ut&&(rt.clampRegionEdge=!0),2===x.length&&13===x[1].primitiveType&&3<=x[1].indicesCount&&(O=At.S3MEdgeProcessor.createEdgeDataByIndices(rt,x[1],i)),T=g.bytesOffset,a[B]={vertexPackage:rt,arrIndexPackage:x,edgeGeometry:O}}if(_t.defined(o)&&o){var w=e.getUint16(T+r,!0);if(T+=Uint16Array.BYTES_PER_ELEMENT,1===w){var z=e.getUint32(T+r,!0);T+=Uint32Array.BYTES_PER_ELEMENT;var V=e.getUint32(T+r,!0);T+=Uint32Array.BYTES_PER_ELEMENT;var k;e.getFloat32(T+r,!0);T+=Float32Array.BYTES_PER_ELEMENT;var G=new Array(z),q=new Array(z),H=new Array(z),W=new Array(z);for(k=0;k<z;k++){var X=e.getFloat32(T+r,!0);T+=Float32Array.BYTES_PER_ELEMENT,G[k]=X;var j=e.getUint16(T+r,!0);T+=Uint16Array.BYTES_PER_ELEMENT,q[k]=j;var J=e.getUint16(T+r,!0);T+=Uint16Array.BYTES_PER_ELEMENT;for(var Z=(H[k]=J)*V,Q=new Array(Z),K=0;K<Z;K++){var $=e.getFloat32(T+r,!0);T+=Float32Array.BYTES_PER_ELEMENT,Q[K]=$}W[k]=Q}}var tt=new at.Cartesian3,et=new at.Cartesian3;tt.x=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,tt.y=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,tt.z=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,et.x=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,et.y=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,et.z=e.getFloat64(T+r,!0),T+=Float64Array.BYTES_PER_ELEMENT,a[B].min=tt,a[B].max=et;var rt=a[B].vertexPackage;_t.defined(rt.instanceBuffer)&&2===E&&(rt.instanceBounds=new Float32Array(6),at.Cartesian3.pack(tt,rt.instanceBounds,0),at.Cartesian3.pack(et,rt.instanceBounds,3))}}}function R(t,e,r,a,n){var i={},o=[],E=new nt.Matrix4,s=t;n=_t.defaultValue(n,{});for(var y=0;y<16;y++)E[y]=e.getFloat64(r+a,!0),r+=Float64Array.BYTES_PER_ELEMENT;i.matrix=E,i.skeletonNames=o;var f=e.getUint32(r+a,!0);r+=Uint32Array.BYTES_PER_ELEMENT;for(var T=0;T<f;T++){var u=bt(e,a,s,r),d=u.string;r=u.bytesOffset,o.push(d),n[d]=E}return{byteOffset:r,geode:i}}function H(t){var e=t.indexOf("Geometry");if(-1===e)return t;var r=t.substring(e,t.length);return t.replace(r,"")}function f(t,e,r,a,n){var i={},o=e.getFloat32(r+a,!0);r+=Float32Array.BYTES_PER_ELEMENT;var E=e.getUint16(r+a,!0);r+=Uint16Array.BYTES_PER_ELEMENT,i.rangeMode=E,i.rangeList=o;var s=new at.Cartesian3;s.x=e.getFloat64(r+a,!0),r+=Float64Array.BYTES_PER_ELEMENT,s.y=e.getFloat64(r+a,!0),r+=Float64Array.BYTES_PER_ELEMENT,s.z=e.getFloat64(r+a,!0),r+=Float64Array.BYTES_PER_ELEMENT;var y=e.getFloat64(r+a,!0);r+=Float64Array.BYTES_PER_ELEMENT,i.boundingSphere=new nt.BoundingSphere(s,y);var f=(d=bt(e,a,t,r)).string;r=d.bytesOffset,f=H(f=f.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,"")),i.childTile=f,i.geodes=[];var T=e.getUint32(r+a,!0);r+=Uint32Array.BYTES_PER_ELEMENT;for(var u=0;u<T;u++){var d;r=(d=R(t,e,r,a,n)).byteOffset,i.geodes.push(d.geode)}return{pageLOD:i,bytesOffset:r}}function It(t,e,r,a){var n=0,i={},o=[],E=e.getUint32(n+r,!0);n+=Uint32Array.BYTES_PER_ELEMENT;for(var s=0;s<E;s++){var y=f(t,e,n,r,a);n=y.bytesOffset,o.push(y.pageLOD)}return i.pageLods=o,i}function Ct(t,e,r,a){for(var n=r.length,i=0;i<n;i++)for(var o=r[i],E=o.subName.split("_")[0],s=o.subVertexOffsetArr,y=0;y<s.length;y++){var f=s[y],T=f.geoName,u=f.offset,d=f.count,l=f.texUnitIndex,_=e[T].vertexPackage.verticesCount,p=a[T];_t.defined(p)||(p=a[T]={});var A=p[l];_t.defined(A)||(A=p[l]=new Float32Array(_),M.arrayFill(A,-1));var c=_t.defined(t)?t[E]:i;M.arrayFill(A,c,u,u+d)}}function x(t,e,r){var a=t.vertexAttributes,n=t.attrLocation,i=a.length;n[1===r?"instanceId":"batchId"]=i,a.push({index:i,typedArray:e,componentsPerAttribute:1,componentDatatype:it.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,instanceDivisor:r})}var Y=65536;function Ft(t,e,r,a,n){var i=0,o=t,E=e.getUint32(i+r,!0);i+=Uint32Array.BYTES_PER_ELEMENT;for(var s=0;s<E;s++){var y=bt(e,r,o,i),f=y.string;i=y.bytesOffset;var T=e.getUint32(i+r,!0);i+=Uint32Array.BYTES_PER_ELEMENT;var u={};if(a[f].pickInfo=u,-1==a[f].vertexPackage.instanceIndex){for(var d=new Float32Array(a[f].vertexPackage.verticesCount),l=0;l<T;l++){var _=e.getUint32(i+r,!0);i+=Uint32Array.BYTES_PER_ELEMENT;var p=e.getUint32(i+r,!0);i+=Uint32Array.BYTES_PER_ELEMENT;var A=0,c=0;u[_]={batchId:l};for(var v=0;v<p;v++)c=e.getUint32(i+r,!0),i+=Uint32Array.BYTES_PER_ELEMENT,A=e.getUint32(i+r,!0),i+=Uint32Array.BYTES_PER_ELEMENT,M.arrayFill(d,l,c,c+A);u[_].vertexColorOffset=c,u[_].vertexCount=A}x(a[f].vertexPackage,d,void 0)}else{var m=a[f].vertexPackage.instanceCount,g=a[f].vertexPackage.instanceBuffer,B=a[f].vertexPackage.instanceMode,P=new Float32Array(m),U=[];for(l=0;l<T;l++){_=e.getUint32(i+r,!0);U.push(_),i+=Uint32Array.BYTES_PER_ELEMENT;p=e.getUint32(i+r,!0);i+=Uint32Array.BYTES_PER_ELEMENT;for(v=0;v<p;v++){e.getUint32(i+r,!0);if(i+=Uint32Array.BYTES_PER_ELEMENT,3===n){A=e.getUint32(i+r,!0);i+=Uint32Array.BYTES_PER_ELEMENT}}}var S=17===B?16:28;for(S*=Float32Array.BYTES_PER_ELEMENT,l=0;l<m;l++){var b=(P[l]=l)*B*Float32Array.BYTES_PER_ELEMENT+S;N.Color.unpack(g,b,h);var L=2===n?U[l]:h.red+256*h.green+h.blue*Y;void 0===u[L]&&(u[L]={vertexColorCount:1,instanceIds:[],vertexColorOffset:l}),u[L].instanceIds.push(l)}x(a[f].vertexPackage,P,1)}}}function Dt(t){return t<1e-10&&-1e-10<t}function Ot(t,e,r,a,n,i,o,E){var s=new DataView(t),y=new Uint8Array(t),f=s.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var T=At.getStringFromTypedArray(y,r,f);T=T.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,""),r+=f;var u=s.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;for(var d=0;d<u;d++){var l={},_=s.getFloat32(r,!0);r+=Float32Array.BYTES_PER_ELEMENT;var p=s.getUint16(r,!0);r+=Uint16Array.BYTES_PER_ELEMENT,l.rangeMode=p,l.rangeList=_;var A={};A.x=s.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT,A.y=s.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT,A.z=s.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT;var c=s.getFloat64(r,!0);r+=Float64Array.BYTES_PER_ELEMENT,l.boundingSphere={center:A,radius:c},f=s.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT;var v=At.getStringFromTypedArray(y,r,f);r+=f,v=H(v=v.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,"")),l.childTile=v}var m={},g=s.getFloat32(r,!0);r+=Float32Array.BYTES_PER_ELEMENT;s.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var B=s.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var P=new Uint8Array(t,r,B),U=r+B,S=pt.pako.inflate(P).buffer;E.push(S),s=new DataView(S);y=new Uint8Array(S);r=0;var b=s.getUint32(r,!0),L=St(s,S,r+=Uint32Array.BYTES_PER_ELEMENT),M=L.buffer;r=L.byteOffset;var N=It(M,s,L.dataViewByteOffset),h=r%4;0!==h&&(r+=4-h),Yt((L=St(s,S,r)).buffer,s,L.dataViewByteOffset,m,!1);(L=St(s,S,r=L.byteOffset)).buffer;var R={};!function(t,e,r,a,n,i,o,E,s,y){var f=E,T=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;for(var u,d,l,_,p,A,c={},v=0;v<T;v++){var m=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var g=At.getStringFromTypedArray(i,f-E,m),B=(f+=m)%4;0!==B&&(f+=4-B),o.getUint32(f,!0),f+=Uint32Array.BYTES_PER_ELEMENT,o.getUint8(f,!0),f+=Uint8Array.BYTES_PER_ELEMENT;var P=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var U=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var S=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var b=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var L,M=o.getUint32(f,!0);if(f+=Uint32Array.BYTES_PER_ELEMENT,a){var N=f-E;L=i.subarray(N,N+b),f+=b}var h=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;for(var R=[],x=0;x<h;x++){m=o.getUint32(f,!0),f+=Uint32Array.BYTES_PER_ELEMENT;var Y=At.getStringFromTypedArray(i,f-E,m);f+=m,R.push(Y),r[Y]=g}var I=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var C=[];for(x=0;x<I;x++){m=o.getUint32(f,!0),f+=Uint32Array.BYTES_PER_ELEMENT;var F=At.getStringFromTypedArray(i,f-E,m);f+=m,C.push(F)}var D=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var O=[],w=void 0,z=g;if(a)w=e[g]={};else{var V=r[g];for(z=V;_t.defined(V);)V=r[z=V];_t.defined(z)&&(w=e[z])}var k=0;for(x=0;x<D;x++){m=o.getUint32(f,!0),f+=Uint32Array.BYTES_PER_ELEMENT;var G=At.getStringFromTypedArray(i,f-E,m);if(f+=m,a){var q=G.split("_")[0];_t.defined(w[q])?k++:w[q]=x-k}var H=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var W=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var X=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var j=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var J=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;for(var Z=[],Q=0;Q<J;Q++){m=o.getUint32(f,!0),f+=Uint32Array.BYTES_PER_ELEMENT;var K=At.getStringFromTypedArray(i,f-E,m);f+=m;var $=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var tt=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT;var et=o.getUint32(f,!0);f+=Uint32Array.BYTES_PER_ELEMENT,Z.push({geoName:K,offset:$,count:tt,texUnitIndex:et})}O.push({subName:G,offsetX:H,offsetY:W,width:X,height:j,subVertexOffsetArr:Z})}Ct(w,t,O,c),_t.defined(L)&&S===At.S3MPixelFormat.CRN_DXT5&&st&&(L=yt({data:L},y).bufferView),s[g]={id:g,rootTextureName:z,width:P,height:U,compressType:S,size:b,format:M,textureData:L,subTexInfos:O,requestNames:C}}for(var K in c)if(c.hasOwnProperty(K)){var rt=t[K].vertexPackage,at=c[K];for(var et in at)if(at.hasOwnProperty(et)){var nt=at[et];d=nt,l=et,p=_=void 0,_=(u=rt).vertexAttributes,p=u.attrLocation,A=_.length,p["aTextureBatchId"+l]=A,_.push({index:A,typedArray:d,componentsPerAttribute:1,componentDatatype:it.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0})}}}(m,a,n,i,0,(L=St(s,S,r=L.byteOffset)).buffer,s,L.dataViewByteOffset,R,E),r=L.byteOffset;var x=s.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var Y=y.subarray(r,r+x),I=At.getStringFromTypedArray(Y);r+=x;var C=JSON.parse(I);(b&mt.SVO_HasInstSelInfo)==mt.SVO_HasInstSelInfo&&(Ft((L=St(s,S,r)).buffer,s,L.dataViewByteOffset,m,g),r=L.byteOffset);var F=N.pageLods,D=!0;for(d=0;d<F.length;d++){var O=F[d];D=""===O.childTile;for(var w=O.geodes,z=0;z<w.length;z++)for(var V=w[z].skeletonNames,k=0;k<V.length;k++){var G=V[k];if(D){var q=m[G].vertexPackage;q.boundingSphere=At.S3MVertexPackage.calcBoundingSphereInWorker(1,q)}}}o[T]={result:!0,groupNode:N,geoPackage:m,matrials:C,texturePackage:R,version:vt.S3M4,rootBatchIdMap:a,ancestorMap:n},U<e&&Ot(t,e,U,a,n,!1,o,E)}function y(t,e){var r=t.buffer,a=t.isS3MZ,n=t.fileType,i=t.supportCompressType,o=t.bVolume,E=t.isS3MBlock,s=t.modelMatrix,y=t.materialType,f=null,T=null,u=null;if(o&&t.volbuffer.byteLength<8&&(o=!1),o){var d=t.volbuffer,l=new Uint8Array(d,8),_=pt.pako.inflate(l).buffer,p=new Float64Array(_,0,1),A=new Uint32Array(_,48,1);if(0===p[0]||3200===A[0]||3201===A[0]){var c=0;0==p[0]&&(c=8),e.push(_);var v=new Float64Array(_,c,6),m=v[0],g=v[1],B=v[2],P=v[3],U=v[4]<v[5]?v[4]:v[5],S=v[4]>v[5]?v[4]:v[5];T={left:m,top:g,right:B,bottom:P,minHeight:U,maxHeight:S,width:(f=new Ut(m,P,B,g,U,S)).width,length:f.length,height:f.height};var b=new Uint32Array(_,48+c,7),L=b[0],M=b[1],N=b[2],h=b[3];u={nFormat:L,nSideBlockCount:M,nBlockLength:N,nLength:h,nWidth:b[4],nHeight:b[5],nDepth:b[6],imageArray:new Uint8Array(_,76+c,h*h*4)}}}var R=0,x={};x.ignoreNormal=t.ignoreNormal;var Y=t.rootBatchIdMap||{},I=t.ancestorMap||{},C={},F=new DataView(r),D=F.getFloat32(R,!0);if(R+=Float32Array.BYTES_PER_ELEMENT,2.2<D)return{result:!1};if(E){F.getUint32(R,!0);return R+=Uint32Array.BYTES_PER_ELEMENT,Ot(r,r.byteLength,R,Y,I,t.isRoot,C,e),C}var O,w=!1;if(2<=D&&(O=F.getUint32(R,!0),R+=Uint32Array.BYTES_PER_ELEMENT),Dt(D-1)||Dt(D-2)||2.09<D&&D<2.11){var z=F.getUint32(R,!0);R+=Uint32Array.BYTES_PER_ELEMENT;var V=new Uint8Array(r,R,z);r=!0===gt?function(t,e){var r=e||4*t.length,a=ct.unzip._malloc(Uint8Array.BYTES_PER_ELEMENT*r),n=new Uint8Array(r);ct.unzip.HEAPU8.set(n,a/Uint8Array.BYTES_PER_ELEMENT);var i,o=ct.unzip._malloc(Uint8Array.BYTES_PER_ELEMENT*t.length);for(ct.unzip.HEAPU8.set(t,o/Uint8Array.BYTES_PER_ELEMENT);0==(i=Bt(a,r,o,t.length));)Pt(a),r*=4,a=ct.unzip._malloc(Uint8Array.BYTES_PER_ELEMENT*r),n=new Uint8Array(r),ct.unzip.HEAPU8.set(n,a/Uint8Array.BYTES_PER_ELEMENT);var E=new Uint8Array(ct.unzip.HEAPU8.buffer,a,i);n=t=null;var s=new Uint8Array(E).buffer;return Pt(a),Pt(o),s}(V,O):pt.pako.inflate(V).buffer,e.push(r),F=new DataView(r),R=0}else if(1.199<D&&D<1.201){z=F.getUint32(R,!0);R+=Uint32Array.BYTES_PER_ELEMENT,e.push(r)}else{w=!0,R=0;z=F.getInt32(R,!0);if(R+=Int32Array.BYTES_PER_ELEMENT,R+=Uint8Array.BYTES_PER_ELEMENT*z,a){F.getUint32(R,!0);R+=Uint32Array.BYTES_PER_ELEMENT;l=new Uint8Array(r,R);r=pt.pako.inflate(l).buffer,e.push(r),F=new DataView(r),R=0}}var k=F.getUint32(R,!0),G=St(F,r,R+=Uint32Array.BYTES_PER_ELEMENT),q=G.buffer;R=G.byteOffset;var H={},W=It(q,F,G.dataViewByteOffset,H),X=R%4;0!==X&&(R+=4-X);var j=2.09<D;if(Yt((G=St(F,r,R)).buffer,F,G.dataViewByteOffset,x,w,e,j,D,s,H),R=G.byteOffset,j)for(var J=0;J<W.pageLods.length;J++)for(var Z=W.pageLods[J],Q=Z.geodes,K=0;K<Q.length;K++)for(var $=Q[K].skeletonNames,tt=0;tt<$.length;tt++){var et=$[tt];_t.defined(x[et].max)&&(_t.defined(Z.max)?(Z.max.x=Math.max(x[et].max.x,Z.max.x),Z.max.y=Math.max(x[et].max.y,Z.max.y),Z.max.z=Math.max(x[et].max.z,Z.max.z),Z.min.x=Math.min(x[et].min.x,Z.min.x),Z.min.y=Math.min(x[et].min.y,Z.min.y),Z.min.z=Math.min(x[et].min.z,Z.min.z)):(Z.max=x[et].max,Z.min=x[et].min))}(G=St(F,r,R)).buffer;var rt={};!function(t,e,r,a,n,i){var o=0,E=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(var s=0;s<E;s++){var y=bt(r,a,e,o),f=y.string,T=(o=y.bytesOffset)%4;0!==T&&(o+=4-T);var u=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var d=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var l=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var _=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var p=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var A=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var c=e.subarray(o,o+p);o+=p;var v=null;_===At.S3MCompressType.enrS3TCDXTN&&1!=t?(At.DXTTextureDecode.decode(v,d,l,c,A),v=A>At.S3MPixelFormat.BGR||A===At.S3MPixelFormat.LUMINANCE_ALPHA?new Uint8Array(d*l*4):new Uint16Array(d*l),At.DXTTextureDecode.decode(v,d,l,c,A),i.push(v.buffer),_=0):v=c,n[f]={id:f,width:d,height:l,compressType:_,nFormat:A,imageBuffer:v,mipmapLevel:u}}}(i,(G=St(F,r,R=G.byteOffset)).buffer,F,G.dataViewByteOffset,rt,e),R=G.byteOffset;var at=F.getUint32(R,!0);R+=Uint32Array.BYTES_PER_ELEMENT;var nt=new Uint8Array(r).subarray(R,R+at),it=At.getStringFromTypedArray(nt);R+=at,it=it.replace(/\n\0/,"");var ot=JSON.parse(it);(k&mt.SVO_HasInstSelInfo)==mt.SVO_HasInstSelInfo&&Ft((G=St(F,r,R)).buffer,F,G.dataViewByteOffset,x,D);var Et=W.pageLods,st=!0;for(J=0;J<Et.length;J++){var yt=Et[J];st=""===yt.childTile;for(var ft=yt.geodes,Tt=0;Tt<ft.length;Tt++){$=ft[Tt].skeletonNames;for(var ut=0;ut<$.length;ut++){var dt=$[ut];if(st){var lt=x[dt].vertexPackage;lt.boundingSphere=At.S3MVertexPackage.calcBoundingSphereInWorker(n,lt)}}}}return"BatchPBR"===y&&function(t,e,r){for(var a in delete t.ignoreNormal,t)if(t.hasOwnProperty(a)){var n=t[a],i=n.arrIndexPackage;if(i.length<1)continue;if(1===i.length||2===i.length&&13===i[1].primitiveType){var o=n.vertexPackage.attrLocation.aTexCoord0;if(void 0!==o){var E=n.vertexPackage.vertexAttributes[o],s=new Float32Array(E.typedArray.buffer,E.typedArray.byteOffset,E.typedArray.byteLength/4);if(3===E.componentsPerAttribute&&s[2]<0)continue}}var y,f,T=0,u={},d=void 0;for(y=0,f=i.length;y<f;y++)13!==i[y].primitiveType?T+=i[y].indicesTypedArray.byteLength:d=i[y],0===y&&(u.indicesCount=0,u.indexType=i[y].indexType,u.primitiveType=i[y].primitiveType,u.materialCode=i[y].materialCode);u.indicesCount=T/2;var l=new Uint8Array(T),_=0;for(y=0,f=i.length;y<f;y++)13!==(L=i[y]).primitiveType&&(l.set(L.indicesTypedArray,_,_+L.indicesTypedArray.byteLength),_+=L.indicesTypedArray.byteLength);u.indicesTypedArray=l,n.arrIndexPackage=[u],_t.defined(d)&&(n.arrIndexPackage.push(d),n.edgeGeometry=At.S3MEdgeProcessor.createEdgeDataByIndices(n.vertexPackage,d));var p=2*i.length*4,A=new Float32Array(p),c={};for(y=0,f=r.material.length;y<f;y++)c[(M=r.material[y].material).id]=M;for(y=0,f=i.length;y<f;y++)if(M=c[(L=i[y]).materialCode]){var v=M.pbrMetallicRoughness;if(v){A[8*y]=v.metallicFactor,A[8*y+1]=v.roughnessFactor,A[8*y+2]=M.alphaCutoff;var m=""===M.alphaMode?0:1,g="none"===M.cullMode?0:1;A[8*y+3]=g|m<<16,A[8*y+4]=v.emissiveFactor.x,A[8*y+5]=v.emissiveFactor.y,A[8*y+6]=v.emissiveFactor.z,A[8*y+7]=0,M.pbrIndex=y}}var B="PBRMaterialParam_"+a;for(y=0,f=r.material.length;y<f;y++)if((M=r.material[y].material).id===u.materialCode){M.textureunitstates.push({textureunitstate:{addressmode:{u:0,v:0,w:0},filteringoption:0,filtermax:2,filtermin:2,id:B,texmodmatrix:[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],url:""}});break}var P,U,S=n.vertexPackage,b=S.attrLocation.aTexCoord1;if(void 0!==b){var E=S.vertexAttributes[b];P=new Float32Array(2*S.verticesCount),E.typedArray=P}else P=new Float32Array(2*S.verticesCount),b=S.vertexAttributes.length,S.attrLocation.aTexCoord1=b,S.vertexAttributes.push({index:b,typedArray:P,componentsPerAttribute:2,componentDatatype:5126,offsetInBytes:0,strideInBytes:8,normalize:!1});for(void 0!==(b=S.attrLocation.aColor)&&(U=(E=S.vertexAttributes[b]).typedArray),y=0,f=i.length;y<f;y++){var L,M;if((M=c[(L=i[y]).materialCode])&&M.pbrMetallicRoughness)for(var N=M.pbrMetallicRoughness.baseColor,h=void 0!==U,R=M.pbrIndex,x=(l=L.indicesTypedArray,0),Y=(l=0===L.indexType?new Uint16Array(l.buffer,l.byteOffset,l.byteLength/2):new Uint32Array(l.buffer,l.byteOffset,l.byteLength/4)).length;x<Y;x++){var I=l[x];P[2*I]=R,h&&(U[4*I]=255*N.x,U[4*I+1]=255*N.y,U[4*I+2]=255*N.z,U[4*I+3]=255*N.w)}}e[B]={id:B,width:2*i.length,height:1,compressType:0,nFormat:25,imageBuffer:A,mipmapLevel:0}}}(x,rt,ot),{result:!0,groupNode:W,geoPackage:x,matrials:ot,texturePackage:rt,version:vt.S3M4,volImageBuffer:u,volBounds:T}}function T(){_t.defined(b)&&_t.defined(ft)&&(b.onRuntimeInitialized=function(){st=!0},self.onmessage=a(y),self.postMessage(!0))}return function(t){if("undefined"==typeof WebAssembly)return self.onmessage=a(y),void self.postMessage(!0);var e=t.data.webAssemblyConfig;return _t.defined(e)?s.FeatureDetection.isInternetExplorer()?require([r.buildModuleUrl("ThirdParty/Workers/ie-webworker-promise-polyfill.js")],function(t){return self.Promise=t,-1!==e.modulePath.indexOf("crunch")?require([e.modulePath],function(t){_t.defined(e.wasmBinaryFile)&&(_t.defined(t)||(t=self.Module)),b=t,T()}):require([e.modulePath],function(t){_t.defined(e.wasmBinaryFile)?(_t.defined(t)||(t=self.DracoDecoderModule),t(e).then(function(t){ft=t,T()})):(ft=t(),T())})}):-1!==e.modulePath.indexOf("crunch")?require([e.modulePath],function(t){_t.defined(e.wasmBinaryFile)&&(_t.defined(t)||(t=self.Module)),b=t,T()}):require([e.modulePath],function(t){_t.defined(e.wasmBinaryFile)?(_t.defined(t)||(t=self.DracoDecoderModule),t(e).then(function(t){ft=t,T()})):(ft=t(),T())}):void 0}});