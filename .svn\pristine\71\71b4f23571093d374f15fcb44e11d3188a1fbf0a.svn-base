<template>
    <div id="battery-wrapper"></div>
</template>

<script>
export default {
    name: 'Battery',
    props: ["deviceInfo", "deviceId", "sectionId"],
    data() {
        return {
            time: ['11:25:00', '11:55:00', '12:15:10', '12:35:40', '13:05:10', '13:55:10', '14:25:30'],
            totalVoltage: [20, 32, 70, 34, 50, 23, 71],
            totalBattery: [22, 18, 19, 34, 30, 33, 31],
            soc: [50, 32, 20, 54, 40, 30, 41]
        }
    },
    mounted() {

    },
    
    watch: {
        "deviceInfo.id": {
            handler(val) {
                this.getBatteryInfo();
            },
            deep: true
        },
    },

    methods: {
        async getBatteryInfo() {
            let time = [];
            let voltage = [];
            let battery = [];
            let soc = [];
            let res = await this.$http.get(`/equip/build/batteryLineChart?id=${this.deviceId}&sectionId=${this.sectionId||''}`);
            res?.result?.map(i=>{
                time.push(i.hour);
                voltage.push(i.totalVoltage);
                battery.push(i.totalCurrent);
                soc.push(i.soc);
            })
            this.time = time;
            this.totalVoltage =voltage;
            this.totalBattery = battery;
            this.soc = soc;
            this.setChart();
        },
        // 初始化
        setChart() {
            let _ = this;
            let myChart = _.$echarts.init(
                document.getElementById("battery-wrapper"),
                null,
                {
                    renderer: "svg",
                }
            );
            myChart.setOption(
                {
                    color: ['#65C1DD', '#60BF79', '#F58A69'],
                    tooltip: {
                        trigger: 'axis'
                    },
                    grid: {
                        left: '3%',
                        right: '2%',
                        bottom: '1%',
                        top: 30,
                        containLabel: true
                    },
                    legend: {
                        data: ['总电压', '总电流', 'SOC']
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: this.time,
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '电流 / A',
                        },
                        {
                            type: 'value',
                            name: '电压 / V',
                            min: 0,
                            max: 300,
                            interval: 50,
                        }
                    ],
                    series: [
                        {
                            name: '总电压',
                            type: 'line',
                            tooltip: {
                                valueFormatter: function (value) {
                                    return value + ' ml';
                                }
                            },
                            data: this.totalVoltage
                        },
                        {
                            name: '总电流',
                            type: 'line',
                            tooltip: {
                                valueFormatter: function (value) {
                                    return value + ' ml';
                                }
                            },
                            data: this.totalBattery
                        },
                        {
                            name: 'SOC',
                            type: 'line',
                            tooltip: {
                                valueFormatter: function (value) {
                                    return value + ' °C';
                                }
                            },
                            data: this.soc
                        }
                    ]
                }
            )
        },
    }
}
</script>

<style lang="scss" scoped>
#battery-wrapper {
    width: 100%;
    height: 100%;
}
</style>