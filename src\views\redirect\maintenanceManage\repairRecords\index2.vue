<template>
  <div id="repair">
    <el-card class="card-content">
      <div class="content-option">
        <!-- <div class="content-title">维修台账管理</div> -->
        <el-form :inline="true" :model="formInline" ref="formInline" class="form-view">
          <!-- <el-form-item label="维修单号" class="form-item form-item-device-name">
            <el-input placeholder="请输入维修单号" v-model="formInline.deviceName" clearable
              prefix-icon="el-icon-search" style="width: 130px;"></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="设备编号">
            <el-input placeholder="请输入设备编号" v-model="formInline.specificationModel" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item> -->
          <el-form-item label="工单状态" class="form-item form-item-status">
            <el-input placeholder="请输入工单状态" v-model="formInline.status" clearable
              prefix-icon="el-icon-search" style="width: 130px;"></el-input>
          </el-form-item>
          <el-form-item label="维修起始时间" class="form-item form-item-start-time" >
                      <el-date-picker
                        v-model="formInline.startRepairTime"
                        type="datetime"
                        placeholder="维修起始日期"
                        clearable
                        class="form-item"
                        style="width: 140px;"
                        ></el-date-picker>
          </el-form-item>
          <el-form-item label="维修结束时间" class="form-item form-item-end-time">
                        <el-date-picker
                        v-model="formInline.endRepairTime"
                        type="datetime"
                        placeholder="维修结束日期"
                        clearable
                        class="form-item"
                        style="width: 140px;"
                        ></el-date-picker>
          </el-form-item>
          <el-form-item></el-form-item>
          <el-form-item  class="form-item-buttons">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery" class="form-item">查询</el-button>
            <el-button icon="el-icon-plus" type="primary" @click="handleOperate(null)" class="form-item">新增维修台账</el-button>
            <!-- <el-button icon="el-icon-upload2" @click="handleImport">导入数据</el-button> -->
            <el-button icon="el-icon-download" @click="handleExport" :loading="loading" class="form-item">导出数据</el-button>
            <router-link 
              to="/devices/maintenanceStatistics" 
              tag="el-button" 
              :loading="loading" 
              class="jump-to-screen form-item">
              维保统计
            </router-link>
            <!-- <el-button icon="" @click="" :loading="loading" class="jump-to-screen">维保统计</el-button> -->
          </el-form-item>
        </el-form>
      </div>
      <div class="content-table">
        <!-- 设备编号、设备名称、故障简介、报修原因、维修时间、故障原因、故障图片、报修人和维修人 -->
        <el-table ref="table" :data="tableData" style="width: 100%"  border :header-cell-style="headerCellStyle" :cell-style="cellStyle">
                    <el-table-column type="index" min-width="50" label="序号" align="center" />
                    <!-- <el-table-column prop="repairNo" label="维修单号" align="center" min-width="120" /> -->
                    <el-table-column prop="sectionName" label="所属标段" align="center" min-width="100" />
                    <el-table-column prop="deviceNum" label="设备编号" align="center" min-width="110" />
                    <el-table-column prop="deviceName" label="设备名称" align="center" min-width="100" />
                    <el-table-column prop="faultCode" label="故障编码" align="center" min-width="80" />
                    <el-table-column prop="repairReason" label="报修原因" align="center" min-width="140" />
                    <el-table-column prop="faultReason" label="故障原因" align="center" min-width="140" />
                    <el-table-column prop="dealMeasure" label="处置措施" align="center" min-width="110" />
                    <el-table-column prop="status" label="工单状态" align="center" min-width="100">
                      <template slot-scope="scope">
                          <div :class="getStatusClass(scope.row.status)" class="status-indicator">
                            {{ scope.row.status }}
                          </div> 
                      </template>
                        <!-- <template v-slot="scope">
                           <el-select v-model="scope.row.status" placeholder="请选择">
                                <el-option
                                   v-for="item in statusOptions"
                                   :key="item.value"
                                   :label="item.label"
                                   :value="item.value">
                                </el-option>
                           </el-select>
                        </template> -->
                    </el-table-column>
                    <el-table-column prop="startRepairTime" label="维修起始时间" align="center" min-width="150" />
                    <el-table-column prop="endRepairTime" label="维修结束时间" align="center" min-width="150" />
                    <el-table-column prop="submitPerson" label="报修人" align="center" min-width="80" />
                    <el-table-column prop="repairPerson" label="维修人" align="center" min-width="80" />
                    <el-table-column label="操作" min-width="100" align="center" fixed="right">
                       <template slot-scope="scope">
                          <el-button @click="handleOperate(scope.row)" type="text" size="small" class="table-edit">编辑</el-button>
                            <el-popconfirm title="确定删除吗？" @confirm="handleDelete(scope.row.id, '/equip/deviceRepairInfo/deleteById')">
                               <el-button slot="reference" type="text" size="small"> 删除</el-button>
                           </el-popconfirm>
                       </template>
                   </el-table-column>
                  <!-- <el-table-column prop="endReason" label="结束原因" align="center" width="160" fixed="right"/> -->
        </el-table>
          
        <!-- 分页组件，用于处理表格数据的分页显示 -->
        <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
          :total="total">
        </el-pagination>
        <!-- <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
          :total="127" :pages="7">
        </el-pagination> -->
        
      </div>
    </el-card>
    <AddLedgerDialog v-if="dialogVisible" :curData="curData" :dialogVisible="dialogVisible" @onClose="onClose"
      @onSave="onSave" />
    <ImportDialog v-if="importvisible" :importvisible="importvisible" @onClose="handleClose" />
  </div>
</template>

<script>
import tableUtils from '@/mixins/tableUtils';
import AddLedgerDialog from './Dialog/AddLedgerDialog.vue';
import ImportDialog from './ImportDialog/ImportDialog.vue';
import { exportRepair } from '@/api/baseApi/common';
import { formattedTime } from "@/utils/timer";
export default {
  name: 'Sbtzgl',
  mixins: [tableUtils],
  components: { AddLedgerDialog, ImportDialog },
  data() {
    return {
      // formInline: {},
      // dialogVisible: false,
      // curData: null,
      // tableData: [],
      // importvisible: false,
      // loading: false,
      formInline: {},
      dialogVisible: false,
      curData: null,
      tableData: [],
      importvisible: false,
      loading: false,
      
      startTime:'',
      endTime:'',
      statusOptions: [
          { value: '待处理', label: '待处理' },
          { value: '处理中', label: '处理中' },
          { value: '已完成', label: '已完成' }
      ],
      tableData:[
          
          
      ],
      pageParams:{
          page: 1,
          limit: 20,
      },
      total:0,
      requestLoad : {
          sectionId: 0,
      },
    }
  },
  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.requestList();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {

    getStatusClass(status) {
      // console.log(1111111111111111111111);
      // console.log(status);
      switch (status) {
        case '严重':
          return 'severity';
        case '一般':
          return 'general';
        case '已修复':
          return 'completed';
        default:
          return '';
      }
    },

    async requestList() {//此方法会从后台获取数据
      // console.log(...this.formInline);
      // console.log(22222222222);
      let params = {
        //...this.formInline,
        status: this.formInline.status,
        startRepairTime:formattedTime(this.formInline.startRepairTime),
        endRepairTime:formattedTime(this.formInline.endRepairTime),
        sectionId: this.$store.state.tree.sectionId
      };
      let res = await this.$http.post(`/equip/deviceRepairInfo/page?page=${this.pageParams.page}&limit=${this.pageParams.limit}`, params);
      this.tableData = res.result?.records;
      this.total = res.result?.total;
    },

    async handleExport() {
      this.loading = true;
      let params = {
        ...this.formInline,
        sectionId: this.$store.state.tree.sectionId
      };
      let res = await exportRepair(params);
      var list = new Blob([res], {
        type: "application/vnd.ms-excel;charset=utf-8",
      });
      var downloadUrl = window.URL.createObjectURL(list);
      var anchor = document.createElement("a");
      anchor.href = downloadUrl;
      anchor.download = '设备维修台账管理表.xlsx';
      anchor.click();
      window.URL.revokeObjectURL(list);
      this.loading = false;
    },

    handleImport() {
      this.importvisible = true;
    },

    handleOperate(item) {
      if (item) {
        this.curData = { data: { ...item }, type: 2 }
      } else {
        this.curData = { data: {}, type: 1 }
      }
      this.dialogVisible = true
    },

    onClose() {
      this.dialogVisible = false;
    },

    handleClose() {
      this.requestList();
      this.importvisible = false;
    },

    onSave() {
      this.requestList();
      this.dialogVisible = false;
    }
  }
}
</script>

<style lang="scss" scoped>
#repair {
  .content-option {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .content-title {
      line-height: 40px;
      font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
      font-weight: 700;
      font-style: normal;
      font-size: 18px;
      color: #3A3F5D;
      margin-right: 5%;
    }

    .form-view {
      flex: 1;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
    }
    
    .form-item {
      //flex: 1; /* 默认情况下每个表单项占据相等的空间 */
      //min-width: 150px; /* 设置最小宽度，防止过窄 */
    }

    ::v-deep .el-input {
      width: 175px;
    }

  }


   .status-indicator {
        display: inline-block;
        width: 80px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 15px;
        //color: white;
    }

    .severity {
      background-color: #ff4d4f; /* 红色 */
    }

    .general {
      background-color: #faad14; /* 黄色 */
    }

    .completed {
      background-color: #52c41a; /* 绿色 */
    }


  .table-edit {
    margin-right: 10px;
  }

  .pagination-part {
    margin-top: 20px;
  }
}
.jump-to-screen {
  margin-left: 5px;
  background-color: #379de5;
  color: white;
}
</style>