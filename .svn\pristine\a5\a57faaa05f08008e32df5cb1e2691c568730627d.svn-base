<template>
  <div id="protect">
    <el-card class="card-content">
      <div class="content-option">
        <div class="content-title">保养台账管理</div>
        <el-form :inline="true" :model="formInline" ref="formInline" class="form-view">
          <el-form-item label="工单状态">
            <el-input placeholder="请输入工单状态" v-model="formInline.status" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item>
          <!-- <el-form-item label="设备编号">
            <el-input placeholder="请输入设备编号" v-model="formInline.specificationModel" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="工单状态">
            <el-input placeholder="请输入工单状态" v-model="formInline.manufacturer" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item> -->
          <el-form-item label="保养发起时间">
              <el-date-picker
              v-model="formInline.startProtectTime"
              type="datetime"
              placeholder="选择保养发起日期"
              clearable
              ></el-date-picker>
          </el-form-item>
          <el-form-item label="保养结束时间">
              <el-date-picker
              v-model="formInline.endProtectTime"
              type="datetime"
              placeholder="选择保养结束日期"
              clearable
              ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-plus" type="primary" @click="handleOperate(null)">新增保养台账</el-button>
            <!-- <el-button icon="el-icon-upload2" @click="handleImport">导入数据</el-button> -->
            <el-button icon="el-icon-download" @click="handleExport" :loading="loading">导出数据</el-button>
            <router-link 
                to="/devices/maintenanceStatistics" 
                tag="el-button" 
                :loading="loading" 
                class="jump-to-screen">
                返回维保统计
            </router-link>
            <!-- <el-button icon="" @click="" :loading="loading" class="jump-to-screen">返回维保统计</el-button> -->
          </el-form-item>
        </el-form>
      </div>
      <div class="content-table">
        <el-table ref="table" :data="tableData" style="width: 100%"  border :header-cell-style="headerCellStyle" :cell-style="cellStyle">
              <el-table-column type="index" min-width="50" label="序号" align="center" />
              <!-- <el-table-column prop="protectNo" label="保养单号" align="center" min-width="120" /> -->
              <el-table-column prop="sectionName" label="所属标段" align="center" min-width="110" />
              <el-table-column prop="deviceNum" label="设备编号" align="center" min-width="110" />
              <el-table-column prop="deviceName" label="设备名称" align="center" min-width="180" />
              <el-table-column prop="protectLevel" label="保养级别" align="center" min-width="80" />
              <el-table-column prop="protectDetail" label="保养内容" align="center" min-width="200" />
              <el-table-column prop="startProtectTime" label="保养发起时间" align="center" min-width="80" />
              <el-table-column prop="endProtectTime" label="保养完成时间" align="center" min-width="80" />
              <el-table-column prop="protectPerson" label="保养人员" align="center" min-width="80" />
              <el-table-column prop="status" label="工单状态" align="center" min-width="100">
                  <!-- <template v-slot="scope">
                      <el-select v-model="scope.row.status" placeholder="请选择">
                          <el-option
                              v-for="item in statusOptions"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value">
                          </el-option>
                      </el-select>
                  </template> -->
                  <template slot-scope="scope">
                        <div :class="getStatusClass(scope.row.status)" class="status-indicator">
                          <!-- {{ getStatusClass(scope.row.status) }} -->
                          {{ scope.row.status }}
                        </div> 
                  </template>
              </el-table-column>
              <el-table-column label="操作" min-width="100" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-button @click="handleOperate(scope.row)" type="text" size="small" class="table-edit">编辑</el-button>
                      <el-popconfirm title="确定删除吗？" @confirm="handleDelete(scope.row.id, '/equip/deviceProtectInfo/deleteById')">
                          <el-button slot="reference" type="text" size="small"> 删除</el-button>
                      </el-popconfirm>
                  </template>
              </el-table-column>
              <!-- <el-table-column prop="endReason" label="结束原因" align="center" width="160" fixed="right"/> -->
        </el-table>
          
        <!-- 分页组件，用于处理表格数据的分页显示 -->
        <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
          :total="total">
        </el-pagination>

        
      </div>
    </el-card>
    <AddLedgerDialog v-if="dialogVisible" :curData="curData" :dialogVisible="dialogVisible" @onClose="onClose"
      @onSave="onSave" />
    <ImportDialog v-if="importvisible" :importvisible="importvisible" @onClose="handleClose" />
  </div>
</template>

<script>
import tableUtils from '@/mixins/tableUtils';
import AddLedgerDialog from './Dialog/AddLedgerDialog.vue';
import ImportDialog from './ImportDialog/ImportDialog.vue';
import { exportProtect } from '@/api/baseApi/common';
import { formattedTime } from "@/utils/timer";
export default {
  name: 'Sbtzgl',
  mixins: [tableUtils],
  components: { AddLedgerDialog, ImportDialog },
  data() {
    return {
      // formInline: {},
      // dialogVisible: false,
      // curData: null,
      // tableData: [],
      // importvisible: false,
      // loading: false,
      formInline: {},
      dialogVisible: false,
      curData: null,
      tableData: [],
      importvisible: false,
      loading: false,
      
      startTime:'',
      endTime:'',
      statusOptions: [
          { value: '待处理', label: '待处理' },
          { value: '处理中', label: '处理中' },
          { value: '已完成', label: '已完成' }
      ],
      tableData:[
          // {
          //     protectNo: 'p202407000001',
          //     equipmentNo: '387983097',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动自卸车-CR2003-CZTL-SB-197',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查车辆外观、轮胎拆卸检查、调整刹车系统、添加润滑油和检查三电系统等。',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '萧远航',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000002',
          //     equipmentNo: '387983092',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动装载机-CR2003-CZTL-SB-305',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查外观、电池和电机，并对设备进行清洁',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '邵逸辰',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000003',
          //     equipmentNo: '3879830890',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动自卸车-CR2003-CZTL-SB-193',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查车辆外观、轮胎拆卸检查、调整刹车系统、添加润滑油和检查三电系统等。',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '萧远航',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000004',
          //     equipmentNo: '1724979386370',//数据库中的设备编号，此为车辆
          //     equipmentName: '充电机-004',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查充电机是否有明显的物理损坏，并清理充电机外部灰尘和异物。',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '孙志远',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000005',
          //     equipmentNo: '387983079',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动装载机-CR2003-CZTL-SB-203',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查外观、电池和电机，并对设备进行清洁',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '邵逸辰',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000006',
          //     equipmentNo: '1724979386367',//数据库中的设备编号，此为车辆
          //     equipmentName: '充电机-001',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查充电机是否有明显的物理损坏，并清理充电机外部灰尘和异物。',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '孙志远',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000007',
          //     equipmentNo: '387983077',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动自卸车-CR2003-CZTL-SB-119',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查车辆外观、轮胎拆卸检查、调整刹车系统、添加润滑油和检查三电系统等。',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '萧远航',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000008',
          //     equipmentNo: '1724979386375',//数据库中的设备编号，此为车辆
          //     equipmentName: '充电机-009',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查充电机是否有明显的物理损坏，并清理充电机外部灰尘和异物。',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '孙志远',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000009',
          //     equipmentNo: '1724979386374',//数据库中的设备编号，此为车辆
          //     equipmentName: '充电机-008',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查充电机是否有明显的物理损坏，并清理充电机外部灰尘和异物。',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '孙志远',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000010',
          //     equipmentNo: '387983096',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动自卸车-CR2003-CZTL-SB-196',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查车辆外观、轮胎拆卸检查、调整刹车系统、添加润滑油和检查三电系统等。',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '萧远航',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000011',
          //     equipmentNo: '387983095',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动装载机-CR2003-CZTL-SB-308',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查外观、电池和电机，并对设备进行清洁',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '邵逸辰',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000012',
          //     equipmentNo: '387983092',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动装载机-CR2003-CZTL-SB-305',
          //     protectLevel: '深度保养',
          //     protectDetail: '深度清洁机器和更换磨损严重的零部件，并且检查设备的控制系统',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '邵逸辰',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000013',
          //     equipmentNo: '1724979386372',//数据库中的设备编号，此为车辆
          //     equipmentName: '充电机-006',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查充电机是否有明显的物理损坏，并清理充电机外部灰尘和异物。',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '秦朗',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000014',
          //     equipmentNo: '3879830890',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动自卸车-CR2003-CZTL-SB-193',
          //     protectLevel: '深度保养',
          //     protectDetail: '更换制动液、冷却液、齿轮油和雨刷片，并且进行全方位检查',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '萧远航',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000015',
          //     equipmentNo: '387983085',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动装载机-CR2003-CZTL-SB-303',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查外观、电池和电机，并对设备进行清洁',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '邵逸辰',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000016',
          //     equipmentNo: '1724979386370',//数据库中的设备编号，此为车辆
          //     equipmentName: '充电机-004',
          //     protectLevel: '深度保养',
          //     protectDetail: '对电气部件进行深度检查和测试。检查并校准充电机的工作参数，并更换磨损部件。',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '秦朗',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000017',
          //     equipmentNo: '387983088',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动自卸车-CR2003-CZTL-SB-121',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查车辆外观、轮胎拆卸检查、调整刹车系统、添加润滑油和检查三电系统等。',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '萧远航',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000018',
          //     equipmentNo: '387983083',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动装载机-CR2003-CZTL-SB-301',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查外观、电池和电机，并对设备进行清洁',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '邵逸辰',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000019',
          //     equipmentNo: '387983079',//数据库中的设备编号，此为车辆
          //     equipmentName: '电动装载机-CR2003-CZTL-SB-203',
          //     protectLevel: '深度保养',
          //     protectDetail: '深度清洁机器和更换磨损严重的零部件，并且检查设备的控制系统',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '邵逸辰',
          //     status:'已完成',
          // },
          // {
          //     protectNo: 'p202407000020',
          //     equipmentNo: '1724979386368',//数据库中的设备编号，此为车辆
          //     equipmentName: '充电机-002',
          //     protectLevel: '日常保养',
          //     protectDetail: '检查充电机是否有明显的物理损坏，并清理充电机外部灰尘和异物。',
          //     startProtect: '2024-07-17',
          //     endProtect: '2024-07-19',
          //     protectPerson: '秦朗',
          //     status:'已完成',
          // }
          
      ],
      pageParams:{
          page: 1,
          limit: 20,
      },
      total:0,
      requestLoad : {
          sectionId: 0,
      },
    }
  },
  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.requestList();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {

    getStatusClass(status) {
      switch (status) {
        case '待处理':
          return 'severity';
        case '处理中':
          return 'general';
        case '已完成':
          return 'completed';
        default:
          return '';
      }
    },    

    
    
    async requestList() {//目前为静态页面，注释此方法，此方法会从后台获取数据
      let params = {
        //...this.formInline,
        status: this.formInline.status,
        startProtectTime:formattedTime(this.formInline.startProtectTime),
        endProtectTime:formattedTime(this.formInline.endProtectTime),
        sectionId: this.$store.state.tree.sectionId
      };
      let res = await this.$http.post(`/equip/deviceProtectInfo/page?page=${this.pageParams.page}&limit=${this.pageParams.limit}`, params);
      this.tableData = res.result?.records;
      this.total = res.result?.total;
    },

    async handleExport() {
      this.loading = true;
      let params = {
        ...this.formInline,
        sectionId: this.$store.state.tree.sectionId
      };
      let res = await exportProtect(params);
      var list = new Blob([res], {
        type: "application/vnd.ms-excel;charset=utf-8",
      });
      var downloadUrl = window.URL.createObjectURL(list);
      var anchor = document.createElement("a");
      anchor.href = downloadUrl;
      anchor.download = '设备保养台账管理表.xlsx';
      anchor.click();
      window.URL.revokeObjectURL(list);
      this.loading = false;
    },

    handleImport() {
      this.importvisible = true;
    },

    handleOperate(item) {
      if (item) {
        this.curData = { data: { ...item }, type: 2 }
      } else {
        this.curData = { data: {}, type: 1 }
      }
      this.dialogVisible = true
    },

    onClose() {
      this.dialogVisible = false;
    },

    handleClose() {
      this.requestList();
      this.importvisible = false;
    },

    onSave() {
      this.requestList();
      this.dialogVisible = false;
    }
  }
}
</script>

<style lang="scss" scoped>
#protect {
  .content-option {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .content-title {
      line-height: 40px;
      font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
      font-weight: 700;
      font-style: normal;
      font-size: 18px;
      color: #3A3F5D;
      margin-right: 5%;
    }

    .form-view {
      flex: 1;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }

    ::v-deep .el-input {
      width: 175px;
    }
  }
  
  .status-indicator {
        display: inline-block;
        width: 80px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 15px;
        //color: white;
    }

    .severity {
      background-color: #ff4d4f; /* 红色 */
    }

    .general {
      background-color: #faad14; /* 黄色 */
    }

    .completed {
      background-color: #52c41a; /* 绿色 */
    }

  .table-edit {
    margin-right: 10px;
  }

  .pagination-part {
    margin-top: 20px;
  }
}
.jump-to-screen {
  margin-left: 5px;
  background-color: #379de5;
  color: white;
}
.content-table{
  font-size: 10px;
}
</style>