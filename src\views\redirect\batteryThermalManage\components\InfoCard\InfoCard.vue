<template>
    <div class="info-card">
        <div class="info-card__title">
            <slot name="title"></slot>
        </div>
        <div class="info-card__content">
            <slot name="content"></slot>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.info-card {
    padding: 10px;
    // background: #fff;
    // border-radius: 4px;
    // box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #666;
    height: 100%;
    &__title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 10px;
    }
    &__content {
        font-size: 14px;
        color: #666;
        display: flex;
        flex-wrap: wrap;
        gap: 10px; // 使用 gap 处理间距

        & > * {
            flex: 0 0 calc(33% - 5px); // 33.33% 减去间距的一半（gap 为 10px 时）
            box-sizing: border-box;       // 包含 padding 和 border 在宽度内
            // 可选：添加边框或背景色以便观察布局
            // border: 1px solid #ccc;
            // padding: 10px;
        }
    }
}
</style>