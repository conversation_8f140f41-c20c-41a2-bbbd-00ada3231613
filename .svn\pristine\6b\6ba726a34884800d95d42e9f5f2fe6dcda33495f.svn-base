<template>
  <div id="electricity-container">
    <div class="ele-count">
      <div class="count-total">
        <div class="total-first">
          <svg-icon icon-class="total-bg" class="icon-logo" />
          <div class="total-title">累计数据</div>
        </div>
        <div class="total-second">
          <div class="top">
            <span>{{ this.eleData.totalchargeCapacity + " " }}</span> kwh
          </div>
          <div class="bottom">
            <span>累计充电量</span>
          </div>
        </div>
        <div class="total-three">
          <div class="top">
            <span>{{ this.eleData.totalDischargeCapacity + " " }}</span> kwh
          </div>
          <div class="bottom">
            <span>累计耗电量</span>
          </div>
        </div>
        <div class="total-four">
          <img class="cd" src="@/assets/images/charging-plugin.png" />
        </div>
      </div>
      <div class="count-today">
        <div class="total-first">
          <svg-icon icon-class="today-bg" class="icon-logo" />
          <div class="total-title">今日数据</div>
        </div>
        <div class="total-second">
          <div class="top">
            <span>{{ this.eleData.todayChargeTotal + " " }}</span> kwh
          </div>
          <div class="bottom">
            <span>今日充电量</span>
          </div>
        </div>
        <div class="total-three">
          <div class="top">
            <span>{{ this.eleData.todayDischargeTotal + " " }}</span> kwh
          </div>
          <div class="bottom">
            <span>今日耗电量</span>
          </div>
        </div>
        <div class="total-four">
          <img class="cd" src="@/assets/images/charging-plug.png" />
        </div>
      </div>
    </div>
    <div class="text">近一月电量数据</div>
    <div id="ele-chart" ref="electricityChart"></div>
  </div>
</template>

<script>
import { pxToNum } from "@/utils/pxToVw";
export default {
  name: "electricity",
  props: [],
  data() {
    return {
      xAxis: [],
      eleData: {},
      totalCharge: [],
      totalConsume: [],
    };
  },
  mounted() {},
  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.getBatteryInfo();
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    async getBatteryInfo() {
      let x = [];
      let yc = [];
      let yh = [];
      let res = await this.$http.get(
        `/equip/build/electricStatistics?sectionId=${this.$store.state.tree.sectionId||''}`
      );
      this.eleData = {
        todayChargeTotal: res.result?.todayChargeTotal || 0,
        todayDischargeTotal: res.result?.todayDischargeTotal || 0,
        totalchargeCapacity: res.result?.totalchargeCapacity || 0,
        totalDischargeCapacity: res.result?.totalDischargeCapacity || 0,
      };
      res.result?.statistic?.map((ele) => {
        x.push(ele.sectionName);
        yc.push(ele.totalchargeCapacity);
        yh.push(ele.totalDischargeCapacity);
      });
      this.xAxis = x;
      this.totalCharge = yc;
      this.totalConsume = yh;
      this.setChart();
    },
    // 初始化
    setChart() {
      let _ = this;
      let myChart = _.$echarts.init(
        document.getElementById("ele-chart"),
        null,
        {
          renderer: "svg",
        }
      );
      myChart.setOption({
        color: ["#18A6EA", "#2CD0BB"],
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "1%",
          right: "0%",
          bottom: 20,
          top: 30,
          containLabel: true,
        },
        legend: {
          data: ["充电量", "耗电量"],
          textStyle:{
            fontSize: pxToNum(16),    
          }
          
        },
        xAxis: [
          {
            type: "category",
            data: this.xAxis,
            axisLabel: {
              fontSize: pxToNum(16),
            },
          },
          
        ],
        yAxis: [
          {
            type: "value",
            name: "单位 / 辆",
            nameTextStyle: {
            fontSize: pxToNum(16),
            },
            axisLabel: {
              fontSize: pxToNum(16),
            },
          },
        ],
        // 动条
        dataZoom: [
          {
            show: true, // 是否显示滑动条，不影响使用
            type: "slider",
            startValue: 0,
            endValue: 7,
            bottom: 0,
            height: 20,
          },
        ],
        series: [
          {
            name: "充电量",
            type: "line",
            tooltip: {
              valueFormatter: function (value) {
                return value + " kwh";
              },
            },
            data: this.totalCharge,
          },
          {
            name: "耗电量",
            type: "line",
            tooltip: {
              valueFormatter: function (value) {
                return value + " kwh";
              },
            },
            data: this.totalConsume,
          },
        ],
      });
    },
  },
};
</script>

<style lang="scss" scoped>
#electricity-container {
  width: 100%;
  height: 100%;
  .ele-count {
    width: 100%;
    height: 105px;
    display: flex;
    justify-content: space-between;

    .count-total {
      flex: 1;
      background-image: linear-gradient(to right, #f5f5f5, #c9e5ff);
      border: 1px solid #c9e5ff;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;

      .total-first {
        width: 39px;
        height: 105px;
        position: relative;

        .icon-logo {
          width: 100%;
          height: 100%;
        }

        .total-title {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          color: #fff;
          font-family: "思源黑体 CN Medium", "思源黑体 CN", sans-serif;
          font-weight: 500;
          font-style: normal;
          font-size: 16px;
          writing-mode: vertical-lr;
          letter-spacing: 5px;
        }
      }

      .total-second {
        flex: 1;
        display: flex;
        flex-direction: column;

        .top {
          width: 100%;
          height: 50%;
          text-align: center;
          line-height: 80px;
          font-size: 16px;
          font-family: "优设标题黑", sans-serif;
          font-style: normal;
          color: #3a3f5d;

          span {
            font-size: 26px;
            font-weight: 400;
          }
        }

        .bottom {
          width: 100%;
          height: 50%;
          text-align: center;
          line-height: 50px;
          font-family: "微软雅黑", sans-serif;
          font-weight: 400;
          font-style: normal;
          font-size: 16px;
          color: #3a3f5d;
        }
      }

      .total-three {
        flex: 1;
        display: flex;
        flex-direction: column;

        .top {
          width: 100%;
          height: 50%;
          text-align: center;
          line-height: 80px;
          font-size: 16px;
          font-family: "优设标题黑", sans-serif;
          font-style: normal;
          color: #3a3f5d;

          span {
            font-size: 26px;
            font-weight: 400;
          }
        }

        .bottom {
          width: 100%;
          height: 50%;
          text-align: center;
          line-height: 50px;
          font-family: "微软雅黑", sans-serif;
          font-weight: 400;
          font-style: normal;
          font-size: 16px;
          color: #3a3f5d;
        }
      }

      .total-four {
        width: 55px;
        margin-right: 20px;
        display: flex;
        justify-content: center;
        align-items: center;

        .cd {
          width: 100%;
          height: 64px;
          background-size: contain;
        }
      }
    }

    .count-today {
      flex: 1;
      margin-left: 10px;
      background-image: linear-gradient(to right, #c9e5ff, #f5f5f5);
      border: 1px solid #76b4f3;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;

      .total-first {
        width: 39px;
        height: 105px;
        position: relative;

        .icon-logo {
          width: 100%;
          height: 100%;
        }

        .total-title {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          color: #fff;
          font-family: "思源黑体 CN Medium", "思源黑体 CN", sans-serif;
          font-weight: 500;
          font-style: normal;
          font-size: 16px;
          writing-mode: vertical-lr;
          letter-spacing: 5px;
        }
      }

      .total-second {
        flex: 1;
        display: flex;
        flex-direction: column;

        .top {
          width: 100%;
          height: 50%;
          text-align: center;
          line-height: 80px;
          font-size: 16px;
          font-family: "优设标题黑", sans-serif;
          font-style: normal;
          color: #3a3f5d;

          span {
            font-size: 26px;
            font-weight: 400;
          }
        }

        .bottom {
          width: 100%;
          height: 50%;
          text-align: center;
          line-height: 50px;
          font-family: "微软雅黑", sans-serif;
          font-weight: 400;
          font-style: normal;
          font-size: 16px;
          color: #3a3f5d;
        }
      }

      .total-three {
        flex: 1;
        display: flex;
        flex-direction: column;

        .top {
          width: 100%;
          height: 50%;
          text-align: center;
          line-height: 80px;
          font-size: 16px;
          font-family: "优设标题黑", sans-serif;
          font-style: normal;
          color: #3a3f5d;

          span {
            font-size: 26px;
            font-weight: 400;
          }
        }

        .bottom {
          width: 100%;
          height: 50%;
          text-align: center;
          line-height: 50px;
          font-family: "微软雅黑", sans-serif;
          font-weight: 400;
          font-style: normal;
          font-size: 16px;
          color: #3a3f5d;
        }
      }

      .total-four {
        width: 55px;
        margin-right: 20px;
        display: flex;
        justify-content: center;
        align-items: center;

        .cd {
          width: 100%;
          height: 64px;
          background-size: contain;
        }
      }
    }
  }
  .text{
    font-size: 16px;
    font-family: "微软雅黑", sans-serif;
    font-weight: 400;
    font-style: normal;
    color: #3a3f5d;
    position: relative;
    // float: inline-end;
    left:90%;
    margin-top: 5px;
  }
  #ele-chart {
    width: 100%;
    height: 220px;
    margin-top: 0;
    position: relative;
  }
}
</style>
