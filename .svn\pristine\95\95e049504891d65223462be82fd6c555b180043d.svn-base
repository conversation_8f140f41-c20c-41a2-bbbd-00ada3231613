<template>
  <div id="car-container">
    <div class="title-bg">
      <div class="img-bg">
        <svg-icon icon-class="car-bg" class="icon-logo" />
        <div class="title">项目车辆总数：{{ carNum }}</div>
      </div>
    </div>
    <div id="car-chart"></div>
  </div>
</template>

<script>
import { pxToNum } from "@/utils/pxToVw";
export default {
  name: "car",
  props: [],
  data() {
    return {
      carNum: null,
      xAxis: [],
      yAxis: [],
    };
  },
  mounted() {},
  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.getWorkHour();
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    async getWorkHour() {
      let x = [];
      let y = [];
      let res = await this.$http.get(
        `/equip/build/carInfo?sectionId=${this.$store.state.tree.sectionId}`
      );
      this.carNum = res.result?.total || 0;
      res.result?.statistic.map((i) => {
        x.push(i.sectionName);
        y.push(i.num);
      });
      this.xAxis = x;
      this.yAxis = y;
      this.setChart();
    },
    // 初始化
    setChart() {
      let _ = this;
      let myChart = _.$echarts.init(
        document.getElementById("car-chart"),
        null,
        {
          renderer: "svg",
        }
      );
      myChart.setOption({
        color: ["#A6AAF8"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "item",
          },
        },
        grid: {
          left: "1%",
          right: "0%",
          bottom: 20,
          top: 30,
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.xAxis,
            axisPointer: {
              type: "shadow",
            },
            axisLabel: {
              fontSize: pxToNum(16),
            },
          },
        ],
        yAxis: {
          type: "value",
          name: "单位/辆",
          nameTextStyle: {
            fontSize: pxToNum(16),
          },
        },
        // 动条
        dataZoom: [
          {
            show: true, // 是否显示滑动条，不影响使用
            type: "slider",
            startValue: 0,
            endValue: 7,
            bottom: 0,
            height: 20,
          },
        ],
        series: [
          {
            name: "车辆数量",
            type: "bar",
            tooltip: {
              valueFormatter: function (value) {
                return value + " 辆";
              },
            },
            barWidth: "30%",
            data: this.yAxis,
          },
        ],
      });
    },
  },
};
</script>

<style lang="scss" scoped>
#car-container {
  width: 100%;
  height: 100%;

  .title-bg {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .img-bg {
      width: 530px;
      height: 100%;
      position: relative;

      .icon-logo {
        width: 100%;
        height: 100%;
      }

      .title {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-family: "优设标题黑", sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: 22px;
        color: #3a3f5d;
      }
    }
  }

  #car-chart {
    width: 100%;
    height: 300px;
  }
}
</style>
