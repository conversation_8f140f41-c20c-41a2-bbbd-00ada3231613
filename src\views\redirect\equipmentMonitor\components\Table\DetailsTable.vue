<template>
    <div id="DetailsTable">
      <el-card class="card-content">
        <div class="content-table">
          <el-table ref="table" :data="tableData" style="width: 100%;" height="70vh" border :header-cell-style="headerCellStyle"
            :cell-style="cellStyle">
            <el-table-column type="index" width="50" label="序号" align="center" />
            <el-table-column  prop="sectionName" min-width="150" label="标段" align="center" />
            <!-- <el-table-column prop="siteName" min-width="150" label="工点" align="center" /> -->
            <!-- <el-table-column prop="projectSite" label="工点" align="center" min-width="150" /> -->
            <el-table-column  prop="deviceName" label="设备名称" align="center" min-width="150" />
            
            <el-table-column  label="型号规格" align="center" min-width="150" prop="specificationModel">
            </el-table-column>
            <el-table-column prop="factoryNum" label="出厂编号" align="center" min-width="180" />
            <el-table-column prop="managementNumber" label="管理编号" align="center" min-width="180" />
            <el-table-column prop="carNum" label="车牌号码" align="center" min-width="130" />
            <el-table-column prop="dynamicType" label="动力类型" align="center" min-width="130" />
            <el-table-column prop="energyType" label="补能类型" align="center" min-width="130" />
            <el-table-column prop="deviceNum" label="设备编号" align="center" min-width="130" />
            <el-table-column prop="collecterId" label="采集盒子ID" align="center" min-width="130" />
            <el-table-column prop="carBrand" label="车辆品牌" align="center" min-width="130" />
            <el-table-column prop="deviceTypeName" label="机械类型" align="center" min-width="150" />
            <!-- <el-table-column label="机械类型" align="center" min-width="130">
              <template slot-scope="scope">
                <span v-if="scope.row.deviceType == '0'">充电挖掘机</span>
                <span v-else-if="scope.row.deviceType == '1'">换电挖掘机</span>
                <span v-else-if="scope.row.deviceType == '2'">充电装载机</span>
                <span v-else-if="scope.row.deviceType == '3'">换电装载机</span>
                <span v-else-if="scope.row.deviceType == '4'">充电自卸车</span>
                <span v-else-if="scope.row.deviceType == '5'">换电自卸车</span>
                <span v-else-if="scope.row.deviceType == '6'">充电混泥土车</span>
                <span v-else-if="scope.row.deviceType == '7'">换电混泥土车</span>
              </template>
            </el-table-column> -->
            <el-table-column prop="manufacturer" label="制造厂家" align="center" min-width="220" />
            <el-table-column v-if="checkColumn('inspectionReport')" prop="inspectionReport" label="型式检验报告" align="center" min-width="110" />
            <el-table-column prop="entryAcceptance" label="进场验收情况" align="center" min-width="130" />
            
            <el-table-column prop="entryTime" label="进场时间" align="center" min-width="130" />
            <el-table-column v-if="checkColumn('exitTime')" prop="exitTime" label="退场时间" align="center" min-width="130" />
            <el-table-column prop="inspectionDate" label="设备检验日期" align="center" min-width="150" />
            <el-table-column  prop="operationPerson" label="作业人员" align="center" min-width="150" />
            <el-table-column v-if="checkColumn('remark')" prop="remark" label="备注" align="center" min-width="150" />
          </el-table>
          <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
            :total="total">
          </el-pagination>
        </div>
      </el-card>
    </div>
  </template>
  
  <script>
  import tableUtils from '@/mixins/tableUtils';
  export default {
    name: 'DetailsTable',
    mixins: [tableUtils],
    props: {
      urlpath:String,
      isclosed:Boolean,
    },
    data() {
      return {
        formInline: {},
        tableData: [],
      }
    },
    // mounted() {
    //   this.requestList();
    // },
    watch: {
      "$store.state.tree.sectionId": {
        
        
        handler: function () {
          this.requestList();
          
        },
        deep: true,
        immediate: true,
      },
      isclosed: {
        handler: function () {
          if (this.isclosed) {
            this.tableData = [];
            this.total = 0;
          }else{
            this.requestList();
          }
        },
      }
    },
    methods: {
      async requestList() {
        let params = {
          ...this.formInline,
          sectionId: this.$store.state.tree.sectionId||''
        };
        let res = await this.$http.post(`${this.urlpath}?page=${this.pageParams.page}&limit=${this.pageParams.limit}`, params);
        this.tableData = res.result?.records;
        this.total = res.result?.total;
      },
      checkColumn(columnProp) {
        return this.tableData.some(row => row[columnProp] !== undefined && row[columnProp] !== null && row[columnProp] !== '');
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  #DetailsTable {
    .content-option {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
  
      .content-title {
        line-height: 40px;
        font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
        font-weight: 700;
        font-style: normal;
        font-size: 18px;
        color: #3A3F5D;
        margin-right: 5%;
      }
  
      .form-view {
        flex: 1;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
      }
  
      ::v-deep .el-input {
        width: 175px;
      }
    }
  
    .table-edit {
      margin-right: 10px;
    }
  
    .pagination-part {
      margin-top: 20px;
    }
  }
  </style>