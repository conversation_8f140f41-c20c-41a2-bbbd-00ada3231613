<template>
  <div class="chart-container">
    <canvas id="line-chart" ref="chart"></canvas>
  </div>

  </template>
  
  <script>
  import Chart from 'chart.js';
  
  export default {
    name: 'LineChart',
    props: {
      ChartData:{
        type: Object,
        required: true,
      }
    },
    data(){
      return{
        dataSets:null,
        options:null,
      }
    },
    watch:{
      ChartData:{
        handler(){
          this.handleUpdate();
        },
        deep:true
      }
    },
    mounted() {
      this.renderChart();
    },
    methods: {
      renderChart() {
        const ctx = this.$refs.chart.getContext('2d');
        this.chart = new Chart(ctx, {
          type: 'line',
          data: {
            labels: this.ChartData.labels,
            datasets: this.ChartData.dataSets.map((item)=>{return {
              fill: false,
              pointRadius:0,
              lineTension:0,
              ...item  
            }})
          },
          options:{
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                xAxes: [{
                  display: true,
                  gridLines:{
                    display:false,
                  },
                }],
                ...this.ChartData.options.scales
              },
              legend:{
                labels: {
                  boxWidth: 20,
                  fontSize: 15,
                }
              }
              
            }
        });

      },
      handleUpdate(){
        // console.log(this.chart)
        this.chart.data.labels = this.ChartData.labels;
        this.chart.data.datasets = this.ChartData.dataSets.map((item)=>{return {
          fill: false,
          pointRadius:0,
          lineTension:0,
          ...item  
        }})
        this.chart.update();
      },
    }
  };
  // this.$nextTick(()=>{this.$refs.chart.resize()})
  </script>
  
  <style scoped>
  .chart-container {
    width: 100%;
    height: 100%;
    /* object-fit: contain; */
  }
  </style>