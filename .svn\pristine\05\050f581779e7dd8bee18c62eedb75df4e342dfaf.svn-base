<template>
    <div id="Wbgl">
      <div class="container-left">
        <Card :title="title" class="card-preview">
          
        </Card>
      </div>
      <div class="container-center">
        <Card :title="title" class="card-preview">
        </Card>
      </div>
      <div class="container-right">
        <Card :title="title" class="card-preview">
        </Card>
        
      </div>
    </div>
  </template>
  
  <script>
  import Card from './components/Card/Card.vue';
  export default {
    name: 'Wbgl',
    components: { Card },
    data() {
      return {
        title:"测试标题",
      }
    },
    mounted() {

    },
    watch: {

    },
  
    methods: {
    }
  }
  </script>
  
  <style lang="scss" scoped>
  #Wbgl {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
  
    .container-left {
      width: 405px;
  
      .card-preview {
        height: 280px;
      }
  
      .card-type {
        height: 325px;
  

      }
  
      .card-info {
        height: 315px;
      }
    }
  
    .container-center {
      height: 960px;
      flex: 1;
      margin: 0 10px;
  
      .card-preview {
        width: 100%;
        height: 100%;
      }
    }
  
    .container-right {
      width: 390px;
  
      .card-preview {
        height: 280px;
      }
  
      .card-type {
        height: 325px;
      }
  
      .card-info {
        height: 315px;
      }
    }
  
    .card-item {
      margin-top: 20px;
    }
  }
  </style>
  