<template>
  <div id="errWarn">
    <el-card class="card-content">
      <div class="content-option">
        <div class="content-title">设备故障警报</div>
        <el-form
          :inline="true"
          :model="formInline"
          ref="formInline"
          class="form-view"
        >
          <!-- <el-form-item>
            <el-button type="primary" @click="unsolvedList"
              >未处理警报</el-button
            >
          </el-form-item> -->
          <!-- <el-form-item>
            <el-button type="primary" @click="solvedList">已处理警报</el-button>
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="handleOperate(null)"
              >故障申报</el-button
            >
          </el-form-item>
          <!-- <el-form-item>
            <el-button type="primary" @click="errLogList"
              >故障申报记录</el-button
            >
          </el-form-item> -->
          <el-form-item label="故障等级">
            <el-input
              placeholder="请输入故障等级"
              v-model="formInline.faultLevel"
              clearable
              prefix-icon="el-icon-search"
            ></el-input>
          </el-form-item>
          <el-form-item label="规格型号">
            <el-input
              placeholder="请输入设备规格型号"
              v-model="formInline.specificationModel"
              clearable
              prefix-icon="el-icon-search"
            ></el-input>
          </el-form-item>
          <el-form-item label="制造厂家">
            <el-input
              placeholder="请输入厂家关键词"
              v-model="formInline.manufacturer"
              clearable
              prefix-icon="el-icon-search"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery"
              >查询</el-button
            >
            <!-- <el-button icon="el-icon-upload2" @click="handleImport">导入数据</el-button> -->
            <!-- <el-button
              icon="el-icon-download"
              @click="handleExport"
              :loading="loading"
              style=""
              >导出数据</el-button
            > -->
            <router-link 
                to="/devices/maintenanceStatistics"
                type="primary"
                tag="el-button" 
                :loading="loading" 
                class="jump-to-screen">
                返回维保统计
            </router-link>
            <!-- <el-button type="primary">返回维保统计</el-button> -->
          </el-form-item>
        </el-form>
      </div>
      <div class="content-table">
        <div class="table-title">
          <!-- <i class="el-icon-s-management" style="margin-right: 10px"></i>
          {{ tableTitle }} -->
        </div>
        <warnRecords v-if="!errLogVis" :tableData="tableData" />
        <errRecords v-if="errLogVis" :tableData="tableData" />

        <el-pagination
          class="pagination-part"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageParams.page"
          :page-sizes="pageSizes"
          :page-size="pageParams.limit"
          :layout="tableLayout"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-card>
    <addErrReport
      v-if="dialogVisible"
      :curData="curData"
      :dialogVisible="dialogVisible"
      @onClose="onClose"
      @onSave="onSave"
    />
  </div>
</template>

<script>
import tableUtils from "@/mixins/tableUtils";
import addErrReport from "./components/addErrReport.vue";
import { exportPlan } from "@/api/baseApi/common";
import { originalData, errorReport } from "./staticData";
import warnRecords from "./components/warnRecords.vue";
import errRecords from "./components/errReportRecords.vue";

export default {
  components: { warnRecords, errRecords, addErrReport },
  name: "errWarn",
  mixins: [tableUtils],
  //   components: { AddLedgerDialog, ImportDialog },
  data() {
    return {
      formInline: {},
      dialogVisible: false,
      curData: null,
      tableData: [],
      tableTitle: "未处理警报",
      originalData,
      errorReport,
      importvisible: false,
      loading: false,
      errLogVis: false,
      deviceTypeMap: {
        0: "充电挖掘机",
        1: "换电挖掘机",
        2: "充电装载机",
        3: "换电装载机",
        4: "充电自卸车",
        5: "换电自卸车",
        6: "充电混泥土车",
        7: "换电混泥土车",
      },
    };
  },
  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.requestList();
        // this.unsolvedList();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    async requestList() {
      let params = {
        ...this.formInline,
        sectionId: this.$store.state.tree.sectionId||'',
      };
      let res = await this.$http.post(
        `/equip/fault/page?page=${this.pageParams.page}&limit=${this.pageParams.limit}`,
        params
      );
      this.tableData = res.result?.records;
      this.total = res.result?.total;
    },

    unsolvedList() {
      // this.tableData = this.originalData.filter(
      //   (item) => item.taskStatus === "未处理"
      // );
      this.tableTitle = "未处理警报";
      this.total = this.tableData.length;
      this.errLogVis = false;

      console.log(this.tableData);
    },

    solvedList() {
      this.tableData = this.originalData.filter(
        (item) => item.taskStatus !== "未处理"
      );
      this.tableTitle = "已处理警报";
      this.total = this.tableData.length;
      this.errLogVis = false;
    },

    errLogList() {
      this.tableData = this.errorReport;
      this.tableTitle = "故障申报记录";
      this.total = this.tableData.length;
      this.errLogVis = true;
    },

    async handleExport() {
      this.loading = true;
      let params = {
        ...this.formInline,
        sectionId: this.$store.state.tree.sectionId,
      };
      let res = await exportPlan(params);
      var list = new Blob([res], {
        type: "application/vnd.ms-excel;charset=utf-8",
      });
      var downloadUrl = window.URL.createObjectURL(list);
      var anchor = document.createElement("a");
      anchor.href = downloadUrl;
      anchor.download = "设备故障表.xlsx";
      anchor.click();
      window.URL.revokeObjectURL(list);
      this.loading = false;
    },

    handleImport() {
      this.importvisible = true;
      this.requestList();
    },

    handleOperate(item) {
      if (item) {
        this.curData = { data: { ...item }, type: 2 };
      } else {
        this.curData = { data: {}, type: 1 };
      }
      this.dialogVisible = true;
    },

    onClose() {
      this.dialogVisible = false;
    },

    handleClose() {
      this.requestList();
      this.importvisible = false;
    },

    onSave() {
      this.requestList();
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
#errWarn {
  .table-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin: 10px 0;
    padding: 10px 0;

    display: flex;
    justify-content: center;
    align-items: center;
  }
  .content-option {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    height: 30px;

    .content-title {
      line-height: 40px;
      font-family: "微软雅黑 Bold", "微软雅黑 Regular", "微软雅黑", sans-serif;
      font-weight: 700;
      font-style: normal;
      font-size: 18px;
      color: #3a3f5d;
      margin-right: 2%;
      padding: 0px;
    }

    .form-view {
      flex: 1;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      padding: 0px;
    }

    ::v-deep .el-input {
      width: 175px;
    }
  }

  .table-edit {
    margin-right: 10px;
  }

  .pagination-part {
    margin-top: 20px;
  }
}
.jump-to-screen {
  margin-left: 5px;
  background-color: #379de5;
  color: white;
}
</style>
