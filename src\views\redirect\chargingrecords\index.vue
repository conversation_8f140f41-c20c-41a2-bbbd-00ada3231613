<template>
    <div id="Cdjl">
        <el-card class="card-content">
            <div class="content-option">
                <div class="content-title">充电记录</div>
                <el-form :inline="true" :model="formInline" ref="formInline" class="form-view">
                    <el-form-item label="电池ID">
                        <el-input v-model="formInline.batId" clearable 
                        prefix-icon="el-icon-search"></el-input>
                    </el-form-item>
                    <el-form-item label="开始时间">
                        <el-date-picker
                        v-model="startTime"
                        type="datetime"
                        placeholder="选择开始日期"
                        clearable
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="结束时间">
                        <el-date-picker
                        v-model="endTime"
                        type="datetime"
                        placeholder="选择结束日期"
                        clearable
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item class="button-container">
                        <el-button type="primary" @click="handleQuery()">查询</el-button>
                        <el-button @click="handelExport()" :loading="loading">导出</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="content-table">
                <el-table ref="table" :data="tableData" style="width: 100%"  border :header-cell-style="headerCellStyle" :cell-style="cellStyle">
                    <el-table-column type="index" width="50" label="序号" align="center" />
                    <el-table-column prop="chargeNo" label="充电架号" align="center" width="100" />
                    <el-table-column prop="batId" label="电池ID" align="center" width="120" />
                    <el-table-column prop="startTime" label="开始时间" align="center" width="200" />
                    <el-table-column prop="endTime" label="结束时间" align="center" width="200" />
                    <el-table-column prop="chargeTime" label="充电时间(分钟)" align="center" min-width="160" />
                    <el-table-column prop="startSoc" label="起始SOC(%)" align="center" min-width="160" />
                    <el-table-column prop="endSoc" label="结束SOC(%)" align="center" min-width="160" />
                    <el-table-column prop="startMeterValue" label="起始电量(KWH)" align="center" min-width="160" />
                    <el-table-column prop="endMeterValue" label="结束电量(KWH)" align="center" min-width="160" />
                    <el-table-column prop="chargingElectricity" label="充电电量(KWH)" align="center" min-width="160" />
                    <el-table-column prop="endReason" label="结束原因" align="center" width="160" fixed="right"/>
                </el-table>
                <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
                :total="total">
                </el-pagination>
            </div>
        </el-card>
    </div>
</template>
<script>
import tableUtils from '@/mixins/tableUtils';
import { exportRecordExcel } from '@/api/baseApi/common';
import { formattedTime } from '@/utils/timer';
export default{
    name:'Cdjl',
    mixins: [tableUtils],
    components: {},
    mounted(){
        this.getRecodes();
    },
    computed: {
        treeInfo() {
            return this.$store.state.tree.sectionId;
        }
    },
    watch:{
        treeInfo(){
            console.log(this.treeInfo);
            this.getRecodes()
        }
    },
    data(){
        return{
            loading: false,
            formInline: {},
            startTime:'',
            endTime:'',
            tableData:[],
            pageParams:{
                page: 1,
                limit: 20,
            },
            total:0,
            requestLoad : {
                sectionId: 0,
            },
        }
    },
    methods:{
        handleSizeChange(val){
            this.pageParams = {
                ...this.pageParams,
                limit: val
            }
            // console.log(this.pageParams);
            this.handleQuery()
        },
        handleCurrentChange(val){
            this.pageParams = {
                ...this.pageParams,
                page: val
            }
            // console.log(val)
            this.handleQuery()
        },
        async handelExport(){
            this.loading = true;
            let params = {
                ...this.formInline,
                startTime:formattedTime(this.startTime),
                endTime:formattedTime(this.endTime),
                // ...this.requestLoad,
                sectionId: this.treeInfo,
            };
            // console.log(params);
            let res = await exportRecordExcel(params)
            var list = new Blob([res], {
                type: "application/vnd.ms-excel;charset=utf-8",
            });
            var downloadUrl = window.URL.createObjectURL(list);
            var anchor = document.createElement("a");
            anchor.href = downloadUrl;
            anchor.download = '充电记录表.xlsx';
            anchor.click();
            window.URL.revokeObjectURL(list);
            this.loading = false;
            // console.log(res instanceof Blob);
        },
        handleQuery(){
            this.getRecodes()
            // this.formInline = {}
        },
        async getRecodes(){
            let params = {
                ...this.formInline,
                // ...this.requestLoad,
                startTime:formattedTime(this.startTime),
                endTime:formattedTime(this.endTime),
                sectionId: this.treeInfo,
            };
            console.log(params);
            let res = await this.$http.post(`/equip/chargeRecord/page?limit=${this.pageParams.limit}&page=${this.pageParams.page}`, params)
            this.tableData = res.result?.records;
            this.total = res.result?.total;

            // console.log(res);
        },
    }
}
</script>

<style lang="scss" scoped>
#Cdjl {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width:100%;
    // height: 100%;
    .card-content{
        .content-option {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            width:100%;

            .content-title {
                line-height: 40px;
                font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
                font-weight: 700;
                font-style: normal;
                font-size: 18px;
                color: #3A3F5D;
                margin-right: 5%;
            }

            .form-view {
                display: flex;
                align-items: flex-end;
                // float: right;
                // width: 80%;
                // flex-direction: row;
                // flex-wrap: wrap;
                .button-container{
                    min-width:150px;
                }
            }
        }
    }

}
</style>