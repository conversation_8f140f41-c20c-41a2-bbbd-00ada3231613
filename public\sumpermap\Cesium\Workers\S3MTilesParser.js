/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./IndexDatatype-9435b55f","./createTaskProcessorWorker","./BoundingRectangle-3d4f3d01","./Color-69f1845f","./pako_inflate-8ea163f9","./S3MCompressType-c0bf5136"],function(t,e,n,r,a,i,E,o,s,O,y,T,p,A,_,u,pt,At){function _t(t,e,n,r,a,i){this.left=t,this.bottom=e,this.right=n,this.top=r,this.minHeight=a,this.maxHeight=i,this.width=n-t,this.length=r-e,this.height=i-a}function ut(t,e,n,r,a){var i=n.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var E=0,o={},s=o.vertexAttributes=[],y=o.attrLocation={};o.instanceCount=0;var T=o.instanceMode=0;n.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var p=n.getUint16(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var A=p;4<p&&(A=p>>8,p&=15);var _=n.getUint32(r,!0);if(r+=Uint32Array.BYTES_PER_ELEMENT,0<_){var u=n.getUint16(r,!0);u=p*Float32Array.BYTES_PER_ELEMENT,r+=Uint32Array.BYTES_PER_ELEMENT,E=_*u,y.aPosition=T,s.push({index:y.aPosition,typedArray:e.subarray(r,r+E),componentsPerAttribute:p,componentDatatype:O.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:u,normalize:!1}),T++,r+=E}var v=n.getUint32(r,!0);if(r+=Uint32Array.BYTES_PER_ELEMENT,0<v){var c=n.getUint16(r,!0);c=A*Float32Array.BYTES_PER_ELEMENT,r+=Uint32Array.BYTES_PER_ELEMENT,E=v*c,t.ignoreNormal||(y.aNormal=T,s.push({index:y.aNormal,typedArray:e.subarray(r,r+E),componentsPerAttribute:A,componentDatatype:O.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:c,normalize:!1}),T++),r+=E}var f=n.getUint32(r,!0);if(r+=Uint32Array.BYTES_PER_ELEMENT,0<f){var B=new Uint8Array(4*f);a.push(B.buffer);var m=n.getUint32(r,!0);m=4*Float32Array.BYTES_PER_ELEMENT,r+=Uint32Array.BYTES_PER_ELEMENT,E=f*m;for(var l=new Float32Array(e.buffer,r,4*_),U=0;U<_;U++)B[4*U]=255*l[4*U],B[4*U+1]=255*l[4*U+1],B[4*U+2]=255*l[4*U+2],B[4*U+3]=255*l[4*U+3];r+=E,y.aColor=T,s.push({index:y.aColor,typedArray:B,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.UNSIGNED_BYTE,offsetInBytes:0,strideInBytes:4,normalize:!0}),T++}var P=n.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT,0<P&&(r+=E=16*P);var d=n.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;for(var g,L,N=-1,M=0;M<d;M++){g=n.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT,L=n.getUint16(r,!0),r+=Uint16Array.BYTES_PER_ELEMENT,n.getUint16(r,!0),r+=Uint16Array.BYTES_PER_ELEMENT,E=g*L*Float32Array.BYTES_PER_ELEMENT;var S,h=e.subarray(r,r+E);if(-1!=N||20!=L&&35!=L)if(-1!==N)o.instanceBounds=new Float32Array(e.buffer,r,g*L);else{var R="aTexCoord"+M;y[R]=T++,s.push({index:y[R],typedArray:h,componentsPerAttribute:L,componentDatatype:O.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:L*Float32Array.BYTES_PER_ELEMENT,normalize:!1})}else N=M,o.instanceCount=g,o.instanceMode=L,o.instanceBuffer=h,20===L?(S=20*Float32Array.BYTES_PER_ELEMENT,y.uv2=T++,s.push({index:y.uv2,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:S,instanceDivisor:1}),y.uv3=T++,s.push({index:y.uv3,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:S,instanceDivisor:1}),y.uv4=T++,s.push({index:y.uv4,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:S,instanceDivisor:1}),y.secondary_colour=T++,s.push({index:y.secondary_colour,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:S,instanceDivisor:1}),y.uv6=T++,s.push({index:y.uv6,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:16*Float32Array.BYTES_PER_ELEMENT,strideInBytes:S,instanceDivisor:1})):35===L&&(S=35*Float32Array.BYTES_PER_ELEMENT,y.uv1=T++,s.push({index:y.uv1,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:S,instanceDivisor:1,byteLength:E}),y.uv2=T++,s.push({index:y.uv2,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:S,instanceDivisor:1}),y.uv3=T++,s.push({index:y.uv3,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:S,instanceDivisor:1}),y.uv4=T++,s.push({index:y.uv4,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:S,instanceDivisor:1}),y.uv5=T++,s.push({index:y.uv5,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:16*Float32Array.BYTES_PER_ELEMENT,strideInBytes:S,instanceDivisor:1}),y.uv6=T++,s.push({index:y.uv6,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:20*Float32Array.BYTES_PER_ELEMENT,strideInBytes:S,instanceDivisor:1}),y.uv7=T++,s.push({index:y.uv7,componentsPerAttribute:3,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:24*Float32Array.BYTES_PER_ELEMENT,strideInBytes:S,instanceDivisor:1}),y.secondary_colour=T++,s.push({index:y.secondary_colour,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:27*Float32Array.BYTES_PER_ELEMENT,strideInBytes:S,instanceDivisor:1}),y.uv9=T++,s.push({index:y.uv9,componentsPerAttribute:4,componentDatatype:O.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:31*Float32Array.BYTES_PER_ELEMENT,strideInBytes:S,instanceDivisor:1}));r+=E}o.verticesCount=_,o.instanceIndex=N;var Y=n.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var D=[];for(M=0;M<Y;M++){var b={},I=n.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var F=n.getUint8(r,!0);r+=Uint8Array.BYTES_PER_ELEMENT;n.getUint8(r,!0);r+=Uint8Array.BYTES_PER_ELEMENT;var x=n.getUint8(r,!0);r+=Uint8Array.BYTES_PER_ELEMENT,r+=1,b.indicesCount=I,b.indexType=F,b.primitiveType=x;var C=r;0<I&&(0==F?(r+=E=I*Uint16Array.BYTES_PER_ELEMENT,I%2==1&&(r+=2)):r+=E=4*I),b.indicesTypedArray=e.subarray(C,C+E);var w=n.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var k=n.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT*w,b.materialCode=k,D.push(b)}return t[i]={vertexPackage:o,arrIndexPackage:D},r}function vt(t,e,n){var r=t.vertexAttributes,a=t.attrLocation,i=r.length;a[1===n?"instanceId":"batchId"]=i,r.push({index:i,typedArray:e,componentsPerAttribute:1,componentDatatype:O.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,instanceDivisor:n})}return A(function(t,e){var n=t.buffer,r=t.supportCompressType,a=t.bVolume,i=null,E=null,o=null;if(a&&t.volbuffer.byteLength<8&&(a=!1),a){var s=t.volbuffer,y=new Uint8Array(s,8),T=pt.pako.inflate(y).buffer,p=new Float64Array(T,0,1),A=new Uint32Array(T,48,1);if(0===p[0]||3200===A[0]||3201===A[0]){var _=0;0===p[0]&&(_=8),e.push(T);var u=new Float64Array(T,_,6),v=u[0],c=u[1],f=u[2],B=u[3],m=u[4]<u[5]?u[4]:u[5],l=u[4]>u[5]?u[4]:u[5];E={left:v,top:c,right:f,bottom:B,minHeight:m,maxHeight:l,width:(i=new _t(v,B,f,c,m,l)).width,length:i.length,height:i.height};var U=new Uint32Array(T,48+_,7),P=U[0],d=U[1],g=U[2],L=U[3];o={nFormat:P,nSideBlockCount:d,nBlockLength:g,nLength:L,nWidth:U[4],nHeight:U[5],nDepth:U[6],imageArray:new Uint8Array(T,76+_,L*L*4)}}}var N=0,M=new Uint8Array(n,0,4);if(115!==M[0]||51!==M[1]||109!==M[2])return{result:!1};var S=M[3],h=(y=new Uint8Array(n,4),pt.pako.inflate(y).buffer),R=new Uint8Array(h);e.push(R.buffer);var Y=new DataView(h),D=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT;var b=new Uint8Array(h,N,D),I=D%4;I&&(I=4-I),N+=D+I;var F=At.getStringFromTypedArray(b,void 0,void 0,"gbk");F=(F=F.replace(new RegExp("\r\n","gm"),"")).replace(new RegExp(":","gm"),""),Y.getUint32(N,!0),N+=Uint32Array.BYTES_PER_ELEMENT;var x=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT;var C={};C.ignoreNormal=t.ignoreNormal;for(var w=0;w<x;w++)N=ut(C,R,Y,N,e);Y.getUint32(N,!0),N+=Uint32Array.BYTES_PER_ELEMENT;var k=Y.getUint32(N,!0);for(N+=Uint32Array.BYTES_PER_ELEMENT,w=0;w<k;w++){var O=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT;var z=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT;var H={};if(-1==C[O].vertexPackage.instanceIndex){for(var G=new Float32Array(C[O].vertexPackage.verticesCount),W=0;W<z;W++){var V=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT;var X=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT;var j=0,q=0;H[V]={batchId:W};for(var J=0;J<X;J++)if(q=Y.getUint32(N,!0),N+=Uint32Array.BYTES_PER_ELEMENT,j=Y.getUint32(N,!0),N+=Uint32Array.BYTES_PER_ELEMENT,G.fill)G.fill(W,q,q+j);else for(var K=q+q,Q=q;Q<K;Q++)G[Q]=W;H[V].vertexColorOffset=q,H[V].vertexColorCount=j}vt(C[O].vertexPackage,G,void 0)}else{var Z=C[O].vertexPackage.instanceCount,$=(C[O].vertexPackage.instanceBuffer,C[O].vertexPackage.instanceMode,new Float32Array(Z)),tt=0;for(W=0;W<z;W++)for(V=Y.getUint32(N,!0),N+=Uint32Array.BYTES_PER_ELEMENT,X=Y.getUint32(N,!0),N+=Uint32Array.BYTES_PER_ELEMENT,J=0;J<X;J++){var et=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT,$[tt]=tt,void 0===H[V]&&(H[V]={vertexColorCount:1,instanceIds:[],vertexColorOffset:tt}),H[V].instanceIds.push(et),tt++}vt(C[O].vertexPackage,$,1)}C[O].pickInfo=H}Y.getUint32(N,!0),N+=Uint32Array.BYTES_PER_ELEMENT;var nt=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT;var rt={};for(w=0;w<nt;w++){var at=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT;var it=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT;var Et=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT;var ot=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT,X=Y.getUint32(N,!0),N+=Uint32Array.BYTES_PER_ELEMENT;var st=Y.getUint32(N,!0);N+=Uint32Array.BYTES_PER_ELEMENT;var yt=null;if(ot===At.S3MCompressType.enrS3TCDXTN&&1!==r){var Tt=null;yt=st>At.S3MPixelFormat.BGR||st===At.S3MPixelFormat.LUMINANCE_ALPHA?(Tt=new Uint8Array(h,N,it*Et),new Uint8Array(it*Et*4)):(Tt=new Uint16Array(h,N,X/2),new Uint16Array(it*Et)),At.DXTTextureDecode.decode(yt,it,Et,Tt,st),e.push(yt.buffer),ot=0}else yt=new Uint8Array(h,N,X);rt[at]={id:at,width:it,height:Et,compressType:ot,nFormat:st,imageBuffer:yt},N+=X}return{result:!0,version:S,xmlDoc:F,geoPackage:C,texturePackage:rt,volImageBuffer:o,volBounds:E}})});