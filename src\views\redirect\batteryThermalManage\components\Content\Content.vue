<template>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="card-content">
        <slot name="label" class="label"></slot>
      <!-- <el-tab-pane label="用户管理" name="first">用户管理</el-tab-pane> -->
      <!-- <el-tab-pane label="配置管理" name="second">配置管理</el-tab-pane>
      <el-tab-pane label="角色管理" name="third">角色管理</el-tab-pane>
      <el-tab-pane label="定时任务补偿" name="fourth">定时任务补偿</el-tab-pane> -->
    </el-tabs>
  </template>
  <script>
    export default {
      data() {
        return {
          activeName: '1',
        };
      },
      methods: {
        handleClick(tab, event) {
          console.log(tab, event);
        }
      }
    };
  </script>
<style lang="scss" scoped>
.card-content::v-deep(.el-tabs__content) {
    height: 100%; /* 设置你想要的高度 */
}
.card-content{
    height: 91%;
    width: 99%;

    .label{
        height: 100%;
        width: 100%;
    }
}
</style>