/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240"],function(e,c,o,u){function i(e,t,n,r){this.x=c.defaultValue(e,0),this.y=c.defaultValue(t,0),this.z=c.defaultValue(n,0),this.w=c.defaultValue(r,0)}i.fromElements=function(e,t,n,r,a){return c.defined(a)?(a.x=e,a.y=t,a.z=n,a.w=r,a):new i(e,t,n,r)},i.fromColor=function(e,t){return o.Check.typeOf.object("color",e),c.defined(t)?(t.x=e.red,t.y=e.green,t.z=e.blue,t.w=e.alpha,t):new i(e.red,e.green,e.blue,e.alpha)},i.clone=function(e,t){if(c.defined(e))return c.defined(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new i(e.x,e.y,e.z,e.w)},i.packedLength=4,i.pack=function(e,t,n){return o.Check.typeOf.object("value",e),o.Check.defined("array",t),n=c.defaultValue(n,0),t[n++]=e.x,t[n++]=e.y,t[n++]=e.z,t[n]=e.w,t},i.unpack=function(e,t,n){return o.Check.defined("array",e),t=c.defaultValue(t,0),c.defined(n)||(n=new i),n.x=e[t++],n.y=e[t++],n.z=e[t++],n.w=e[t],n},i.packArray=function(e,t){o.Check.defined("array",e);var n=e.length,r=4*n;if(c.defined(t)){if(!Array.isArray(t)&&t.length!==r)throw new o.DeveloperError("If result is a typed array, it must have exactly array.length * 4 elements");t.length!==r&&(t.length=r)}else t=new Array(r);for(var a=0;a<n;++a)i.pack(e[a],t,4*a);return t},i.unpackArray=function(e,t){if(o.Check.defined("array",e),o.Check.typeOf.number.greaterThanOrEquals("array.length",e.length,4),e.length%4!=0)throw new o.DeveloperError("array length must be a multiple of 4.");var n=e.length;c.defined(t)?t.length=n/4:t=new Array(n/4);for(var r=0;r<n;r+=4){var a=r/4;t[a]=i.unpack(e,r,t[a])}return t},i.fromArray=i.unpack,i.maximumComponent=function(e){return o.Check.typeOf.object("cartesian",e),Math.max(e.x,e.y,e.z,e.w)},i.minimumComponent=function(e){return o.Check.typeOf.object("cartesian",e),Math.min(e.x,e.y,e.z,e.w)},i.minimumByComponent=function(e,t,n){return o.Check.typeOf.object("first",e),o.Check.typeOf.object("second",t),o.Check.typeOf.object("result",n),n.x=Math.min(e.x,t.x),n.y=Math.min(e.y,t.y),n.z=Math.min(e.z,t.z),n.w=Math.min(e.w,t.w),n},i.maximumByComponent=function(e,t,n){return o.Check.typeOf.object("first",e),o.Check.typeOf.object("second",t),o.Check.typeOf.object("result",n),n.x=Math.max(e.x,t.x),n.y=Math.max(e.y,t.y),n.z=Math.max(e.z,t.z),n.w=Math.max(e.w,t.w),n},i.magnitudeSquared=function(e){return o.Check.typeOf.object("cartesian",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w},i.magnitude=function(e){return Math.sqrt(i.magnitudeSquared(e))};var n=new i;i.distance=function(e,t){return o.Check.typeOf.object("left",e),o.Check.typeOf.object("right",t),i.subtract(e,t,n),i.magnitude(n)},i.distanceSquared=function(e,t){return o.Check.typeOf.object("left",e),o.Check.typeOf.object("right",t),i.subtract(e,t,n),i.magnitudeSquared(n)},i.normalize=function(e,t){o.Check.typeOf.object("cartesian",e),o.Check.typeOf.object("result",t);var n=i.magnitude(e);if(t.x=e.x/n,t.y=e.y/n,t.z=e.z/n,t.w=e.w/n,isNaN(t.x)||isNaN(t.y)||isNaN(t.z)||isNaN(t.w))throw new o.DeveloperError("normalized result is not a number");return t},i.dot=function(e,t){return o.Check.typeOf.object("left",e),o.Check.typeOf.object("right",t),e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w},i.multiplyComponents=function(e,t,n){return o.Check.typeOf.object("left",e),o.Check.typeOf.object("right",t),o.Check.typeOf.object("result",n),n.x=e.x*t.x,n.y=e.y*t.y,n.z=e.z*t.z,n.w=e.w*t.w,n},i.divideComponents=function(e,t,n){return o.Check.typeOf.object("left",e),o.Check.typeOf.object("right",t),o.Check.typeOf.object("result",n),n.x=e.x/t.x,n.y=e.y/t.y,n.z=e.z/t.z,n.w=e.w/t.w,n},i.add=function(e,t,n){return o.Check.typeOf.object("left",e),o.Check.typeOf.object("right",t),o.Check.typeOf.object("result",n),n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n},i.subtract=function(e,t,n){return o.Check.typeOf.object("left",e),o.Check.typeOf.object("right",t),o.Check.typeOf.object("result",n),n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n},i.multiplyByScalar=function(e,t,n){return o.Check.typeOf.object("cartesian",e),o.Check.typeOf.number("scalar",t),o.Check.typeOf.object("result",n),n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n},i.divideByScalar=function(e,t,n){return o.Check.typeOf.object("cartesian",e),o.Check.typeOf.number("scalar",t),o.Check.typeOf.object("result",n),n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n},i.negate=function(e,t){return o.Check.typeOf.object("cartesian",e),o.Check.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t},i.abs=function(e,t){return o.Check.typeOf.object("cartesian",e),o.Check.typeOf.object("result",t),t.x=Math.abs(e.x),t.y=Math.abs(e.y),t.z=Math.abs(e.z),t.w=Math.abs(e.w),t};var a=new i;i.lerp=function(e,t,n,r){return o.Check.typeOf.object("start",e),o.Check.typeOf.object("end",t),o.Check.typeOf.number("t",n),o.Check.typeOf.object("result",r),i.multiplyByScalar(t,n,a),r=i.multiplyByScalar(e,1-n,r),i.add(a,r,r)};var r=new i;i.mostOrthogonalAxis=function(e,t){o.Check.typeOf.object("cartesian",e),o.Check.typeOf.object("result",t);var n=i.normalize(e,r);return i.abs(n,n),t=n.x<=n.y?n.x<=n.z?n.x<=n.w?i.clone(i.UNIT_X,t):i.clone(i.UNIT_W,t):n.z<=n.w?i.clone(i.UNIT_Z,t):i.clone(i.UNIT_W,t):n.y<=n.z?n.y<=n.w?i.clone(i.UNIT_Y,t):i.clone(i.UNIT_W,t):n.z<=n.w?i.clone(i.UNIT_Z,t):i.clone(i.UNIT_W,t)},i.equals=function(e,t){return e===t||c.defined(e)&&c.defined(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w},i.equalsArray=function(e,t,n){return e.x===t[n]&&e.y===t[n+1]&&e.z===t[n+2]&&e.w===t[n+3]},i.equalsEpsilon=function(e,t,n,r){return e===t||c.defined(e)&&c.defined(t)&&u.CesiumMath.equalsEpsilon(e.x,t.x,n,r)&&u.CesiumMath.equalsEpsilon(e.y,t.y,n,r)&&u.CesiumMath.equalsEpsilon(e.z,t.z,n,r)&&u.CesiumMath.equalsEpsilon(e.w,t.w,n,r)},i.ZERO=Object.freeze(new i(0,0,0,0)),i.UNIT_X=Object.freeze(new i(1,0,0,0)),i.UNIT_Y=Object.freeze(new i(0,1,0,0)),i.UNIT_Z=Object.freeze(new i(0,0,1,0)),i.UNIT_W=Object.freeze(new i(0,0,0,1)),i.prototype.clone=function(e){return i.clone(this,e)},i.prototype.equals=function(e){return i.equals(this,e)},i.prototype.equalsEpsilon=function(e,t,n){return i.equalsEpsilon(this,e,t,n)},i.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+", "+this.w+")"};var f=new Float32Array(1),y=256;i.packFloat=function(e,t){if(o.Check.typeOf.number("value",e),c.defined(t)||(t=new i),f[0]=e,0===(e=f[0]))return i.clone(i.ZERO,t);var n,r=e<0?1:0;isFinite(e)?(e=Math.abs(e),n=Math.floor(u.CesiumMath.logBase(e,10))+1,e/=Math.pow(10,n)):(e=.1,n=38);var a=e*y;return t.x=Math.floor(a),a=(a-t.x)*y,t.y=Math.floor(a),a=(a-t.y)*y,t.z=Math.floor(a),t.w=2*(n+38)+r,t},i.unpackFloat=function(e){o.Check.typeOf.object("packedFloat",e);var t=e.w/2,n=Math.floor(t),r=2*(t-n);if(r=-(r=2*r-1),38<=(n-=38))return r<0?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY;var a=r*e.x*.00390625;return a+=r*e.y*(1/65536),(a+=r*e.z*(1/16777216))*Math.pow(10,n)},e.Cartesian4=i});