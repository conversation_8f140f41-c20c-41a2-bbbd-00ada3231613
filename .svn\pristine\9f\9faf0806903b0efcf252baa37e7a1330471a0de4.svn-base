<template>
    <div id="Wbgl">
      <div class="container-left">
        <Card :title="'设备维修管理'" class="card-preview">
          <router-link to="/devices/repairRecords" class="card-content">
            <ECharts :options="chartOptions"/>
          </router-link>
        </Card>
        
        <Card :title="'设备故障警报'" class="card-preview">
          <router-link to="/devices/errorwarning" class="card-content">
            <div class="card-content">
              <ECharts :options="pieChartOptions"/>
            </div>
          </router-link>
          
        </Card>
      </div>

      <div class="container-right">
        <router-link to="/devices/protectRecords" class="card-content">
              <Card :title="'设备保养与检测'" class="card-preview">
              <div class="card-content">
                <ECharts :options="ringChartOptions"/>
              </div>
            </Card>
        </router-link>
        

        <router-link to="/devices/knowledgecenter" class="card-content">
          <Card :title="'维保知识库'" class="card-preview">
            <div class="card-content">
               <ECharts :options="lineChartOptions"/>
            </div>
          </Card>
        </router-link>
        
      </div>
    </div>
  </template>
  
  <script>
  import Card from './components/Card/Card.vue';
  import ECharts from './components/Chart/Chart.vue';
  export default {
    name: 'Wbgl',
    components: { Card, ECharts },
    data() {
      return {
        chartOptions: {
          title: {
            text: '维修记录统计'
          },
          tooltip: {},
          xAxis: {
            data: ["电动装载机","电动自卸车","充电桩","换电站","电动挖掘机"]
          },
          yAxis: {},
          series: [
            {
            
              name: '待审批',
              type: 'bar',
              data: [3, 2, 0, 0, 1]
            },
            {
              name: '维修中',
              type: 'bar',
              data: [1, 1, 2, 1, 1]
            },
            {
              name: '已维修',
              type: 'bar',
              data: [5, 20, 2, 1, 10]
            },
          ],
          legend: {
            orient: 'horizontal',
            left: 'center'
          },
        },
        pieChartOptions: {
          title: {
            text: '设备故障报警信息',
            left: 'center'
          },
          tooltip: {
            trigger: 'item'
          },
          legend: {
            orient: 'vertical',
            left: 'left'
          },
          series: [
            {
              name: '访问来源',
              type: 'pie',
              radius: '50%',
              data: [
                { value: 48, name: '未处理' },
                { value: 35, name: '已处理' },
                { value: 50, name: '申报中' },
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        },
        ringChartOptions: {
          title: {
            text: '设备保养统计',
            left: 'center'
          },
          tooltip: {
            trigger: 'item'
          },
          legend: {
            orient: 'vertical',
            left: 'left'
          },
          series: [
            {
              name: '设备类型',
              type: 'pie',
              radius: ['40%', '70%'], // 修改这里，创建一个环形图
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '30',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 1048, name: '电动挖掘机' },
                { value: 735, name: '电动装载机' },
                { value: 580, name: '电动自卸车' },
                { value: 484, name: '充电桩' },
                { value: 300, name: '换电站' }
              ]
            }
          ]
        },
        lineChartOptions: {
          title: {
            text: '知识库访问趋势',
            left: 'left'
          },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['访问量', '历史同期访问量']
          },
          xAxis: {
            type: 'category',
            data: ['1', '2', '3', '4', '5', '6', '7']
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '访问量',
              type: 'line',
              data: [120, 200, 150, 80, 70, 110, 130]
            },
            {
              name: '历史同期访问量',
              type: 'line',
              data: [100, 150, 160, 130, 120, 140, 150]
            }
          ]
        }
      }
    },
    mounted() {

    },
    watch: {

    },
  
    methods: {
    }
  }
  </script>
  
  <style lang="scss" scoped>
  #Wbgl {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
  
    .container-left {
      width: 49%;
      margin-right: 1%;
  
      .card-preview {
        height: 43vh;
        margin-bottom: 1%;
        .card-content{
          width:100%;
          height:100%;
          display: flex;
          flex-direction: row;
          .text-content{
            width: 40%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 60px;

          }
        }
      }
  
    }
  
  
    .container-right {
      width: 49%;
      margin-right: 1%;
      .card-preview {
        height: 43vh;
        margin-bottom: 1%;
        .card-content{
          width:100%;
          height:100%;
        }
      }
  
    }
  
    .card-item {
      margin-top: 20px;
    }
  }
  </style>
  