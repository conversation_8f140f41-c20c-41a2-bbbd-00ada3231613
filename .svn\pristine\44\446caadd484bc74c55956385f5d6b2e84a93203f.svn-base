/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./GeometryAttributes-aacecde6","./IndexDatatype-9435b55f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./ArcType-66bc286a","./EllipsoidRhumbLine-6ca4b1e6","./EllipsoidGeodesic-db2069b3","./PolylinePipeline-65700d85","./Color-69f1845f"],function(x,f,I,R,c,O,e,o,r,M,U,q,t,a,i,N,F,l,n,H,s,d,W,Y){function z(e,o,r,t,a,i,l){var n,s=W.PolylinePipeline.numberOfPoints(e,o,a),d=r.red,p=r.green,f=r.blue,c=r.alpha,u=t.red,y=t.green,h=t.blue,C=t.alpha;if(Y.Color.equals(r,t)){for(n=0;n<s;n++)i[l++]=Y.Color.floatToByte(d),i[l++]=Y.Color.floatToByte(p),i[l++]=Y.Color.floatToByte(f),i[l++]=Y.Color.floatToByte(c);return l}var g=(u-d)/s,T=(y-p)/s,v=(h-f)/s,b=(C-c)/s,m=l;for(n=0;n<s;n++)i[m++]=Y.Color.floatToByte(d+n*g),i[m++]=Y.Color.floatToByte(p+n*T),i[m++]=Y.Color.floatToByte(f+n*v),i[m++]=Y.Color.floatToByte(c+n*b);return m}function u(e){var o=(e=x.defaultValue(e,x.defaultValue.EMPTY_OBJECT)).positions,r=e.colors,t=x.defaultValue(e.colorsPerVertex,!1);if(!x.defined(o)||o.length<2)throw new f.DeveloperError("At least two positions are required.");if(x.defined(r)&&(t&&r.length<o.length||!t&&r.length<o.length-1))throw new f.DeveloperError("colors has an invalid length.");this._positions=o,this._colors=r,this._colorsPerVertex=t,this._arcType=x.defaultValue(e.arcType,H.ArcType.GEODESIC),this._granularity=x.defaultValue(e.granularity,I.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=x.defaultValue(e.ellipsoid,c.Ellipsoid.WGS84),this._workerName="createSimplePolylineGeometry";var a=1+o.length*R.Cartesian3.packedLength;a+=x.defined(r)?1+r.length*Y.Color.packedLength:1,this.packedLength=a+c.Ellipsoid.packedLength+3}u.pack=function(e,o,r){if(!x.defined(e))throw new f.DeveloperError("value is required");if(!x.defined(o))throw new f.DeveloperError("array is required");var t;r=x.defaultValue(r,0);var a=e._positions,i=a.length;for(o[r++]=i,t=0;t<i;++t,r+=R.Cartesian3.packedLength)R.Cartesian3.pack(a[t],o,r);var l=e._colors;for(i=x.defined(l)?l.length:0,o[r++]=i,t=0;t<i;++t,r+=Y.Color.packedLength)Y.Color.pack(l[t],o,r);return c.Ellipsoid.pack(e._ellipsoid,o,r),r+=c.Ellipsoid.packedLength,o[r++]=e._colorsPerVertex?1:0,o[r++]=e._arcType,o[r]=e._granularity,o},u.unpack=function(e,o,r){if(!x.defined(e))throw new f.DeveloperError("array is required");var t;o=x.defaultValue(o,0);var a=e[o++],i=new Array(a);for(t=0;t<a;++t,o+=R.Cartesian3.packedLength)i[t]=R.Cartesian3.unpack(e,o);var l=0<(a=e[o++])?new Array(a):void 0;for(t=0;t<a;++t,o+=Y.Color.packedLength)l[t]=Y.Color.unpack(e,o);var n=c.Ellipsoid.unpack(e,o);o+=c.Ellipsoid.packedLength;var s=1===e[o++],d=e[o++],p=e[o];return x.defined(r)?(r._positions=i,r._colors=l,r._ellipsoid=n,r._colorsPerVertex=s,r._arcType=d,r._granularity=p,r):new u({positions:i,colors:l,ellipsoid:n,colorsPerVertex:s,arcType:d,granularity:p})};var J=new Array(2),j=new Array(2),K={positions:J,height:j,ellipsoid:void 0,minDistance:void 0,granularity:void 0};return u.createGeometry=function(e){var o,r,t,a,i,l=e._positions,n=e._colors,s=e._colorsPerVertex,d=e._arcType,p=e._granularity,f=e._ellipsoid,c=I.CesiumMath.chordLength(p,f.maximumRadius),u=x.defined(n)&&!s,y=l.length,h=0;if(d===H.ArcType.GEODESIC||d===H.ArcType.RHUMB){var C,g,T;T=d===H.ArcType.GEODESIC?(C=I.CesiumMath.chordLength(p,f.maximumRadius),g=W.PolylinePipeline.numberOfPoints,W.PolylinePipeline.generateArc):(C=p,g=W.PolylinePipeline.numberOfPointsRhumbLine,W.PolylinePipeline.generateRhumbArc);var v=W.PolylinePipeline.extractHeights(l,f),b=K;if(d===H.ArcType.GEODESIC?b.minDistance=c:b.granularity=p,b.ellipsoid=f,u){var m=0;for(o=0;o<y-1;o++)m+=g(l[o],l[o+1],C)+1;r=new Float64Array(3*m),a=new Uint8Array(4*m),b.positions=J,b.height=j;var E=0;for(o=0;o<y-1;++o){J[0]=l[o],J[1]=l[o+1],j[0]=v[o],j[1]=v[o+1];var P=T(b);if(x.defined(n)){var _=P.length/3;i=n[o];for(var B=0;B<_;++B)a[E++]=Y.Color.floatToByte(i.red),a[E++]=Y.Color.floatToByte(i.green),a[E++]=Y.Color.floatToByte(i.blue),a[E++]=Y.Color.floatToByte(i.alpha)}r.set(P,h),h+=P.length}}else if(b.positions=l,b.height=v,r=new Float64Array(T(b)),x.defined(n)){for(a=new Uint8Array(r.length/3*4),o=0;o<y-1;++o){h=z(l[o],l[o+1],n[o],n[o+1],c,a,h)}var A=n[y-1];a[h++]=Y.Color.floatToByte(A.red),a[h++]=Y.Color.floatToByte(A.green),a[h++]=Y.Color.floatToByte(A.blue),a[h++]=Y.Color.floatToByte(A.alpha)}}else{t=u?2*y-2:y,r=new Float64Array(3*t),a=x.defined(n)?new Uint8Array(4*t):void 0;var w=0,k=0;for(o=0;o<y;++o){var D=l[o];if(u&&0<o&&(R.Cartesian3.pack(D,r,w),w+=3,i=n[o-1],a[k++]=Y.Color.floatToByte(i.red),a[k++]=Y.Color.floatToByte(i.green),a[k++]=Y.Color.floatToByte(i.blue),a[k++]=Y.Color.floatToByte(i.alpha)),u&&o===y-1)break;R.Cartesian3.pack(D,r,w),w+=3,x.defined(n)&&(i=n[o],a[k++]=Y.Color.floatToByte(i.red),a[k++]=Y.Color.floatToByte(i.green),a[k++]=Y.Color.floatToByte(i.blue),a[k++]=Y.Color.floatToByte(i.alpha))}}var G=new N.GeometryAttributes;G.position=new U.GeometryAttribute({componentDatatype:M.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:r}),x.defined(n)&&(G.color=new U.GeometryAttribute({componentDatatype:M.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:a,normalize:!0}));var L=2*((t=r.length/3)-1),V=F.IndexDatatype.createTypedArray(t,L),S=0;for(o=0;o<t-1;++o)V[S++]=o,V[S++]=o+1;return new U.Geometry({attributes:G,indices:V,primitiveType:q.PrimitiveType.LINES,boundingSphere:O.BoundingSphere.fromPoints(l)})},function(e,o){return x.defined(o)&&(e=u.unpack(e,o)),e._ellipsoid=c.Ellipsoid.clone(e._ellipsoid),u.createGeometry(e)}});