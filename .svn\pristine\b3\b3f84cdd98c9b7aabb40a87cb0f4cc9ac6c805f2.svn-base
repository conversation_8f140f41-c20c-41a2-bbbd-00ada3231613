/**
 * @param {Viewer} viewer
 * 
 */
export default class DragEntity {
  constructor(val) {
    this.viewer = val.viewer
  }
  addEntity(value) {
    const _textColor = "rgb(11,255,244)";
    let text = '挖';
    switch (value.deviceType) {
      case '0':
        text = '挖';
        break;
      case '1':
        text = '挖';
        break;
      case '2':
        text = '装';
        break;
      case '3':
        text = '装';
        break;
      case '4':
        text = '卸';
        break;
      case '5':
        text = '卸';
        break;
      case '6':
        text = '罐';
        break;
      case '7':
        text = '罐';
        break;
      default:
        break;
    };
    let pinBuilder = new Cesium.PinBuilder();
    let poin = this.viewer.entities.add({
      id: value.id,
      name: value.deviceName,
      position: Cesium.Cartesian3.fromDegrees(parseFloat(value.lon / 4000), parseFloat(value.lat / 4000)),
      // 文字
      label: {
        text: value.managementNumber,
        font: "14px monospace",
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        fillColor: new Cesium.Color.fromCssColorString(_textColor),
        outlineWidth: 4,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        pixelOffset: new Cesium.Cartesian2(0, -50),
      },
      billboard: {
        image: pinBuilder.fromText(text, Cesium.Color.ROYALBLUE, 48).toDataURL(),
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      },
      monitoItems: {
        data: value
      },
    });
    return poin
  }

}
