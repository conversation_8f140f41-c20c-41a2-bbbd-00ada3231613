<template>
    <div class="bms-wrapper">
        <div class="bms-item">
            <div class="item-panel" id="voltage-panal">
                <div class="panel-title">总电压（V）</div>
            </div>
            <div class="item-panel" id="current-panel">
                <div class="panel-title">总电流（A）</div>
            </div>
            <div class="item-panel" id="soc-panel">
                <div class="panel-title">SOC（%）</div>
            </div>
        </div>
        <div class="bms-info">
            <div class="info-img">
                <svg-icon icon-class="dc-blue" class="icon-logo" />
                <div class="img-info">
                    <div class="info-item">最高单体电压：{{ this.deviceInfo.cellMaxVoltageValue || 0 }}V，位置 {{
                        this.deviceInfo.cellMaxVoltageNum || 0 }} 号</div>
                    <div class="info-item">最低单体电压：{{ this.deviceInfo.cellMinVoltageValue || 0 }}V，位置{{
                        this.deviceInfo.cellMinVoltageNum || 0 }}号</div>
                </div>
            </div>
            <div class="info-img">
                <svg-icon icon-class="dc-green" class="icon-logo" />
                <div class="img-info">
                    <div class="info-item">最高温度：{{ this.deviceInfo.cellMaxTempValue || 0 }} ℃，位置 {{
                        this.deviceInfo.cellMaxTempNum || 0 }} 号</div>
                    <div class="info-item">最低温度：{{ this.deviceInfo.cellMinTempValue || 0 }} ℃，位置{{
                        this.deviceInfo.cellMinTempNum || 0 }}号</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'BMSInfo',
    props: ['deviceInfo'],
    data() {
        return {
        }
    },

    watch: {
        "deviceInfo.bgCurrent": {
            handler(val) {
                this.setChartCurrent(val || 0);
            },
            deep: true
        },
        "deviceInfo.bgVoltage": {
            handler(val) {
                this.setChart(val || 0);
            },
            deep: true
        },
        "deviceInfo.soc": {
            handler(val) {
                this.setChartSoc(val || 0);
            },
            deep: true
        },
    },

    mounted() {
    },

    methods: {
        // 初始化电压 
        setChart(bgVoltage) {
            let _ = this;
            let myChart = _.$echarts.init(
                document.getElementById("voltage-panal"),
                null,
                {
                    renderer: "svg",
                }
            );
            myChart.setOption(
                {
                    series: [
                        {
                            type: 'gauge',
                            center: ['50%', '47%'],
                            radius: '90%',
                            min: 0,
                            max: 100,
                            axisLine: { // 仪表盘轴线
                                lineStyle: {
                                    width: 5,
                                    color: [
                                        [0.3, '#60BF79'],
                                        [0.7, '#FCD36B'],
                                        [1, '#F66166']
                                    ]
                                }
                            },
                            pointer: { // 指针
                                itemStyle: {
                                    color: 'auto',
                                },
                                width: 3,
                                length: '60%',
                            },
                            axisTick: { // 刻度
                                distance: -10,
                                lineStyle: {
                                    color: '#fff',
                                }
                            },
                            splitLine: { // 分隔线
                                distance: -10,
                                length: 10,
                                lineStyle: {
                                    color: '#fff',
                                    width: 2
                                }
                            },
                            axisLabel: { // 刻度
                                distance: 0,
                                fontSize: 12
                            },
                            detail: {
                                valueAnimation: true,
                                formatter: '{value} V',
                                color: '#4575E7',
                                fontSize: 18
                            },
                            data: [
                                {
                                    value: bgVoltage
                                }
                            ]
                        }
                    ]
                }
            )
        },

        // 初始化电流 
        setChartCurrent(bgCurrent) {
            let _ = this;
            let myChart = _.$echarts.init(
                document.getElementById("current-panel"),
                null,
                {
                    renderer: "svg",
                }
            );
            myChart.setOption(
                {
                    series: [
                        {
                            type: 'gauge',
                            center: ['50%', '47%'],
                            radius: '90%',
                            min: 0,
                            max: 100,
                            axisLine: { // 仪表盘轴线
                                lineStyle: {
                                    width: 5,
                                    color: [
                                        [0.3, '#60BF79'],
                                        [0.7, '#FCD36B'],
                                        [1, '#F66166']
                                    ]
                                }
                            },
                            pointer: { // 指针
                                itemStyle: {
                                    color: 'auto',
                                },
                                width: 3,
                                length: '60%',
                            },
                            axisTick: { // 刻度
                                distance: -10,
                                lineStyle: {
                                    color: '#fff',
                                }
                            },
                            splitLine: { // 分隔线
                                distance: -10,
                                length: 10,
                                lineStyle: {
                                    color: '#fff',
                                    width: 2
                                }
                            },
                            axisLabel: { // 刻度
                                distance: 0,
                                fontSize: 12
                            },
                            detail: {
                                valueAnimation: true,
                                formatter: '{value} A',
                                color: '#4575E7',
                                fontSize: 18
                            },
                            data: [
                                {
                                    value: bgCurrent
                                }
                            ]
                        }
                    ]
                }
            )
        },

        // 初始化SOC 
        setChartSoc(soc) {
            let _ = this;
            let myChart = _.$echarts.init(
                document.getElementById("soc-panel"),
                null,
                {
                    renderer: "svg",
                }
            );
            myChart.setOption(
                {
                    series: [
                        {
                            type: 'gauge',
                            center: ['50%', '47%'],
                            radius: '90%',
                            min: 0,
                            max: 100,
                            axisLine: { // 仪表盘轴线
                                lineStyle: {
                                    width: 5,
                                    color: [
                                        [0.3, '#60BF79'],
                                        [0.7, '#FCD36B'],
                                        [1, '#F66166']
                                    ]
                                }
                            },
                            pointer: { // 指针
                                itemStyle: {
                                    color: 'auto',
                                },
                                width: 3,
                                length: '60%',
                            },
                            axisTick: { // 刻度
                                distance: -10,
                                lineStyle: {
                                    color: '#fff',
                                }
                            },
                            splitLine: { // 分隔线
                                distance: -10,
                                length: 10,
                                lineStyle: {
                                    color: '#fff',
                                    width: 2
                                }
                            },
                            axisLabel: { // 刻度
                                distance: 0,
                                fontSize: 12
                            },
                            detail: {
                                valueAnimation: true,
                                formatter: '{value} %',
                                color: '#4575E7',
                                fontSize: 18
                            },
                            data: [
                                {
                                    value: soc
                                }
                            ]
                        }
                    ]
                }
            )
        },
    }
}
</script>

<style lang="scss" scoped>
.bms-wrapper {
    width: 100%;
    height: 250px;
    position: relative;

    .bms-item {
        width: 100%;
        height: 230px;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;

        .item-panel {
            flex: 1;
            position: relative;
        }

        .panel-title {
            position: absolute;
            left: 35%;
            bottom: 40px;
            width: 80px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            font-family: '微软雅黑', sans-serif;
            font-weight: 400;
            font-style: normal;
            color: #4570E7;
            font-size: 12px;
        }
    }

    .bms-info {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 50px;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-around;

        .info-img {
            width: 310px;
            height: 100%;
            position: relative;

            .icon-logo {
                width: 100%;
                height: 100%;
                background-size: contain;
            }

            .img-info {
                position: absolute;
                width: 90%;
                height: 40px;
                left: 5%;
                top: 5px;

                .info-item {
                    width: 100%;
                    height: 20px;
                    line-height: 21px;
                    text-align: center;
                    font-family: '微软雅黑', sans-serif;
                    font-weight: 400;
                    font-style: normal;
                    font-size: 14px;
                    color: #FFFFFF;
                }
            }
        }
    }
}
</style>