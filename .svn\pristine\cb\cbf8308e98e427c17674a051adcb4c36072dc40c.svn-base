<template>
  <el-dialog
    :title="titleStr"
    :visible.sync="dialogVisible"
    width="55%"
    :before-close="handleClose"
    class="popup-dialog"
  >
    <div class="dialog-part">
      <div class="item">
        <el-form ref="form" :model="formData" label-width="100px">
          <el-form-item label="设备名称">
            <el-input
              v-model="formData.deviceName"
              placeholder="请输入设备名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="管理编号">
            <el-input
              v-model="formData.managementNumber"
              placeholder="请输入管理编号"
            ></el-input>
          </el-form-item>
          <el-form-item label="进场验收情况">
            <el-input
              v-model="formData.entryAcceptance"
              placeholder="请输入进场验收情况"
            ></el-input>
          </el-form-item>
          <el-form-item label="车牌号码">
            <el-input
              v-model="formData.carNum"
              placeholder="请输入车牌号码"
            ></el-input>
          </el-form-item>
          <el-form-item label="设备编号">
            <el-input
              v-model="formData.deviceNum"
              placeholder="请输入设备编号"
            ></el-input>
          </el-form-item>
          <el-form-item label="车辆品牌">
            <el-input
              v-model="formData.carBrand"
              placeholder="请输入车辆品牌"
            ></el-input>
          </el-form-item>
          <el-form-item label="进场时间">
            <el-date-picker
              v-model="formData.entryTime"
              type="date"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="item item-right">
        <el-form ref="form" :model="formData" label-width="100px">
          <el-form-item label="标段">
            <el-input
              v-model="formData.sectionName"
              placeholder="请输入标段"
            ></el-input>
          </el-form-item>
          <el-form-item label="车架号">
            <el-input
              v-model="formData.vehicleIdentify"
              placeholder="请输入车架号"
            ></el-input>
          </el-form-item>
          <el-form-item label="型号规格">
            <el-input
              v-model="formData.specificationModel"
              placeholder="请输入型号规格"
            ></el-input>
          </el-form-item>
          <el-form-item label="制造厂家">
            <el-input
              v-model="formData.manufacturer"
              placeholder="请输入制造厂家"
            ></el-input>
          </el-form-item>

          <el-form-item label="动力类型">
            <el-select
              v-model="formData.dynamicType"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(item, index) in dynamicTypeData"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="采集盒子ID">
            <el-input
              v-model="formData.collecterId"
              placeholder="请输入采集盒子ID"
            ></el-input>
          </el-form-item>
          <el-form-item label="退场时间">
            <el-date-picker
              v-model="formData.exitTime"
              type="date"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="item item-right">
        <el-form ref="form" :model="formData" label-width="100px">
          <el-form-item label="工区">
            <el-input
              v-model="formData.projectAreaName"
              placeholder="请输入工区"
            ></el-input>
          </el-form-item>
          <el-form-item label="出厂编号">
            <el-input
              v-model="formData.factoryNum"
              placeholder="请输入出厂编号"
            ></el-input>
          </el-form-item>
          <el-form-item label="型式检验报告">
            <el-input
              v-model="formData.inspectionReport"
              placeholder="请输入型式检验报告"
            ></el-input>
          </el-form-item>
          <el-form-item label="作业人员">
            <el-input
              v-model="formData.operationPerson"
              placeholder="请输入作业人员"
            ></el-input>
          </el-form-item>
          <el-form-item label="补能类型">
            <el-select
              v-model="formData.energyType"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(item, index) in energyTypeData"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="机械类型">
            <el-select
              v-model="formData.deviceType"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(item, index) in deviceTypeData"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备检验日期">
            <el-date-picker
              v-model="formData.inspectionDate"
              type="date"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-form ref="form" :model="formData" label-width="100px">
      <el-form-item label="上传附件">
        <el-upload
          :limit="1"
          :action="upload_url"
          :on-remove="handleRemoved"
          :file-list="fileList1"
          :on-success="handleSucceed"
        >
          <el-button size="small" type="primary">点击上传</el-button>
          <div slot="tip" class="el-upload__tip">
            支持扩展名：.xls,.xlsx,.doc,.docx,.pdf,.jpg,.jpeg,.png,.git
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="备注说明">
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入内容"
          v-model="formData.remark"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click.native.prevent="submit">保存</el-button>
      <el-button @click="onClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import FormUtils from "@/mixins/formUtils";
export default {
  name: "AddLedgerDialog",
  mixins: [FormUtils],
  props: ["curData", "dialogVisible"],
  components: {},
  data() {
    return {
      formData: {},
      fileList: [],
      fileList1: [],
      deviceTypeData: [],
      dynamicTypeData: [
        {
          label: "纯电",
          value: "纯电",
        },
        {
          label: "混动",
          value: "混动",
        },
        {
          label: "拖电",
          value: "拖电",
        },
        {
          label: "氢燃料",
          value: "氢燃料",
        },
      ],
      energyTypeData: [
        {
          label: "充电",
          value: "充电",
        },
        {
          label: "换电",
          value: "换电",
        },
      ],
    };
  },
  computed: {
    titleStr() {
      return this.curData.type == "1" ? "新增设备台账" : "编辑设备台账";
    },
  },

  created() {
    let arr = this.curData.data.annxArray
      ? JSON.parse(this.curData.data.annxArray)
      : [];
    if (this.curData.type != "1" && arr) {
      this.fileList = arr.map((item) => {
        return { id: item.id, name: item.name };
      });
      this.fileList1 = arr.map((item) => {
        return {
          name: item.name,
          url: `/equip/fileinfo/download?id=${item.id}`,
        };
      });
    }
  },

  mounted() {
    this.formData = this.curData.data;
    this.getDeviceType();
  },

  methods: {
    onClose() {
      this.$emit("onClose");
    },

    async getDeviceType() {
      let device = [];
      let res = await this.$http.get(`/equip/dictData/type?dictName=机械类型`);
      res?.result.map((item) => {
        device.push({
          label: item.dictLabel,
          value: item.dictValue,
        });
      });
      this.deviceTypeData = device;
    },

    async submit() {
      let url =
        this.curData.type === 1
          ? "/equip/ledger/insert"
          : "/equip/ledger/saveOrUpdate";
      let params = {
        ...this.formData,
        annxArray:
          this.fileList.length > 0 ? JSON.stringify(this.fileList) : "",
        sectionId: this.$store.state.tree.sectionId,//访问store目录下的tree组件
      };
      const res = await this.$http.post(url, params);
      this.$emit("onSave");
      this.$msgSuccess(res.msg);
    },

    handleClose() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("onClose");
        })
        .catch((_) => {});
    },

    handleRemoved(file, files) {
      this.handleRemove(file, files, "fileList");
    },

    handleSucceed(file, files) {
      this.handleSuccess(file, files, [], "fileList");
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-dialog {
  .dialog-part {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .item {
      flex: 1;
    }

    .item-right {
      margin-left: 10px;
    }
  }

  ::v-deep .el-dialog__header {
    background-color: #4575e7;

    .el-dialog__title {
      color: #fff;
    }

    .el-dialog__close {
      color: #fff;
    }
  }

  ::v-deep .el-date-editor {
    width: 100%;
  }

  ::v-deep .el-select {
    width: 100%;
  }

  ::v-deep .el-input {
    width: 175px;
  }
}
</style>