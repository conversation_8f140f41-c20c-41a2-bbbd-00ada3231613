<template>
    <div id="RepairDetailsTable">
      <el-card class="card-content">
        <div class="content-table">
          <el-table ref="table" :data="tableData" style="width: 100%;" height="70vh" border :header-cell-style="headerCellStyle"
            :cell-style="cellStyle">
            <el-table-column type="index" min-width="50" label="序号" align="center" />
            <!-- <el-table-column prop="repairNo" label="维修单号" align="center" min-width="120" /> -->
            <el-table-column prop="sectionName" label="所属标段" align="center" min-width="70" />
            <el-table-column prop="deviceNum" label="设备编号" align="center" min-width="100" />
            <el-table-column prop="deviceName" label="设备名称" align="center" min-width="195" />
            <el-table-column prop="faultCode" label="故障编码" align="center" min-width="80" />
            <el-table-column prop="repairReason" label="报修原因" align="center" min-width="140" />
            <el-table-column prop="faultReason" label="故障原因" align="center" min-width="140" />
            <el-table-column prop="dealMeasure" label="处置措施" align="center" min-width="110" />
            <el-table-column prop="status" label="工单状态" align="center" min-width="100">
              <template slot-scope="scope">
                  <div :class="getStatusClass(scope.row.status)" class="status-indicator">
                    {{ scope.row.status }}
                  </div> 
              </template>
                <!-- <template v-slot="scope">
                    <el-select v-model="scope.row.status" placeholder="请选择">
                        <el-option
                            v-for="item in statusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </template> -->
            </el-table-column>
            <el-table-column prop="startRepairTime" label="维修起始时间" align="center" min-width="100" />
            <el-table-column prop="endRepairTime" label="维修结束时间" align="center" min-width="100" />
            <el-table-column prop="submitPerson" label="报修人" align="center" min-width="80" />
            <el-table-column prop="repairPerson" label="维修人" align="center" min-width="80" />
          </el-table>
          <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
            :total="total">
          </el-pagination>
        </div>
      </el-card>
    </div>
  </template>
  
  <script>
  import tableUtils from '@/mixins/tableUtils';
  export default {
    name: 'RepairDetailsTable',
    mixins: [tableUtils],
    props: {
      urlpath:String,
      isclosed:Boolean,
    },
    data() {
      return {
        formInline: {},
        tableData: [],
      }
    },
    // mounted() {
    //   this.requestList();
    // },
    watch: {
      "$store.state.tree.sectionId": {
        
        
        handler: function () {
          this.requestList();
          
        },
        deep: true,
        immediate: true,
      },
      isclosed: {
        handler: function () {
          if (this.isclosed) {
            this.tableData = [];
            this.total = 0;
          }else{
            this.requestList();
          }
        },
      }
    },
    methods: {
      async requestList() {
        let params = {
          ...this.formInline,
          sectionId: this.$store.state.tree.sectionId||''
        };
        let res = await this.$http.post(`${this.urlpath}?page=${this.pageParams.page}&limit=${this.pageParams.limit}`, params);
        this.tableData = res.result?.records;
        this.total = res.result?.total;
      },
      getStatusClass(status) {
        // console.log(1111111111111111111111);
        // console.log(status);
        switch (status) {
          case '严重':
            return 'severity';
          case '一般':
            return 'general';
          case '已修复':
            return 'completed';
          default:
            return '';
        }
      },
    }
  }
  </script>
  
  <style lang="scss" scoped>
  #RepairDetailsTable {
    .content-option {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
  
      .content-title {
        line-height: 40px;
        font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
        font-weight: 700;
        font-style: normal;
        font-size: 18px;
        color: #3A3F5D;
        margin-right: 5%;
      }
  
      .form-view {
        flex: 1;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
      }
  
      ::v-deep .el-input {
        width: 175px;
      }
    }
    .status-indicator {
        display: inline-block;
        width: 80px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 15px;
        //color: white;
    }
    .severity {
      background-color: #ff4d4f; /* 红色 */
    }

    .general {
      background-color: #faad14; /* 黄色 */
    }

    .completed {
      background-color: #52c41a; /* 绿色 */
    }
    .table-edit {
      margin-right: 10px;
    }
  
    .pagination-part {
      margin-top: 20px;
    }
  }
  </style>