/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./RuntimeError-ba10bc3e","./createTaskProcessorWorker","./pako_inflate-8ea163f9"],function(e,c,I,r,s){var g=**********,d=**********;function T(r,t){if(T.passThroughDataForTesting)return t;c.Check.typeOf.object("key",r),c.Check.typeOf.object("data",t);var e=r.byteLength;if(0===e||e%4!=0)throw new I.RuntimeError("The length of key must be greater than 0 and a multiple of 4.");var n=new DataView(t),i=n.getUint32(0,!0);if(i===g||i===d)return t;for(var a,o=new DataView(r),s=0,u=t.byteLength,f=u-u%8,h=e,v=8;s<f;)for(a=v=(v+8)%24;s<f&&a<h;)n.setUint32(s,n.getUint32(s,!0)^o.getUint32(a,!0),!0),n.setUint32(s+4,n.getUint32(s+4,!0)^o.getUint32(a+4,!0),!0),s+=8,a+=24;if(s<u)for(h<=a&&(a=v=(v+8)%24);s<u;)n.setUint8(s,n.getUint8(s)^o.getUint8(a)),s++,a++}function t(r,t){return 0!=(r&t)}T.passThroughDataForTesting=!1;var n=[1,2,4,8];function V(r,t,e,n,i,a){this._bits=r,this.cnodeVersion=t,this.imageryVersion=e,this.terrainVersion=n,this.imageryProvider=i,this.terrainProvider=a,this.ancestorHasTerrain=!1,this.terrainState=void 0}V.clone=function(r,t){return e.defined(t)?(t._bits=r._bits,t.cnodeVersion=r.cnodeVersion,t.imageryVersion=r.imageryVersion,t.terrainVersion=r.terrainVersion,t.imageryProvider=r.imageryProvider,t.terrainProvider=r.terrainProvider):t=new V(r._bits,r.cnodeVersion,r.imageryVersion,r.terrainVersion,r.imageryProvider,r.terrainProvider),t.ancestorHasTerrain=r.ancestorHasTerrain,t.terrainState=r.terrainState,t},V.prototype.setParent=function(r){this.ancestorHasTerrain=r.ancestorHasTerrain||this.hasTerrain()},V.prototype.hasSubtree=function(){return t(this._bits,16)},V.prototype.hasImagery=function(){return t(this._bits,64)},V.prototype.hasTerrain=function(){return t(this._bits,128)},V.prototype.hasChildren=function(){return t(this._bits,15)},V.prototype.hasChild=function(r){return t(this._bits,n[r])},V.prototype.getChildBitmask=function(){return 15&this._bits};var _=Uint16Array.BYTES_PER_ELEMENT,A=Int32Array.BYTES_PER_ELEMENT,k=Uint32Array.BYTES_PER_ELEMENT,o={METADATA:0,TERRAIN:1,DBROOT:2};o.fromString=function(r){return"Metadata"===r?o.METADATA:"Terrain"===r?o.TERRAIN:"DbRoot"===r?o.DBROOT:void 0};var P=32301;var u=**********,f=**********;return r(function(r,t){var e=o.fromString(r.type),n=r.buffer;T(r.key,n);var i=function(r){var t=new DataView(r),e=0,n=t.getUint32(e,!0);if(e+=k,n!==u&&n!==f)throw new I.RuntimeError("Invalid magic");var i=t.getUint32(e,n===u);e+=k;var a=new Uint8Array(r,e),o=s.pako.inflate(a);if(o.length===i)return o;throw new I.RuntimeError("Size of packet doesn't match header")}(n);n=i.buffer;var a=i.length;switch(e){case o.METADATA:return function(r,t,e){var n=new DataView(r),i=0,a=n.getUint32(i,!0);if(i+=k,a!==P)throw new I.RuntimeError("Invalid magic");var o=n.getUint32(i,!0);if(i+=k,1!==o)throw new I.RuntimeError("Invalid data type. Must be 1 for QuadTreePacket");var s=n.getUint32(i,!0);if(i+=k,2!==s)throw new I.RuntimeError("Invalid QuadTreePacket version. Only version 2 is supported.");var u=n.getInt32(i,!0);i+=A;var f=n.getInt32(i,!0);if(i+=A,32!==f)throw new I.RuntimeError("Invalid instance size.");var h=n.getInt32(i,!0);i+=A;var v=n.getInt32(i,!0);i+=A;var c=n.getInt32(i,!0);if(h!==u*f+(i+=A))throw new I.RuntimeError("Invalid dataBufferOffset");if(h+v+c!==t)throw new I.RuntimeError("Invalid packet offsets");for(var g=[],d=0;d<u;++d){var T=n.getUint8(i);++i,++i;var w=n.getUint16(i,!0);i+=_;var p=n.getUint16(i,!0);i+=_;var y=n.getUint16(i,!0);i+=_,i+=_,i+=_,i+=A,i+=A,i+=8;var E=n.getUint8(i++),l=n.getUint8(i++);i+=_,g.push(new V(T,w,p,y,E,l))}var m=[],b=0,U=0,R=g[b++];return""===e?++U:m[e]=R,function r(t,e,n){var i=!1;if(4===n){if(e.hasSubtree())return;i=!0}for(var a=0;a<4;++a){var o=t+a.toString();if(i)m[o]=null;else if(n<4)if(e.hasChild(a)){if(b===u)return void console.log("Incorrect number of instances");var s=g[b++];m[o]=s,r(o,s,n+1)}else m[o]=null}}(e,R,U),m}(n,a,r.quadKey);case o.TERRAIN:return function(r,t,e){for(var n=new DataView(r),i=0,a=[];i<t;){for(var o=i,s=0;s<4;++s){var u=n.getUint32(i,!0);i+=k,i+=u}var f=r.slice(o,i);e.push(f),a.push(f)}return a}(n,a,t);case o.DBROOT:return t.push(n),{buffer:n}}})});