/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./GeometryAttributes-aacecde6","./AttributeCompression-75ce15eb","./GeometryPipeline-8e55e413","./EncodedCartesian3-87cd0c1f","./IndexDatatype-9435b55f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./VertexFormat-fe4db402","./GeometryInstance-9ddb8c73","./arrayRemoveDuplicates-2869246d","./BoundingRectangle-3d4f3d01","./EllipsoidTangentPlane-9c25b2da","./OrientedBoundingBox-7b25e901","./CoplanarPolygonGeometryLibrary-051a16f8","./ArcType-66bc286a","./EllipsoidRhumbLine-6ca4b1e6","./earcut-2.2.1-b404d9e6","./PolygonPipeline-cc78b34e","./PolygonGeometryLibrary-ec05daff"],function(s,p,V,R,M,I,e,t,a,H,B,O,n,S,r,z,o,w,i,N,l,c,y,A,F,u,d,m,G,g,b,h,Q,L){var j=new R.Cartesian3,E=new u.BoundingRectangle,U=new M.Cartesian2,Y=new M.Cartesian2,T=new R.Cartesian3,k=new R.Cartesian3,D=new R.Cartesian3,_=new R.Cartesian3,q=new R.Cartesian3,J=new R.Cartesian3,W=new S.Quaternion,Z=new I.Matrix3,K=new I.Matrix3,X=new R.Cartesian3;function $(e,t,a,n,r,o,i,l){var s=e.positions,p=Q.PolygonPipeline.triangulate(e.positions2D,e.holes);p.length<3&&(p=[0,1,2]);var c=N.IndexDatatype.createTypedArray(s.length,p.length);c.set(p);var y=Z;if(0!==n){var u=S.Quaternion.fromAxisAngle(o,n,W);if(y=I.Matrix3.fromQuaternion(u,y),t.tangent||t.bitangent){u=S.Quaternion.fromAxisAngle(o,-n,W);var d=I.Matrix3.fromQuaternion(u,K);i=R.Cartesian3.normalize(I.Matrix3.multiplyByVector(d,i,i),i),t.bitangent&&(l=R.Cartesian3.normalize(R.Cartesian3.cross(o,i,l),l))}}else y=I.Matrix3.clone(I.Matrix3.IDENTITY,y);var m=Y;t.st&&(m.x=a.x,m.y=a.y);for(var g=s.length,b=3*g,h=new Float64Array(b),v=t.normal?new Float32Array(b):void 0,f=t.tangent?new Float32Array(b):void 0,C=t.bitangent?new Float32Array(b):void 0,x=t.st?new Float32Array(2*g):void 0,P=0,w=0,A=0,F=0,G=0,L=0;L<g;L++){var E=s[L];if(h[P++]=E.x,h[P++]=E.y,h[P++]=E.z,t.st){var T=r(I.Matrix3.multiplyByVector(y,E,j),U);M.Cartesian2.subtract(T,m,T);var k=V.CesiumMath.clamp(T.x/a.width,0,1),D=V.CesiumMath.clamp(T.y/a.height,0,1);x[G++]=k,x[G++]=D}t.normal&&(v[w++]=o.x,v[w++]=o.y,v[w++]=o.z),t.tangent&&(f[F++]=i.x,f[F++]=i.y,f[F++]=i.z),t.bitangent&&(C[A++]=l.x,C[A++]=l.y,C[A++]=l.z)}var _=new z.GeometryAttributes;return t.position&&(_.position=new B.GeometryAttribute({componentDatatype:H.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:h})),t.normal&&(_.normal=new B.GeometryAttribute({componentDatatype:H.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:v})),t.tangent&&(_.tangent=new B.GeometryAttribute({componentDatatype:H.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:f})),t.bitangent&&(_.bitangent=new B.GeometryAttribute({componentDatatype:H.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:C})),t.st&&(_.st=new B.GeometryAttribute({componentDatatype:H.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:x})),new B.Geometry({attributes:_,indices:c,primitiveType:O.PrimitiveType.TRIANGLES})}function v(e){var t=(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).polygonHierarchy;p.Check.defined("options.polygonHierarchy",t);var a=s.defaultValue(e.vertexFormat,y.VertexFormat.DEFAULT);this._vertexFormat=y.VertexFormat.clone(a),this._polygonHierarchy=t,this._stRotation=s.defaultValue(e.stRotation,0),this._ellipsoid=M.Ellipsoid.clone(s.defaultValue(e.ellipsoid,M.Ellipsoid.WGS84)),this._workerName="createCoplanarPolygonGeometry",this.packedLength=L.PolygonGeometryLibrary.computeHierarchyPackedLength(t)+y.VertexFormat.packedLength+M.Ellipsoid.packedLength+2}v.fromPositions=function(e){return e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT),p.Check.defined("options.positions",e.positions),new v({polygonHierarchy:{positions:e.positions},vertexFormat:e.vertexFormat,stRotation:e.stRotation,ellipsoid:e.ellipsoid})},v.pack=function(e,t,a){return p.Check.typeOf.object("value",e),p.Check.defined("array",t),a=s.defaultValue(a,0),a=L.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,a),M.Ellipsoid.pack(e._ellipsoid,t,a),a+=M.Ellipsoid.packedLength,y.VertexFormat.pack(e._vertexFormat,t,a),a+=y.VertexFormat.packedLength,t[a++]=e._stRotation,t[a]=e.packedLength,t};var f=M.Ellipsoid.clone(M.Ellipsoid.UNIT_SPHERE),C=new y.VertexFormat,x={polygonHierarchy:{}};return v.unpack=function(e,t,a){p.Check.defined("array",e),t=s.defaultValue(t,0);var n=L.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t);t=n.startingIndex,delete n.startingIndex;var r=M.Ellipsoid.unpack(e,t,f);t+=M.Ellipsoid.packedLength;var o=y.VertexFormat.unpack(e,t,C);t+=y.VertexFormat.packedLength;var i=e[t++],l=e[t];return s.defined(a)||(a=new v(x)),a._polygonHierarchy=n,a._ellipsoid=M.Ellipsoid.clone(r,a._ellipsoid),a._vertexFormat=y.VertexFormat.clone(o,a._vertexFormat),a._stRotation=i,a.packedLength=l,a},v.createGeometry=function(e){var t=e._vertexFormat,a=e._polygonHierarchy,n=e._stRotation,r=a.positions;if(!((r=F.arrayRemoveDuplicates(r,R.Cartesian3.equalsEpsilon,!0)).length<3)){var o=T,i=k,l=D,s=q,p=J;if(G.CoplanarPolygonGeometryLibrary.computeProjectTo2DArguments(r,_,s,p)){if(o=R.Cartesian3.cross(s,p,o),o=R.Cartesian3.normalize(o,o),!R.Cartesian3.equalsEpsilon(_,R.Cartesian3.ZERO,V.CesiumMath.EPSILON6)){var c=e._ellipsoid.geodeticSurfaceNormal(_,X);R.Cartesian3.dot(o,c)<0&&(o=R.Cartesian3.negate(o,o),s=R.Cartesian3.negate(s,s))}var y=G.CoplanarPolygonGeometryLibrary.createProjectPointsTo2DFunction(_,s,p),u=G.CoplanarPolygonGeometryLibrary.createProjectPointTo2DFunction(_,s,p);t.tangent&&(i=R.Cartesian3.clone(s,i)),t.bitangent&&(l=R.Cartesian3.clone(p,l));var d=L.PolygonGeometryLibrary.polygonsFromHierarchy(a,y,!1),m=d.hierarchy,g=d.polygons;if(0!==m.length){r=m[0].outerRing;for(var b=I.BoundingSphere.fromPoints(r),h=L.PolygonGeometryLibrary.computeBoundingRectangle(o,u,r,n,E),v=[],f=0;f<g.length;f++){var C=new A.GeometryInstance({geometry:$(g[f],t,h,n,u,o,i,l)});v.push(C)}var x=w.GeometryPipeline.combineInstances(v)[0];x.attributes.position.values=new Float64Array(x.attributes.position.values),x.indices=N.IndexDatatype.createTypedArray(x.attributes.position.values.length/3,x.indices);var P=x.attributes;return t.position||delete P.position,new B.Geometry({attributes:P,indices:x.indices,primitiveType:x.primitiveType,boundingSphere:b})}}}},function(e,t){return s.defined(t)&&(e=v.unpack(e,t)),v.createGeometry(e)}});