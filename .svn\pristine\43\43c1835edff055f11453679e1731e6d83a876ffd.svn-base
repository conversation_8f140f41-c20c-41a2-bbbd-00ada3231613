<template>
  <div id="layout">
    <div class="header">
      <Header></Header>
    </div>
    <div class="main">
      <el-aside class="aside_main" :class="{ asidemainshow: !asideStatus }">
        <!-- 目录结构树组件 -->
        <tree />
      </el-aside>
      <el-container>
        <el-main class="main_cont">
          <div class="aside_open_close" @click="asidechange">
            <i class="el-icon-arrow-left" v-if="aside_open_close"></i>
            <i class="el-icon-arrow-right" v-else></i>
          </div>
          <div class="container">
            <TagsView></TagsView>
            <div class="container-main">
              <router-view></router-view>
            </div>
          </div>
        </el-main>
      </el-container>
    </div>
  </div>
</template>

<script>
import Header from "@/views/header/header.vue";
import Tree from '@/views/components/Tree/tree.vue';
import screenfull from "screenfull";
export default {
  name: "Layout",
  components: { Header, Tree },
  data() {
    return {
      asideStatus: false,
      aside_open_close: false,
    };
  },
  mounted() {
    const element = document.getElementById("layout"); //指定全屏区域元素
    document.getElementById("full-wrap-bt").addEventListener("click", () => {
      if (screenfull.isEnabled) {
        screenfull.request(element);
      }
      screenfull.toggle();
    }); //实现模块全屏
  },
  methods: {
    // 侧边栏收缩与展开
    asidechange() {
      this.asideStatus = !this.asideStatus
      if (this.asideStatus) {
        setTimeout(() => {
          this.aside_open_close = true
        }, 300)
      } else {
        setTimeout(() => {
          this.aside_open_close = false
        }, 300)
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/mixin.scss";
@import "@/styles/variables.scss";

#layout {
  .header {
    position: fixed;
    width: 100vw;
    left: 0;
    top: 0;
    z-index: 99;
  }

  .main {
    width: 100vw;
    background-color: #fff;
    padding-top: 63px;
    height: calc(100vh);
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .container {
      width: 100%;
      height: 100%;
      background-color: #E6ECF9;

      .container-main {
        padding: 10px;
        padding-bottom: 0px;
        height: calc(100vh - 100px);
        overflow: auto;
      }
    }

    .aside_main {
      width: 290px !important;
      transition: width 0.5s;
    }

    .asidemainshow {
      width: 0px !important;
    }

    .main_cont {
      position: relative;
      margin: 0;
      padding: 0;
      background-color: #E9EEF3;

      /* 侧边栏按钮样式 */
      .aside_open_close {
        position: absolute;
        left: 0;
        top: 50%;
        width: 16px;
        height: 60px;
        line-height: 60px;
        color: #fff;
        background-color: #ccc;
        border-radius: 0 6px 6px 0;
        font-size: 20px;
        z-index: 1000;
        cursor: pointer;
      }

      .aside_open_close:hover {
        background-color: #aaa;
        color: #fff;
      }
    }
  }
}
</style>
