@import './variables.scss';
// @import './mixin.scss';
// @import './transition.scss';
// @import './element-ui.scss';
@import './sidebar.scss';
html,body {
  width: 100%;
  height:100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-size:12px;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}
body{
position:relative;
overflow: hidden;
}
label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}
// ::-webkit-scrollbar {/*滚动条整体样式*/
//   width: 14px;     /*高宽分别对应横竖滚动条的尺寸*/
//   height: 14px;
// }
// ::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
//   // border-radius: 5px;
//   // box-shadow: inset 0 0 5px #f8f8f8;
//   // background:red;
//   background:#90d1ef;
//   //  background-color: #b6b6b6;
// }
// ::-webkit-scrollbar-track {/*滚动条里面轨道*/
//   box-shadow: inset 0 0 5px #f8f8f8;
//   border-radius: 0;
//   background: rgba(185,182,182,0.1);
// }
#app {
  height: 100%;
  // color:#606266;
}
.operation {
  span {
    display: inline-block;
    margin-right: 10px;
    color: #409eff;
    cursor: pointer;
  }
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  outline: none;
  text-decoration: none;
}
img{
  padding: 0px;;
  margin: 0px;;
}
div:focus {
  outline: none;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}
textarea {
  resize: none;
}
.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}
// 关于float
.hidden {
  display: none;
}

.padding {
  padding: 0 !important;
}

.margin {
  margin: 0 !important;
}

.pull-right {
  float: right !important;
  ;
}

.pull-left {
  float: left !important;
}
/* 清除浮动 */
.ks-clear:after,
.clear:after {
  content: ' ';
  display: block;
  height: 0;
  clear: both;
}
.clear {
  *zoom: 1;
}
.ks-clear,


.pull-center {
  text-align: center;
}

.pull-auto {
  height: auto;
  overflow: hidden;
}
.pull-container{
  width:100%;
  height:100%;
  box-sizing: border-box;
}
.pull-height {
  height: 100%
}

.pull-fixed {
  position: fixed;
  left: 0;
  top: 0;
}
*{
  padding:0;
  margin:0;
  list-style:none;
  text-decoration: none;
  box-sizing: border-box;
  /* 背景背景颜色，图片重复还是不重复 位置等等 */
}
//关于布局的样式;
.su-fxFill {
  display: flex;
  margin: 0;
  width: 100%;
  height: 100%;
  min-width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}
.flex-row{
  display:flex;
  flex-direction: row;
  box-sizing: border-box;
}
.flex-column{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-center{
  display:flex;
  justify-content: center;
  align-items: center;
  box-sizing:border-box;
}
.flex-split{
  display:flex;
  flex:1 1 0%;
  box-sizing: border-box;
}
.flex-wrap{
  display: flex;
  flex-wrap: nowrap;
  box-sizing: border-box;
}
.flex-between{
  display: flex;
 justify-content:space-between;
  box-sizing: border-box;
}
.card{
  box-shadow: 1px 1px 1px rgba(0,0,0,0.1);
  box-sizing: border-box;
}
.cc-row{
  display: flex;
  flex-wrap: nowrap;
  flex:1 1 0%;
}
.el-col{
  align-items: stretch;
}
* .el-table__header{
  width: 100%!important;
}
* .el-table__body{
  width: 100%!important
}
.el-table{
  width:99.9%!important
}
.el-transfer {
  font-size: 12px;
}
.el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label {
  font-size: 14px;
  color: #303133;
  font-weight: 400;
}
body .el-table th.gutter{
  display: table-cell!important;
}
.green{
  color:#2BAC82;
}
.orang{
  color: #F56C6C ;
}
.gb{
  background:rgba(43, 172, 130, 1) ;
  color:#fff
}
.orangebg{
  background: #F56C6C ;
  color:#fff
}
.fs_16{
  font-size:16px;
  font-weight: bold;
}
.warn{
  background: #E6A23C ;
  color:#fff
}
.qualirate_content{
  .el-table--border,
  .el-table td,
  .el-table th.is-leaf,
  .el-table td,
  .el-table th.is-leaf,
  .el-table--border td,
  .el-table--border th,
  .el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
    border: 0px;
  }
  .el-table--border::after,
  .el-table--group::after,
  .el-table::before {
    background: #fff;
  }
   .header_end .el-select:hover .el-input__inner,.header_end .el-input__inner {
        background: #f1f1f1;
  }
}
/**/
#mobile-bar{
    height: 40px;
    line-height: 40px;
    background: #213f7e;
    width: 100%;
    color: #fff;
    font-size: 14px;
    display:none ;
    position: fixed;
    z-index:99999
}
// @import './mobile.scss';
