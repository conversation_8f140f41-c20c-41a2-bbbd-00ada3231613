/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./Transforms-1509c877","./IntersectionTests-dbfba52c","./Plane-2bcb9154"],function(e,p,o,x,c,s,n,a,m,r){function y(e,n,t){this.minimum=x.Cartesian3.clone(p.defaultValue(e,x.Cartesian3.ZERO)),this.maximum=x.Cartesian3.clone(p.defaultValue(n,x.Cartesian3.ZERO)),t=p.defined(t)?x.Cartesian3.clone(t):x.Cartesian3.midpoint(this.minimum,this.maximum,new x.Cartesian3),this.center=t}y.fromPoints=function(e,n){if(p.defined(n)||(n=new y),!p.defined(e)||0===e.length)return n.minimum=x.Cartesian3.clone(x.Cartesian3.ZERO,n.minimum),n.maximum=x.Cartesian3.clone(x.Cartesian3.ZERO,n.maximum),n.center=x.Cartesian3.clone(x.Cartesian3.ZERO,n.center),n;for(var t=e[0].x,i=e[0].y,a=e[0].z,r=e[0].x,s=e[0].y,o=e[0].z,c=e.length,m=1;m<c;m++){var l=e[m],d=l.x,u=l.y,f=l.z;t=Math.min(d,t),r=Math.max(d,r),i=Math.min(u,i),s=Math.max(u,s),a=Math.min(f,a),o=Math.max(f,o)}var h=n.minimum;h.x=t,h.y=i,h.z=a;var C=n.maximum;return C.x=r,C.y=s,C.z=o,n.center=x.Cartesian3.midpoint(h,C,n.center),n},y.clone=function(e,n){if(p.defined(e))return p.defined(n)?(n.minimum=x.Cartesian3.clone(e.minimum,n.minimum),n.maximum=x.Cartesian3.clone(e.maximum,n.maximum),n.center=x.Cartesian3.clone(e.center,n.center),n):new y(e.minimum,e.maximum,e.center)},y.equals=function(e,n){return e===n||p.defined(e)&&p.defined(n)&&x.Cartesian3.equals(e.center,n.center)&&x.Cartesian3.equals(e.minimum,n.minimum)&&x.Cartesian3.equals(e.maximum,n.maximum)};var l=new x.Cartesian3;y.intersectPlane=function(e,n){o.Check.defined("box",e),o.Check.defined("plane",n),l=x.Cartesian3.subtract(e.maximum,e.minimum,l);var t=x.Cartesian3.multiplyByScalar(l,.5,l),i=n.normal,a=t.x*Math.abs(i.x)+t.y*Math.abs(i.y)+t.z*Math.abs(i.z),r=x.Cartesian3.dot(e.center,i)+n.distance;return 0<r-a?s.Intersect.INSIDE:r+a<0?s.Intersect.OUTSIDE:s.Intersect.INTERSECTING},y.prototype.clone=function(e){return y.clone(this,e)},y.prototype.intersectPlane=function(e){return y.intersectPlane(this,e)},y.prototype.equals=function(e){return y.equals(this,e)};var d=new n.Cartesian4;function t(e,n){if(o.Check.defined("origin",e),e=(n=p.defaultValue(n,c.Ellipsoid.WGS84)).scaleToGeodeticSurface(e),!p.defined(e))throw new o.DeveloperError("origin must not be at the center of the ellipsoid.");var t=a.Transforms.eastNorthUpToFixedFrame(e,n);this._ellipsoid=n,this._origin=e,this._xAxis=x.Cartesian3.fromCartesian4(s.Matrix4.getColumn(t,0,d)),this._yAxis=x.Cartesian3.fromCartesian4(s.Matrix4.getColumn(t,1,d));var i=x.Cartesian3.fromCartesian4(s.Matrix4.getColumn(t,2,d));this._plane=r.Plane.fromPointNormal(e,i)}Object.defineProperties(t.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},origin:{get:function(){return this._origin}},plane:{get:function(){return this._plane}},xAxis:{get:function(){return this._xAxis}},yAxis:{get:function(){return this._yAxis}},zAxis:{get:function(){return this._plane.normal}}});var i=new y;t.fromPoints=function(e,n){return o.Check.defined("cartesians",e),new t(y.fromPoints(e,i).center,n)};var u=new m.Ray,f=new x.Cartesian3;t.prototype.projectPointOntoPlane=function(e,n){o.Check.defined("cartesian",e);var t=u;t.origin=e,x.Cartesian3.normalize(e,t.direction);var i=m.IntersectionTests.rayPlane(t,this._plane,f);if(p.defined(i)||(x.Cartesian3.negate(t.direction,t.direction),i=m.IntersectionTests.rayPlane(t,this._plane,f)),p.defined(i)){var a=x.Cartesian3.subtract(i,this._origin,i),r=x.Cartesian3.dot(this._xAxis,a),s=x.Cartesian3.dot(this._yAxis,a);return p.defined(n)?(n.x=r,n.y=s,n):new c.Cartesian2(r,s)}},t.prototype.projectPointsOntoPlane=function(e,n){o.Check.defined("cartesians",e),p.defined(n)||(n=[]);for(var t=0,i=e.length,a=0;a<i;a++){var r=this.projectPointOntoPlane(e[a],n[t]);p.defined(r)&&(n[t]=r,t++)}return n.length=t,n},t.prototype.projectPointToNearestOnPlane=function(e,n){o.Check.defined("cartesian",e),p.defined(n)||(n=new c.Cartesian2);var t=u;t.origin=e,x.Cartesian3.clone(this._plane.normal,t.direction);var i=m.IntersectionTests.rayPlane(t,this._plane,f);p.defined(i)||(x.Cartesian3.negate(t.direction,t.direction),i=m.IntersectionTests.rayPlane(t,this._plane,f));var a=x.Cartesian3.subtract(i,this._origin,i),r=x.Cartesian3.dot(this._xAxis,a),s=x.Cartesian3.dot(this._yAxis,a);return n.x=r,n.y=s,n},t.prototype.projectPointsToNearestOnPlane=function(e,n){o.Check.defined("cartesians",e),p.defined(n)||(n=[]);var t=e.length;n.length=t;for(var i=0;i<t;i++)n[i]=this.projectPointToNearestOnPlane(e[i],n[i]);return n};var h=new x.Cartesian3;t.prototype.projectPointOntoEllipsoid=function(e,n){o.Check.defined("cartesian",e),p.defined(n)||(n=new x.Cartesian3);var t=this._ellipsoid,i=this._origin,a=this._xAxis,r=this._yAxis,s=h;return x.Cartesian3.multiplyByScalar(a,e.x,s),n=x.Cartesian3.add(i,s,n),x.Cartesian3.multiplyByScalar(r,e.y,s),x.Cartesian3.add(n,s,n),t.scaleToGeocentricSurface(n,n),n},t.prototype.projectPointsOntoEllipsoid=function(e,n){o.Check.defined("cartesians",e);var t=e.length;p.defined(n)?n.length=t:n=new Array(t);for(var i=0;i<t;++i)n[i]=this.projectPointOntoEllipsoid(e[i],n[i]);return n},e.AxisAlignedBoundingBox=y,e.EllipsoidTangentPlane=t});