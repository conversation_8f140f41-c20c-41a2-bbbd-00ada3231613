"use strict";
const path = require("path");

function resolve(dir) {
  return path.join(__dirname, dir);
}
const name = "新能源管控平台"; // page title
const port = process.env.port || process.env.npm_config_port || 9528; // dev port
module.exports = {
  publicPath: "/",
  outputDir: "dist", // 输出文件目录
  assetsDir: "static", // 放置静态资源
  lintOnSave: process.env.NODE_ENV === "development",
  productionSourceMap: false,
  devServer: {
    proxy: {
      "/": {
        ws: false,
        target: "http://101.207.130.161:8071/", // 线上
        // target: "http://10.10.100.105:8001/",
        // target: "http://10.48.252.78:8001/", // 本地
        changeOrigin: true,
      },
    },
    port: port,
    open: false,
  },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
      fallback: {
        path: require.resolve("path-browserify"),
      },
    },
  },
  chainWebpack: (config) => {
    config.module.rule("svg").exclude.add(resolve("src/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end();
  },
};
