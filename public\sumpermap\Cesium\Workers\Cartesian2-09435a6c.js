/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939"],function(e,l,p,C,s){function n(e,t,a,n){t=l.defaultValue(t,0),a=l.defaultValue(a,0),n=l.defaultValue(n,0),p.Check.typeOf.number.greaterThanOrEquals("x",t,0),p.Check.typeOf.number.greaterThanOrEquals("y",a,0),p.Check.typeOf.number.greaterThanOrEquals("z",n,0),C.CesiumMath.equalsEpsilon(n,6356752.314245179,C.CesiumMath.EPSILON10)&&(C.CesiumMath.Radius=n),e._radii=new s.Cartesian3(t,a,n),e._radiiSquared=new s.Cartesian3(t*t,a*a,n*n),e._radiiToTheFourth=new s.Cartesian3(t*t*t*t,a*a*a*a,n*n*n*n),e._oneOverRadii=new s.Cartesian3(0===t?0:1/t,0===a?0:1/a,0===n?0:1/n),e._oneOverRadiiSquared=new s.Cartesian3(0===t?0:1/(t*t),0===a?0:1/(a*a),0===n?0:1/(n*n)),e._minimumRadius=Math.min(t,a,n),e._maximumRadius=Math.max(t,a,n),e._centerToleranceSquared=C.CesiumMath.EPSILON1,0!==e._radiiSquared.z&&(e._squaredXOverSquaredZ=e._radiiSquared.x/e._radiiSquared.z)}function m(e,t,a){this._radii=void 0,this._radiiSquared=void 0,this._radiiToTheFourth=void 0,this._oneOverRadii=void 0,this._oneOverRadiiSquared=void 0,this._minimumRadius=void 0,this._maximumRadius=void 0,this._centerToleranceSquared=void 0,this._squaredXOverSquaredZ=void 0,n(this,e,t,a)}Object.defineProperties(m.prototype,{radii:{get:function(){return this._radii}},radiiSquared:{get:function(){return this._radiiSquared}},radiiToTheFourth:{get:function(){return this._radiiToTheFourth}},oneOverRadii:{get:function(){return this._oneOverRadii}},oneOverRadiiSquared:{get:function(){return this._oneOverRadiiSquared}},minimumRadius:{get:function(){return this._minimumRadius}},maximumRadius:{get:function(){return this._maximumRadius}}}),m.clone=function(e,t){if(l.defined(e)){var a=e._radii;return l.defined(t)?(s.Cartesian3.clone(a,t._radii),s.Cartesian3.clone(e._radiiSquared,t._radiiSquared),s.Cartesian3.clone(e._radiiToTheFourth,t._radiiToTheFourth),s.Cartesian3.clone(e._oneOverRadii,t._oneOverRadii),s.Cartesian3.clone(e._oneOverRadiiSquared,t._oneOverRadiiSquared),t._minimumRadius=e._minimumRadius,t._maximumRadius=e._maximumRadius,t._centerToleranceSquared=e._centerToleranceSquared,t):new m(a.x,a.y,a.z)}},m.fromCartesian3=function(e,t){return l.defined(t)||(t=new m),l.defined(e)&&n(t,e.x,e.y,e.z),t},m.WGS84=Object.freeze(new m(6378137,6378137,C.CesiumMath.Radius)),m.XIAN80=Object.freeze(new m(6378140,6378140,6356755.29)),m.CGCS2000=Object.freeze(new m(6378137,6378137,6356752.31)),m.UNIT_SPHERE=Object.freeze(new m(1,1,1)),m.MOON=Object.freeze(new m(C.CesiumMath.LUNAR_RADIUS,C.CesiumMath.LUNAR_RADIUS,C.CesiumMath.LUNAR_RADIUS)),m.prototype.clone=function(e){return m.clone(this,e)},m.packedLength=s.Cartesian3.packedLength,m.pack=function(e,t,a){return p.Check.typeOf.object("value",e),p.Check.defined("array",t),a=l.defaultValue(a,0),s.Cartesian3.pack(e._radii,t,a),t},m.unpack=function(e,t,a){p.Check.defined("array",e),t=l.defaultValue(t,0);var n=s.Cartesian3.unpack(e,t);return m.fromCartesian3(n,a)},m.prototype.geocentricSurfaceNormal=s.Cartesian3.normalize,m.prototype.geodeticSurfaceNormalCartographic=function(e,t){p.Check.typeOf.object("cartographic",e);var a=e.longitude,n=e.latitude,r=Math.cos(n),i=r*Math.cos(a),o=r*Math.sin(a),u=Math.sin(n);return l.defined(t)||(t=new s.Cartesian3),t.x=i,t.y=o,t.z=u,s.Cartesian3.normalize(t,t)},m.prototype.geodeticSurfaceNormal=function(e,t){return l.defined(t)||(t=new s.Cartesian3),t=s.Cartesian3.multiplyComponents(e,this._oneOverRadiiSquared,t),s.Cartesian3.normalize(t,t)};var i=new s.Cartesian3,o=new s.Cartesian3;m.prototype.cartographicToCartesian=function(e,t){var a=i,n=o;this.geodeticSurfaceNormalCartographic(e,a),s.Cartesian3.multiplyComponents(this._radiiSquared,a,n);var r=Math.sqrt(s.Cartesian3.dot(a,n));return s.Cartesian3.divideByScalar(n,r,n),s.Cartesian3.multiplyByScalar(a,e.height,a),l.defined(t)||(t=new s.Cartesian3),s.Cartesian3.add(n,a,t)},m.prototype.cartographicArrayToCartesianArray=function(e,t){p.Check.defined("cartographics",e);var a=e.length;l.defined(t)?t.length=a:t=new Array(a);for(var n=0;n<a;n++)t[n]=this.cartographicToCartesian(e[n],t[n]);return t};var h=new s.Cartesian3,c=new s.Cartesian3,d=new s.Cartesian3;function y(e,t,a,n){this.west=l.defaultValue(e,0),this.south=l.defaultValue(t,0),this.east=l.defaultValue(a,0),this.north=l.defaultValue(n,0)}m.prototype.cartesianToCartographic=function(e,t){var a=this.scaleToGeodeticSurface(e,c);if(l.defined(a)){var n=this.geodeticSurfaceNormal(a,h),r=s.Cartesian3.subtract(e,a,d),i=Math.atan2(n.y,n.x),o=Math.asin(n.z),u=C.CesiumMath.sign(s.Cartesian3.dot(r,e))*s.Cartesian3.magnitude(r);return l.defined(t)?(t.longitude=i,t.latitude=o,t.height=u,t):new s.Cartographic(i,o,u)}},m.prototype.cartesianArrayToCartographicArray=function(e,t){p.Check.defined("cartesians",e);var a=e.length;l.defined(t)?t.length=a:t=new Array(a);for(var n=0;n<a;++n)t[n]=this.cartesianToCartographic(e[n],t[n]);return t},m.prototype.scaleToGeodeticSurface=function(e,t){return s.scaleToGeodeticSurface(e,this._oneOverRadii,this._oneOverRadiiSquared,this._centerToleranceSquared,t)},m.prototype.scaleToGeocentricSurface=function(e,t){p.Check.typeOf.object("cartesian",e),l.defined(t)||(t=new s.Cartesian3);var a=e.x,n=e.y,r=e.z,i=this._oneOverRadiiSquared,o=1/Math.sqrt(a*a*i.x+n*n*i.y+r*r*i.z);return s.Cartesian3.multiplyByScalar(e,o,t)},m.prototype.transformPositionToScaledSpace=function(e,t){return l.defined(t)||(t=new s.Cartesian3),s.Cartesian3.multiplyComponents(e,this._oneOverRadii,t)},m.prototype.transformPositionFromScaledSpace=function(e,t){return l.defined(t)||(t=new s.Cartesian3),s.Cartesian3.multiplyComponents(e,this._radii,t)},m.prototype.equals=function(e){return this===e||l.defined(e)&&s.Cartesian3.equals(this._radii,e._radii)},m.prototype.toString=function(){return this._radii.toString()},m.prototype.getSurfaceNormalIntersectionWithZAxis=function(e,t,a){if(p.Check.typeOf.object("position",e),!C.CesiumMath.equalsEpsilon(this._radii.x,this._radii.y,C.CesiumMath.EPSILON15))throw new p.DeveloperError("Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)");p.Check.typeOf.number.greaterThan("Ellipsoid.radii.z",this._radii.z,0),t=l.defaultValue(t,0);var n=this._squaredXOverSquaredZ;if(l.defined(a)||(a=new s.Cartesian3),a.x=0,a.y=0,a.z=e.z*(1-n),!(Math.abs(a.z)>=this._radii.z-t))return a},Object.defineProperties(y.prototype,{width:{get:function(){return y.computeWidth(this)}},height:{get:function(){return y.computeHeight(this)}}}),y.packedLength=4,y.pack=function(e,t,a){return p.Check.typeOf.object("value",e),p.Check.defined("array",t),a=l.defaultValue(a,0),t[a++]=e.west,t[a++]=e.south,t[a++]=e.east,t[a]=e.north,t},y.unpack=function(e,t,a){return p.Check.defined("array",e),t=l.defaultValue(t,0),l.defined(a)||(a=new y),a.west=e[t++],a.south=e[t++],a.east=e[t++],a.north=e[t],a},y.computeWidth=function(e){p.Check.typeOf.object("rectangle",e);var t=e.east,a=e.west;return t<a&&(t+=C.CesiumMath.TWO_PI),t-a},y.computeHeight=function(e){return p.Check.typeOf.object("rectangle",e),e.north-e.south},y.fromDegrees=function(e,t,a,n,r){return e=C.CesiumMath.toRadians(l.defaultValue(e,0)),t=C.CesiumMath.toRadians(l.defaultValue(t,0)),a=C.CesiumMath.toRadians(l.defaultValue(a,0)),n=C.CesiumMath.toRadians(l.defaultValue(n,0)),l.defined(r)?(r.west=e,r.south=t,r.east=a,r.north=n,r):new y(e,t,a,n)},y.fromRadians=function(e,t,a,n,r){return l.defined(r)?(r.west=l.defaultValue(e,0),r.south=l.defaultValue(t,0),r.east=l.defaultValue(a,0),r.north=l.defaultValue(n,0),r):new y(e,t,a,n)},y.fromCartographicArray=function(e,t){p.Check.defined("cartographics",e);for(var a=Number.MAX_VALUE,n=-Number.MAX_VALUE,r=Number.MAX_VALUE,i=-Number.MAX_VALUE,o=Number.MAX_VALUE,u=-Number.MAX_VALUE,s=0,h=e.length;s<h;s++){var c=e[s];a=Math.min(a,c.longitude),n=Math.max(n,c.longitude),o=Math.min(o,c.latitude),u=Math.max(u,c.latitude);var d=0<=c.longitude?c.longitude:c.longitude+C.CesiumMath.TWO_PI;r=Math.min(r,d),i=Math.max(i,d)}return i-r<n-a&&(a=r,(n=i)>C.CesiumMath.PI&&(n-=C.CesiumMath.TWO_PI),a>C.CesiumMath.PI&&(a-=C.CesiumMath.TWO_PI)),l.defined(t)?(t.west=a,t.south=o,t.east=n,t.north=u,t):new y(a,o,n,u)},y.fromCartesianArray=function(e,t,a){p.Check.defined("cartesians",e),t=l.defaultValue(t,m.WGS84);for(var n=Number.MAX_VALUE,r=-Number.MAX_VALUE,i=Number.MAX_VALUE,o=-Number.MAX_VALUE,u=Number.MAX_VALUE,s=-Number.MAX_VALUE,h=0,c=e.length;h<c;h++){var d=t.cartesianToCartographic(e[h]);n=Math.min(n,d.longitude),r=Math.max(r,d.longitude),u=Math.min(u,d.latitude),s=Math.max(s,d.latitude);var f=0<=d.longitude?d.longitude:d.longitude+C.CesiumMath.TWO_PI;i=Math.min(i,f),o=Math.max(o,f)}return o-i<r-n&&(n=i,(r=o)>C.CesiumMath.PI&&(r-=C.CesiumMath.TWO_PI),n>C.CesiumMath.PI&&(n-=C.CesiumMath.TWO_PI)),l.defined(a)?(a.west=n,a.south=u,a.east=r,a.north=s,a):new y(n,u,r,s)},y.clone=function(e,t){if(l.defined(e))return l.defined(t)?(t.west=e.west,t.south=e.south,t.east=e.east,t.north=e.north,t):new y(e.west,e.south,e.east,e.north)},y.equalsEpsilon=function(e,t,a){return p.Check.typeOf.number("absoluteEpsilon",a),e===t||l.defined(e)&&l.defined(t)&&Math.abs(e.west-t.west)<=a&&Math.abs(e.south-t.south)<=a&&Math.abs(e.east-t.east)<=a&&Math.abs(e.north-t.north)<=a},y.prototype.clone=function(e){return y.clone(this,e)},y.prototype.equals=function(e){return y.equals(this,e)},y.equals=function(e,t){return e===t||l.defined(e)&&l.defined(t)&&e.west===t.west&&e.south===t.south&&e.east===t.east&&e.north===t.north},y.prototype.equalsEpsilon=function(e,t){return p.Check.typeOf.number("epsilon",t),y.equalsEpsilon(this,e,t)},y.validate=function(e){p.Check.typeOf.object("rectangle",e);var t=e.north;p.Check.typeOf.number.greaterThanOrEquals("north",t,-C.CesiumMath.PI_OVER_TWO),p.Check.typeOf.number.lessThanOrEquals("north",t,C.CesiumMath.PI_OVER_TWO);var a=e.south;p.Check.typeOf.number.greaterThanOrEquals("south",a,-C.CesiumMath.PI_OVER_TWO),p.Check.typeOf.number.lessThanOrEquals("south",a,C.CesiumMath.PI_OVER_TWO);var n=e.west;p.Check.typeOf.number.greaterThanOrEquals("west",n,-Math.PI),p.Check.typeOf.number.lessThanOrEquals("west",n,Math.PI);var r=e.east;p.Check.typeOf.number.greaterThanOrEquals("east",r,-Math.PI),p.Check.typeOf.number.lessThanOrEquals("east",r,Math.PI)},y.southwest=function(e,t){return p.Check.typeOf.object("rectangle",e),l.defined(t)?(t.longitude=e.west,t.latitude=e.south,t.height=0,t):new s.Cartographic(e.west,e.south)},y.northwest=function(e,t){return p.Check.typeOf.object("rectangle",e),l.defined(t)?(t.longitude=e.west,t.latitude=e.north,t.height=0,t):new s.Cartographic(e.west,e.north)},y.northeast=function(e,t){return p.Check.typeOf.object("rectangle",e),l.defined(t)?(t.longitude=e.east,t.latitude=e.north,t.height=0,t):new s.Cartographic(e.east,e.north)},y.southeast=function(e,t){return p.Check.typeOf.object("rectangle",e),l.defined(t)?(t.longitude=e.east,t.latitude=e.south,t.height=0,t):new s.Cartographic(e.east,e.south)},y.center=function(e,t){p.Check.typeOf.object("rectangle",e);var a=e.east,n=e.west;a<n&&(a+=C.CesiumMath.TWO_PI);var r=C.CesiumMath.negativePiToPi(.5*(n+a)),i=.5*(e.south+e.north);return l.defined(t)?(t.longitude=r,t.latitude=i,t.height=0,t):new s.Cartographic(r,i)},y.intersection=function(e,t,a){p.Check.typeOf.object("rectangle",e),p.Check.typeOf.object("otherRectangle",t);var n=e.east,r=e.west,i=t.east,o=t.west;n<r&&0<i?n+=C.CesiumMath.TWO_PI:i<o&&0<n&&(i+=C.CesiumMath.TWO_PI),n<r&&o<0?o+=C.CesiumMath.TWO_PI:i<o&&r<0&&(r+=C.CesiumMath.TWO_PI);var u=C.CesiumMath.negativePiToPi(Math.max(r,o)),s=C.CesiumMath.negativePiToPi(Math.min(n,i));if(!((e.west<e.east||t.west<t.east)&&s<=u)){var h=Math.max(e.south,t.south),c=Math.min(e.north,t.north);if(!(c<=h))return l.defined(a)?(a.west=u,a.south=h,a.east=s,a.north=c,a):new y(u,h,s,c)}},y.simpleIntersection=function(e,t,a){p.Check.typeOf.object("rectangle",e),p.Check.typeOf.object("otherRectangle",t);var n=Math.max(e.west,t.west),r=Math.max(e.south,t.south),i=Math.min(e.east,t.east),o=Math.min(e.north,t.north);if(!(o<=r||i<=n))return l.defined(a)?(a.west=n,a.south=r,a.east=i,a.north=o,a):new y(n,r,i,o)},y.union=function(e,t,a){p.Check.typeOf.object("rectangle",e),p.Check.typeOf.object("otherRectangle",t),l.defined(a)||(a=new y);var n=e.east,r=e.west,i=t.east,o=t.west;n<r&&0<i?n+=C.CesiumMath.TWO_PI:i<o&&0<n&&(i+=C.CesiumMath.TWO_PI),n<r&&o<0?o+=C.CesiumMath.TWO_PI:i<o&&r<0&&(r+=C.CesiumMath.TWO_PI);var u=C.CesiumMath.convertLongitudeRange(Math.min(r,o)),s=C.CesiumMath.convertLongitudeRange(Math.max(n,i));return a.west=u,a.south=Math.min(e.south,t.south),a.east=s,a.north=Math.max(e.north,t.north),a},y.expand=function(e,t,a){return p.Check.typeOf.object("rectangle",e),p.Check.typeOf.object("cartographic",t),l.defined(a)||(a=new y),a.west=Math.min(e.west,t.longitude),a.south=Math.min(e.south,t.latitude),a.east=Math.max(e.east,t.longitude),a.north=Math.max(e.north,t.latitude),a},y.contains=function(e,t){p.Check.typeOf.object("rectangle",e),p.Check.typeOf.object("cartographic",t);var a=t.longitude,n=t.latitude,r=e.west,i=e.east;return i<r&&(i+=C.CesiumMath.TWO_PI,a<0&&(a+=C.CesiumMath.TWO_PI)),(r<a||C.CesiumMath.equalsEpsilon(a,r,C.CesiumMath.EPSILON14))&&(a<i||C.CesiumMath.equalsEpsilon(a,i,C.CesiumMath.EPSILON14))&&n>=e.south&&n<=e.north};var f=new s.Cartographic;y.subsample=function(e,t,a,n){p.Check.typeOf.object("rectangle",e),t=l.defaultValue(t,m.WGS84),a=l.defaultValue(a,0),l.defined(n)||(n=[]);var r=0,i=e.north,o=e.south,u=e.east,s=e.west,h=f;h.height=a,h.longitude=s,h.latitude=i,n[r]=t.cartographicToCartesian(h,n[r]),r++,h.longitude=u,n[r]=t.cartographicToCartesian(h,n[r]),r++,h.latitude=o,n[r]=t.cartographicToCartesian(h,n[r]),r++,h.longitude=s,n[r]=t.cartographicToCartesian(h,n[r]),r++,h.latitude=i<0?i:0<o?o:0;for(var c=1;c<8;++c)h.longitude=-Math.PI+c*C.CesiumMath.PI_OVER_TWO,y.contains(e,h)&&(n[r]=t.cartographicToCartesian(h,n[r]),r++);return 0===h.latitude&&(h.longitude=s,n[r]=t.cartographicToCartesian(h,n[r]),r++,h.longitude=u,n[r]=t.cartographicToCartesian(h,n[r]),r++),n.length=r,n};var t=new s.Cartographic;function u(e,t){this.x=l.defaultValue(e,0),this.y=l.defaultValue(t,0)}y.prototype.contains=function(e){return y.contains(this,y.southwest(e,t))&&y.contains(this,y.northwest(e,t))&&y.contains(this,y.southeast(e,t))&&y.contains(this,y.northeast(e,t))},y.MAX_VALUE=Object.freeze(new y(-Math.PI,-C.CesiumMath.PI_OVER_TWO,Math.PI,C.CesiumMath.PI_OVER_TWO)),u.fromElements=function(e,t,a){return l.defined(a)?(a.x=e,a.y=t,a):new u(e,t)},u.fromCartesian3=u.clone=function(e,t){if(l.defined(e))return l.defined(t)?(t.x=e.x,t.y=e.y,t):new u(e.x,e.y)},u.fromCartesian4=u.clone,u.packedLength=2,u.pack=function(e,t,a){return p.Check.typeOf.object("value",e),p.Check.defined("array",t),a=l.defaultValue(a,0),t[a++]=e.x,t[a]=e.y,t},u.unpack=function(e,t,a){return p.Check.defined("array",e),t=l.defaultValue(t,0),l.defined(a)||(a=new u),a.x=e[t++],a.y=e[t],a},u.packArray=function(e,t){p.Check.defined("array",e);var a=e.length,n=2*a;if(l.defined(t)){if(!Array.isArray(t)&&t.length!==n)throw new p.DeveloperError("If result is a typed array, it must have exactly array.length * 2 elements");t.length!==n&&(t.length=n)}else t=new Array(n);for(var r=0;r<a;++r)u.pack(e[r],t,2*r);return t},u.unpackArray=function(e,t){if(p.Check.defined("array",e),p.Check.typeOf.number.greaterThanOrEquals("array.length",e.length,2),e.length%2!=0)throw new p.DeveloperError("array length must be a multiple of 2.");var a=e.length;l.defined(t)?t.length=a/2:t=new Array(a/2);for(var n=0;n<a;n+=2){var r=n/2;t[r]=u.unpack(e,n,t[r])}return t},u.fromArray=u.unpack,u.maximumComponent=function(e){return p.Check.typeOf.object("cartesian",e),Math.max(e.x,e.y)},u.minimumComponent=function(e){return p.Check.typeOf.object("cartesian",e),Math.min(e.x,e.y)},u.minimumByComponent=function(e,t,a){return p.Check.typeOf.object("first",e),p.Check.typeOf.object("second",t),p.Check.typeOf.object("result",a),a.x=Math.min(e.x,t.x),a.y=Math.min(e.y,t.y),a},u.maximumByComponent=function(e,t,a){return p.Check.typeOf.object("first",e),p.Check.typeOf.object("second",t),p.Check.typeOf.object("result",a),a.x=Math.max(e.x,t.x),a.y=Math.max(e.y,t.y),a},u.magnitudeSquared=function(e){return p.Check.typeOf.object("cartesian",e),e.x*e.x+e.y*e.y},u.magnitude=function(e){return Math.sqrt(u.magnitudeSquared(e))};var a=new u;u.distance=function(e,t){return p.Check.typeOf.object("left",e),p.Check.typeOf.object("right",t),u.subtract(e,t,a),u.magnitude(a)},u.distanceSquared=function(e,t){return p.Check.typeOf.object("left",e),p.Check.typeOf.object("right",t),u.subtract(e,t,a),u.magnitudeSquared(a)},u.normalize=function(e,t){p.Check.typeOf.object("cartesian",e),p.Check.typeOf.object("result",t);var a=u.magnitude(e);if(t.x=e.x/a,t.y=e.y/a,isNaN(t.x)||isNaN(t.y))throw new p.DeveloperError("normalized result is not a number");return t},u.dot=function(e,t){return p.Check.typeOf.object("left",e),p.Check.typeOf.object("right",t),e.x*t.x+e.y*t.y},u.multiplyComponents=function(e,t,a){return p.Check.typeOf.object("left",e),p.Check.typeOf.object("right",t),p.Check.typeOf.object("result",a),a.x=e.x*t.x,a.y=e.y*t.y,a},u.divideComponents=function(e,t,a){return p.Check.typeOf.object("left",e),p.Check.typeOf.object("right",t),p.Check.typeOf.object("result",a),a.x=e.x/t.x,a.y=e.y/t.y,a},u.add=function(e,t,a){return p.Check.typeOf.object("left",e),p.Check.typeOf.object("right",t),p.Check.typeOf.object("result",a),a.x=e.x+t.x,a.y=e.y+t.y,a},u.subtract=function(e,t,a){return p.Check.typeOf.object("left",e),p.Check.typeOf.object("right",t),p.Check.typeOf.object("result",a),a.x=e.x-t.x,a.y=e.y-t.y,a},u.multiplyByScalar=function(e,t,a){return p.Check.typeOf.object("cartesian",e),p.Check.typeOf.number("scalar",t),p.Check.typeOf.object("result",a),a.x=e.x*t,a.y=e.y*t,a},u.divideByScalar=function(e,t,a){return p.Check.typeOf.object("cartesian",e),p.Check.typeOf.number("scalar",t),p.Check.typeOf.object("result",a),a.x=e.x/t,a.y=e.y/t,a},u.negate=function(e,t){return p.Check.typeOf.object("cartesian",e),p.Check.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t},u.abs=function(e,t){return p.Check.typeOf.object("cartesian",e),p.Check.typeOf.object("result",t),t.x=Math.abs(e.x),t.y=Math.abs(e.y),t};var r=new u;u.lerp=function(e,t,a,n){return p.Check.typeOf.object("start",e),p.Check.typeOf.object("end",t),p.Check.typeOf.number("t",a),p.Check.typeOf.object("result",n),u.multiplyByScalar(t,a,r),n=u.multiplyByScalar(e,1-a,n),u.add(r,n,n)};var O=new u,g=new u;u.angleBetween=function(e,t){return p.Check.typeOf.object("left",e),p.Check.typeOf.object("right",t),u.normalize(e,O),u.normalize(t,g),C.CesiumMath.acosClamped(u.dot(O,g))};var b=new u;u.mostOrthogonalAxis=function(e,t){p.Check.typeOf.object("cartesian",e),p.Check.typeOf.object("result",t);var a=u.normalize(e,b);return u.abs(a,a),t=a.x<=a.y?u.clone(u.UNIT_X,t):u.clone(u.UNIT_Y,t)},u.equals=function(e,t){return e===t||l.defined(e)&&l.defined(t)&&e.x===t.x&&e.y===t.y},u.equalsArray=function(e,t,a){return e.x===t[a]&&e.y===t[a+1]},u.equalsEpsilon=function(e,t,a,n){return e===t||l.defined(e)&&l.defined(t)&&C.CesiumMath.equalsEpsilon(e.x,t.x,a,n)&&C.CesiumMath.equalsEpsilon(e.y,t.y,a,n)},u.ZERO=Object.freeze(new u(0,0)),u.UNIT_X=Object.freeze(new u(1,0)),u.UNIT_Y=Object.freeze(new u(0,1)),u.prototype.clone=function(e){return u.clone(this,e)},u.prototype.equals=function(e){return u.equals(this,e)},u.prototype.equalsEpsilon=function(e,t,a){return u.equalsEpsilon(this,e,t,a)},u.prototype.toString=function(){return"("+this.x+", "+this.y+")"},e.Cartesian2=u,e.Ellipsoid=m,e.Rectangle=y});