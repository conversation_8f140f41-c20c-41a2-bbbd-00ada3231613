export function formattedTime(date) {
    if (date) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
    return '';
}

export function calculateDuration(row) {
    // 将时间字符串转换为Date对象
    
    const start = new Date(row.occurTime);
    const end = row.endTime?new Date(row.endTime):new Date();

    // 计算两个时间之间的毫秒差
    const diff = end - start;

    // 将毫秒差转换为分钟
    const diffInMinutes = Math.round(diff / (1000 * 60));

    return diffInMinutes;
}