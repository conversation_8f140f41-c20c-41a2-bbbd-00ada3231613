import request from "@/utils/request";

// 导出计划（设备台账）
export function exportPlan(data) {
  return request({
    url: "/equip/ledger/exportExcel",
    method: "post",
    data,
    responseType: "blob",
  });
}

// 导出计划（设备维修台账）
export function exportRepair(data) {
  return request({
    url: "/equip/deviceRepairInfo/exportExcel",
    method: "post",
    data,
    responseType: "blob",
  });
}

// 导出计划（设备保养台账）
export function exportProtect(data) {
  return request({
    url: "/equip/deviceProtectInfo/exportExcel",
    method: "post",
    data,
    responseType: "blob",
  });
}


// 导出计划（感知设备接入列表）
export function exportExcel(data) {
  return request({
    url: `/equip/battery/exportExcel?keyword=${data.keyword}`,
    method: "post",
    data: {
      sectionId: data.sectionId
    },
    responseType: "blob",
  });
}

// 导出计划（充电记录）
export function exportRecordExcel(data) {
  return request({
    url: `/equip/chargeRecord/exportExcel?sectionId=${data.sectionId}&batId=${data.batId}`,
    data: {
      sectionId: data.sectionId,
      batId: data.batId
    },
    method: "post",
    responseType: "blob",
  });
}

// 导出计划（换电记录）
export function exportChangeElectricityRecordExcel(data) {
  return request({
    url: `/equip/changeElectricityRecord/exportExcel`,
    method: "post",
    data,
    responseType: "blob",
  });
}

// 导出计划（充电机故障）
export function exportChargeFaultRecordExcel(data) {
  return request({
    url: `/equip/chargeFault/exportExcel`,
    method: "post",
    data,
    responseType: "blob",
  });
}
// 导出计划（换电设备故障）
export function exportExchangeFaultRecordExcel(data) {
  return request({
    url: `/equip/changeElectricityFault/exportExcel`,
    method: "post",
    data,
    responseType: "blob",
  });
}