<template>
  <div id="work-container"></div>
</template>

<script>
import { pxToNum } from "@/utils/pxToVw";
export default {
  name: "WorkHour",
  props: ["dropdownValue"],
  data() {
    return {
      xAxis: ["电动挖掘机", "电动装载机", "电动自卸车", "电动搅拌车"],//设备名称
      totalTime: [50, 120, 150, 200],
      averageTime: [50, 100, 150, 100],
      keyword: 1,
    };
  },
  watch: {
    dropdownValue: {
      handler() {
        this.keyword = this.dropdownValue;
        this.getWorkHour();
      },
      deep: true,
    },
    "$store.state.tree.sectionId": {
      handler: function () {
        this.getWorkHour();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},

  methods: {
    async getWorkHour() {
      let deviceName = [];
      let total = [];
      let avgDay = [];
      let res = await this.$http(
        `/equip/build/workHourSummary?sectionId=${this.$store.state.tree.sectionId||''}&keyword=${this.keyword}`
      );
      res?.result.map((i) => {
        deviceName.push(i.deviceName);
        total.push(i.total);
        avgDay.push(i.avgDay);
      });
      //this.xAxis = deviceName;
      this.totalTime = total;
      this.averageTime = avgDay;
      this.setChart();
    },
    // 初始化
    setChart() {
      let _ = this;
      let myChart = _.$echarts.init(
        document.getElementById("work-container"),
        null,
        {
          renderer: "svg",
        }
      );
      myChart.setOption({
        color: ["#65C1DD", "#60BF79"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
          },
        },
        legend: {
          data: ["累计时长", "日均时长"],
          left: "right",
          textStyle: {
            fontSize: pxToNum(14),
            color: "#000",
          },
        },
        grid: {
          left: "1%",
          right: "0%",
          bottom: "0%",
          top: 30,
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.xAxis,
            axisPointer: {
              type: "shadow",
            },
            axisLabel: {
              fontSize: pxToNum(12),
              interval: 0,     // 强制显示所有标签
            },
          },
        ],
        yAxis: {
          type: "value",
          name: "单位/小时",
          nameTextStyle: {
            fontSize: pxToNum(12),
          },
        },
        series: [
          {
            name: "累计时长",
            type: "bar",
            tooltip: {
              valueFormatter: function (value) {
                return value + " ml";
              },
            },
            barWidth: "30%",
            data: this.totalTime,
          },
          {
            name: "日均时长",
            type: "line",
            tooltip: {
              valueFormatter: function (value) {
                console.log("value", value);
                return value + " °C";
              },
            },
            data: this.averageTime,
          },
        ],
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
#work-container {
  width: 370px;
  height: 100%;
}
</style>