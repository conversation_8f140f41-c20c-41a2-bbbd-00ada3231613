<template>
  <div id="Zhtj">
    <div class="container-left">
      <card :title="titleData.car" class="card-preview">
        <car />
      </card>
      <card :title="titleData.electricity" class="card-type card-item">
        
        <electricity />
      </card>
    </div>
    <div class="container-right">
      <card :title="titleData.insert" class="card-preview">
        <inset />
      </card>
      <card :title="titleData.type" class="card-type card-item">
        <chart />
      </card>
      <card :title="titleData.brand" class="card-brand card-item">
        <brand />
      </card>
    </div>
  </div>
</template>

<script>
import card from "./components/card/card.vue";
import car from "./components/car/car.vue";
import electricity from "./components/electricity/electricity.vue";
import inset from "./components/inset/inset.vue";
import chart from "./components/chart/chart.vue";
import brand from "./components/brand/brand.vue";
export default {
  name: "Zhtj",
  components: { card, car, electricity, inset, chart,brand },
  data() {
    return {
      titleData: {
        car: "车辆统计",
        electricity: "电量统计",
        insert: "接入情况",
        type: "机械类型",
        brand: "品牌厂家",
      },
    };
  },
  watch: {},
  methods: {},
};
</script>

<style lang="scss" scoped>
#Zhtj {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  height: 87vh;
  width: 100%;
  .container-left {
    height: 100%;
    flex: 1;
    margin: 0 10px;

    .card-preview {
      width: 100%;
      height: 48%;
    }

    .card-type {
      width: 100%;
      height: 49%;
    }
  }

  .container-right {
    width: 480px;
    height: 100%;

    .card-preview {
      width: 100%;
      height: 26%;
    }

    .card-type {
      width: 100%;
      height: 35%;
    }

    .card-brand {
      width: 100%;
      height: 35%;
    }
  }

  .card-item {
    margin-top: 10px;
  }
}
</style>
