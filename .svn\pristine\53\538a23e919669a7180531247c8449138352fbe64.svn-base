<template>
  <div id="Sbtzgl">
    <el-card class="card-content">
      <div class="content-option">
        <!-- <div class="content-title">设备台账管理</div> -->
        <el-form :inline="true" :model="formInline" ref="formInline" class="form-view">
          <el-form-item label="标段">
            <el-input placeholder="请输入标段" v-model="formInline.sectionName" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item>
          <el-form-item label="工点">
            <el-input placeholder="请输入工点" v-model="formInline.projectSite" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item>
          <el-form-item label="设备信息">
            <el-input placeholder="请输入设备关键词" v-model="formInline.deviceName" clearable
              prefix-icon="el-icon-search"></el-input>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-plus" type="primary" @click="handleOperate(null)">新增台账</el-button>
            <!-- <el-button icon="el-icon-upload2" @click="handleImport">导入数据</el-button> -->
            <el-button icon="el-icon-download" @click="handleExport" :loading="loading">导出数据</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="content-table">
        <el-table ref="table" :data="tableData" style="width: 100%" border :header-cell-style="headerCellStyle"
          :cell-style="cellStyle">
          <el-table-column type="index" width="50" label="序号" align="center" />
          <el-table-column prop="sectionName" min-width="150" label="标段" align="center" />
          <!-- <el-table-column prop="siteName" min-width="150" label="工点" align="center" /> -->
          <!-- <el-table-column prop="projectSite" label="工点" align="center" min-width="150" /> -->
          <el-table-column prop="deviceName" label="设备名称" align="center" min-width="150" />
          <el-table-column label="型号规格" align="center" min-width="150" prop="specificationModel">
          </el-table-column>
          <el-table-column prop="factoryNum" label="出厂编号" align="center" min-width="180" />
          <el-table-column prop="managementNumber" label="管理编号" align="center" min-width="180" />
          <el-table-column prop="carNum" label="车牌号码" align="center" min-width="130" />
          <el-table-column prop="dynamicType" label="动力类型" align="center" min-width="130" />
          <el-table-column prop="energyType" label="补能类型" align="center" min-width="130" />
          <el-table-column prop="deviceNum" label="设备编号" align="center" min-width="130" />
          <el-table-column prop="collecterId" label="采集盒子ID" align="center" min-width="130" />
          <el-table-column prop="carBrand" label="车辆品牌" align="center" min-width="130" />
          <el-table-column label="机械类型" align="center" min-width="130">
            <template slot-scope="scope">
              <span v-if="scope.row.deviceType == '0'">充电挖掘机</span>
              <span v-else-if="scope.row.deviceType == '1'">换电挖掘机</span>
              <span v-else-if="scope.row.deviceType == '2'">充电装载机</span>
              <span v-else-if="scope.row.deviceType == '3'">换电装载机</span>
              <span v-else-if="scope.row.deviceType == '4'">充电自卸车</span>
              <span v-else-if="scope.row.deviceType == '5'">换电自卸车</span>
              <span v-else-if="scope.row.deviceType == '6'">充电混泥土车</span>
              <span v-else-if="scope.row.deviceType == '7'">换电混泥土车</span>
            </template>
          </el-table-column>
          <el-table-column prop="manufacturer" label="制造厂家" align="center" min-width="220" />
          <el-table-column prop="inspectionReport" label="型式检验报告" align="center" min-width="110" />
          <el-table-column prop="entryAcceptance" label="进场验收情况" align="center" min-width="130" />
          
          <el-table-column prop="entryTime" label="进场时间" align="center" min-width="130" />
          <el-table-column prop="exitTime" label="退场时间" align="center" min-width="130" />
          <el-table-column prop="inspectionDate" label="设备检验日期" align="center" min-width="150" />
          <el-table-column prop="operationPerson" label="作业人员" align="center" min-width="150" />
          <el-table-column prop="remark" label="备注" align="center" min-width="150" />
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button @click="handleOperate(scope.row)" type="text" size="small" class="table-edit">编辑</el-button>
              <el-popconfirm title="确定删除吗？" @confirm="handleDelete(scope.row.id, '/equip/ledger/deleteById')">
                <el-button slot="reference" type="text" size="small"> 删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
          :total="total">
        </el-pagination>
      </div>
    </el-card>
    <AddLedgerDialog v-if="dialogVisible" :curData="curData" :dialogVisible="dialogVisible" @onClose="onClose"
      @onSave="onSave" />
    <ImportDialog v-if="importvisible" :importvisible="importvisible" @onClose="handleClose" />
  </div>
</template>

<script>
import tableUtils from '@/mixins/tableUtils';
import AddLedgerDialog from './components/Dialog/AddLedgerDialog.vue';
import ImportDialog from './components/ImportDialog/ImportDialog.vue';
import { exportPlan } from '@/api/baseApi/common';
export default {
  name: 'Sbtzgl',
  mixins: [tableUtils],
  components: { AddLedgerDialog, ImportDialog },
  data() {
    return {
      formInline: {},
      dialogVisible: false,
      curData: null,
      tableData: [],
      importvisible: false,
      loading: false,
    }
  },
  watch: {
    "$store.state.tree.sectionId": {
      
      
      handler: function () {
        this.requestList();
        
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    async requestList() {
      let params = {
        ...this.formInline,
        sectionId: this.$store.state.tree.sectionId
      };
      let res = await this.$http.post(`/equip/ledger/page?page=${this.pageParams.page}&limit=${this.pageParams.limit}`, params);
      this.tableData = res.result?.records;
      this.total = res.result?.total;
    },

    async handleExport() {
      this.loading = true;
      let params = {
        ...this.formInline,
        sectionId: this.$store.state.tree.sectionId
      };
      let res = await exportPlan(params);
      var list = new Blob([res], {
        type: "application/vnd.ms-excel;charset=utf-8",
      });
      var downloadUrl = window.URL.createObjectURL(list);
      var anchor = document.createElement("a");
      anchor.href = downloadUrl;
      anchor.download = '设备台账管理表.xlsx';
      anchor.click();
      window.URL.revokeObjectURL(list);
      this.loading = false;
    },

    handleImport() {
      this.importvisible = true;
    },

    handleOperate(item) {
      if (item) {
        this.curData = { data: { ...item }, type: 2 }
      } else {
        this.curData = { data: {}, type: 1 }
      }
      this.dialogVisible = true
    },

    onClose() {
      this.dialogVisible = false;
    },

    handleClose() {
      this.requestList();
      this.importvisible = false;
    },

    onSave() {
      this.requestList();
      this.dialogVisible = false;
    }
  }
}
</script>

<style lang="scss" scoped>
#Sbtzgl {
  .content-option {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .content-title {
      line-height: 40px;
      font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
      font-weight: 700;
      font-style: normal;
      font-size: 18px;
      color: #3A3F5D;
      margin-right: 5%;
    }

    .form-view {
      flex: 1;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }

    ::v-deep .el-input {
      width: 175px;
    }
  }

  .table-edit {
    margin-right: 10px;
  }

  .pagination-part {
    margin-top: 20px;
  }
}
</style>