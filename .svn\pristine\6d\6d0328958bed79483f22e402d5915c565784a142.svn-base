<template>
    <div class="overview-wrapper">
        <div class="overview-img">
            <img src="@/assets/images/zhzaiji2.png" alt="充电装载机" class="img-bg"
                v-if="this.deviceInfo.deviceType == '2' || this.deviceInfo.deviceType =='3'">
            <img src="@/assets/images/zixieche2.png" alt="自卸车" class="img-bg"
                v-else-if="this.deviceInfo.deviceType == '4' || this.deviceInfo.deviceType =='5'">
            <img src="@/assets/images/hunningtu2.png" alt="混凝土罐车" class="img-bg"
                v-else-if="this.deviceInfo.deviceType == '6' || this.deviceInfo.deviceType =='7'">
            <img src="@/assets/images/waji2.png" alt="充电挖机" class="img-bg" v-else>
        </div>
        <div class="overview-info">
            <!-- <div class="info-item">
                <span>设备类型:</span>
                <span class="item">{{ this.deviceInfo.deviceType || '' }}</span>
                <span class="item" v-if="this.deviceInfo.deviceType == '1'">换电挖掘机</span>
                <span class="item" v-else-if="this.deviceInfo.deviceType == '2'">充电装载机</span>
                <span class="item" v-else-if="this.deviceInfo.deviceType == '3'">换电装载机</span>
                <span class="item" v-else-if="this.deviceInfo.deviceType == '4'">充电自卸车</span>
                <span class="item" v-else-if="this.deviceInfo.deviceType == '5'">换电自卸车</span>
                <span class="item" v-else-if="this.deviceInfo.deviceType == '6'">充电混泥土车</span>
                <span class="item" v-else-if="this.deviceInfo.deviceType == '7'">换电混泥土车</span>
                <span class="item" v-else>充电挖掘机</span>
            </div> -->
            <div class="info-item">
                <span>设备名称:</span>
                <span class="item">{{ this.deviceInfo.deviceName || '' }}</span>
            </div>
            <div class="info-item">
                <span>车架号:</span>
                <span class="item">{{ this.deviceInfo.vehicleIdentify || '' }}</span>
            </div>
            <div class="info-item">
                <span>设备品牌:</span>
                <span class="item">{{ this.deviceInfo.carBrand || '' }}</span>
            </div>
            <div class="info-item">
                <span>充电状态:</span>
                <span class="item">{{ this.deviceInfo.chargeStatus  || '' }}</span>
            </div>
            <div class="info-item">
                <span>电池类型:</span>
                <span class="item">{{ this.deviceInfo.batteryType  || '' }}</span>
            </div>
            <!-- <div class="info-item">
                <span>工作状态:</span>
                <span class="item">{{ this.deviceInfo.workStatus  || '' }}</span>
            </div> -->

        </div>
    </div>
</template>

<script>
export default {
    name: 'MechanicalOverview',
    props: ['deviceInfo'],
    data() {
        return {
        }
    },
    mounted() {
    },

    methods: {

    }
}
</script>

<style lang="scss" scoped>
.overview-wrapper {
    width: 100%;
    height: 100%;
    // padding: 15px 10px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .overview-img {
        width: 50%;
        height: 100%;

        .img-bg {
            width: 100%;
            height: 100%;
            background-size: contain;
        }
    }

    .overview-info {
        flex: 1;
        margin-left: 15px;
        height: 100%;
        width: 48%;

        .info-item {
            width: 100%;
            height: 20%;
            line-height: 200%;
            font-family: '微软雅黑', sans-serif;
            font-weight: 400;
            font-style: normal;
            font-size: 16px;
            overflow: hidden; //（文字长度超出限定宽度，则隐藏超出的内容）
            white-space: nowrap; //（设置文字在一行显示，不能换行）
            text-overflow: ellipsis; //（规定当文本溢出时，显示省略符号来代表被修剪的文本）

            .item {
                margin-left: 10px;
            }
        }
    }
}
</style>