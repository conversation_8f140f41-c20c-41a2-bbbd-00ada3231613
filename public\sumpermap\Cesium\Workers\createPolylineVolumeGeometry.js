/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./GeometryAttribute-2243653a","./PrimitiveType-97893bc7","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./GeometryAttributes-aacecde6","./AttributeCompression-75ce15eb","./GeometryPipeline-8e55e413","./EncodedCartesian3-87cd0c1f","./IndexDatatype-9435b55f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./VertexFormat-fe4db402","./arrayRemoveDuplicates-2869246d","./BoundingRectangle-3d4f3d01","./EllipsoidTangentPlane-9c25b2da","./EllipsoidRhumbLine-6ca4b1e6","./earcut-2.2.1-b404d9e6","./PolygonPipeline-cc78b34e","./PolylineVolumeGeometryLibrary-ac3b176f","./EllipsoidGeodesic-db2069b3","./PolylinePipeline-65700d85"],function(x,u,i,y,m,R,e,t,n,O,S,B,r,a,q,I,o,N,l,U,s,p,g,d,c,f,h,b,W,v,C,P){function E(e){var t=(e=x.defaultValue(e,x.defaultValue.EMPTY_OBJECT)).polylinePositions,n=e.shapePositions;if(!x.defined(t))throw new u.DeveloperError("options.polylinePositions is required.");if(!x.defined(n))throw new u.DeveloperError("options.shapePositions is required.");this._positions=t,this._shape=n,this._ellipsoid=m.Ellipsoid.clone(x.defaultValue(e.ellipsoid,m.Ellipsoid.WGS84)),this._cornerType=x.defaultValue(e.cornerType,v.CornerType.ROUNDED),this._vertexFormat=g.VertexFormat.clone(x.defaultValue(e.vertexFormat,g.VertexFormat.DEFAULT)),this._granularity=x.defaultValue(e.granularity,i.CesiumMath.RADIANS_PER_DEGREE),this._workerName="createPolylineVolumeGeometry",this.enuCenter=x.defaultValue(e.enuCenter,y.Cartesian3.ZERO);var r=1+t.length*y.Cartesian3.packedLength;r+=1+n.length*m.Cartesian2.packedLength+y.Cartesian3.packedLength,this.packedLength=r+m.Ellipsoid.packedLength+g.VertexFormat.packedLength+2}E.pack=function(e,t,n){if(!x.defined(e))throw new u.DeveloperError("value is required");if(!x.defined(t))throw new u.DeveloperError("array is required");var r;n=x.defaultValue(n,0);var i=e._positions,a=i.length;for(t[n++]=a,r=0;r<a;++r,n+=y.Cartesian3.packedLength)y.Cartesian3.pack(i[r],t,n);var o=e._shape;for(a=o.length,t[n++]=a,r=0;r<a;++r,n+=m.Cartesian2.packedLength)m.Cartesian2.pack(o[r],t,n);return m.Ellipsoid.pack(e._ellipsoid,t,n),n+=m.Ellipsoid.packedLength,g.VertexFormat.pack(e._vertexFormat,t,n),n+=g.VertexFormat.packedLength,t[n++]=e._cornerType,t[n++]=e._granularity,y.Cartesian3.pack(e.enuCenter,t,n),t};var _=m.Ellipsoid.clone(m.Ellipsoid.UNIT_SPHERE),k=new g.VertexFormat,L={polylinePositions:void 0,shapePositions:void 0,ellipsoid:_,vertexFormat:k,cornerType:void 0,granularity:void 0,enuCenter:void 0};E.unpack=function(e,t,n){if(!x.defined(e))throw new u.DeveloperError("array is required");var r;t=x.defaultValue(t,0);var i=e[t++],a=new Array(i);for(r=0;r<i;++r,t+=y.Cartesian3.packedLength)a[r]=y.Cartesian3.unpack(e,t);i=e[t++];var o=new Array(i);for(r=0;r<i;++r,t+=m.Cartesian2.packedLength)o[r]=m.Cartesian2.unpack(e,t);var l=m.Ellipsoid.unpack(e,t,_);t+=m.Ellipsoid.packedLength;var s=g.VertexFormat.unpack(e,t,k);t+=g.VertexFormat.packedLength;var p,d=e[t++],c=e[t++];return p=y.Cartesian3.unpack(e,t),x.defined(n)?(n._positions=a,n._shape=o,n._ellipsoid=m.Ellipsoid.clone(l,n._ellipsoid),n._vertexFormat=g.VertexFormat.clone(s,n._vertexFormat),n._cornerType=d,n._granularity=c,n.enuCenter=p,n):(L.polylinePositions=a,L.shapePositions=o,L.cornerType=d,L.granularity=c,L.enuCenter=p,new E(L))};var w=new c.BoundingRectangle;return E.createGeometry=function(e){for(var t=e._positions,n=d.arrayRemoveDuplicates(t,y.Cartesian3.equalsEpsilon),r=n.length,i=new Array(r),a=0;a<r;a++)i[a]=y.Cartesian3.clone(n[a]);var o=e._shape;if(o=v.PolylineVolumeGeometryLibrary.removeDuplicatesFromShape(o),!(n.length<2||o.length<3)){W.PolygonPipeline.computeWindingOrder2D(o)===W.WindingOrder.CLOCKWISE&&o.reverse();var l=c.BoundingRectangle.fromPoints(o,w),s={};if(s.combinedPositions=v.PolylineVolumeGeometryLibrary.computePositions(i,o,l,e,!0),!y.Cartesian3.equals(e.enuCenter,y.Cartesian3.ZERO)){var p=new Array(r);for(a=0;a<r;a++)p[a]=y.Cartesian3.clone(n[a]);s.combinedLocalPositions=v.PolylineVolumeGeometryLibrary.computeLocalPositions(p,o,l,e,!0,e.enuCenter)}return function(e,t,n,r){var i=e.combinedPositions,a=e.combinedLocalPositions,o=new I.GeometryAttributes;r.position&&(o.position=new S.GeometryAttribute({componentDatatype:O.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:i}));var l,s,p,d,c,u,y=t.length,m=i.length/3,g=(m-2*y)/(2*y),f=W.PolygonPipeline.triangulate(t),h=(g-1)*y*6+2*f.length,b=U.IndexDatatype.createTypedArray(m,h),v=2*y,C=0;for(l=0;l<g-1;l++){for(s=0;s<y-1;s++)u=(p=2*s+l*y*2)+v,c=(d=p+1)+v,b[C++]=d,b[C++]=p,b[C++]=c,b[C++]=c,b[C++]=p,b[C++]=u;c=(d=1+(p=2*y-2+l*y*2))+v,u=p+v,b[C++]=d,b[C++]=p,b[C++]=c,b[C++]=c,b[C++]=p,b[C++]=u}if(r.st||r.tangent||r.bitangent){var P,E,_=new Float32Array(2*m),k=1/(g-1),L=1/n.height,w=n.height/2,D=0;for(l=0;l<g;l++){for(P=l*k,E=L*(t[0].y+w),_[D++]=P,_[D++]=E,s=1;s<y;s++)E=L*(t[s].y+w),_[D++]=P,_[D++]=E,_[D++]=P,_[D++]=E;E=L*(t[0].y+w),_[D++]=P,_[D++]=E}for(s=0;s<y;s++)P=0,E=L*(t[s].y+w),_[D++]=P,_[D++]=E;for(s=0;s<y;s++)P=(g-1)*k,E=L*(t[s].y+w),_[D++]=P,_[D++]=E;o.st=new S.GeometryAttribute({componentDatatype:O.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:new Float32Array(_)})}var V=m-2*y;for(l=0;l<f.length;l+=3){var F=f[l]+V,T=f[l+1]+V,A=f[l+2]+V;b[C++]=F,b[C++]=T,b[C++]=A,b[C++]=A+y,b[C++]=T+y,b[C++]=F+y}var G=new S.Geometry({attributes:o,indices:b,boundingSphere:R.BoundingSphere.fromVertices(i),primitiveType:B.PrimitiveType.TRIANGLES});if(r.normal&&(G=N.GeometryPipeline.computeNormal(G)),r.tangent||r.bitangent){try{G=N.GeometryPipeline.computeTangentAndBitangent(G)}catch(e){q.oneTimeWarning("polyline-volume-tangent-bitangent","Unable to compute tangents and bitangents for polyline volume geometry")}r.tangent||(G.attributes.tangent=void 0),r.bitangent||(G.attributes.bitangent=void 0),r.st||(G.attributes.st=void 0)}return x.defined(a)&&(G.attributes.position.values=a,G.attributes.position.componentDatatype=O.ComponentDatatype.FLOAT),G}(s,o,l,e._vertexFormat)}},function(e,t){return x.defined(t)&&(e=E.unpack(e,t)),e._ellipsoid=m.Ellipsoid.clone(e._ellipsoid),E.createGeometry(e)}});