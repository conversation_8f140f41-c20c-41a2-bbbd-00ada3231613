<template>
  <div class="headerContainer pull-container">
    <div class="flex-wrap pull-container">
      <div class="logo-con">
        <img src="@/assets/images/bg.png" alt="" />
        <svg-icon icon-class="logo" class="icon-logo" />
        <span class="logo-title">新能源管控平台</span>
      </div>
      <nav class="nav-content">
        <el-menu
          :default-active="activeIndex"
          class="nav_list"
          mode="horizontal"
          background-color="#4570e7"
          text-color="#fff"
        >
          <el-menu-item
            v-for="item in filteredMenuItems"
            :key="item.index"
            :index="item.index"
            @click="handleMenuItemClick(item)"
          >
            <router-link :to="item.path">
              <svg-icon :icon-class="item.icon" class="icon" />
              <span>{{ item.title }}</span>
            </router-link>
          </el-menu-item>
        </el-menu>
      </nav>
      <div class="header-right flex-wrap">
        <div id="full-wrap-bt">
          <svg-icon icon-class="big-full" class="icon" />
          <span class="text"> 大屏 </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios"; // 引入axios
import SpecEquipMange from "../redirect/specEquipManage/specEquipManage.js";
export default {
  name: "Header",
  data() {
    return {
      activeIndex: "2",
      menuItems: [
        {
          index: "2",
          path: "/devices/monitor",
          icon: "s-device",
          title: "施工装备监控",
          permissions: ["monitor", "admin"],
        },

        {
          index: "3",
          path: "/devices/manage",
          icon: "m_device",
          title: "设备台账管理",
          permissions: ["manage", "admin"],
        },
        {
          index: "4",
          path: "/devices/powerstationmanage",
          icon: "charge",
          title: "充换电站管理",
          permissions: ["powerstation", "admin"],
        },
        {
          index: "5",
          path: "/devices/maintenancemanage",
          icon: "people",
          title: "维保管理",
          permissions: ["maintenance", "admin"],
        },
        {
          index: "6",
          path: "/devices/errorwarning",
          icon: "message",
          title: "设备故障列表",
          permissions: ["errorwarning", "admin"],
        },
        {
          index: "1",
          path: "/devices/statistics",
          icon: "s-statics",
          title: "综合统计",
          permissions: ["statistics", "admin"],
        },
        // { index: '7', path: '/devices/batterymanage', icon: 'message', title: '电池热管理', permissions: ['batterymanage', 'admin'] },
        {
          index: "8",
          path: "",
          icon: "message",
          title: "碳排放管理",
          permissions: ["cRealese", "admin"],
        },
        {
          index: "9",
          path: "",
          icon: "message",
          title: "专用设备管理",
          permissions: ["cRealese", "admin"],
        },
      ],
    };
  },
  computed: {
    userPermissions() {
      //console.log('userPermissions:', this.$store.state.user.permissions); // 调试信息
      console.log("permissions:", this.$store.state.user);
      //return this.$store.state.permissions || [];
      return (
        JSON.parse(JSON.stringify(this.$store.state.user.permissions)) || []
      );
      //return ['manage','statistics'];
    },
    filteredMenuItems() {
      return this.menuItems.filter((item) =>
        item.permissions.some((permission) =>
          this.userPermissions.includes(permission)
        )
      );
    },
  },
  mounted() {},
  methods: {
    handleClick() {
      console.log("我被点击了");
    },
    async handleMenuItemClick(item) {
      if (item.title === "碳排放管理") {
        console.log("点击了碳排放管理");
        try {
          console.log("进入了try");
          // 发送POST请求获取token
          const response = await axios.post(
            "http://data.nevczibo.com.cn:8081/datagy/sso/login",
            {
              username: "ssouser",
              password: "NEvc_SDu_Ji_1903",
            }
          );
          console.log("已获取token");

          // 提取token
          const token = response.data.token;
          console.log("token是:", token);

          // 获取到 token 后，拼接完整 URL 并跳转
          const targetUrl = `http://data.nevczibo.com.cn:8081/index?token=${token}`;
          window.location.href = targetUrl;

          // 使用token跳转到目标URL
          //this.$router.push({ path: 'http://data.nevczibo.com.cn:8081/index', query: { token: token } });
        } catch (error) {
          console.error("获取token失败:", error);
        }
        // 跳转到其他页面，例如 '/other-page'
        //this.$router.push('/other-page');
      } else if (item.title === "专用设备管理") {
        SpecEquipMange.goSpecEquipMange();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.pull-container {
  background: #4570e7;
  height: 63px;

  .logo-con {
    position: relative;
    height: 100%;
    width: 40%;

    img {
      height: 100%;
    }

    .icon-logo {
      position: absolute;
      left: 37px;
      top: 14px;
      width: 50px;
      height: 38px;
    }

    .logo-title {
      position: absolute;
      color: #ffffff;
      width: 90%;
      left: 85px;
      top: 20px;
      font-size: 20px;
      text-align: left;
      font-family: "微软雅黑", sans-serif;
      font-weight: 400;
      font-style: normal;
    }
  }

  .nav-content {
    height: 100%;
    width: 33%;
    margin-left: 0px;

    .nav_list {
      display: flex;
      height: 100%;
      justify-content: center;

      .icon {
        width: 18px;
        height: 15px;
        margin-top: 5px;
        margin-right: 5px;
      }

      .nav-li {
        color: rgba(255, 255, 255, 0.996078431372549);
        font-size: 15px;
        display: flex;
        padding: 21px 15px;
        flex-shrink: 0;
        white-space: nowrap !important;

        &:hover {
          background: #3599ee;
          cursor: pointer;
        }

        &:active {
          background: #3599ee;
          cursor: pointer;
        }

        justify-content: center;
        align-items: center;

        img {
          width: 14px;
          height: 14px;
          margin-right: 4px;
        }
      }

      li .router-link-active {
        cursor: pointer;
      }
    }
  }

  .header-right {
    position: relative;
    padding: 19px 15px;
    color: #ffffff;
    font-size: 14px;
    height: 100%;
    margin-left: auto;

    .text {
      margin-top: -3px;
      margin-left: 3px;
      margin-right: 5px;
      font-family: "微软雅黑", sans-serif;
      font-weight: 400;
      font-style: normal;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
    }

    .icon {
      width: 18px;
      height: 18px;
    }
  }

  #full-wrap-bt {
    cursor: pointer;
  }
}

.headerContainer {
  width: 100% !important;

  .header-right .el-input__inner {
    color: #fff;
  }

  .el-menu.el-menu--horizontal {
    border: none;

    .el-menu-item {
      height: 63px;
      font-family: "微软雅黑", sans-serif;
      font-weight: 400;
      font-style: normal;
      font-size: 16px;
    }

    ::v-deep .el-submenu__title {
      height: 63px;
      line-height: 63px;
      font-family: "微软雅黑", sans-serif;
      font-weight: 400;
      font-style: normal;
      font-size: 16px;
    }
  }

  ::v-deep .el-submenu__title i {
    color: transparent;
  }
}

.icon-list {
  width: 18px !important;
  height: 15px !important;
  margin-right: 10px;
}
</style>

<style lang="scss">
// 控制二级菜单的颜色背景
.el-menu--horizontal {
  .el-menu--popup-bottom-start {
    background-color: #fff !important;

    .menu-item {
      background-color: #fff !important;
      color: #7f86a3 !important;

      &:hover {
        background-color: #edf4fe !important;
      }
    }
  }
}

// 控制点击后的颜色背景
.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom: 2px solid #4570e7 !important;
  color: #fff !important;
}

// 控制点击二级菜单后以及菜单颜色异常
.el-menu--horizontal > .el-submenu.is-active .el-submenu__title {
  border-bottom: 2px solid #4570e7 !important;
  color: #fff !important;
}
</style>
