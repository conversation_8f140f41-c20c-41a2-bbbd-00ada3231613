<template>
  <div id="layout">
    <div class="header">
      <Header></Header>
    </div>
    <div class="main">
      <el-container>
        <div class="container">
          <TagsView></TagsView>
          <div class="container-main">
            <router-view></router-view>
          </div>
        </div>
      </el-container>
    </div>
  </div>
</template>

<script>
import Header from "@/views/header/header.vue";
import Tree from '@/views/components/Tree/tree.vue';
import screenfull from "screenfull";
export default {
  name: "ComponentLayout",
  components: { Header, Tree },
  data() {
    return {
      asideStatus: true,
      aside_open_close: true,
    };
  },
  mounted() {
    const element = document.getElementById("layout"); //指定全屏区域元素
    document.getElementById("full-wrap-bt").addEventListener("click", () => {
      if (screenfull.isEnabled) {
        screenfull.request(element);
      }
      screenfull.toggle();
    }); //实现模块全屏
  },
  methods: {
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/mixin.scss";
@import "@/styles/variables.scss";

#layout {
  .header {
    position: fixed;
    width: 100vw;
    left: 0;
    top: 0;
    z-index: 99;
  }

  .main {
    width: 100vw;
    background-color: #fff;
    padding-top: 63px;
    height: calc(100vh);
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .container {
      width: 100%;
      height: 100%;
      background-color: #E6ECF9;

      .container-main {
        padding: 10px;
        padding-bottom: 0px;
        height: calc(100vh - 100px);
        overflow: auto;
      }
    }
  }
}
</style>
