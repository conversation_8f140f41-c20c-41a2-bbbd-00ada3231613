import Vue from "vue";
import Router from "vue-router";
import Layout from "../views/Layout.vue";
import ComponentLayout from '@/views/components/Layout/Layout.vue';
import error from "../views/redirect/404.vue";
import store from '../store/index';

Vue.use(Router);

export const constantRoutes = [{
    path: "/",
    redirect: "/devices/maintenancemanage",
    hidden: true,
  },
  {
    path: "/login",
    hidden: true,
    component: () => import("../views/login/index.vue"),
  },
  {
    path: "/devices",
    name: "设备管理",
    component: Layout,
    hidden: true,
    meta: {
      title: "设备管理",
      requireAuth: true,
    },
    children: [{
        path: 'manage',
        name: '设备台账管理',
        component: () => import("../views/redirect/deviceLedgerManage/index.vue"),
        meta: {
          title: "设备台账管理",
          requireAuth: true,
        },
      },
      {
        path: 'monitor',
        name: '施工装备监控',
        component: () => import("../views/redirect/equipmentMonitor/index.vue"),
        meta: {
          title: "施工装备监控",
          requireAuth: true,
        },
      },
      {
        path: 'statistics',
        name: '综合统计',
        component: () => import("../views/redirect/compreStatistics/index.vue"),
        meta: {
          title: "综合统计",
          requireAuth: true,
        },
      },
      {
        path: 'powerstationmanage',
        name: '充换电站管理',
        component: () => import("../views/redirect/powerstationManage/index.vue"),
      },
      {
        path: 'chargingrecords',
        name: '充电记录',
        component: () => import("../views/redirect/chargingrecords/index.vue"),
      },
      {
        path: 'exchangerecords',
        name: '换电记录',
        component: () => import("../views/redirect/exchangerecords/index.vue"),
      },
      {
        path: 'chargingfault',
        name: '充电机故障',
        component: () => import("../views/redirect/chargingfault/index.vue"),
      },
      {
        path: 'exchangingfault',
        name: '换电设备故障',
        component: () => import("../views/redirect/exchangingfault/index.vue"),
      },
      {
        path: 'maintenancemanage',
        name: '维保管理',
        component: () => import("../views/redirect/maintenanceManage/index.vue"),
      },
      {
        path: 'access',
        component: () => import("../views/redirect/equipmentMonitor/list.vue"),
      },
    ]
  },
  {
    path: "/devices",
    name: "设备管理",
    component: ComponentLayout,
    hidden: true,
    meta: {
      title: "设备管理",
      requireAuth: true,
    },
    children: [{
      path: 'details',
      component: () => import("../views/redirect/equipmentMonitor/details.vue"),
      meta: {
        requireAuth: true,
      },
    }, ]
  },
  {
    path: "/404",
    component: error,
    hidden: true,
    meta: {
      title: "404未找到"
    },
  },
];
const createRouter = () =>
  new Router({
    scrollBehavior: () => ({
      y: 0
    }),
    routes: constantRoutes,
  });
const router = createRouter();

export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

// 路由守卫
router.beforeEach((to, from, next) => {
  let token = store.state.user.token;
  if (to.meta.requireAuth) { // 需要权限
    //判断当前是否拥有权限
    if (token) {
      next();
    } else { // 无权，跳转登录
      next('/login')
    }
  } else { // 不需要权限，直接访问
    next();
  }
})

export default router;
