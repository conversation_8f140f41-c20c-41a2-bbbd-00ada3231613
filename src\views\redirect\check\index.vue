<template>
  <div id="Sbcheck">
    <el-card class="card-content">
      <div class="content-table">
        <el-table ref="table" :data="tableData" style="width: 100%;" height="70vh" border :header-cell-style="headerCellStyle"
          :cell-style="cellStyle">
          <el-table-column type="index" width="50" label="序号" align="center" />
          <el-table-column prop="deviceName" label="设备名称" align="center" min-width="150" />
          <el-table-column prop="managementNumber" label="管理编码" align="center" min-width="180" />
          <el-table-column prop="entryTime" label="在线状态" align="center" min-width="130" />
          <el-table-column prop="exitTime" label="上传时间" align="center" min-width="130" />
          <el-table-column prop="inspectionDate" label="设备检验日期" align="center" min-width="150" />
          <el-table-column prop="operationPerson" label="作业人员" align="center" min-width="150" />
          <el-table-column prop="remark" label="备注" align="center" min-width="150" />
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button @click="handleOperate(scope.row)" type="text" size="small" class="table-edit">编辑</el-button>
              <el-popconfirm title="确定删除吗？" @confirm="handleDelete(scope.row.id, '/equip/ledger/deleteById')">
                <el-button slot="reference" type="text" size="small"> 删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="pagination-part" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pageParams.page" :page-sizes="pageSizes" :page-size="pageParams.limit" :layout="tableLayout"
          :total="total">
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import tableUtils from '@/mixins/tableUtils';
export default {
  name: 'Sbcheck',
  mixins: [tableUtils],
  components: {  },
  data() {
    return {
      formInline: {},
      dialogVisible: false,
      curData: null,
      tableData: [],
      importvisible: false,
      loading: false,
    }
  },
  watch: {
    "$store.state.tree.sectionId": {
      
      
      handler: function () {
        this.requestList();
        
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    async requestList() {
      let params = {
        ...this.formInline,
        sectionId: this.$store.state.tree.sectionId
      };
      let res = await this.$http.post(`/equip/battery/pages?page=${this.pageParams.page}&limit=${this.pageParams.limit}`, params);
      this.tableData = res.result?.records;
      this.total = res.result?.total;
    },



    handleImport() {
      this.importvisible = true;
    },

    handleOperate(item) {
      if (item) {
        // if (item.deviceName.includes('挖掘')){
        //   item.deviceType = 0
        // }
        // else if (item.deviceName.includes('装载')){
        //   item.deviceType = 1
        // }
        // else if (item.deviceName.includes('自卸') ){
        //   item.deviceType = 2
        // }
        // else if (item.deviceName.includes('混凝土')){
        //   item.deviceType = 3
        // }
        // else{
        //   item.deviceType = 4
        // }
        this.curData = { data: { ...item }, type: 2 }
      } else {
        this.curData = { data: {}, type: 1 }
      }
      this.dialogVisible = true
    },

    onClose() {
      this.dialogVisible = false;
    },

    handleClose() {
      this.requestList();
      this.importvisible = false;
    },

    onSave() {
      this.requestList();
      this.dialogVisible = false;
    }
  }
}
</script>

<style lang="scss" scoped>
#Sbcheck {
  .content-option {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .content-title {
      line-height: 40px;
      font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
      font-weight: 700;
      font-style: normal;
      font-size: 18px;
      color: #3A3F5D;
      margin-right: 5%;
    }

    .form-view {
      flex: 1;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }

    ::v-deep .el-input {
      width: 175px;
    }
  }

  .table-edit {
    margin-right: 10px;
  }

  .pagination-part {
    margin-top: 20px;
  }
}
</style>