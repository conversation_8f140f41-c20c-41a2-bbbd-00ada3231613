/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./AttributeCompression-75ce15eb","./IndexDatatype-9435b55f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./createTaskProcessorWorker","./EllipsoidTangentPlane-9c25b2da","./OrientedBoundingBox-7b25e901","./Color-69f1845f"],function(Le,e,Oe,Ue,Fe,a,r,n,t,i,o,s,Pe,Se,f,d,c,u,De,Me){var Re=new Ue.Cartesian3,_e=new Fe.Ellipsoid,Ge=new Fe.Rectangle,Ye={min:void 0,max:void 0,indexBytesPerElement:void 0};function Ve(e,a,r){var n=a.length,t=2+n*De.OrientedBoundingBox.packedLength+1+function(e){for(var a=e.length,r=0,n=0;n<a;++n)r+=Me.Color.packedLength+3+e[n].batchIds.length;return r}(r),i=new Float64Array(t),o=0;i[o++]=e,i[o++]=n;for(var s=0;s<n;++s)De.OrientedBoundingBox.pack(a[s],i,o),o+=De.OrientedBoundingBox.packedLength;var f=r.length;i[o++]=f;for(var d=0;d<f;++d){var c=r[d];Me.Color.pack(c.color,i,o),o+=Me.Color.packedLength,i[o++]=c.offset,i[o++]=c.count;var u=c.batchIds,h=u.length;i[o++]=h;for(var l=0;l<h;++l)i[o++]=u[l]}return i}var He=new Ue.Cartesian3,We=new Ue.Cartesian3,ze=new Ue.Cartesian3,Ze=new Ue.Cartesian3,je=new Ue.Cartesian3,qe=new Ue.Cartographic,Je=new Fe.Rectangle;return c(function(e,a){var r,n,t,i;r=e.packedBuffer,n=new Float64Array(r),t=0,Ye.indexBytesPerElement=n[t++],Ye.min=n[t++],Ye.max=n[t++],Ue.Cartesian3.unpack(n,t,Re),t+=Ue.Cartesian3.packedLength,Fe.Ellipsoid.unpack(n,t,_e),t+=Fe.Ellipsoid.packedLength,Fe.Rectangle.unpack(n,t,Ge),i=2===Ye.indexBytesPerElement?new Uint16Array(e.indices):new Uint32Array(e.indices);var o,s,f,d=new Uint16Array(e.positions),c=new Uint32Array(e.counts),u=new Uint32Array(e.indexCounts),h=new Uint32Array(e.batchIds),l=new Uint32Array(e.batchTableColors),b=new Array(c.length),g=Re,p=_e,C=Ge,y=Ye.min,I=Ye.max,m=e.minimumHeights,v=e.maximumHeights;Le.defined(m)&&Le.defined(v)&&(m=new Float32Array(m),v=new Float32Array(v));var w=d.length/2,x=d.subarray(0,w),A=d.subarray(w,2*w);Pe.AttributeCompression.zigZagDeltaDecode(x,A);var E=new Float32Array(3*w);for(o=0;o<w;++o){var N=x[o],T=A[o],k=Oe.CesiumMath.lerp(C.west,C.east,N/32767),B=Oe.CesiumMath.lerp(C.south,C.north,T/32767),L=Ue.Cartographic.fromRadians(k,B,0,qe),O=p.cartographicToCartesian(L,He);Ue.Cartesian3.pack(O,E,3*o)}var U=c.length,F=new Array(U),P=new Array(U),S=0,D=0;for(o=0;o<U;++o)F[o]=S,P[o]=D,S+=c[o],D+=u[o];var M,R=new Float32Array(3*w*2),_=new Uint16Array(2*w),G=new Uint32Array(P.length),Y=new Uint32Array(u.length),V=[],H={};for(o=0;o<U;++o)f=l[o],Le.defined(H[f])?(H[f].positionLength+=c[o],H[f].indexLength+=u[o],H[f].batchIds.push(o)):H[f]={positionLength:c[o],indexLength:u[o],offset:0,indexOffset:0,batchIds:[o]};var W=0,z=0;for(f in H)if(H.hasOwnProperty(f)){(M=H[f]).offset=W,M.indexOffset=z;var Z=2*M.positionLength,j=2*M.indexLength+6*M.positionLength;W+=Z,z+=j,M.indexLength=j}var q=[];for(f in H)H.hasOwnProperty(f)&&(M=H[f],q.push({color:Me.Color.fromRgba(parseInt(f)),offset:M.indexOffset,count:M.indexLength,batchIds:M.batchIds}));for(o=0;o<U;++o){var J=(M=H[f=l[o]]).offset,K=3*J,Q=J,X=F[o],$=c[o],ee=h[o],ae=y,re=I;Le.defined(m)&&Le.defined(v)&&(ae=m[o],re=v[o]);var ne=Number.POSITIVE_INFINITY,te=Number.NEGATIVE_INFINITY,ie=Number.POSITIVE_INFINITY,oe=Number.NEGATIVE_INFINITY;for(s=0;s<$;++s){var se=Ue.Cartesian3.unpack(E,3*X+3*s,He);p.scaleToGeodeticSurface(se,se);var fe=p.cartesianToCartographic(se,qe),de=fe.latitude,ce=fe.longitude;ne=Math.min(de,ne),te=Math.max(de,te),ie=Math.min(ce,ie),oe=Math.max(ce,oe);var ue=p.geodeticSurfaceNormal(se,We),he=Ue.Cartesian3.multiplyByScalar(ue,ae,ze),le=Ue.Cartesian3.add(se,he,Ze);he=Ue.Cartesian3.multiplyByScalar(ue,re,he);var be=Ue.Cartesian3.add(se,he,je);Ue.Cartesian3.subtract(be,g,be),Ue.Cartesian3.subtract(le,g,le),Ue.Cartesian3.pack(be,R,K),Ue.Cartesian3.pack(le,R,K+3),_[Q]=ee,_[Q+1]=ee,K+=6,Q+=2}(C=Je).west=ie,C.east=oe,C.south=ne,C.north=te,b[o]=De.OrientedBoundingBox.fromRectangle(C,y,I,p);var ge=M.indexOffset,pe=P[o],Ce=u[o];for(G[o]=ge,s=0;s<Ce;s+=3){var ye=i[pe+s]-X,Ie=i[pe+s+1]-X,me=i[pe+s+2]-X;V[ge++]=2*ye+J,V[ge++]=2*Ie+J,V[ge++]=2*me+J,V[ge++]=2*me+1+J,V[ge++]=2*Ie+1+J,V[ge++]=2*ye+1+J}for(s=0;s<$;++s){var ve=s,we=(s+1)%$;V[ge++]=2*ve+1+J,V[ge++]=2*we+J,V[ge++]=2*ve+J,V[ge++]=2*ve+1+J,V[ge++]=2*we+1+J,V[ge++]=2*we+J}M.offset+=2*$,M.indexOffset=ge,Y[o]=ge-G[o]}V=Se.IndexDatatype.createTypedArray(R.length/3,V);for(var xe=q.length,Ae=0;Ae<xe;++Ae){for(var Ee=q[Ae].batchIds,Ne=0,Te=Ee.length,ke=0;ke<Te;++ke)Ne+=Y[Ee[ke]];q[Ae].count=Ne}var Be=Ve(2===V.BYTES_PER_ELEMENT?Se.IndexDatatype.UNSIGNED_SHORT:Se.IndexDatatype.UNSIGNED_INT,b,q);return a.push(R.buffer,V.buffer,G.buffer,Y.buffer,_.buffer,Be.buffer),{positions:R.buffer,indices:V.buffer,indexOffsets:G.buffer,indexCounts:Y.buffer,batchIds:_.buffer,packedBuffer:Be.buffer}})});