<template>
    <div class="bms-wrapper">
        <div class="bms-item">
            <div class="item-info">
                <div class="info-title">电池包电流</div>
                <div class="info-num">{{ this.deviceInfo.bgCurrent || 0 }} A</div>
            </div>
            <div class="item-info">
                <div class="info-title">电池包电压</div>
                <div class="info-num">{{ this.deviceInfo.bgVoltage || 0 }} V</div>
            </div>
            <div class="item-info">
                <div class="info-title">电池包剩余SOC值</div>
                <div class="info-num">{{ this.deviceInfo.soc || '0 ' }}%</div>
            </div>
            <div class="item-info">
                <div class="info-title">电池包剩余SOH值</div>
                <div class="info-num">{{ this.deviceInfo.soh || '0' }}%</div>
            </div>
            <div class="item-info">
                <div class="info-title">当前最大允许放电功率</div>
                <div class="info-num">{{ this.deviceInfo.curMaxAllowDischargePower || '0' }}KW</div>
            </div>
            <div class="item-info">
                <div class="info-title">当前最大允许充电功率</div>
                <div class="info-num">{{ this.deviceInfo.curMaxAllowChargePower || '0' }} KW</div>
            </div>
            <div class="item-info">
                <div class="info-title">当前可放电量</div>
                <div class="info-num">{{ this.deviceInfo.curDischargeCapacity || '0' }} KW</div>
            </div>
            <div class="item-info info">
                <div class="info-title">当前可充电量</div>
                <div class="info-num">{{ this.deviceInfo.curRechargeCapacity || '0' }} KW</div>
            </div>
        </div>
        <div class="bms-info">
            <div class="item-info">
                <div class="info-title">最高单体温度</div>
                <div class="info-num">{{ this.deviceInfo.cellMaxTempValue || 0 }} ℃</div>
            </div>
            <div class="item-info">
                <div class="info-title">最高单体温度所在编号</div>
                <div class="info-num">{{ this.deviceInfo.cellMaxTempNum || 0 }}</div>
            </div>
            <div class="item-info">
                <div class="info-title">最低单体温度</div>
                <div class="info-num">{{ this.deviceInfo.cellMinTempValue || 0 }} ℃</div>
            </div>
            <div class="item-info">
                <div class="info-title">最低单体温度所在编号</div>
                <div class="info-num">{{ this.deviceInfo.cellMinTempNum || 0 }}</div>
            </div>
            <div class="item-info">
                <div class="info-title">最高单体电压值</div>
                <div class="info-num">{{ this.deviceInfo.cellMaxVoltageValue || 0 }} V</div>
            </div>
            <div class="item-info">
                <div class="info-title">最高单体电压所在编号</div>
                <div class="info-num">{{ this.deviceInfo.cellMaxVoltageNum || 0 }}</div>
            </div>
            <div class="item-info">
                <div class="info-title">最低单体电压压值</div>
                <div class="info-num">{{ this.deviceInfo.cellMinVoltageValue || 0 }} V</div>
            </div>
            <div class="item-info info">
                <div class="info-title">最低单体电压所在编号</div>
                <div class="info-num">{{ this.deviceInfo.cellMinVoltageNum || 0 }}</div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'BMS',
    props: ['deviceInfo'],
    data() {
        return {
        }
    },
    mounted() {
    },

    methods: {

    }
}
</script>

<style lang="scss" scoped>
.bms-wrapper {
    width: 100%;
    height: 250px;
    padding: 0 5px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .bms-item {
        width: 250px;
        height: 100%;
    }

    .bms-info {
        flex: 1;
        margin-left: 10px;
    }

    .item-info {
        width: 100%;
        height: 31px;
        line-height: 31px;
        border-bottom: 1px solid #46AFCA;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: flex-end;
        font-family: 'S微软 ', 'S微软', sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: 14px;
        text-align: left;

        .info-title {
            flex: 1;
        }

        .info-num {
            width: 90px;
            text-align: right;
            margin-right: 10px;
        }
    }

    .info {
        border: none;
    }
}
</style>