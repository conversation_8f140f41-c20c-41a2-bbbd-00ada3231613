import { JSEncrypt } from "jsencrypt";
export default {
  encodeData(value, publicKey) {
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(publicKey);
    return encrypt.encrypt(value);
  },
  goSpecEquipMange() {
    let account = "";
    let password = "";
    let publicKey =
      "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQClJ9OoelNqzUe09yZlSEl1P4dK7ty9SAI1ZQeEHCFngJOL916iGZKNfq5zHldHh43qWZ7ccVIiJHuMnIP7XEmDQQjPBUNsSyQKKbUxhhi692QR0COG7yIOxkD+MueIPz6+kBgtH/4Ap9wzvCO9lIu4v3AWRForEnz00StQEHNiVQIDAQAB";
    account = this.encodeData("cznq", publicKey);
    password = this.encodeData("Nq2487980d.", publicKey);
    const url = `https://iot.crchi.com:9000/tunnelGreen/oauthIOT?usraccountnumber=${encodeURIComponent(
      account
    )}&usrtokenstr=${encodeURIComponent(password)}`;
    window.open(url, "_blank");
  },
};
