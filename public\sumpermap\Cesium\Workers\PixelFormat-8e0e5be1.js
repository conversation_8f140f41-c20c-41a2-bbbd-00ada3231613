/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./WebGLConstants-4c11ee5f"],function(_,t){var G={UNSIGNED_BYTE:t.WebGLConstants.UNSIGNED_BYTE,UNSIGNED_SHORT:t.WebGLConstants.UNSIGNED_SHORT,UNSIGNED_INT:t.WebGLConstants.UNSIGNED_INT,FLOAT:t.WebGLConstants.FLOAT,HALF_FLOAT:t.WebGLConstants.HALF_FLOAT_OES,UNSIGNED_INT_24_8:t.WebGLConstants.UNSIGNED_INT_24_8,UNSIGNED_SHORT_4_4_4_4:t.WebGLConstants.UNSIGNED_SHORT_4_4_4_4,UNSIGNED_SHORT_5_5_5_1:t.WebGLConstants.UNSIGNED_SHORT_5_5_5_1,UNSIGNED_SHORT_5_6_5:t.WebGLConstants.UNSIGNED_SHORT_5_6_5,isPacked:function(_){return _===G.UNSIGNED_INT_24_8||_===G.UNSIGNED_SHORT_4_4_4_4||_===G.UNSIGNED_SHORT_5_5_5_1||_===G.UNSIGNED_SHORT_5_6_5},sizeInBytes:function(_){switch(_){case G.UNSIGNED_BYTE:return 1;case G.UNSIGNED_SHORT:case G.UNSIGNED_SHORT_4_4_4_4:case G.UNSIGNED_SHORT_5_5_5_1:case G.UNSIGNED_SHORT_5_6_5:case G.HALF_FLOAT:return 2;case G.UNSIGNED_INT:case G.FLOAT:case G.UNSIGNED_INT_24_8:return 4}},validate:function(_){return _===G.UNSIGNED_BYTE||_===G.UNSIGNED_SHORT||_===G.UNSIGNED_INT||_===G.FLOAT||_===G.HALF_FLOAT||_===G.UNSIGNED_INT_24_8||_===G.UNSIGNED_SHORT_4_4_4_4||_===G.UNSIGNED_SHORT_5_5_5_1||_===G.UNSIGNED_SHORT_5_6_5}},r={DEPTH_COMPONENT:t.WebGLConstants.DEPTH_COMPONENT,DEPTH_STENCIL:t.WebGLConstants.DEPTH_STENCIL,ALPHA:t.WebGLConstants.ALPHA,RGB:t.WebGLConstants.RGB,RGBA:t.WebGLConstants.RGBA,LUMINANCE:t.WebGLConstants.LUMINANCE,LUMINANCE_ALPHA:t.WebGLConstants.LUMINANCE_ALPHA,RGB_DXT1:t.WebGLConstants.COMPRESSED_RGB_S3TC_DXT1_EXT,RGBA_DXT1:t.WebGLConstants.COMPRESSED_RGBA_S3TC_DXT1_EXT,RGBA_DXT3:t.WebGLConstants.COMPRESSED_RGBA_S3TC_DXT3_EXT,RGBA_DXT5:t.WebGLConstants.COMPRESSED_RGBA_S3TC_DXT5_EXT,RGB_PVRTC_4BPPV1:t.WebGLConstants.COMPRESSED_RGB_PVRTC_4BPPV1_IMG,RGB_PVRTC_2BPPV1:t.WebGLConstants.COMPRESSED_RGB_PVRTC_2BPPV1_IMG,RGBA_PVRTC_4BPPV1:t.WebGLConstants.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG,RGBA_PVRTC_2BPPV1:t.WebGLConstants.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG,RGB_ETC1:t.WebGLConstants.COMPRESSED_RGB_ETC1_WEBGL,componentsLength:function(_){switch(_){case r.RGB:return 3;case r.RGBA:return 4;case r.LUMINANCE_ALPHA:return 2;case r.ALPHA:case r.LUMINANCE:default:return 1}},validate:function(_){return _===r.DEPTH_COMPONENT||_===r.DEPTH_STENCIL||_===r.ALPHA||_===r.RGB||_===r.RGBA||_===r.LUMINANCE||_===r.LUMINANCE_ALPHA||_===r.RGB_DXT1||_===r.RGBA_DXT1||_===r.RGBA_DXT3||_===r.RGBA_DXT5||_===r.RGB_PVRTC_4BPPV1||_===r.RGB_PVRTC_2BPPV1||_===r.RGBA_PVRTC_4BPPV1||_===r.RGBA_PVRTC_2BPPV1||_===r.RGB_ETC1},isColorFormat:function(_){return _===r.ALPHA||_===r.RGB||_===r.RGBA||_===r.LUMINANCE||_===r.LUMINANCE_ALPHA},isDepthFormat:function(_){return _===r.DEPTH_COMPONENT||_===r.DEPTH_STENCIL},isCompressedFormat:function(_){return _===r.RGB_DXT1||_===r.RGBA_DXT1||_===r.RGBA_DXT3||_===r.RGBA_DXT5||_===r.RGB_PVRTC_4BPPV1||_===r.RGB_PVRTC_2BPPV1||_===r.RGBA_PVRTC_4BPPV1||_===r.RGBA_PVRTC_2BPPV1||_===r.RGB_ETC1},isDXTFormat:function(_){return _===r.RGB_DXT1||_===r.RGBA_DXT1||_===r.RGBA_DXT3||_===r.RGBA_DXT5},isPVRTCFormat:function(_){return _===r.RGB_PVRTC_4BPPV1||_===r.RGB_PVRTC_2BPPV1||_===r.RGBA_PVRTC_4BPPV1||_===r.RGBA_PVRTC_2BPPV1},isETC1Format:function(_){return _===r.RGB_ETC1},compressedTextureSizeInBytes:function(_,t,n){switch(_){case r.RGB_DXT1:case r.RGBA_DXT1:case r.RGB_ETC1:return Math.floor((t+3)/4)*Math.floor((n+3)/4)*8;case r.RGBA_DXT3:case r.RGBA_DXT5:return Math.floor((t+3)/4)*Math.floor((n+3)/4)*16;case r.RGB_PVRTC_4BPPV1:case r.RGBA_PVRTC_4BPPV1:return Math.floor((Math.max(t,8)*Math.max(n,8)*4+7)/8);case r.RGB_PVRTC_2BPPV1:case r.RGBA_PVRTC_2BPPV1:return Math.floor((Math.max(t,16)*Math.max(n,8)*2+7)/8);default:return 0}},textureSizeInBytes:function(_,t,n,e){var T=r.componentsLength(_);return G.isPacked(t)&&(T=1),T*G.sizeInBytes(t)*n*e},alignmentInBytes:function(_,t,n){var e=r.textureSizeInBytes(_,t,n,1)%4;return 0===e?4:2===e?2:1},createTypedArray:function(_,t,n,e){var T=G.sizeInBytes(t);return new(T===Uint8Array.BYTES_PER_ELEMENT?Uint8Array:T===Uint16Array.BYTES_PER_ELEMENT?Uint16Array:T===Float32Array.BYTES_PER_ELEMENT&&t===G.FLOAT?Float32Array:Uint32Array)(r.componentsLength(_)*n*e)},flipY:function(_,t,n,e,T){if(1===T)return _;for(var G=r.createTypedArray(t,n,e,T),R=r.componentsLength(t),P=e*R,E=0;E<T;++E)for(var N=E*T*R,s=(T-E-1)*T*R,B=0;B<P;++B)G[s+B]=_[N+B];return G}},n=Object.freeze(r);_.PixelDatatype=G,_.PixelFormat=n});