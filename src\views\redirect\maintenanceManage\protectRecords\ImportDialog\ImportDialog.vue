<template>
    <el-dialog title="导入" :visible.sync="importvisible" width="25%" :before-close="handleClose" class="popup-dialog">
        <div class="dialog-part">
            <el-upload class="upload-demo" drag action="/equip/ledger/importExcel" multiple :limit="1">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip">只能上传设备台账管理表，且只能上传一个文件</div>
            </el-upload>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="onClose" type="primary">关闭</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    name: 'ImportDialog',
    props: ['importvisible'],
    components: {
    },
    data() {
        return {

        }
    },

    mounted() {

    },

    methods: {
        onClose() {
            this.$emit('onClose');
        },

        handleClose() {
            this.$confirm('确认关闭？')
                .then(_ => {
                    this.$emit('onClose')
                })
                .catch(_ => { });
        },
    },
}
</script>

<style lang="scss" scoped>
.popup-dialog {
    .dialog-part {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    ::v-deep .el-dialog__header {
        background-color: #4575E7;

        .el-dialog__title {
            color: #fff;
        }

        .el-dialog__close {
            color: #fff;
        }
    }
}
</style>