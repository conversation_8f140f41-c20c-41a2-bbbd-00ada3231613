/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60"],function(e,l){var s,n,r,t,u,i,o,c,f,d,a,p,m,A,v,g,y,F,b,h,E,w={requestFullscreen:void 0,exitFullscreen:void 0,fullscreenEnabled:void 0,fullscreenElement:void 0,fullscreenchange:void 0,fullscreenerror:void 0},x={};function C(e){for(var n=e.split("."),r=0,t=n.length;r<t;++r)n[r]=parseInt(n[r],10);return n}function S(){if(!l.defined(r)&&(r=!1,!q())){var e=/ Chrome\/([\.0-9]+)/.exec(n.userAgent);null!==e&&(r=!0,t=C(e[1]))}return r}function I(){if(!l.defined(u)&&(u=!1,!S()&&!q()&&/ Safari\/[\.0-9]+/.test(n.userAgent))){var e=/ Version\/([\.0-9]+)/.exec(n.userAgent);null!==e&&(u=!0,i=C(e[1]))}return u}function V(){if(!l.defined(o)){o=!1;var e=/ AppleWebKit\/([\.0-9]+)(\+?)/.exec(n.userAgent);null!==e&&(o=!0,(c=C(e[1])).isNightly=!!e[2])}return o}function W(){var e;l.defined(f)||(f=!1,"Microsoft Internet Explorer"===n.appName?null!==(e=/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(n.userAgent))&&(f=!0,d=C(e[1])):"Netscape"===n.appName&&null!==(e=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(n.userAgent))&&(f=!0,d=C(e[1])));return f}function q(){if(!l.defined(a)){a=!1;var e=/ Edge\/([\.0-9]+)/.exec(n.userAgent);null!==e&&(a=!0,p=C(e[1]))}return a}function P(){if(!l.defined(m)){m=!1;var e=/Firefox\/([\.0-9]+)/.exec(n.userAgent);null!==e&&(m=!0,A=C(e[1]))}return m}function k(){if(!l.defined(b)){var e=document.createElement("canvas");e.setAttribute("style","image-rendering: -moz-crisp-edges;image-rendering: pixelated;");var n=e.style.imageRendering;(b=l.defined(n)&&""!==n)&&(F=n)}return b}function N(){if(l.defined(E))return E.promise;E=l.when.defer(),q()&&(h=!1,E.resolve(h));var e=new Image;return e.onload=function(){h=0<e.width&&0<e.height,E.resolve(h)},e.onerror=function(){h=!1,E.resolve(h)},e.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",E.promise}Object.defineProperties(x,{element:{get:function(){if(x.supportsFullscreen())return document[w.fullscreenElement]}},changeEventName:{get:function(){if(x.supportsFullscreen())return w.fullscreenchange}},errorEventName:{get:function(){if(x.supportsFullscreen())return w.fullscreenerror}},enabled:{get:function(){if(x.supportsFullscreen())return document[w.fullscreenEnabled]}},fullscreen:{get:function(){if(x.supportsFullscreen())return null!==x.element}}}),x.supportsFullscreen=function(){if(l.defined(s))return s;s=!1;var e=document.body;if("function"==typeof e.requestFullscreen)return w.requestFullscreen="requestFullscreen",w.exitFullscreen="exitFullscreen",w.fullscreenEnabled="fullscreenEnabled",w.fullscreenElement="fullscreenElement",w.fullscreenchange="fullscreenchange",w.fullscreenerror="fullscreenerror",s=!0;for(var n,r=["webkit","moz","o","ms","khtml"],t=0,u=r.length;t<u;++t){var i=r[t];"function"==typeof e[n=i+"RequestFullscreen"]?(w.requestFullscreen=n,s=!0):"function"==typeof e[n=i+"RequestFullScreen"]&&(w.requestFullscreen=n,s=!0),n=i+"ExitFullscreen","function"==typeof document[n]?w.exitFullscreen=n:(n=i+"CancelFullScreen","function"==typeof document[n]&&(w.exitFullscreen=n)),n=i+"FullscreenEnabled",void 0!==document[n]?w.fullscreenEnabled=n:(n=i+"FullScreenEnabled",void 0!==document[n]&&(w.fullscreenEnabled=n)),n=i+"FullscreenElement",void 0!==document[n]?w.fullscreenElement=n:(n=i+"FullScreenElement",void 0!==document[n]&&(w.fullscreenElement=n)),n=i+"fullscreenchange",void 0!==document["on"+n]&&("ms"===i&&(n="MSFullscreenChange"),w.fullscreenchange=n),n=i+"fullscreenerror",void 0!==document["on"+n]&&("ms"===i&&(n="MSFullscreenError"),w.fullscreenerror=n)}return s},x.requestFullscreen=function(e,n){x.supportsFullscreen()&&e[w.requestFullscreen]({vrDisplay:n})},x.exitFullscreen=function(){x.supportsFullscreen()&&document[w.exitFullscreen]()},x._names=w,n="undefined"!=typeof navigator?navigator:{};var R=[];"undefined"!=typeof ArrayBuffer&&(R.push(Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array),"undefined"!=typeof Uint8ClampedArray&&R.push(Uint8ClampedArray),"undefined"!=typeof CanvasPixelArray&&R.push(CanvasPixelArray));var U={isChrome:S,chromeVersion:function(){return S()&&t},isSafari:I,safariVersion:function(){return I()&&i},isWebkit:V,webkitVersion:function(){return V()&&c},isInternetExplorer:W,internetExplorerVersion:function(){return W()&&d},isEdge:q,edgeVersion:function(){return q()&&p},isFirefox:P,firefoxVersion:function(){return P()&&A},isWindows:function(){return l.defined(v)||(v=/Windows/i.test(n.appVersion)),v},isNodeJs:function(){return l.defined(g)||(g="object"==typeof process&&"[object process]"===Object.prototype.toString.call(process)),g},hardwareConcurrency:l.defaultValue(n.hardwareConcurrency,3),supportsPointerEvents:function(){return l.defined(y)||(y=!P()&&"undefined"!=typeof PointerEvent&&(!l.defined(n.pointerEnabled)||n.pointerEnabled)),y},supportsImageRenderingPixelated:k,supportsWebP:N,supportsWebPSync:function(){return l.defined(E)||N(),h},imageRenderingValue:function(){return k()?F:void 0},typedArrayTypes:R,isPCBroswer:function(){var e=window.navigator.userAgent.toLowerCase(),n="ipad"==e.match(/ipad/i),r="iphone os"==e.match(/iphone os/i),t="midp"==e.match(/midp/i),u="rv:*******"==e.match(/rv:*******/i),i="ucweb"==e.match(/ucweb/i),l="android"==e.match(/android/i),s="windows ce"==e.match(/windows ce/i),o="windows mobile"==e.match(/windows mobile/i);return!(n||r||t||u||i||l||s||o)}};U.supportsFullscreen=function(){return x.supportsFullscreen()},U.supportsTypedArrays=function(){return"undefined"!=typeof ArrayBuffer},U.supportsWebWorkers=function(){return"undefined"!=typeof Worker},U.supportsWebAssembly=function(){return"undefined"!=typeof WebAssembly&&!U.isEdge()},U.supportsOffscreenCanvas=function(){return"undefined"!=typeof OffscreenCanvas&&!U.isEdge()},e.FeatureDetection=U});