<template>
  <el-table
    ref="table"
    style="width: 100%"
    :data="tableData"
    border
    :header-cell-style="headerCellStyle"
    :cell-style="cellStyle"
  >
    <el-table-column type="index" width="50" label="序号" align="center" />
    <el-table-column
      prop="deviceName"
      label="设备名称"
      align="center"
      min-width="150"
    />
    <el-table-column
      prop="deviceNum"
      label="设备编号"
      align="center"
      min-width="130"
    />
    <el-table-column
      prop="componentName"
      label="部件名称"
      align="center"
      min-width="180"
    />
    <el-table-column
      prop="errorType"
      label="故障类型"
      align="center"
      min-width="180"
    />
    <el-table-column
      prop="errorTime"
      label="故障时间"
      align="center"
      min-width="130"
    />
    <el-table-column
      prop="possibleReason"
      label="可能原因"
      align="center"
      min-width="130"
    />
    <el-table-column
      prop="collecterId"
      label="采集盒子ID"
      align="center"
      min-width="130"
    />
    <el-table-column label="机械类型" align="center" min-width="130">
      <template slot-scope="scope">
        <span>{{ scope.row.deviceType }}</span>
      </template>
    </el-table-column>
    <el-table-column
      prop="manufacturer"
      label="制造厂家"
      align="center"
      min-width="220"
    />
    <el-table-column
      prop="projectSite"
      label="工点"
      align="center"
      min-width="150"
    />
    <el-table-column
      prop="reportPerson"
      label="申报人"
      align="center"
      min-width="150"
    />
    <el-table-column
      prop="ReportTime"
      label="申报时间"
      align="center"
      min-width="150"
    />
    <el-table-column
      prop="remark"
      label="备注"
      align="center"
      min-width="150"
    />
    <el-table-column label="操作" width="100" align="center" fixed="right">
      <template slot-scope="scope">
        <el-button
          @click="handleOperate(scope.row)"
          type="text"
          size="small"
          class="table-edit"
          style="margin-right: 15%"
          >编辑</el-button
        >
        <el-popconfirm
          title="确定删除吗？"
          @confirm="handleDelete(scope.row.id, '/equip/ledger/deleteById')"
        >
          <el-button slot="reference" type="text" size="small"> 删除</el-button>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: "errRecords",
  props: ["tableData"],
  //   watch: {
  //     tableData: {
  //       handler: function () {
  //         //this.$forceUpdate();
  //       },
  //     },
  //   },
};
</script>
