/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
var BASIS=function(){var Dr="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return"undefined"!=typeof __filename&&(Dr=Dr||__filename),function(e){var t,i,a=void 0!==(e=e||{})?e:{};a.ready=new Promise(function(e,r){t=e,readyPromiseRejectza=r});var r,n={};for(r in a)a.hasOwnProperty(r)&&(n[r]=a[r]);var o,u,s=[],c=!1,f=!1;c="object"==typeof window,f="function"==typeof importScripts,o="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,u=!c&&!o&&!f;var l,p,d,h,v,y="";o?(y=f?require("path").dirname(y)+"/":__dirname+"/",l=function(e,r){return h||(h=require("fs")),v||(v=require("path")),e=v.normalize(e),h.readFileSync(e,r?null:"utf8")},d=function(e){var r=l(e,!0);return r.buffer||(r=new Uint8Array(r)),C(r.buffer),r},1<process.argv.length&&process.argv[1].replace(/\\/g,"/"),s=process.argv.slice(2),process.on("uncaughtException",function(e){if(!(e instanceof function(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}))throw e}),process.on("unhandledRejection",Q),function(e){process.exit(e)},a.inspect=function(){return"[Emscripten Module object]"}):u?("undefined"!=typeof read&&(l=function(e){return read(e)}),d=function(e){var r;return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(C("object"==typeof(r=read(e,"binary"))),r)},"undefined"!=typeof scriptArgs?s=scriptArgs:void 0!==arguments&&(s=arguments),"function"==typeof quit&&function(e){quit(e)},"undefined"!=typeof print&&("undefined"==typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!=typeof printErr?printErr:print)):(c||f)&&(f?y=self.location.href:"undefined"!=typeof document&&document.currentScript&&(y=document.currentScript.src),Dr&&(y=Dr),y=0!==y.indexOf("blob:")?y.substr(0,y.lastIndexOf("/")+1):"",l=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},f&&(d=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),p=function(e,r,t){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?r(n.response):t()},n.onerror=t,n.send(null)});var m=a.print||console.log.bind(console),g=a.printErr||console.warn.bind(console);for(r in n)n.hasOwnProperty(r)&&(a[r]=n[r]);n=null,a.arguments&&(s=a.arguments),a.thisProgram&&a.thisProgram,a.quit&&a.quit;var w;a.wasmBinary&&(w=a.wasmBinary);var T;a.noExitRuntime;"object"!=typeof WebAssembly&&Q("no native wasm support detected");var b=!1;function C(e,r){e||Q("Assertion failed: "+r)}var $="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function P(e,r,t){for(var n=r+t,o=r;e[o]&&!(n<=o);)++o;if(16<o-r&&e.subarray&&$)return $.decode(e.subarray(r,o));for(var i="";r<o;){var a=e[r++];if(128&a){var u=63&e[r++];if(192!=(224&a)){var s=63&e[r++];if((a=224==(240&a)?(15&a)<<12|u<<6|s:(7&a)<<18|u<<12|s<<6|63&e[r++])<65536)i+=String.fromCharCode(a);else{var c=a-65536;i+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else i+=String.fromCharCode((31&a)<<6|u)}else i+=String.fromCharCode(a)}return i}function A(e,r){return e?P(W,e,r):""}var _,S,W,E,F,k,O,j,R,x="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function I(e,r){for(var t=e,n=t>>1,o=n+r/2;!(o<=n)&&F[n];)++n;if(32<(t=n<<1)-e&&x)return x.decode(W.subarray(e,t));for(var i="",a=0;!(r/2<=a);++a){var u=E[e+2*a>>1];if(0==u)break;i+=String.fromCharCode(u)}return i}function D(e,r,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var n=r,o=(t-=2)<2*e.length?t/2:e.length,i=0;i<o;++i){var a=e.charCodeAt(i);E[r>>1]=a,r+=2}return E[r>>1]=0,r-n}function U(e){return 2*e.length}function B(e,r){for(var t=0,n="";!(r/4<=t);){var o=k[e+4*t>>2];if(0==o)break;if(++t,65536<=o){var i=o-65536;n+=String.fromCharCode(55296|i>>10,56320|1023&i)}else n+=String.fromCharCode(o)}return n}function M(e,r,t){if(void 0===t&&(t=2147483647),t<4)return 0;for(var n=r,o=n+t-4,i=0;i<e.length;++i){var a=e.charCodeAt(i);if(55296<=a&&a<=57343)a=65536+((1023&a)<<10)|1023&e.charCodeAt(++i);if(k[r>>2]=a,o<(r+=4)+4)break}return k[r>>2]=0,r-n}function V(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);55296<=n&&n<=57343&&++t,r+=4}return r}function q(e){_=e,a.HEAP8=S=new Int8Array(e),a.HEAP16=E=new Int16Array(e),a.HEAP32=k=new Int32Array(e),a.HEAPU8=W=new Uint8Array(e),a.HEAPU16=F=new Uint16Array(e),a.HEAPU32=O=new Uint32Array(e),a.HEAPF32=j=new Float32Array(e),a.HEAPF64=R=new Float64Array(e)}a.INITIAL_MEMORY;var H,z=[],N=[],G=[],L=[];var X=0,J=null,K=null;function Q(e){a.onAbort&&a.onAbort(e),g(e+=""),b=!0,1,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.";var r=new WebAssembly.RuntimeError(e);throw i(r),r}function Y(e,r){return String.prototype.startsWith?e.startsWith(r):0===e.indexOf(r)}a.preloadedImages={},a.preloadedAudios={};var Z="data:application/octet-stream;base64,";function ee(e){return Y(e,Z)}var re="file://";function te(e){return Y(e,re)}var ne,oe="basis_transcoder.wasm";function ie(e){try{if(e==oe&&w)return new Uint8Array(w);if(d)return d(e);throw"both async and sync fetching of the wasm failed"}catch(e){Q(e)}}function ae(e){for(;0<e.length;){var r=e.shift();if("function"!=typeof r){var t=r.func;"number"==typeof t?void 0===r.arg?H.get(t)():H.get(t)(r.arg):t(void 0===r.arg?null:r.arg)}else r(a)}}ee(oe)||(ne=oe,oe=a.locateFile?a.locateFile(ne,y):y+ne);var ue={};function se(e){for(;e.length;){var r=e.pop();e.pop()(r)}}function ce(e){return this.fromWireType(O[e>>2])}var fe={},le={},pe={},de=48,he=57;function ve(e){if(void 0===e)return"_unknown";var r=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return de<=r&&r<=he?"_"+e:e}function ye(e,r){return e=ve(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function me(e,t){var r=ye(t,function(e){this.name=t,this.message=e;var r=new Error(e).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))});return r.prototype=Object.create(e.prototype),(r.prototype.constructor=r).prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var ge=void 0;function we(e){throw new ge(e)}function Te(n,r,o){function t(e){var r=o(e);r.length!==n.length&&we("Mismatched type converter count");for(var t=0;t<n.length;++t)_e(n[t],r[t])}n.forEach(function(e){pe[e]=r});var i=new Array(r.length),a=[],u=0;r.forEach(function(e,r){le.hasOwnProperty(e)?i[r]=le[e]:(a.push(e),fe.hasOwnProperty(e)||(fe[e]=[]),fe[e].push(function(){i[r]=le[e],++u===a.length&&t(i)}))}),0===a.length&&t(i)}function be(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var Ce=void 0;function $e(e){for(var r="",t=e;W[t];)r+=Ce[W[t++]];return r}var Pe=void 0;function Ae(e){throw new Pe(e)}function _e(e,r,t){if(t=t||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=r.name;if(e||Ae('type "'+n+'" must have a positive integer typeid pointer'),le.hasOwnProperty(e)){if(t.ignoreDuplicateRegistrations)return;Ae("Cannot register type '"+n+"' twice")}if(le[e]=r,delete pe[e],fe.hasOwnProperty(e)){var o=fe[e];delete fe[e],o.forEach(function(e){e()})}}function Se(e){if(!(this instanceof Me))return!1;if(!(e instanceof Me))return!1;for(var r=this.$$.ptrType.registeredClass,t=this.$$.ptr,n=e.$$.ptrType.registeredClass,o=e.$$.ptr;r.baseClass;)t=r.upcast(t),r=r.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return r===n&&t===o}function We(e){Ae(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Ee=!1;function Fe(e){}function ke(e){var r;e.count.value-=1,0===e.count.value&&((r=e).smartPtr?r.smartPtrType.rawDestructor(r.smartPtr):r.ptrType.registeredClass.rawDestructor(r.ptr))}function Oe(e){return"undefined"==typeof FinalizationGroup?(Oe=function(e){return e},e):(Ee=new FinalizationGroup(function(e){for(var r=e.next();!r.done;r=e.next()){var t=r.value;t.ptr?ke(t):console.warn("object already deleted: "+t.ptr)}}),Fe=function(e){Ee.unregister(e.$$)},(Oe=function(e){return Ee.register(e,e.$$,e.$$),e})(e))}function je(){if(this.$$.ptr||We(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,r=Oe(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return r.$$.count.value+=1,r.$$.deleteScheduled=!1,r}function Re(){this.$$.ptr||We(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Ae("Object already scheduled for deletion"),Fe(this),ke(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function xe(){return!this.$$.ptr}var Ie=void 0,De=[];function Ue(){for(;De.length;){var e=De.pop();e.$$.deleteScheduled=!1,e.delete()}}function Be(){return this.$$.ptr||We(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Ae("Object already scheduled for deletion"),De.push(this),1===De.length&&Ie&&Ie(Ue),this.$$.deleteScheduled=!0,this}function Me(){}var Ve={};function qe(e,r,t){if(void 0===e[r].overloadTable){var n=e[r];e[r]=function(){return e[r].overloadTable.hasOwnProperty(arguments.length)||Ae("Function '"+t+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[r].overloadTable+")!"),e[r].overloadTable[arguments.length].apply(this,arguments)},e[r].overloadTable=[],e[r].overloadTable[n.argCount]=n}}function He(e,r,t){a.hasOwnProperty(e)?((void 0===t||void 0!==a[e].overloadTable&&void 0!==a[e].overloadTable[t])&&Ae("Cannot register public name '"+e+"' twice"),qe(a,e,e),a.hasOwnProperty(t)&&Ae("Cannot register multiple overloads of a function with the same number of arguments ("+t+")!"),a[e].overloadTable[t]=r):(a[e]=r,void 0!==t&&(a[e].numArguments=t))}function ze(e,r,t,n,o,i,a,u){this.name=e,this.constructor=r,this.instancePrototype=t,this.rawDestructor=n,this.baseClass=o,this.getActualType=i,this.upcast=a,this.downcast=u,this.pureVirtualFunctions=[]}function Ne(e,r,t){for(;r!==t;)r.upcast||Ae("Expected null or instance of "+t.name+", got an instance of "+r.name),e=r.upcast(e),r=r.baseClass;return e}function Ge(e,r){if(null===r)return this.isReference&&Ae("null is not a valid "+this.name),0;r.$$||Ae('Cannot pass "'+Cr(r)+'" as a '+this.name),r.$$.ptr||Ae("Cannot pass deleted object as a pointer of type "+this.name);var t=r.$$.ptrType.registeredClass;return Ne(r.$$.ptr,t,this.registeredClass)}function Le(e,r){var t;if(null===r)return this.isReference&&Ae("null is not a valid "+this.name),this.isSmartPointer?(t=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,t),t):0;r.$$||Ae('Cannot pass "'+Cr(r)+'" as a '+this.name),r.$$.ptr||Ae("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&r.$$.ptrType.isConst&&Ae("Cannot convert argument of type "+(r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name)+" to parameter type "+this.name);var n=r.$$.ptrType.registeredClass;if(t=Ne(r.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===r.$$.smartPtr&&Ae("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:r.$$.smartPtrType===this?t=r.$$.smartPtr:Ae("Cannot convert argument of type "+(r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:t=r.$$.smartPtr;break;case 2:if(r.$$.smartPtrType===this)t=r.$$.smartPtr;else{var o=r.clone();t=this.rawShare(t,Tr(function(){o.delete()})),null!==e&&e.push(this.rawDestructor,t)}break;default:Ae("Unsupporting sharing policy")}return t}function Xe(e,r){if(null===r)return this.isReference&&Ae("null is not a valid "+this.name),0;r.$$||Ae('Cannot pass "'+Cr(r)+'" as a '+this.name),r.$$.ptr||Ae("Cannot pass deleted object as a pointer of type "+this.name),r.$$.ptrType.isConst&&Ae("Cannot convert argument of type "+r.$$.ptrType.name+" to parameter type "+this.name);var t=r.$$.ptrType.registeredClass;return Ne(r.$$.ptr,t,this.registeredClass)}function Je(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function Ke(e){this.rawDestructor&&this.rawDestructor(e)}function Qe(e){null!==e&&e.delete()}function Ye(){return Object.keys(rr).length}function Ze(){var e=[];for(var r in rr)rr.hasOwnProperty(r)&&e.push(rr[r]);return e}function er(e){Ie=e,De.length&&Ie&&Ie(Ue)}var rr={};function tr(e,r){return r=function(e,r){for(void 0===r&&Ae("ptr should not be undefined");e.baseClass;)r=e.upcast(r),e=e.baseClass;return r}(e,r),rr[r]}function nr(e,r){return r.ptrType&&r.ptr||we("makeClassHandle requires ptr and ptrType"),!!r.smartPtrType!==!!r.smartPtr&&we("Both smartPtrType and smartPtr must be specified"),r.count={value:1},Oe(Object.create(e,{$$:{value:r}}))}function or(e){var r=this.getPointee(e);if(!r)return this.destructor(e),null;var t=tr(this.registeredClass,r);if(void 0!==t){if(0===t.$$.count.value)return t.$$.ptr=r,t.$$.smartPtr=e,t.clone();var n=t.clone();return this.destructor(e),n}function o(){return this.isSmartPointer?nr(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:r,smartPtrType:this,smartPtr:e}):nr(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var i,a=this.registeredClass.getActualType(r),u=Ve[a];if(!u)return o.call(this);i=this.isConst?u.constPointerType:u.pointerType;var s=function e(r,t,n){if(t===n)return r;if(void 0===n.baseClass)return null;var o=e(r,t,n.baseClass);return null===o?null:n.downcast(o)}(r,this.registeredClass,i.registeredClass);return null===s?o.call(this):this.isSmartPointer?nr(i.registeredClass.instancePrototype,{ptrType:i,ptr:s,smartPtrType:this,smartPtr:e}):nr(i.registeredClass.instancePrototype,{ptrType:i,ptr:s})}function ir(e,r,t,n,o,i,a,u,s,c,f){this.name=e,this.registeredClass=r,this.isReference=t,this.isConst=n,this.isSmartPointer=o,this.pointeeType=i,this.sharingPolicy=a,this.rawGetPointee=u,this.rawConstructor=s,this.rawShare=c,this.rawDestructor=f,o||void 0!==r.baseClass?this.toWireType=Le:this.destructorFunction=(this.toWireType=n?Ge:Xe,null)}function ar(e,r,t){a.hasOwnProperty(e)||we("Replacing nonexistant public symbol"),void 0!==a[e].overloadTable&&void 0!==t?a[e].overloadTable[t]=r:(a[e]=r,a[e].argCount=t)}function ur(e,r,t){return-1!=e.indexOf("j")?(n=r,o=t,i=a["dynCall_"+e],o&&o.length?i.apply(null,[n].concat(o)):i.call(null,n)):H.get(r).apply(null,t);var n,o,i}function sr(e,r){var t,n,o,i=-1!=(e=$e(e)).indexOf("j")?(t=e,n=r,o=[],function(){o.length=arguments.length;for(var e=0;e<arguments.length;e++)o[e]=arguments[e];return ur(t,n,o)}):H.get(r);return"function"!=typeof i&&Ae("unknown function pointer with signature "+e+": "+r),i}var cr=void 0;function fr(e){var r=xr(e),t=$e(r);return Rr(r),t}function lr(e,r){var t=[],n={};throw r.forEach(function e(r){n[r]||le[r]||(pe[r]?pe[r].forEach(e):(t.push(r),n[r]=!0))}),new cr(e+": "+t.map(fr).join([", "]))}function pr(e,r){for(var t=[],n=0;n<e;n++)t.push(k[(r>>2)+n]);return t}function dr(e,r){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var t=ye(e.name||"unknownFunctionName",function(){});t.prototype=e.prototype;var n=new t,o=e.apply(n,r);return o instanceof Object?o:n}function hr(e,r,t,n,o){var i=r.length;i<2&&Ae("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var a=null!==r[1]&&null!==t,u=!1,s=1;s<r.length;++s)if(null!==r[s]&&void 0===r[s].destructorFunction){u=!0;break}var c="void"!==r[0].name,f="",l="";for(s=0;s<i-2;++s)f+=(0!==s?", ":"")+"arg"+s,l+=(0!==s?", ":"")+"arg"+s+"Wired";var p="return function "+ve(e)+"("+f+") {\nif (arguments.length !== "+(i-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(i-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var d=u?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[Ae,n,o,se,r[0],r[1]];a&&(p+="var thisWired = classParam.toWireType("+d+", this);\n");for(s=0;s<i-2;++s)p+="var arg"+s+"Wired = argType"+s+".toWireType("+d+", arg"+s+"); // "+r[s+2].name+"\n",h.push("argType"+s),v.push(r[s+2]);if(a&&(l="thisWired"+(0<l.length?", ":"")+l),p+=(c?"var rv = ":"")+"invoker(fn"+(0<l.length?", ":"")+l+");\n",u)p+="runDestructors(destructors);\n";else for(s=a?1:2;s<r.length;++s){var y=1===s?"thisWired":"arg"+(s-2)+"Wired";null!==r[s].destructorFunction&&(p+=y+"_dtor("+y+"); // "+r[s].name+"\n",h.push(y+"_dtor"),v.push(r[s].destructorFunction))}return c&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",h.push(p),dr(Function,h).apply(null,v)}var vr=[],yr=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function mr(e){4<e&&0==--yr[e].refcount&&(yr[e]=void 0,vr.push(e))}function gr(){for(var e=0,r=5;r<yr.length;++r)void 0!==yr[r]&&++e;return e}function wr(){for(var e=5;e<yr.length;++e)if(void 0!==yr[e])return yr[e];return null}function Tr(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=vr.length?vr.pop():yr.length;return yr[r]={refcount:1,value:e},r}}function br(e,r){var t=le[e];return void 0===t&&Ae(r+" has unknown type "+fr(e)),t}function Cr(e){if(null===e)return"null";var r=typeof e;return"object"===r||"array"===r||"function"===r?e.toString():""+e}function $r(e){return e||Ae("Cannot use deleted val. handle = "+e),yr[e].value}var Pr={};function Ar(e){var r=Pr[e];return void 0===r?$e(e):r}var _r=[];function Sr(){return"object"==typeof globalThis?globalThis:Function("return this")()}var Wr={};function Er(e){try{return T.grow(e-_.byteLength+65535>>>16),q(T.buffer),1}catch(e){}}var Fr={mappings:{},buffers:[null,[],[]],printChar:function(e,r){var t=Fr.buffers[e];0===r||10===r?((1===e?m:g)(P(t,0)),t.length=0):t.push(r)},varargs:void 0,get:function(){return Fr.varargs+=4,k[Fr.varargs-4>>2]},getStr:function(e){return A(e)},get64:function(e,r){return e}};ge=a.InternalError=me(Error,"InternalError"),function(){for(var e=new Array(256),r=0;r<256;++r)e[r]=String.fromCharCode(r);Ce=e}(),Pe=a.BindingError=me(Error,"BindingError"),Me.prototype.isAliasOf=Se,Me.prototype.clone=je,Me.prototype.delete=Re,Me.prototype.isDeleted=xe,Me.prototype.deleteLater=Be,ir.prototype.getPointee=Je,ir.prototype.destructor=Ke,ir.prototype.argPackAdvance=8,ir.prototype.readValueFromPointer=ce,ir.prototype.deleteObject=Qe,ir.prototype.fromWireType=or,a.getInheritedInstanceCount=Ye,a.getLiveInheritedInstances=Ze,a.flushPendingDeletes=Ue,a.setDelayFunction=er,cr=a.UnboundTypeError=me(Error,"UnboundTypeError"),a.count_emval_handles=gr,a.get_first_emval=wr;var kr,Or={t:function(e){var r=ue[e];delete ue[e];var o=r.rawConstructor,i=r.rawDestructor,l=r.fields;Te([e],l.map(function(e){return e.getterReturnType}).concat(l.map(function(e){return e.setterArgumentType})),function(c){var f={};return l.forEach(function(e,r){var t=e.fieldName,n=c[r],o=e.getter,i=e.getterContext,a=c[r+l.length],u=e.setter,s=e.setterContext;f[t]={read:function(e){return n.fromWireType(o(i,e))},write:function(e,r){var t=[];u(s,e,a.toWireType(t,r)),se(t)}}}),[{name:r.name,fromWireType:function(e){var r={};for(var t in f)r[t]=f[t].read(e);return i(e),r},toWireType:function(e,r){for(var t in f)if(!(t in r))throw new TypeError('Missing field:  "'+t+'"');var n=o();for(t in f)f[t].write(n,r[t]);return null!==e&&e.push(i,n),n},argPackAdvance:8,readValueFromPointer:ce,destructorFunction:i}]})},I:function(e,t,n,o,i){var a=be(n);_e(e,{name:t=$e(t),fromWireType:function(e){return!!e},toWireType:function(e,r){return r?o:i},argPackAdvance:8,readValueFromPointer:function(e){var r;if(1===n)r=S;else if(2===n)r=E;else{if(4!==n)throw new TypeError("Unknown boolean type size: "+t);r=k}return this.fromWireType(r[e>>a])},destructorFunction:null})},x:function(c,e,r,f,t,l,n,p,o,d,h,i,v){h=$e(h),l=sr(t,l),p&&(p=sr(n,p)),d&&(d=sr(o,d)),v=sr(i,v);var y=ve(h);He(y,function(){lr("Cannot construct "+h+" due to unbound types",[f])}),Te([c,e,r],f?[f]:[],function(e){var r,t;e=e[0],t=f?(r=e.registeredClass).instancePrototype:Me.prototype;var n=ye(y,function(){if(Object.getPrototypeOf(this)!==o)throw new Pe("Use 'new' to construct "+h);if(void 0===i.constructor_body)throw new Pe(h+" has no accessible constructor");var e=i.constructor_body[arguments.length];if(void 0===e)throw new Pe("Tried to invoke ctor of "+h+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(i.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)}),o=Object.create(t,{constructor:{value:n}});n.prototype=o;var i=new ze(h,n,o,v,r,l,p,d),a=new ir(h,i,!0,!1,!1),u=new ir(h+"*",i,!1,!1,!1),s=new ir(h+" const*",i,!1,!0,!1);return Ve[c]={pointerType:u,constPointerType:s},ar(y,n),[a,u,s]})},w:function(e,o,r,t,i,n){C(0<o);var a=pr(o,r);i=sr(t,i);var u=[n],s=[];Te([],[e],function(e){var n="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[o-1])throw new Pe("Cannot register multiple constructors with identical number of parameters ("+(o-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[o-1]=function(){lr("Cannot construct "+e.name+" due to unbound types",a)},Te([],a,function(t){return e.registeredClass.constructor_body[o-1]=function(){arguments.length!==o-1&&Ae(n+" called with "+arguments.length+" arguments, expected "+(o-1)),s.length=0,u.length=o;for(var e=1;e<o;++e)u[e]=t[e].toWireType(s,arguments[e-1]);var r=i.apply(null,u);return se(s),t[0].fromWireType(r)},[]}),[]})},d:function(e,i,a,r,t,u,s,c){var f=pr(a,r);i=$e(i),u=sr(t,u),Te([],[e],function(t){var n=(t=t[0]).name+"."+i;function e(){lr("Cannot call "+n+" due to unbound types",f)}c&&t.registeredClass.pureVirtualFunctions.push(i);var o=t.registeredClass.instancePrototype,r=o[i];return void 0===r||void 0===r.overloadTable&&r.className!==t.name&&r.argCount===a-2?(e.argCount=a-2,e.className=t.name,o[i]=e):(qe(o,i,n),o[i].overloadTable[a-2]=e),Te([],f,function(e){var r=hr(n,e,t,u,s);return void 0===o[i].overloadTable?(r.argCount=a-2,o[i]=r):o[i].overloadTable[a-2]=r,[]}),[]})},k:function(r,e,t){r=$e(r),Te([],[e],function(e){return e=e[0],a[r]=e.fromWireType(t),[]})},H:function(e,r){_e(e,{name:r=$e(r),fromWireType:function(e){var r=yr[e].value;return mr(e),r},toWireType:function(e,r){return Tr(r)},argPackAdvance:8,readValueFromPointer:ce,destructorFunction:null})},n:function(e,r,t,n){var o=be(t);function i(){}r=$e(r),i.values={},_e(e,{name:r,constructor:i,fromWireType:function(e){return this.constructor.values[e]},toWireType:function(e,r){return r.value},argPackAdvance:8,readValueFromPointer:function(e,r,t){switch(r){case 0:return function(e){var r=t?S:W;return this.fromWireType(r[e])};case 1:return function(e){var r=t?E:F;return this.fromWireType(r[e>>1])};case 2:return function(e){var r=t?k:O;return this.fromWireType(r[e>>2])};default:throw new TypeError("Unknown integer type: "+e)}}(r,o,n),destructorFunction:null}),He(r,i)},a:function(e,r,t){var n=br(e,"enum");r=$e(r);var o=n.constructor,i=Object.create(n.constructor.prototype,{value:{value:t},constructor:{value:ye(n.name+"_"+r,function(){})}});o.values[t]=i,o[r]=i},A:function(e,r,t){var n=be(t);_e(e,{name:r=$e(r),fromWireType:function(e){return e},toWireType:function(e,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+Cr(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:function(e,r){switch(r){case 2:return function(e){return this.fromWireType(j[e>>2])};case 3:return function(e){return this.fromWireType(R[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}(r,n),destructorFunction:null})},i:function(t,n,e,r,o,i){var a=pr(n,e);t=$e(t),o=sr(r,o),He(t,function(){lr("Cannot call "+t+" due to unbound types",a)},n-1),Te([],a,function(e){var r=[e[0],null].concat(e.slice(1));return ar(t,hr(t,r,null,o,i),n-1),[]})},j:function(e,t,r,n,o){t=$e(t),-1===o&&(o=4294967295);var i=be(r),a=function(e){return e};if(0===n){var u=32-8*r;a=function(e){return e<<u>>>u}}var s=-1!=t.indexOf("unsigned");_e(e,{name:t,fromWireType:a,toWireType:function(e,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+Cr(r)+'" to '+this.name);if(r<n||o<r)throw new TypeError('Passing a number "'+Cr(r)+'" from JS side to C/C++ side to an argument of type "'+t+'", which is outside the valid range ['+n+", "+o+"]!");return s?r>>>0:0|r},argPackAdvance:8,readValueFromPointer:function(e,r,t){switch(r){case 0:return t?function(e){return S[e]}:function(e){return W[e]};case 1:return t?function(e){return E[e>>1]}:function(e){return F[e>>1]};case 2:return t?function(e){return k[e>>2]}:function(e){return O[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}(t,i,0!==n),destructorFunction:null})},h:function(e,r,t){var o=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function n(e){var r=O,t=r[e>>=2],n=r[e+1];return new o(_,n,t)}_e(e,{name:t=$e(t),fromWireType:n,argPackAdvance:8,readValueFromPointer:n},{ignoreDuplicateRegistrations:!0})},B:function(e,r){var s="std::string"===(r=$e(r));_e(e,{name:r,fromWireType:function(e){var r,t=O[e>>2];if(s)for(var n=e+4,o=0;o<=t;++o){var i=e+4+o;if(o==t||0==W[i]){var a=A(n,i-n);void 0===r?r=a:(r+=String.fromCharCode(0),r+=a),n=i+1}}else{var u=new Array(t);for(o=0;o<t;++o)u[o]=String.fromCharCode(W[e+4+o]);r=u.join("")}return Rr(e),r},toWireType:function(e,r){r instanceof ArrayBuffer&&(r=new Uint8Array(r));var t="string"==typeof r;t||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||Ae("Cannot pass non-string to std::string");var n=(s&&t?function(){return function(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);55296<=n&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++t)),n<=127?++r:r+=n<=2047?2:n<=65535?3:4}return r}(r)}:function(){return r.length})(),o=jr(4+n+1);if(O[o>>2]=n,s&&t)!function(e,r,t,n){if(0<n){for(var o=t+n-1,i=0;i<e.length;++i){var a=e.charCodeAt(i);if(55296<=a&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++i)),a<=127){if(o<=t)break;r[t++]=a}else if(a<=2047){if(o<=t+1)break;r[t++]=192|a>>6,r[t++]=128|63&a}else if(a<=65535){if(o<=t+2)break;r[t++]=224|a>>12,r[t++]=128|a>>6&63,r[t++]=128|63&a}else{if(o<=t+3)break;r[t++]=240|a>>18,r[t++]=128|a>>12&63,r[t++]=128|a>>6&63,r[t++]=128|63&a}}r[t]=0}}(r,W,o+4,n+1);else if(t)for(var i=0;i<n;++i){var a=r.charCodeAt(i);255<a&&(Rr(o),Ae("String has UTF-16 code units that do not fit in 8 bits")),W[o+4+i]=a}else for(i=0;i<n;++i)W[o+4+i]=r[i];return null!==e&&e.push(Rr,o),o},argPackAdvance:8,readValueFromPointer:ce,destructorFunction:function(e){Rr(e)}})},v:function(e,s,o){var c,i,f,a,l;o=$e(o),2===s?(c=I,i=D,a=U,f=function(){return F},l=1):4===s&&(c=B,i=M,a=V,f=function(){return O},l=2),_e(e,{name:o,fromWireType:function(e){for(var r,t=O[e>>2],n=f(),o=e+4,i=0;i<=t;++i){var a=e+4+i*s;if(i==t||0==n[a>>l]){var u=c(o,a-o);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),o=a+s}}return Rr(e),r},toWireType:function(e,r){"string"!=typeof r&&Ae("Cannot pass non-string to C++ string type "+o);var t=a(r),n=jr(4+t+s);return O[n>>2]=t>>l,i(r,n+4,t+s),null!==e&&e.push(Rr,n),n},argPackAdvance:8,readValueFromPointer:ce,destructorFunction:function(e){Rr(e)}})},u:function(e,r,t,n,o,i){ue[e]={name:$e(r),rawConstructor:sr(t,n),rawDestructor:sr(o,i),fields:[]}},c:function(e,r,t,n,o,i,a,u,s,c){ue[e].fields.push({fieldName:$e(r),getterReturnType:t,getter:sr(n,o),getterContext:i,setterArgumentType:a,setter:sr(u,s),setterContext:c})},J:function(e,r){_e(e,{isVoid:!0,name:r=$e(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,r){}})},m:function(e,r,t){e=$r(e),r=br(r,"emval::as");var n=[],o=Tr(n);return k[t>>2]=o,r.toWireType(n,e)},s:function(e,r,t,n){(e=_r[e])(r=$r(r),t=Ar(t),null,n)},b:mr,y:function(e){return 0===e?Tr(Sr()):(e=Ar(e),Tr(Sr()[e]))},p:function(e,r){for(var t=function(e,r){for(var t=new Array(e),n=0;n<e;++n)t[n]=br(k[(r>>2)+n],"parameter "+n);return t}(e,r),n=t[0],o=n.name+"_$"+t.slice(1).map(function(e){return e.name}).join("_")+"$",i=["retType"],a=[n],u="",s=0;s<e-1;++s)u+=(0!==s?", ":"")+"arg"+s,i.push("argType"+s),a.push(t[1+s]);var c="return function "+ve("methodCaller_"+o)+"(handle, name, destructors, args) {\n",f=0;for(s=0;s<e-1;++s)c+="    var arg"+s+" = argType"+s+".readValueFromPointer(args"+(f?"+"+f:"")+");\n",f+=t[s+1].argPackAdvance;for(c+="    var rv = handle[name]("+u+");\n",s=0;s<e-1;++s)t[s+1].deleteObject&&(c+="    argType"+s+".deleteObject(arg"+s+");\n");n.isVoid||(c+="    return retType.toWireType(destructors, rv);\n"),c+="};\n",i.push(c);var l,p,d=dr(Function,i).apply(null,a);return l=d,p=_r.length,_r.push(l),p},r:function(e){return e=Ar(e),Tr(a[e])},e:function(e,r){return Tr((e=$r(e))[r=$r(r)])},g:function(e){4<e&&(yr[e].refcount+=1)},q:function(e,r,t,n){e=$r(e);var o=Wr[r];return o||(o=function(e){for(var r="",t=0;t<e;++t)r+=(0!==t?", ":"")+"arg"+t;var n="return function emval_allocator_"+e+"(constructor, argTypes, args) {\n";for(t=0;t<e;++t)n+="var argType"+t+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+t+'], "parameter '+t+'");\nvar arg'+t+" = argType"+t+".readValueFromPointer(args);\nargs += argType"+t+"['argPackAdvance'];\n";return n+="var obj = new constructor("+r+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",n)(br,a,Tr)}(r),Wr[r]=o),o(e,t,n)},f:function(e){return Tr(Ar(e))},l:function(e){se(yr[e].value),mr(e)},o:function(){Q()},E:function(e,r,t){W.copyWithin(e,r,r+t)},F:function(e){var r,t,n=W.length,o=2147483648;if(o<(e>>>=0))return!1;for(var i=1;i<=4;i*=2){var a=n*(1+.2/i);if(a=Math.min(a,e+100663296),Er(Math.min(o,(0<(r=Math.max(e,a))%(t=65536)&&(r+=t-r%t),r))))return!0}return!1},G:function(e){return 0},C:function(e,r,t,n,o){},z:function(e,r,t,n){for(var o=0,i=0;i<t;i++){for(var a=k[r+8*i>>2],u=k[r+(8*i+4)>>2],s=0;s<u;s++)Fr.printChar(e,W[a+s]);o+=u}return k[n>>2]=o,0},D:function(e){0|e}},jr=(function(){var r={a:Or};function t(e,r){var t,n=e.exports;a.asm=n,q((T=a.asm.K).buffer),H=a.asm.O,t=a.asm.L,N.unshift(t),function(e){if(X--,a.monitorRunDependencies&&a.monitorRunDependencies(X),0==X&&(null!==J&&(clearInterval(J),J=null),K)){var r=K;K=null,r()}}()}function n(e){t(e.instance)}function o(e){return function(){if(!w&&(c||f)){if("function"==typeof fetch&&!te(oe))return fetch(oe,{credentials:"same-origin"}).then(function(e){if(!e.ok)throw"failed to load wasm binary file at '"+oe+"'";return e.arrayBuffer()}).catch(function(){return ie(oe)});if(p)return new Promise(function(r,e){p(oe,function(e){r(new Uint8Array(e))},e)})}return Promise.resolve().then(function(){return ie(oe)})}().then(function(e){return WebAssembly.instantiate(e,r)}).then(e,function(e){g("failed to asynchronously prepare wasm: "+e),Q(e)})}if(X++,a.monitorRunDependencies&&a.monitorRunDependencies(X),a.instantiateWasm)try{return a.instantiateWasm(r,t)}catch(e){return g("Module.instantiateWasm callback failed with error: "+e)}(w||"function"!=typeof WebAssembly.instantiateStreaming||ee(oe)||te(oe)||"function"!=typeof fetch?o(n):fetch(oe,{credentials:"same-origin"}).then(function(e){return WebAssembly.instantiateStreaming(e,r).then(n,function(e){return g("wasm streaming compile failed: "+e),g("falling back to ArrayBuffer instantiation"),o(n)})})).catch(i)}(),a.___wasm_call_ctors=function(){return(a.___wasm_call_ctors=a.asm.L).apply(null,arguments)},a._malloc=function(){return(jr=a._malloc=a.asm.M).apply(null,arguments)}),Rr=a._free=function(){return(Rr=a._free=a.asm.N).apply(null,arguments)},xr=a.___getTypeName=function(){return(xr=a.___getTypeName=a.asm.P).apply(null,arguments)};a.___embind_register_native_and_builtin_types=function(){return(a.___embind_register_native_and_builtin_types=a.asm.Q).apply(null,arguments)},a.dynCall_jiji=function(){return(a.dynCall_jiji=a.asm.R).apply(null,arguments)};function Ir(e){function r(){kr||(kr=!0,a.calledRun=!0,b||(!0,ae(N),ae(G),t(a),a.onRuntimeInitialized&&a.onRuntimeInitialized(),function(){if(a.postRun)for("function"==typeof a.postRun&&(a.postRun=[a.postRun]);a.postRun.length;)e=a.postRun.shift(),L.unshift(e);var e;ae(L)}()))}e=e||s,0<X||(!function(){if(a.preRun)for("function"==typeof a.preRun&&(a.preRun=[a.preRun]);a.preRun.length;)e=a.preRun.shift(),z.unshift(e);var e;ae(z)}(),0<X||(a.setStatus?(a.setStatus("Running..."),setTimeout(function(){setTimeout(function(){a.setStatus("")},1),r()},1)):r()))}if(K=function e(){kr||Ir(),kr||(K=e)},a.run=Ir,a.preInit)for("function"==typeof a.preInit&&(a.preInit=[a.preInit]);0<a.preInit.length;)a.preInit.pop()();return Ir(),e.ready}}();"object"==typeof exports&&"object"==typeof module?module.exports=BASIS:"function"==typeof define&&define.amd?define([],function(){return BASIS}):"object"==typeof exports&&(exports.BASIS=BASIS);