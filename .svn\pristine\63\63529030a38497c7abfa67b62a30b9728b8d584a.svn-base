<template>
    <div id="heat-wrapper"></div>
</template>

<script>
export default {
    name: 'Pressure',
    props: ["deviceInfo", "deviceId", "sectionId"],
    data() {
        return {
            time: ['11:25:00', '11:55:00', '12:15:10', '12:35:40', '13:05:10', '13:55:10', '14:25:30'],
            hightData: [20, 32, 70, 34, 50, 23, 71],
            lowData: [22, 18, 19, 34, 30, 33, 31],
            tempDiff: [50, 32, 20, 54, 40, 30, 41]
        }
    },
    mounted() {
        
    },

    watch: {
        "deviceInfo.id": {
            handler(val) {
                this.getHeatInfo();
            },
            deep: true
        },
    },

    methods: {
        async getHeatInfo() {
            let time = [];
            let hight = [];
            let low = [];
            let temp = [];
            let res = await this.$http.get(`/equip/build/cellTempLineChart?id=${this.deviceId}&sectionId=${this.sectionId}`);
            res?.result?.map(i=>{
                time.push(i.hour);
                hight.push(i.maxValues);
                low.push(i.minxValues);
                temp.push(i.subtraction);
            })
            this.time = time;
            this.hightData = hight;
            this.lowData = low;
            this.tempDiff = temp;
            this.setChart();
        },
        // 初始化
        setChart() {
            let _ = this;
            let myChart = _.$echarts.init(
                document.getElementById("heat-wrapper"),
                null,
                {
                    renderer: "svg",
                }
            );
            myChart.setOption(
                {
                    color: ['#F58A69', '#60BF79', '#65C1DD'],
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        data: ['最高温度', '最低温度', '温差'],
                        left: 'right'
                    },
                    grid: {
                        left: '3%',
                        right: '2%',
                        bottom: '1%',
                        top: 30,
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: this.time
                    },
                    yAxis: {
                        type: 'value',
                        name: '温度 / ℃'
                    },
                    series: [
                        {
                            name: '最高温度',
                            type: 'line',
                            data: this.hightData
                        },
                        {
                            name: '最低温度',
                            type: 'line',
                            data: this.lowData
                        },
                        {
                            name: '温差',
                            type: 'line',
                            data: this.tempDiff
                        }
                    ]
                }
            )
        },
    }
}
</script>

<style lang="scss" scoped>
#heat-wrapper {
    width: 100%;
    height: 270px;
}
</style>