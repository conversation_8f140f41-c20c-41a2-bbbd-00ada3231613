const constData = [{
  id: 1043,
  name: 'CZ项目',
  type: 3,
  children: [{
      id: 11363,
      pid: 1043,
      name: 'CZXZZQ-14A',
      type: 4,
      sectionId: 11363,
      children:[
        {
          id: 21427,
          name: "一分部",
          pid: 11363,
          sectionId: 11363,
          type: 8,
        },
        {
          id: 21428,
          name: "二分部",
          pid: 11363,
          sectionId: 11363,
          type: 8,
        },
        {
          id: 21429,
          name: "三分部",
          pid: 11363,
          sectionId: 11363,
          type: 8,
        },
      ]
    },
    {
      id: 11364,
      pid: 1043,
      name: 'CZXZZQ-14B',
      type: 4,
      sectionId: 11364,
      children:[
        {
          id: 21423,
          name: "二分部",
          pid: 11364,
          sectionId: 11364,
          type: 8,
        },
        {
          id: 21424,
          name: "三分部",
          pid: 11364,
          sectionId: 11364,
          type: 8,
        },
      ]
    },
    {
      id: 11358,
      pid: 1043,
      name: 'CZXZZQ-10',
      type: 4,
      sectionId: 11358,
      children:[
        {
          id: 21433,
          name: "一分部",
          pid: 11358,
          sectionId: 11358,
          type: 8,
        },
      ]
    },
  ]
}]

const powerstationTitle = {
  chargingOverview: '充电概况',
  averageBatterySwappingTime: '平均换电时长',
  chargingCompartment: '充换电区',
  accumulatedChargingTrend: '换电趋势',
  faultMonitoring: '故障与环境监控',
  videoSurveillance: '视频监控',
}

const titleData = {
  mechanicalPreview: '机械概览',
  mechanicalType: '机械类型',
  alarmInfo: '报警信息',
  mechanicalMapPreview: '机械地图一览',
  devicePreview: '感知设备概览',
  timeDate: '工时概况',
  elePreview: '电量概览',
}

const tableData = [{
    deviceName: '挖掘机',
    managementNumber: 'BM000001',
    soc: '63',
    soh: '100',
    battery: '56',
  },
  {
    deviceName: '充电挖机',
    managementNumber: 'BM000002',
    soc: '65',
    soh: '100',
    battery: '58',
  },
  {
    deviceName: '充电装载机',
    managementNumber: 'BM000003',
    soc: '61',
    soh: '100',
    battery: '54',
  },
  {
    deviceName: '充电挖机',
    managementNumber: 'BM000004',
    soc: '65',
    soh: '100',
    battery: '58',
  },
  {
    deviceName: '充电装载机',
    managementNumber: 'BM000005',
    soc: '61',
    soh: '100',
    battery: '54',
  },
  {
    deviceName: '充电挖机',
    managementNumber: 'BM000006',
    soc: '65',
    soh: '100',
    battery: '58',
  },
  {
    deviceName: '充电装载机',
    managementNumber: 'BM000007',
    soc: '61',
    soh: '100',
    battery: '54',
  },
  {
    deviceName: '充电挖机',
    managementNumber: 'BM000008',
    soc: '65',
    soh: '100',
    battery: '58',
  },
  {
    deviceName: '充电装载机',
    managementNumber: 'BM000009',
    soc: '61',
    soh: '100',
    battery: '54',
  },
]

const tableList = [{
    deviceName: '电动轮胎式装载机',
    specificationModel: 'XC968-EV侧翻',
    factoryNum: 'XUG00958JMCB22496',
    managementNumber: 'ZZJ-002-EFB',
    manufacturer: '徐工集团工程机械股份有限公司',
    inspectionReport: '/',
    entryAcceptance: '04-6 寇东阳',
    projectSite: '邦达出口、4号斜井',
    entryTime: '2023-11-6',
    exitTime: '2023-11-6',
    inspectionDate: '2022年2月',
    operationPerson: '祁永来',
    remark: '自购'
  },
  {
    deviceName: '电动自卸',
    specificationModel: 'XC968-EV侧翻',
    factoryNum: 'XUG00958JMCB22496',
    managementNumber: 'ZZJ-002-EFB',
    manufacturer: '徐工集团工程机械股份有限公司',
    inspectionReport: '/',
    entryAcceptance: '04-6 寇东阳',
    projectSite: '邦达出口、4号斜井',
    entryTime: '2023-11-6',
    exitTime: '2023-11-6',
    inspectionDate: '2022年2月',
    operationPerson: '祁永来',
    remark: '自购'
  },
  {
    deviceName: '电动轮胎式装载机',
    specificationModel: 'XC968-EV侧翻',
    factoryNum: 'XUG00958JMCB22496',
    managementNumber: 'ZZJ-002-EFB',
    manufacturer: '徐工集团工程机械股份有限公司',
    inspectionReport: '/',
    entryAcceptance: '04-6 寇东阳',
    projectSite: '邦达出口、4号斜井',
    entryTime: '2023-11-6',
    exitTime: '2023-11-6',
    inspectionDate: '2022年2月',
    operationPerson: '祁永来',
    remark: '自购'
  },
  {
    deviceName: '电动自卸',
    specificationModel: 'XC968-EV侧翻',
    factoryNum: 'XUG00958JMCB22496',
    managementNumber: 'ZZJ-002-EFB',
    manufacturer: '徐工集团工程机械股份有限公司',
    inspectionReport: '/',
    entryAcceptance: '04-6 寇东阳',
    projectSite: '邦达出口、4号斜井',
    entryTime: '2023-11-6',
    exitTime: '2023-11-6',
    inspectionDate: '2022年2月',
    operationPerson: '祁永来',
    remark: '自购'
  },
  {
    deviceName: '电动轮胎式装载机',
    specificationModel: 'XC968-EV侧翻',
    factoryNum: 'XUG00958JMCB22496',
    managementNumber: 'ZZJ-002-EFB',
    manufacturer: '徐工集团工程机械股份有限公司',
    inspectionReport: '/',
    entryAcceptance: '04-6 寇东阳',
    projectSite: '邦达出口、4号斜井',
    entryTime: '2023-11-6',
    exitTime: '2023-11-6',
    inspectionDate: '2022年2月',
    operationPerson: '祁永来',
    remark: '自购'
  },
  {
    deviceName: '电动自卸',
    specificationModel: 'XC968-EV侧翻',
    factoryNum: 'XUG00958JMCB22496',
    managementNumber: 'ZZJ-002-EFB',
    manufacturer: '徐工集团工程机械股份有限公司',
    inspectionReport: '/',
    entryAcceptance: '04-6 寇东阳',
    projectSite: '邦达出口、4号斜井',
    entryTime: '2023-11-6',
    exitTime: '2023-11-6',
    inspectionDate: '2022年2月',
    operationPerson: '祁永来',
    remark: '自购'
  },
  {
    deviceName: '电动轮胎式装载机',
    specificationModel: 'XC968-EV侧翻',
    factoryNum: 'XUG00958JMCB22496',
    managementNumber: 'ZZJ-002-EFB',
    manufacturer: '徐工集团工程机械股份有限公司',
    inspectionReport: '/',
    entryAcceptance: '04-6 寇东阳',
    projectSite: '邦达出口、4号斜井',
    entryTime: '2023-11-6',
    exitTime: '2023-11-6',
    inspectionDate: '2022年2月',
    operationPerson: '祁永来',
    remark: '自购'
  },
  {
    deviceName: '电动自卸',
    specificationModel: 'XC968-EV侧翻',
    factoryNum: 'XUG00958JMCB22496',
    managementNumber: 'ZZJ-002-EFB',
    manufacturer: '徐工集团工程机械股份有限公司',
    inspectionReport: '/',
    entryAcceptance: '04-6 寇东阳',
    projectSite: '邦达出口、4号斜井',
    entryTime: '2023-11-6',
    exitTime: '2023-11-6',
    inspectionDate: '2022年2月',
    operationPerson: '祁永来',
    remark: '自购'
  },
  {
    deviceName: '电动轮胎式装载机',
    specificationModel: 'XC968-EV侧翻',
    factoryNum: 'XUG00958JMCB22496',
    managementNumber: 'ZZJ-002-EFB',
    manufacturer: '徐工集团工程机械股份有限公司',
    inspectionReport: '/',
    entryAcceptance: '04-6 寇东阳',
    projectSite: '邦达出口、4号斜井',
    entryTime: '2023-11-6',
    exitTime: '2023-11-6',
    inspectionDate: '2022年2月',
    operationPerson: '祁永来',
    remark: '自购'
  },
  {
    deviceName: '电动自卸',
    specificationModel: 'XC968-EV侧翻',
    factoryNum: 'XUG00958JMCB22496',
    managementNumber: 'ZZJ-002-EFB',
    manufacturer: '徐工集团工程机械股份有限公司',
    inspectionReport: '/',
    entryAcceptance: '04-6 寇东阳',
    projectSite: '邦达出口、4号斜井',
    entryTime: '2023-11-6',
    exitTime: '2023-11-6',
    inspectionDate: '2022年2月',
    operationPerson: '祁永来',
    remark: '自购'
  }
]

const positionData = [
  {
      deviceType: "7",
      lon: "116.421561",
      deviceName: "换电混泥土车",
      lat: "39.958567"
  },
  {
      deviceType: "6",
      lon: "116.418319",
      deviceName: "充电混泥土车",
      lat: "39.962108"
  },
  {
      deviceType: "5",
      lon: "116.411565",
      deviceName: "换电自卸车",
      lat: "39.958567"
  },
  {
      deviceType: "4",
      lon: "116.41226",
      deviceName: "充电自卸车",
      lat: "39.958932"
  },
  {
      deviceType: "3",
      lon: "116.418532",
      deviceName: "换电装载机",
      lat: "39.959334"
  },
  {
      deviceType: "2",
      lon: "116.40759",
      deviceName: "充电装载机",
      lat: "39.963225"
  },
  {
      deviceType: "1",
      lon: "116.43831",
      deviceName: "换电挖掘机",
      lat: "39.973225"
  },
  {
      deviceType: "1",
      lon: "116.438532",
      deviceName: "换电挖掘机",
      lat: "38.958932"
  },
  {
      deviceType: "0",
      lon: "116.438532",
      deviceName: "充电挖掘机",
      lat: "38.958932"
  }
]

const constDataSet = {
  constData,
  powerstationTitle,
  titleData,
  tableData,
  tableList,
  positionData,
}

export default constDataSet;
