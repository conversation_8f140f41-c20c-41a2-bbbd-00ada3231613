<template>
  <div class="screenfull_icon fr" @click="screenfull">
    <svg-icon :icon-class="iconclass"></svg-icon>
  </div>
</template>

<script>
import screenfull from "screenfull";
export default {
  name: "Screenfull",
  data() {
    return {
      iconclass: "u697",
      fullscreen: "u697",
      exitFullscreen: "u697",
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      if (screenfull.isEnabled) {
        this.iconclass = this.fullscreen;
        screenfull.on("change", this.change);
      }
    },

    change() {
      if (screenfull.isFullscreen) {
        this.iconclass = this.exitFullscreen;
      } else {
        this.iconclass = this.fullscreen;
      }
    },
    screenfull() {
      if (!screenfull.isEnabled) {
        this.$message({ message: "你的浏览器不支持全屏", type: "warning" });
        return false;
      }
      screenfull.toggle();
    },
  },
};
</script>

<style scoped lang="scss">
.svg-icon {
  width: 18px;
  height: 18px;
}
</style>
