<template>
    <div class="card-container">
        <div class="header">
            <div class="header-title">{{ title }}</div>
            <div class="header-dropdown" v-if="status">
                <el-dropdown @command="handleCommand" v-model="dropdownValue">
                    <span class="el-dropdown-link">
                        {{ dropdownValue }}<i class="el-icon-arrow-down el-icon--right"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="累计">累计</el-dropdown-item>
                        <el-dropdown-item command="月度">月度</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>
        <div class="card-body">
            <slot />
        </div>
    </div>
</template>

<script>
export default {
    name: 'Card',
    props: ['title', 'status'],
    data() {
        return {
            dropdownValue: '累计'
        }
    },
    mounted() {
    },

    methods: {
        handleCommand(command) {
            this.$emit('getDropdownValue', command);
            this.dropdownValue = command;
        },
    }
}
</script>

<style lang="scss" scoped>
.card-container {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border: 1px solid #6299C7;

    .header {
        width: 100%;
        height: 39px;
        background: linear-gradient(45deg, #D0E4F9, #FBFDFF);
        border-bottom: 1px solid #6299C7;
        line-height: 39px;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-between;

        .header-title {
            width: 150px;
            height: 100%;
            margin-left: 20px;
            font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
            font-weight: 700;
            font-style: normal;
            font-size: 16px;
            color: #3A3F5D;
        }

        .header-dropdown {
            width: 65px;
            height: 100%;

            .el-dropdown-link {
                cursor: pointer;
                font-family: '微软雅黑', sans-serif;
                font-weight: 400;
                font-style: normal;
                font-size: 16px;
                letter-spacing: normal;
                color: #3A3F5D;
            }

            .el-icon-arrow-down {
                font-size: 12px;
            }
        }
    }

    .card-body {
        height: calc(100% - 39px);
        width: 100%;
        padding: 10px;
        min-height: 100px;
    }
}
</style>