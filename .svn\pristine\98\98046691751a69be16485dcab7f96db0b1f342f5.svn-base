/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["exports","./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./FeatureDetection-7bd32c34","./buildModuleUrl-392763e2"],function(e,S,T,g,f,u,P,i,c,t,d){function w(e,t,r,n){this.x=S.defaultValue(e,0),this.y=S.defaultValue(t,0),this.z=S.defaultValue(r,0),this.w=S.defaultValue(n,0)}var h=new f.Cartesian3;w.fromAxisAngle=function(e,t,r){T.Check.typeOf.object("axis",e),T.Check.typeOf.number("angle",t);var n=t/2,a=Math.sin(n),i=(h=f.Cartesian3.normalize(e,h)).x*a,o=h.y*a,s=h.z*a,l=Math.cos(n);return S.defined(r)?(r.x=i,r.y=o,r.z=s,r.w=l,r):new w(i,o,s,l)};var m=[1,2,0],y=new Array(3);w.fromRotationMatrix=function(e,t){var r,n,a,i,o;T.Check.typeOf.object("matrix",e);var s=e[P.Matrix3.COLUMN0ROW0],l=e[P.Matrix3.COLUMN1ROW1],u=e[P.Matrix3.COLUMN2ROW2],d=s+l+u;if(0<d)o=.5*(r=Math.sqrt(d+1)),r=.5/r,n=(e[P.Matrix3.COLUMN1ROW2]-e[P.Matrix3.COLUMN2ROW1])*r,a=(e[P.Matrix3.COLUMN2ROW0]-e[P.Matrix3.COLUMN0ROW2])*r,i=(e[P.Matrix3.COLUMN0ROW1]-e[P.Matrix3.COLUMN1ROW0])*r;else{var f=0;s<l&&(f=1),s<u&&l<u&&(f=2);var c=m[f],h=m[c];r=Math.sqrt(e[P.Matrix3.getElementIndex(f,f)]-e[P.Matrix3.getElementIndex(c,c)]-e[P.Matrix3.getElementIndex(h,h)]+1);var p=y;p[f]=.5*r,r=.5/r,o=(e[P.Matrix3.getElementIndex(h,c)]-e[P.Matrix3.getElementIndex(c,h)])*r,p[c]=(e[P.Matrix3.getElementIndex(c,f)]+e[P.Matrix3.getElementIndex(f,c)])*r,p[h]=(e[P.Matrix3.getElementIndex(h,f)]+e[P.Matrix3.getElementIndex(f,h)])*r,n=-p[0],a=-p[1],i=-p[2]}return S.defined(t)?(t.x=n,t.y=a,t.z=i,t.w=o,t):new w(n,a,i,o)};var r=new w,n=new w,a=new w,o=new w;w.fromHeadingPitchRoll=function(e,t){return T.Check.typeOf.object("headingPitchRoll",e),o=w.fromAxisAngle(f.Cartesian3.UNIT_X,e.roll,r),a=w.fromAxisAngle(f.Cartesian3.UNIT_Y,-e.pitch,t),t=w.multiply(a,o,a),n=w.fromAxisAngle(f.Cartesian3.UNIT_Z,-e.heading,r),w.multiply(n,t,t)};var l=new f.Cartesian3,s=new f.Cartesian3,p=new w,C=new w,x=new w;w.packedLength=4,w.pack=function(e,t,r){return T.Check.typeOf.object("value",e),T.Check.defined("array",t),r=S.defaultValue(r,0),t[r++]=e.x,t[r++]=e.y,t[r++]=e.z,t[r]=e.w,t},w.unpack=function(e,t,r){return T.Check.defined("array",e),t=S.defaultValue(t,0),S.defined(r)||(r=new w),r.x=e[t],r.y=e[t+1],r.z=e[t+2],r.w=e[t+3],r},w.packedInterpolationLength=3,w.convertPackedArrayForInterpolation=function(e,t,r,n){w.unpack(e,4*r,x),w.conjugate(x,x);for(var a=0,i=r-t+1;a<i;a++){var o=3*a;w.unpack(e,4*(t+a),p),w.multiply(p,x,p),p.w<0&&w.negate(p,p),w.computeAxis(p,l);var s=w.computeAngle(p);n[o]=l.x*s,n[o+1]=l.y*s,n[o+2]=l.z*s}},w.unpackInterpolationResult=function(e,t,r,n,a){S.defined(a)||(a=new w),f.Cartesian3.fromArray(e,0,s);var i=f.Cartesian3.magnitude(s);return w.unpack(t,4*n,C),0===i?w.clone(w.IDENTITY,p):w.fromAxisAngle(s,i,p),w.multiply(p,C,a)},w.clone=function(e,t){if(S.defined(e))return S.defined(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new w(e.x,e.y,e.z,e.w)},w.conjugate=function(e,t){return T.Check.typeOf.object("quaternion",e),T.Check.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=e.w,t},w.magnitudeSquared=function(e){return T.Check.typeOf.object("quaternion",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w},w.magnitude=function(e){return Math.sqrt(w.magnitudeSquared(e))},w.normalize=function(e,t){T.Check.typeOf.object("result",t);var r=1/w.magnitude(e),n=e.x*r,a=e.y*r,i=e.z*r,o=e.w*r;return t.x=n,t.y=a,t.z=i,t.w=o,t},w.inverse=function(e,t){T.Check.typeOf.object("result",t);var r=w.magnitudeSquared(e);return t=w.conjugate(e,t),w.multiplyByScalar(t,1/r,t)},w.add=function(e,t,r){return T.Check.typeOf.object("left",e),T.Check.typeOf.object("right",t),T.Check.typeOf.object("result",r),r.x=e.x+t.x,r.y=e.y+t.y,r.z=e.z+t.z,r.w=e.w+t.w,r},w.subtract=function(e,t,r){return T.Check.typeOf.object("left",e),T.Check.typeOf.object("right",t),T.Check.typeOf.object("result",r),r.x=e.x-t.x,r.y=e.y-t.y,r.z=e.z-t.z,r.w=e.w-t.w,r},w.negate=function(e,t){return T.Check.typeOf.object("quaternion",e),T.Check.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t},w.dot=function(e,t){return T.Check.typeOf.object("left",e),T.Check.typeOf.object("right",t),e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w},w.multiply=function(e,t,r){T.Check.typeOf.object("left",e),T.Check.typeOf.object("right",t),T.Check.typeOf.object("result",r);var n=e.x,a=e.y,i=e.z,o=e.w,s=t.x,l=t.y,u=t.z,d=t.w,f=o*s+n*d+a*u-i*l,c=o*l-n*u+a*d+i*s,h=o*u+n*l-a*s+i*d,p=o*d-n*s-a*l-i*u;return r.x=f,r.y=c,r.z=h,r.w=p,r},w.multiplyByVec=function(e,t,r){var n=new f.Cartesian3,a=new f.Cartesian3,i=new f.Cartesian3(e.x,e.y,e.z);n=f.Cartesian3.cross(i,t,n),a=f.Cartesian3.cross(i,n,a);var o=new f.Cartesian3;o=f.Cartesian3.multiplyByScalar(n,2*e.w,o);var s=new f.Cartesian3;return s=f.Cartesian3.multiplyByScalar(n,2,s),r=f.Cartesian3.add(t,o,r),r=f.Cartesian3.add(r,s,r)},w.multiplyByScalar=function(e,t,r){return T.Check.typeOf.object("quaternion",e),T.Check.typeOf.number("scalar",t),T.Check.typeOf.object("result",r),r.x=e.x*t,r.y=e.y*t,r.z=e.z*t,r.w=e.w*t,r},w.divideByScalar=function(e,t,r){return T.Check.typeOf.object("quaternion",e),T.Check.typeOf.number("scalar",t),T.Check.typeOf.object("result",r),r.x=e.x/t,r.y=e.y/t,r.z=e.z/t,r.w=e.w/t,r},w.computeAxis=function(e,t){T.Check.typeOf.object("quaternion",e),T.Check.typeOf.object("result",t);var r=e.w;if(Math.abs(r-1)<g.CesiumMath.EPSILON6)return t.x=t.y=t.z=0,t;var n=1/Math.sqrt(1-r*r);return t.x=e.x*n,t.y=e.y*n,t.z=e.z*n,t},w.computeAngle=function(e){return T.Check.typeOf.object("quaternion",e),Math.abs(e.w-1)<g.CesiumMath.EPSILON6?0:2*Math.acos(e.w)};var O=new w;w.lerp=function(e,t,r,n){return T.Check.typeOf.object("start",e),T.Check.typeOf.object("end",t),T.Check.typeOf.number("t",r),T.Check.typeOf.object("result",n),O=w.multiplyByScalar(t,r,O),n=w.multiplyByScalar(e,1-r,n),w.add(O,n,n)};var E=new w,D=new w,v=new w;w.slerp=function(e,t,r,n){T.Check.typeOf.object("start",e),T.Check.typeOf.object("end",t),T.Check.typeOf.number("t",r),T.Check.typeOf.object("result",n);var a=w.dot(e,t),i=t;if(a<0&&(a=-a,i=E=w.negate(t,E)),1-a<g.CesiumMath.EPSILON6)return w.lerp(e,i,r,n);var o=Math.acos(a);return D=w.multiplyByScalar(e,Math.sin((1-r)*o),D),v=w.multiplyByScalar(i,Math.sin(r*o),v),n=w.add(D,v,n),w.multiplyByScalar(n,1/Math.sin(o),n)},w.log=function(e,t){T.Check.typeOf.object("quaternion",e),T.Check.typeOf.object("result",t);var r=g.CesiumMath.acosClamped(e.w),n=0;return 0!==r&&(n=r/Math.sin(r)),f.Cartesian3.multiplyByScalar(e,n,t)},w.exp=function(e,t){T.Check.typeOf.object("cartesian",e),T.Check.typeOf.object("result",t);var r=f.Cartesian3.magnitude(e),n=0;return 0!==r&&(n=Math.sin(r)/r),t.x=e.x*n,t.y=e.y*n,t.z=e.z*n,t.w=Math.cos(r),t};var _=new f.Cartesian3,M=new f.Cartesian3,b=new w,N=new w;w.computeInnerQuadrangle=function(e,t,r,n){T.Check.typeOf.object("q0",e),T.Check.typeOf.object("q1",t),T.Check.typeOf.object("q2",r),T.Check.typeOf.object("result",n);var a=w.conjugate(t,b);w.multiply(a,r,N);var i=w.log(N,_);w.multiply(a,e,N);var o=w.log(N,M);return f.Cartesian3.add(i,o,i),f.Cartesian3.multiplyByScalar(i,.25,i),f.Cartesian3.negate(i,i),w.exp(i,b),w.multiply(t,b,n)},w.squad=function(e,t,r,n,a,i){T.Check.typeOf.object("q0",e),T.Check.typeOf.object("q1",t),T.Check.typeOf.object("s0",r),T.Check.typeOf.object("s1",n),T.Check.typeOf.number("t",a),T.Check.typeOf.object("result",i);var o=w.slerp(e,t,a,b),s=w.slerp(r,n,a,N);return w.slerp(o,s,2*a*(1-a),i)};for(var R=new w,I=1.9011074535173003,q=t.FeatureDetection.supportsTypedArrays()?new Float32Array(8):[],A=t.FeatureDetection.supportsTypedArrays()?new Float32Array(8):[],U=t.FeatureDetection.supportsTypedArrays()?new Float32Array(8):[],k=t.FeatureDetection.supportsTypedArrays()?new Float32Array(8):[],j=0;j<7;++j){var z=j+1,F=2*z+1;q[j]=1/(z*F),A[j]=z/F}function W(e,t,r){T.Check.defined("array",e),T.Check.defined("itemToFind",t),T.Check.defined("comparator",r);for(var n,a,i=0,o=e.length-1;i<=o;)if((a=r(e[n=~~((i+o)/2)],t))<0)i=n+1;else{if(!(0<a))return n;o=n-1}return~(o+1)}function L(e,t,r,n,a){this.xPoleWander=e,this.yPoleWander=t,this.xPoleOffset=r,this.yPoleOffset=n,this.ut1MinusUtc=a}function V(){var x=arguments,O=0,e=x[O++],E=function(e,t,r,n){r||(r=" ");var a=e.length>=t?"":Array(1+t-e.length>>>0).join(r);return n?e+a:a+e},D=function(e,t,r,n,a,i){var o=n-e.length;return 0<o&&(e=r||!a?E(e,n,i,r):e.slice(0,t.length)+E("",o,"0",!0)+e.slice(t.length)),e},v=function(e,t,r,n,a,i,o){var s=e>>>0;return e=(r=r&&s&&{2:"0b",8:"0",16:"0x"}[t]||"")+E(s.toString(t),i||0,"0",!1),D(e,r,n,a,o)},_=function(e,t,r,n,a,i){return null!=n&&(e=e.slice(0,n)),D(e,"",t,r,a,i)};return e.replace(/%%|%(\d+\$)?([-+\'#0 ]*)(\*\d+\$|\*|\d+)?(\.(\*\d+\$|\*|\d+))?([scboxXuideEfFgG])/g,function(e,t,r,n,a,i,o){var s,l,u,d,f;if("%%"==e)return"%";for(var c=!1,h="",p=!1,w=!1,m=" ",y=r.length,C=0;r&&C<y;C++)switch(r.charAt(C)){case" ":h=" ";break;case"+":h="+";break;case"-":c=!0;break;case"'":m=r.charAt(C+1);break;case"0":p=!0;break;case"#":w=!0}if((n=n?"*"==n?+x[O++]:"*"==n.charAt(0)?+x[n.slice(1,-1)]:+n:0)<0&&(n=-n,c=!0),!isFinite(n))throw new Error("sprintf: (minimum-)width must be finite");switch(i=i?"*"==i?+x[O++]:"*"==i.charAt(0)?+x[i.slice(1,-1)]:+i:-1<"fFeE".indexOf(o)?6:"d"==o?0:void 0,f=t?x[t.slice(0,-1)]:x[O++],o){case"s":return _(String(f),c,n,i,p,m);case"c":return _(String.fromCharCode(+f),c,n,i,p);case"b":return v(f,2,w,c,n,i,p);case"o":return v(f,8,w,c,n,i,p);case"x":return v(f,16,w,c,n,i,p);case"X":return v(f,16,w,c,n,i,p).toUpperCase();case"u":return v(f,10,w,c,n,i,p);case"i":case"d":return s=+f||0,f=(l=(s=Math.round(s-s%1))<0?"-":h)+E(String(Math.abs(s)),i,"0",!1),D(f,l,c,n,p);case"e":case"E":case"f":case"F":case"g":case"G":return l=(s=+f)<0?"-":h,u=["toExponential","toFixed","toPrecision"]["efg".indexOf(o.toLowerCase())],d=["toString","toUpperCase"]["eEfFgG".indexOf(o)%2],f=l+Math.abs(s)[u](i),D(f,l,c,n,p)[d]();default:return e}})}function Y(e,t,r,n,a,i,o,s){this.year=e,this.month=t,this.day=r,this.hour=n,this.minute=a,this.second=i,this.millisecond=o,this.isLeapSecond=s}function B(e){if(null===e||isNaN(e))throw new T.DeveloperError("year is required and must be a number.");return e%4==0&&e%100!=0||e%400==0}function G(e,t){this.julianDate=e,this.offset=t}q[7]=I/136,A[7]=8*I/17,w.fastSlerp=function(e,t,r,n){T.Check.typeOf.object("start",e),T.Check.typeOf.object("end",t),T.Check.typeOf.number("t",r),T.Check.typeOf.object("result",n);var a,i=w.dot(e,t);0<=i?a=1:(a=-1,i=-i);for(var o=i-1,s=1-r,l=r*r,u=s*s,d=7;0<=d;--d)U[d]=(q[d]*l-A[d])*o,k[d]=(q[d]*u-A[d])*o;var f=a*r*(1+U[0]*(1+U[1]*(1+U[2]*(1+U[3]*(1+U[4]*(1+U[5]*(1+U[6]*(1+U[7])))))))),c=s*(1+k[0]*(1+k[1]*(1+k[2]*(1+k[3]*(1+k[4]*(1+k[5]*(1+k[6]*(1+k[7])))))))),h=w.multiplyByScalar(e,c,R);return w.multiplyByScalar(t,f,n),w.add(h,n,n)},w.fastSquad=function(e,t,r,n,a,i){T.Check.typeOf.object("q0",e),T.Check.typeOf.object("q1",t),T.Check.typeOf.object("s0",r),T.Check.typeOf.object("s1",n),T.Check.typeOf.number("t",a),T.Check.typeOf.object("result",i);var o=w.fastSlerp(e,t,a,b),s=w.fastSlerp(r,n,a,N);return w.fastSlerp(o,s,2*a*(1-a),i)},w.equals=function(e,t){return e===t||S.defined(e)&&S.defined(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w},w.equalsEpsilon=function(e,t,r){return T.Check.typeOf.number("epsilon",r),e===t||S.defined(e)&&S.defined(t)&&Math.abs(e.x-t.x)<=r&&Math.abs(e.y-t.y)<=r&&Math.abs(e.z-t.z)<=r&&Math.abs(e.w-t.w)<=r},w.ZERO=Object.freeze(new w(0,0,0,0)),w.IDENTITY=Object.freeze(new w(0,0,0,1)),w.prototype.clone=function(e){return w.clone(this,e)},w.prototype.equals=function(e){return w.equals(this,e)},w.prototype.equalsEpsilon=function(e,t){return w.equalsEpsilon(this,e,t)},w.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+", "+this.w+")"};var Z=Object.freeze({SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:2400000.5}),X=Object.freeze({UTC:0,TAI:1}),J=new Y,H=[31,28,31,30,31,30,31,31,30,31,30,31];function $(e,t){return he.compare(e.julianDate,t.julianDate)}var Q=new G;function K(e){Q.julianDate=e;var t=he.leapSeconds,r=W(t,Q,$);r<0&&(r=~r),r>=t.length&&(r=t.length-1);var n=t[r].offset;0<r&&(n<he.secondsDifference(t[r].julianDate,e)&&(n=t[--r].offset));he.addSeconds(e,n,e)}function ee(e,t){Q.julianDate=e;var r=he.leapSeconds,n=W(r,Q,$);if(n<0&&(n=~n),0===n)return he.addSeconds(e,-r[0].offset,t);if(n>=r.length)return he.addSeconds(e,-r[n-1].offset,t);var a=he.secondsDifference(r[n].julianDate,e);return 0===a?he.addSeconds(e,-r[n].offset,t):a<=1?void 0:he.addSeconds(e,-r[--n].offset,t)}function te(e,t,r){var n=t/Z.SECONDS_PER_DAY|0;return e+=n,(t-=Z.SECONDS_PER_DAY*n)<0&&(e--,t+=Z.SECONDS_PER_DAY),r.dayNumber=e,r.secondsOfDay=t,r}function re(e,t,r,n,a,i,o){var s=(t-14)/12|0,l=e+4800+s,u=(1461*l/4|0)+(367*(t-2-12*s)/12|0)-(3*((l+100)/100|0)/4|0)+r-32075;(n-=12)<0&&(n+=24);var d=i+(n*Z.SECONDS_PER_HOUR+a*Z.SECONDS_PER_MINUTE+o*Z.SECONDS_PER_MILLISECOND);return 43200<=d&&(u-=1),[u,d]}var ne=/^(\d{4})$/,ae=/^(\d{4})-(\d{2})$/,ie=/^(\d{4})-?(\d{3})$/,oe=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,se=/^(\d{4})-?(\d{2})-?(\d{2})$/,le=/([Z+\-])?(\d{2})?:?(\d{2})?$/,ue=/^(\d{2})(\.\d+)?/.source+le.source,de=/^(\d{2}):?(\d{2})(\.\d+)?/.source+le.source,fe=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+le.source,ce="Invalid ISO 8601 date.";function he(e,t,r){this.dayNumber=void 0,this.secondsOfDay=void 0,e=S.defaultValue(e,0),t=S.defaultValue(t,0),r=S.defaultValue(r,X.UTC);var n=0|e;te(n,t+=(e-n)*Z.SECONDS_PER_DAY,this),r===X.UTC&&K(this)}he.fromGregorianDate=function(e,t){if(!(e instanceof Y))throw new T.DeveloperError("date must be a valid GregorianDate.");var r=re(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return S.defined(t)?(te(r[0],r[1],t),K(t),t):new he(r[0],r[1],X.UTC)},he.fromDate=function(e,t){if(!(e instanceof Date)||isNaN(e.getTime()))throw new T.DeveloperError("date must be a valid JavaScript Date.");var r=re(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return S.defined(t)?(te(r[0],r[1],t),K(t),t):new he(r[0],r[1],X.UTC)},he.fromIso8601=function(e,t){if("string"!=typeof e)throw new T.DeveloperError(ce);var r,n,a,i,o,s=(e=e.replace(",",".")).split("T"),l=1,u=1,d=0,f=0,c=0,h=0,p=s[0],w=s[1];if(!S.defined(p))throw new T.DeveloperError(ce);if(null!==(s=p.match(se))){if(0<(i=p.split("-").length-1)&&2!==i)throw new T.DeveloperError(ce);r=+s[1],l=+s[2],u=+s[3]}else if(null!==(s=p.match(ae)))r=+s[1],l=+s[2];else if(null!==(s=p.match(ne)))r=+s[1];else{var m;if(null!==(s=p.match(ie))){if(r=+s[1],m=+s[2],a=B(r),m<1||a&&366<m||!a&&365<m)throw new T.DeveloperError(ce)}else{if(null===(s=p.match(oe)))throw new T.DeveloperError(ce);r=+s[1];var y=+s[2],C=+s[3]||0;if(0<(i=p.split("-").length-1)&&(!S.defined(s[3])&&1!==i||S.defined(s[3])&&2!==i))throw new T.DeveloperError(ce);m=7*y+C-new Date(Date.UTC(r,0,4)).getUTCDay()-3}(n=new Date(Date.UTC(r,0,1))).setUTCDate(m),l=n.getUTCMonth()+1,u=n.getUTCDate()}if(a=B(r),l<1||12<l||u<1||(2!==l||!a)&&H[l-1]<u||a&&2===l&&29<u)throw new T.DeveloperError(ce);if(S.defined(w)){if(null!==(s=w.match(fe))){if(0<(i=w.split(":").length-1)&&2!==i&&3!==i)throw new T.DeveloperError(ce);d=+s[1],f=+s[2],c=+s[3],h=1e3*+(s[4]||0),o=5}else if(null!==(s=w.match(de))){if(2<(i=w.split(":").length-1))throw new T.DeveloperError(ce);d=+s[1],f=+s[2],c=60*+(s[3]||0),o=4}else{if(null===(s=w.match(ue)))throw new T.DeveloperError(ce);d=+s[1],f=60*+(s[2]||0),o=3}if(60<=f||61<=c||24<d||24===d&&(0<f||0<c||0<h))throw new T.DeveloperError(ce);var x=s[o],O=+s[o+1],E=+(s[o+2]||0);switch(x){case"+":d-=O,f-=E;break;case"-":d+=O,f+=E;break;case"Z":break;default:f+=new Date(Date.UTC(r,l-1,u,d,f)).getTimezoneOffset()}}var D=60===c;for(D&&c--;60<=f;)f-=60,d++;for(;24<=d;)d-=24,u++;for(n=a&&2===l?29:H[l-1];n<u;)u-=n,12<++l&&(l-=12,r++),n=a&&2===l?29:H[l-1];for(;f<0;)f+=60,d--;for(;d<0;)d+=24,u--;for(;u<1;)--l<1&&(l+=12,r--),u+=n=a&&2===l?29:H[l-1];var v=re(r,l,u,d,f,c,h);return S.defined(t)?(te(v[0],v[1],t),K(t)):t=new he(v[0],v[1],X.UTC),D&&he.addSeconds(t,1,t),t},he.now=function(e){return he.fromDate(new Date,e)};var pe=new he(0,0,X.TAI);function we(e){if(e=S.defaultValue(e,S.defaultValue.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._downloadPromise=void 0,this._dataError=void 0,this._addNewLeapSeconds=S.defaultValue(e.addNewLeapSeconds,!0),S.defined(e.data))ye(this,e.data);else if(S.defined(e.url)){var t=d.Resource.createIfNeeded(e.url),r=this;this._downloadPromise=S.when(t.fetchJson(),function(e){ye(r,e)},function(){r._dataError="An error occurred while retrieving the EOP data from the URL "+t.url+"."})}else ye(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}function me(e,t){return he.compare(e.julianDate,t)}function ye(e,t){if(S.defined(t.columnNames))if(S.defined(t.samples)){var r=t.columnNames.indexOf("modifiedJulianDateUtc"),n=t.columnNames.indexOf("xPoleWanderRadians"),a=t.columnNames.indexOf("yPoleWanderRadians"),i=t.columnNames.indexOf("ut1MinusUtcSeconds"),o=t.columnNames.indexOf("xCelestialPoleOffsetRadians"),s=t.columnNames.indexOf("yCelestialPoleOffsetRadians"),l=t.columnNames.indexOf("taiMinusUtcSeconds");if(r<0||n<0||a<0||i<0||o<0||s<0||l<0)e._dataError="Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns";else{var u,d=e._samples=t.samples,f=e._dates=[];e._dateColumn=r,e._xPoleWanderRadiansColumn=n,e._yPoleWanderRadiansColumn=a,e._ut1MinusUtcSecondsColumn=i,e._xCelestialPoleOffsetRadiansColumn=o,e._yCelestialPoleOffsetRadiansColumn=s,e._taiMinusUtcSecondsColumn=l,e._columnCount=t.columnNames.length,e._lastIndex=void 0;for(var c=e._addNewLeapSeconds,h=0,p=d.length;h<p;h+=e._columnCount){var w=d[h+r],m=d[h+l],y=new he(w+Z.MODIFIED_JULIAN_DATE_DIFFERENCE,m,X.TAI);if(f.push(y),c){if(m!==u&&S.defined(u)){var C=he.leapSeconds,x=W(C,y,me);if(x<0){var O=new G(y,m);C.splice(~x,0,O)}}u=m}}}}else e._dataError="Error in loaded EOP data: The samples property is required.";else e._dataError="Error in loaded EOP data: The columnNames property is required."}function Ce(e,t,r,n,a){var i=r*n;a.xPoleWander=t[i+e._xPoleWanderRadiansColumn],a.yPoleWander=t[i+e._yPoleWanderRadiansColumn],a.xPoleOffset=t[i+e._xCelestialPoleOffsetRadiansColumn],a.yPoleOffset=t[i+e._yCelestialPoleOffsetRadiansColumn],a.ut1MinusUtc=t[i+e._ut1MinusUtcSecondsColumn]}function xe(e,t,r){return t+e*(r-t)}function Oe(e,t,r,n,a,i,o){var s=e._columnCount;if(i>t.length-1)return o.xPoleWander=0,o.yPoleWander=0,o.xPoleOffset=0,o.yPoleOffset=0,o.ut1MinusUtc=0,o;var l=t[a],u=t[i];if(l.equals(u)||n.equals(l))return Ce(e,r,a,s,o),o;if(n.equals(u))return Ce(e,r,i,s,o),o;var d=he.secondsDifference(n,l)/he.secondsDifference(u,l),f=a*s,c=i*s,h=r[f+e._ut1MinusUtcSecondsColumn],p=r[c+e._ut1MinusUtcSecondsColumn],w=p-h;if(.5<w||w<-.5){var m=r[f+e._taiMinusUtcSecondsColumn],y=r[c+e._taiMinusUtcSecondsColumn];m!==y&&(u.equals(n)?h=p:p-=y-m)}return o.xPoleWander=xe(d,r[f+e._xPoleWanderRadiansColumn],r[c+e._xPoleWanderRadiansColumn]),o.yPoleWander=xe(d,r[f+e._yPoleWanderRadiansColumn],r[c+e._yPoleWanderRadiansColumn]),o.xPoleOffset=xe(d,r[f+e._xCelestialPoleOffsetRadiansColumn],r[c+e._xCelestialPoleOffsetRadiansColumn]),o.yPoleOffset=xe(d,r[f+e._yCelestialPoleOffsetRadiansColumn],r[c+e._yCelestialPoleOffsetRadiansColumn]),o.ut1MinusUtc=xe(d,h,p),o}function Ee(e,t,r){this.heading=S.defaultValue(e,0),this.pitch=S.defaultValue(t,0),this.roll=S.defaultValue(r,0)}function De(e,t,r){this.x=e,this.y=t,this.s=r}function ve(e){e=S.defaultValue(e,S.defaultValue.EMPTY_OBJECT),this._xysFileUrlTemplate=d.Resource.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=S.defaultValue(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=S.defaultValue(e.sampleZeroJulianEphemerisDate,2442396.5),this._sampleZeroDateTT=new he(this._sampleZeroJulianEphemerisDate,0,X.TAI),this._stepSizeDays=S.defaultValue(e.stepSizeDays,1),this._samplesPerXysFile=S.defaultValue(e.samplesPerXysFile,1e3),this._totalSamples=S.defaultValue(e.totalSamples,27426),this._samples=new Array(3*this._totalSamples),this._chunkDownloadsInProgress=[];for(var t=this._interpolationOrder,r=this._denominators=new Array(t+1),n=this._xTable=new Array(t+1),a=Math.pow(this._stepSizeDays,t),i=0;i<=t;++i){r[i]=a,n[i]=i*this._stepSizeDays;for(var o=0;o<=t;++o)o!==i&&(r[i]*=i-o);r[i]=1/r[i]}this._work=new Array(t+1),this._coef=new Array(t+1)}he.toGregorianDate=function(e,t){if(!S.defined(e))throw new T.DeveloperError("julianDate is required.");var r=!1,n=ee(e,pe);S.defined(n)||(he.addSeconds(e,-1,pe),n=ee(pe,pe),r=!0);var a=n.dayNumber,i=n.secondsOfDay;43200<=i&&(a+=1);var o=a+68569|0,s=4*o/146097|0,l=4e3*((o=o-((146097*s+3)/4|0)|0)+1)/1461001|0,u=80*(o=o-(1461*l/4|0)+31|0)/2447|0,d=o-(2447*u/80|0)|0,f=u+2-12*(o=u/11|0)|0,c=100*(s-49)+l+o|0,h=i/Z.SECONDS_PER_HOUR|0,p=i-h*Z.SECONDS_PER_HOUR,w=p/Z.SECONDS_PER_MINUTE|0,m=0|(p-=w*Z.SECONDS_PER_MINUTE),y=(p-m)/Z.SECONDS_PER_MILLISECOND;return 23<(h+=12)&&(h-=24),r&&(m+=1),S.defined(t)?(t.year=c,t.month=f,t.day=d,t.hour=h,t.minute=w,t.second=m,t.millisecond=y,t.isLeapSecond=r,t):new Y(c,f,d,h,w,m,y,r)},he.toDate=function(e){if(!S.defined(e))throw new T.DeveloperError("julianDate is required.");var t=he.toGregorianDate(e,J),r=t.second;return t.isLeapSecond&&(r-=1),new Date(Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,r,t.millisecond))},he.toIso8601=function(e,t){if(!S.defined(e))throw new T.DeveloperError("julianDate is required.");var r=he.toGregorianDate(e,J),n=r.year,a=r.month,i=r.day,o=r.hour,s=r.minute,l=r.second,u=r.millisecond;return 1e4===n&&1===a&&1===i&&0===o&&0===s&&0===l&&0===u&&(n=9999,a=12,i=31,o=24),S.defined(t)||0===u?S.defined(t)&&0!==t?V("%04d-%02d-%02dT%02d:%02d:%02d.%sZ",n,a,i,o,s,l,(.01*u).toFixed(t).replace(".","").slice(0,t)):V("%04d-%02d-%02dT%02d:%02d:%02dZ",n,a,i,o,s,l):V("%04d-%02d-%02dT%02d:%02d:%02d.%sZ",n,a,i,o,s,l,(.01*u).toString().replace(".",""))},he.clone=function(e,t){if(S.defined(e))return S.defined(t)?(t.dayNumber=e.dayNumber,t.secondsOfDay=e.secondsOfDay,t):new he(e.dayNumber,e.secondsOfDay,X.TAI)},he.compare=function(e,t){if(!S.defined(e))throw new T.DeveloperError("left is required.");if(!S.defined(t))throw new T.DeveloperError("right is required.");var r=e.dayNumber-t.dayNumber;return 0!==r?r:e.secondsOfDay-t.secondsOfDay},he.equals=function(e,t){return e===t||S.defined(e)&&S.defined(t)&&e.dayNumber===t.dayNumber&&e.secondsOfDay===t.secondsOfDay},he.equalsEpsilon=function(e,t,r){if(!S.defined(r))throw new T.DeveloperError("epsilon is required.");return e===t||S.defined(e)&&S.defined(t)&&Math.abs(he.secondsDifference(e,t))<=r},he.totalDays=function(e){if(!S.defined(e))throw new T.DeveloperError("julianDate is required.");return e.dayNumber+e.secondsOfDay/Z.SECONDS_PER_DAY},he.secondsDifference=function(e,t){if(!S.defined(e))throw new T.DeveloperError("left is required.");if(!S.defined(t))throw new T.DeveloperError("right is required.");return(e.dayNumber-t.dayNumber)*Z.SECONDS_PER_DAY+(e.secondsOfDay-t.secondsOfDay)},he.daysDifference=function(e,t){if(!S.defined(e))throw new T.DeveloperError("left is required.");if(!S.defined(t))throw new T.DeveloperError("right is required.");return e.dayNumber-t.dayNumber+(e.secondsOfDay-t.secondsOfDay)/Z.SECONDS_PER_DAY},he.computeTaiMinusUtc=function(e){Q.julianDate=e;var t=he.leapSeconds,r=W(t,Q,$);return r<0&&(r=~r,--r<0&&(r=0)),t[r].offset},he.addSeconds=function(e,t,r){if(!S.defined(e))throw new T.DeveloperError("julianDate is required.");if(!S.defined(t))throw new T.DeveloperError("seconds is required.");if(!S.defined(r))throw new T.DeveloperError("result is required.");return te(e.dayNumber,e.secondsOfDay+t,r)},he.addMinutes=function(e,t,r){if(!S.defined(e))throw new T.DeveloperError("julianDate is required.");if(!S.defined(t))throw new T.DeveloperError("minutes is required.");if(!S.defined(r))throw new T.DeveloperError("result is required.");var n=e.secondsOfDay+t*Z.SECONDS_PER_MINUTE;return te(e.dayNumber,n,r)},he.addHours=function(e,t,r){if(!S.defined(e))throw new T.DeveloperError("julianDate is required.");if(!S.defined(t))throw new T.DeveloperError("hours is required.");if(!S.defined(r))throw new T.DeveloperError("result is required.");var n=e.secondsOfDay+t*Z.SECONDS_PER_HOUR;return te(e.dayNumber,n,r)},he.addDays=function(e,t,r){if(!S.defined(e))throw new T.DeveloperError("julianDate is required.");if(!S.defined(t))throw new T.DeveloperError("days is required.");if(!S.defined(r))throw new T.DeveloperError("result is required.");return te(e.dayNumber+t,e.secondsOfDay,r)},he.lessThan=function(e,t){return he.compare(e,t)<0},he.lessThanOrEquals=function(e,t){return he.compare(e,t)<=0},he.greaterThan=function(e,t){return 0<he.compare(e,t)},he.greaterThanOrEquals=function(e,t){return 0<=he.compare(e,t)},he.prototype.clone=function(e){return he.clone(this,e)},he.prototype.equals=function(e){return he.equals(this,e)},he.prototype.equalsEpsilon=function(e,t){return he.equalsEpsilon(this,e,t)},he.prototype.toString=function(){return he.toIso8601(this)},he.leapSeconds=[new G(new he(2441317,43210,X.TAI),10),new G(new he(2441499,43211,X.TAI),11),new G(new he(2441683,43212,X.TAI),12),new G(new he(2442048,43213,X.TAI),13),new G(new he(2442413,43214,X.TAI),14),new G(new he(2442778,43215,X.TAI),15),new G(new he(2443144,43216,X.TAI),16),new G(new he(2443509,43217,X.TAI),17),new G(new he(2443874,43218,X.TAI),18),new G(new he(2444239,43219,X.TAI),19),new G(new he(2444786,43220,X.TAI),20),new G(new he(2445151,43221,X.TAI),21),new G(new he(2445516,43222,X.TAI),22),new G(new he(2446247,43223,X.TAI),23),new G(new he(2447161,43224,X.TAI),24),new G(new he(2447892,43225,X.TAI),25),new G(new he(2448257,43226,X.TAI),26),new G(new he(2448804,43227,X.TAI),27),new G(new he(2449169,43228,X.TAI),28),new G(new he(2449534,43229,X.TAI),29),new G(new he(2450083,43230,X.TAI),30),new G(new he(2450630,43231,X.TAI),31),new G(new he(2451179,43232,X.TAI),32),new G(new he(2453736,43233,X.TAI),33),new G(new he(2454832,43234,X.TAI),34),new G(new he(2456109,43235,X.TAI),35),new G(new he(2457204,43236,X.TAI),36),new G(new he(2457754,43237,X.TAI),37)],we.NONE=Object.freeze({getPromiseToLoad:function(){return S.when()},compute:function(e,t){return S.defined(t)?(t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0):t=new L(0,0,0,0,0),t}}),we.prototype.getPromiseToLoad=function(){return S.when(this._downloadPromise)},we.prototype.compute=function(e,t){if(S.defined(this._samples)){if(S.defined(t)||(t=new L(0,0,0,0,0)),0===this._samples.length)return t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0,t;var r=this._dates,n=this._lastIndex,a=0,i=0;if(S.defined(n)){var o=r[n],s=r[n+1],l=he.lessThanOrEquals(o,e),u=!S.defined(s),d=u||he.greaterThanOrEquals(s,e);if(l&&d)return a=n,!u&&s.equals(e)&&++a,i=a+1,Oe(this,r,this._samples,e,a,i,t),t}var f=W(r,e,he.compare,this._dateColumn);return 0<=f?(f<r.length-1&&r[f+1].equals(e)&&++f,i=a=f):(a=(i=~f)-1)<0&&(a=0),this._lastIndex=a,Oe(this,r,this._samples,e,a,i,t),t}if(S.defined(this._dataError))throw new c.RuntimeError(this._dataError)},Ee.fromQuaternion=function(e,t){if(!S.defined(e))throw new T.DeveloperError("quaternion is required");S.defined(t)||(t=new Ee);var r=2*(e.w*e.y-e.z*e.x),n=1-2*(e.x*e.x+e.y*e.y),a=2*(e.w*e.x+e.y*e.z),i=1-2*(e.y*e.y+e.z*e.z),o=2*(e.w*e.z+e.x*e.y);return t.heading=-Math.atan2(o,i),t.roll=Math.atan2(a,n),t.pitch=-g.CesiumMath.asinClamped(r),t},Ee.fromDegrees=function(e,t,r,n){if(!S.defined(e))throw new T.DeveloperError("heading is required");if(!S.defined(t))throw new T.DeveloperError("pitch is required");if(!S.defined(r))throw new T.DeveloperError("roll is required");return S.defined(n)||(n=new Ee),n.heading=e*g.CesiumMath.RADIANS_PER_DEGREE,n.pitch=t*g.CesiumMath.RADIANS_PER_DEGREE,n.roll=r*g.CesiumMath.RADIANS_PER_DEGREE,n},Ee.clone=function(e,t){if(S.defined(e))return S.defined(t)?(t.heading=e.heading,t.pitch=e.pitch,t.roll=e.roll,t):new Ee(e.heading,e.pitch,e.roll)},Ee.equals=function(e,t){return e===t||S.defined(e)&&S.defined(t)&&e.heading===t.heading&&e.pitch===t.pitch&&e.roll===t.roll},Ee.equalsEpsilon=function(e,t,r,n){return e===t||S.defined(e)&&S.defined(t)&&g.CesiumMath.equalsEpsilon(e.heading,t.heading,r,n)&&g.CesiumMath.equalsEpsilon(e.pitch,t.pitch,r,n)&&g.CesiumMath.equalsEpsilon(e.roll,t.roll,r,n)},Ee.prototype.clone=function(e){return Ee.clone(this,e)},Ee.prototype.equals=function(e){return Ee.equals(this,e)},Ee.prototype.equalsEpsilon=function(e,t,r){return Ee.equalsEpsilon(this,e,t,r)},Ee.prototype.toString=function(){return"("+this.heading+", "+this.pitch+", "+this.roll+")"};var _e=new he(0,0,X.TAI);function Me(e,t,r){var n=_e;return n.dayNumber=t,n.secondsOfDay=r,he.daysDifference(n,e._sampleZeroDateTT)}function Se(o,s){if(o._chunkDownloadsInProgress[s])return o._chunkDownloadsInProgress[s];var e,l=S.when.defer();o._chunkDownloadsInProgress[s]=l;var t=o._xysFileUrlTemplate;return e=S.defined(t)?t.getDerivedResource({templateValues:{0:s}}):new d.Resource({url:d.buildModuleUrl("Assets/IAU2006_XYS/IAU2006_XYS_"+s+".json")}),S.when(e.fetchJson(),function(e){o._chunkDownloadsInProgress[s]=!1;for(var t=o._samples,r=e.samples,n=s*o._samplesPerXysFile*3,a=0,i=r.length;a<i;++a)t[n+a]=r[a];l.resolve()}),l.promise}ve.prototype.preload=function(e,t,r,n){var a=Me(this,e,t),i=Me(this,r,n),o=a/this._stepSizeDays-this._interpolationOrder/2|0;o<0&&(o=0);var s=i/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;s>=this._totalSamples&&(s=this._totalSamples-1);for(var l=o/this._samplesPerXysFile|0,u=s/this._samplesPerXysFile|0,d=[],f=l;f<=u;++f)d.push(Se(this,f));return S.when.all(d)},ve.prototype.computeXysRadians=function(e,t,r){var n=Me(this,e,t);if(!(n<0)){var a=n/this._stepSizeDays|0;if(!(a>=this._totalSamples)){var i=this._interpolationOrder,o=a-(i/2|0);o<0&&(o=0);var s=o+i;s>=this._totalSamples&&(o=(s=this._totalSamples-1)-i)<0&&(o=0);var l=!1,u=this._samples;if(S.defined(u[3*o])||(Se(this,o/this._samplesPerXysFile|0),l=!0),S.defined(u[3*s])||(Se(this,s/this._samplesPerXysFile|0),l=!0),!l){S.defined(r)?(r.x=0,r.y=0,r.s=0):r=new De(0,0,0);var d,f,c=n-o*this._stepSizeDays,h=this._work,p=this._denominators,w=this._coef,m=this._xTable;for(d=0;d<=i;++d)h[d]=c-m[d];for(d=0;d<=i;++d){for(w[d]=1,f=0;f<=i;++f)f!==d&&(w[d]*=h[f]);w[d]*=p[d];var y=3*(o+d);r.x+=w[d]*u[y++],r.y+=w[d]*u[y++],r.s+=w[d]*u[y]}return r}}}};var Te={},ge={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},Pe={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},be={},Ne={east:new f.Cartesian3,north:new f.Cartesian3,up:new f.Cartesian3,west:new f.Cartesian3,south:new f.Cartesian3,down:new f.Cartesian3},Re=new f.Cartesian3,Ie=new f.Cartesian3,qe=new f.Cartesian3;Te.localFrameToFixedFrameGenerator=function(o,s){if(!ge.hasOwnProperty(o)||!ge[o].hasOwnProperty(s))throw new T.DeveloperError("firstAxis and secondAxis must be east, north, up, west, south or down.");var e,l=ge[o][s],t=o+s;return S.defined(be[t])?e=be[t]:(e=function(e,t,r){if(!S.defined(e))throw new T.DeveloperError("origin is required.");if(S.defined(r)||(r=new P.Matrix4),f.Cartesian3.equalsEpsilon(e,f.Cartesian3.ZERO,g.CesiumMath.EPSILON14))f.Cartesian3.unpack(Pe[o],0,Re),f.Cartesian3.unpack(Pe[s],0,Ie),f.Cartesian3.unpack(Pe[l],0,qe);else if(g.CesiumMath.equalsEpsilon(e.x,0,g.CesiumMath.EPSILON14)&&g.CesiumMath.equalsEpsilon(e.y,0,g.CesiumMath.EPSILON14)){var n=g.CesiumMath.sign(e.z);f.Cartesian3.unpack(Pe[o],0,Re),"east"!==o&&"west"!==o&&f.Cartesian3.multiplyByScalar(Re,n,Re),f.Cartesian3.unpack(Pe[s],0,Ie),"east"!==s&&"west"!==s&&f.Cartesian3.multiplyByScalar(Ie,n,Ie),f.Cartesian3.unpack(Pe[l],0,qe),"east"!==l&&"west"!==l&&f.Cartesian3.multiplyByScalar(qe,n,qe)}else{(t=S.defaultValue(t,u.Ellipsoid.WGS84)).geodeticSurfaceNormal(e,Ne.up);var a=Ne.up,i=Ne.east;i.x=-e.y,i.y=e.x,i.z=0,f.Cartesian3.normalize(i,Ne.east),f.Cartesian3.cross(a,i,Ne.north),f.Cartesian3.multiplyByScalar(Ne.up,-1,Ne.down),f.Cartesian3.multiplyByScalar(Ne.east,-1,Ne.west),f.Cartesian3.multiplyByScalar(Ne.north,-1,Ne.south),Re=Ne[o],Ie=Ne[s],qe=Ne[l]}return r[0]=Re.x,r[1]=Re.y,r[2]=Re.z,r[3]=0,r[4]=Ie.x,r[5]=Ie.y,r[6]=Ie.z,r[7]=0,r[8]=qe.x,r[9]=qe.y,r[10]=qe.z,r[11]=0,r[12]=e.x,r[13]=e.y,r[14]=e.z,r[15]=1,r},be[t]=e),e},Te.eastNorthUpToFixedFrame=Te.localFrameToFixedFrameGenerator("east","north"),Te.northEastDownToFixedFrame=Te.localFrameToFixedFrameGenerator("north","east"),Te.northUpEastToFixedFrame=Te.localFrameToFixedFrameGenerator("north","up"),Te.northWestUpToFixedFrame=Te.localFrameToFixedFrameGenerator("north","west");var Ae=new w,Ue=new f.Cartesian3(1,1,1),ke=new P.Matrix4;Te.headingPitchRollToFixedFrame=function(e,t,r,n,a){T.Check.typeOf.object("HeadingPitchRoll",t),n=S.defaultValue(n,Te.eastNorthUpToFixedFrame);var i=w.fromHeadingPitchRoll(t,Ae),o=P.Matrix4.fromTranslationQuaternionRotationScale(f.Cartesian3.ZERO,i,Ue,ke);return a=n(e,r,a),P.Matrix4.multiply(a,o,a)};var je=new P.Matrix4,ze=new P.Matrix3;Te.headingPitchRollQuaternion=function(e,t,r,n,a){T.Check.typeOf.object("HeadingPitchRoll",t);var i=Te.headingPitchRollToFixedFrame(e,t,r,n,je),o=P.Matrix4.getMatrix3(i,ze);return w.fromRotationMatrix(o,a)};var Fe=new f.Cartesian3(1,1,1),We=new f.Cartesian3,Le=new P.Matrix4,Ve=new P.Matrix4,Ye=new P.Matrix3,Be=new w;Te.fixedFrameToHeadingPitchRoll=function(e,t,r,n){T.Check.defined("transform",e),t=S.defaultValue(t,u.Ellipsoid.WGS84),r=S.defaultValue(r,Te.eastNorthUpToFixedFrame),S.defined(n)||(n=new Ee);var a=P.Matrix4.getTranslation(e,We);if(f.Cartesian3.equals(a,f.Cartesian3.ZERO))return n.heading=0,n.pitch=0,n.roll=0,n;var i=P.Matrix4.inverseTransformation(r(a,t,Le),Le),o=P.Matrix4.setScale(e,Fe,Ve);o=P.Matrix4.setTranslation(o,f.Cartesian3.ZERO,o),i=P.Matrix4.multiply(i,o,i);var s=w.fromRotationMatrix(P.Matrix4.getMatrix3(i,Ye),Be);return s=w.normalize(s,s),Ee.fromQuaternion(s,n)};var Ge=g.CesiumMath.TWO_PI/86400,Ze=new he;Te.computeTemeToPseudoFixedMatrix=function(e,t){if(!S.defined(e))throw new T.DeveloperError("date is required.");var r,n=(Ze=he.addSeconds(e,-he.computeTaiMinusUtc(e),Ze)).dayNumber,a=Ze.secondsOfDay,i=n-2451545,o=(24110.54841+(r=43200<=a?(i+.5)/Z.DAYS_PER_JULIAN_CENTURY:(i-.5)/Z.DAYS_PER_JULIAN_CENTURY)*(8640184.812866+r*(.093104+-62e-7*r)))*Ge%g.CesiumMath.TWO_PI+(72921158553e-15+11772758384668e-32*(n-2451545.5))*((a+.5*Z.SECONDS_PER_DAY)%Z.SECONDS_PER_DAY),s=Math.cos(o),l=Math.sin(o);return S.defined(t)?(t[0]=s,t[1]=-l,t[2]=0,t[3]=l,t[4]=s,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t):new P.Matrix3(s,l,0,-l,s,0,0,0,1)},Te.iau2006XysData=new ve,Te.earthOrientationParameters=we.NONE;var Xe=32.184;Te.preloadIcrfFixed=function(e){var t=e.start.dayNumber,r=e.start.secondsOfDay+Xe,n=e.stop.dayNumber,a=e.stop.secondsOfDay+Xe,i=Te.iau2006XysData.preload(t,r,n,a),o=Te.earthOrientationParameters.getPromiseToLoad();return S.when.all([i,o])},Te.computeIcrfToFixedMatrix=function(e,t){if(!S.defined(e))throw new T.DeveloperError("date is required.");S.defined(t)||(t=new P.Matrix3);var r=Te.computeFixedToIcrfMatrix(e,t);if(S.defined(r))return P.Matrix3.transpose(r,t)};var Je=new De(0,0,0),He=new L(0,0,0,0,0,0),$e=new P.Matrix3,Qe=new P.Matrix3;Te.computeFixedToIcrfMatrix=function(e,t){if(!S.defined(e))throw new T.DeveloperError("date is required.");S.defined(t)||(t=new P.Matrix3);var r=Te.earthOrientationParameters.compute(e,He);if(S.defined(r)){var n=e.dayNumber,a=e.secondsOfDay+Xe,i=Te.iau2006XysData.computeXysRadians(n,a,Je);if(S.defined(i)){var o=i.x+r.xPoleOffset,s=i.y+r.yPoleOffset,l=1/(1+Math.sqrt(1-o*o-s*s)),u=$e;u[0]=1-l*o*o,u[3]=-l*o*s,u[6]=o,u[1]=-l*o*s,u[4]=1-l*s*s,u[7]=s,u[2]=-o,u[5]=-s,u[8]=1-l*(o*o+s*s);var d=P.Matrix3.fromRotationZ(-i.s,Qe),f=P.Matrix3.multiply(u,d,$e),c=e.dayNumber-2451545,h=(e.secondsOfDay-he.computeTaiMinusUtc(e)+r.ut1MinusUtc)/Z.SECONDS_PER_DAY,p=.779057273264+h+.00273781191135448*(c+h);p=p%1*g.CesiumMath.TWO_PI;var w=P.Matrix3.fromRotationZ(p,Qe),m=P.Matrix3.multiply(f,w,$e),y=Math.cos(r.xPoleWander),C=Math.cos(r.yPoleWander),x=Math.sin(r.xPoleWander),O=Math.sin(r.yPoleWander),E=n-2451545+a/Z.SECONDS_PER_DAY,D=-47e-6*(E/=36525)*g.CesiumMath.RADIANS_PER_DEGREE/3600,v=Math.cos(D),_=Math.sin(D),M=Qe;return M[0]=y*v,M[1]=y*_,M[2]=x,M[3]=-C*_+O*x*v,M[4]=C*v+O*x*_,M[5]=-O*y,M[6]=-O*_-C*x*v,M[7]=O*v-C*x*_,M[8]=C*y,P.Matrix3.multiply(m,M,t)}}};var Ke=new i.Cartesian4;Te.pointToWindowCoordinates=function(e,t,r,n){return(n=Te.pointToGLWindowCoordinates(e,t,r,n)).y=2*t[5]-n.y,n},Te.pointToGLWindowCoordinates=function(e,t,r,n){if(!S.defined(e))throw new T.DeveloperError("modelViewProjectionMatrix is required.");if(!S.defined(t))throw new T.DeveloperError("viewportTransformation is required.");if(!S.defined(r))throw new T.DeveloperError("point is required.");S.defined(n)||(n=new u.Cartesian2);var a=Ke;return P.Matrix4.multiplyByVector(e,i.Cartesian4.fromElements(r.x,r.y,r.z,1,a),a),i.Cartesian4.multiplyByScalar(a,1/a.w,a),P.Matrix4.multiplyByVector(t,a,a),u.Cartesian2.fromCartesian4(a,n)};var et=new f.Cartesian3,tt=new f.Cartesian3,rt=new f.Cartesian3;Te.rotationMatrixFromPositionVelocity=function(e,t,r,n){if(!S.defined(e))throw new T.DeveloperError("position is required.");if(!S.defined(t))throw new T.DeveloperError("velocity is required.");var a=S.defaultValue(r,u.Ellipsoid.WGS84).geodeticSurfaceNormal(e,et),i=f.Cartesian3.cross(t,a,tt);f.Cartesian3.equalsEpsilon(i,f.Cartesian3.ZERO,g.CesiumMath.EPSILON6)&&(i=f.Cartesian3.clone(f.Cartesian3.UNIT_X,i));var o=f.Cartesian3.cross(i,t,rt);return f.Cartesian3.normalize(o,o),f.Cartesian3.cross(t,o,i),f.Cartesian3.negate(i,i),f.Cartesian3.normalize(i,i),S.defined(n)||(n=new P.Matrix3),n[0]=t.x,n[1]=t.y,n[2]=t.z,n[3]=i.x,n[4]=i.y,n[5]=i.z,n[6]=o.x,n[7]=o.y,n[8]=o.z,n};var nt=new P.Matrix4(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),at=new f.Cartographic,it=new f.Cartesian3,ot=new f.Cartesian3,st=new P.Matrix3,lt=new P.Matrix4,ut=new P.Matrix4;Te.basisTo2D=function(e,t,r){if(!S.defined(e))throw new T.DeveloperError("projection is required.");if(!S.defined(t))throw new T.DeveloperError("matrix is required.");if(!S.defined(r))throw new T.DeveloperError("result is required.");var n=P.Matrix4.getTranslation(t,ot),a=e.ellipsoid,i=a.cartesianToCartographic(n,at),o=e.project(i,it);f.Cartesian3.fromElements(o.z,o.x,o.y,o);var s=Te.eastNorthUpToFixedFrame(n,a,lt),l=P.Matrix4.inverseTransformation(s,ut),u=P.Matrix4.getMatrix3(t,st),d=P.Matrix4.multiplyByMatrix3(l,u,r);return P.Matrix4.multiply(nt,d,r),P.Matrix4.setTranslation(r,o,r),r},Te.wgs84To2DModelMatrix=function(e,t,r){if(!S.defined(e))throw new T.DeveloperError("projection is required.");if(!S.defined(t))throw new T.DeveloperError("center is required.");if(!S.defined(r))throw new T.DeveloperError("result is required.");var n=e.ellipsoid,a=Te.eastNorthUpToFixedFrame(t,n,lt),i=P.Matrix4.inverseTransformation(a,ut),o=n.cartesianToCartographic(t,at),s=e.project(o,it);f.Cartesian3.fromElements(s.z,s.x,s.y,s);var l=P.Matrix4.fromTranslation(s,lt);return P.Matrix4.multiply(nt,i,r),P.Matrix4.multiply(l,r,r),r},Te.buildUp=function(e,t){var r=t.clone(),n=e.clone();n=f.Cartesian3.normalize(n,n),1<=Math.abs(f.Cartesian3.dot(n,r))&&(n=Math.abs(f.Cartesian3.dot(r,f.Cartesian3.UNIT_Y))<1?f.Cartesian3.clone(f.Cartesian3.UNIT_Y,n):f.Cartesian3.clone(f.Cartesian3.UNIT_Z,n));var a=new f.Cartesian3;return f.Cartesian3.cross(n,r,a),a=f.Cartesian3.normalize(a,a),f.Cartesian3.cross(r,a,n),n=f.Cartesian3.normalize(n,n)},Te.getHeading=function(e,t){var r;return r=g.CesiumMath.equalsEpsilon(Math.abs(e.z),1,g.CesiumMath.EPSILON3)?Math.atan2(t.y,t.x)-g.CesiumMath.PI_OVER_TWO:Math.atan2(e.y,e.x)-g.CesiumMath.PI_OVER_TWO,g.CesiumMath.TWO_PI-g.CesiumMath.zeroToTwoPi(r)},Te.convertToColumbusCartesian=function(e){var t=new P.GeographicProjection,r=t.ellipsoid,n=new f.Cartesian3,a=new f.Cartographic;return r.cartesianToCartographic(e,a),t.project(a,n),f.Cartesian3.fromElements(n.z,n.x,n.y)},Te.convertTo3DCartesian=function(e){var t=new P.GeographicProjection,r=t.ellipsoid,n=new f.Cartesian3,a=new f.Cartographic;return n=f.Cartesian3.fromElements(e.y,e.z,e.x),t.unproject(n,a),r.cartographicToCartesian(a,n)},e.Quaternion=w,e.Transforms=Te});