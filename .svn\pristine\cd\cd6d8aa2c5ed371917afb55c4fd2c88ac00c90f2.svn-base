<template>
  <div id="car-container">
    <div class="title-bg">
      <div class="img-bg">
        <svg-icon icon-class="car-bg" class="icon-logo" />
        <div class="title">项目车辆总数：{{ carNum }}</div>
      </div>
      <el-date-picker
        class="date-time"
        v-model="date"
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      >
      </el-date-picker>
    </div>
    <div id="car-chart">
    </div>
  </div>
</template>

<script>
import { pxToNum } from "@/utils/pxToVw";
export default {
  name: "car",
  props: [],
  data() {
    return {
      carNum: "1,123",
      keyword: 1,
      date: "",
    };
  },
  mounted() {},
  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        this.getWorkHour();
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    async getWorkHour() {
      let deviceName = [];
      let total = [];
      let avgDay = [];
      let res = await this.$http(
        `/equip/build/workHourSummary?sectionId=${this.$store.state.tree.sectionId}&keyword=${this.keyword}`
      );
      res?.result.map((i) => {
        deviceName.push(i.deviceName);
        total.push(i.total);
        avgDay.push(i.avgDay);
      });
      this.xAxis = deviceName;
      this.totalTime = total;
      // this.averageTime = avgDay;
      this.setChart();
    },
    // 初始化
    setChart() {
      let _ = this;
      let myChart = _.$echarts.init(
        document.getElementById("car-chart"),
        null,
        {
          renderer: "svg",
        }
      );
      myChart.setOption({
        color: ["#A6AAF8"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "item",
          },
        },
        grid: {
          left: "1%",
          right: "0%",
          bottom: 20,
          top: 10,
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.xAxis,
            axisPointer: {
              type: "shadow",
            },
            axisLabel: {
              fontSize: pxToNum(14),
            },
          },
        ],
        yAxis: {
          type: "value",
          name: "单位/辆",
          nameTextStyle: {
            fontSize: pxToNum(14),
          },
        },
        // 动条
        dataZoom: [
          {
            show: true, // 是否显示滑动条，不影响使用
            type: "slider",
            startValue: 0,
            endValue: 7,
            bottom: 0,
            height: 20,
          },
        ],
        series: [
          {
            name: "累计时长",
            type: "bar",
            tooltip: {
              valueFormatter: function (value) {
                return value + " ml";
              },
            },
            barWidth: "30%",
            data: this.totalTime,
          },
        ],
      });
    },
  },
};
</script>

<style lang="scss" scoped>
#car-container {
  width: 100%;
  height: 100%;

  .title-bg {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .img-bg {
      width: 530px;
      height: 100%;
      position: relative;

      .icon-logo {
        width: 100%;
        height: 100%;
      }

      .title {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-family: "优设标题黑", sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: 22px;
        color: #3a3f5d;
      }
    }

    .date-time{
      position: absolute;
      top: 8px;
      right: 0;
      width: 220px;
    }
  }

  #car-chart {
    width: 100%;
    height: 300px;
  }
}
</style>
