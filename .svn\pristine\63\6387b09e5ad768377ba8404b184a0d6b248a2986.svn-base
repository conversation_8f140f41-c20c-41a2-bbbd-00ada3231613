export default {
  data() {
    return {
      upload_url: '/equip/fileinfo/upload',
      formData: {}, // 弹出框数据
      rules: [],
      imgList: [],
    }
  },

  methods: {
    onClose(refresh) {
      this.$emit('onClose', refresh)
    },

    handleSuccess(file, files, fileList, key) {
      key = key || 'imgList'
      if (file.code === 0) {
        this[`${key}`].push({
          id: file?.result.id,
          name: file.result.filename
        });
      }
    },

    handleRemove(file, files, key) {
      key = key || 'imgList'
      let remove_url = file.response && file.response.result && file.response.result.id ? file.response.result.id : file.id;
      let remove_index = -1;
      this[`${key}`].forEach((item, index) => {
        if (item.id == remove_url) {
          remove_index = index;
        }
      })
      this[`${key}`].splice(remove_index, 1);
    },
    getPhotoParams(key) {
      key = key || 'imgList'
      return this[`${key}`] ? this[`${key}`].map(item => item.id).join(',') : ''
    },

    onSave(form) {
      this.$refs[`${form || 'form'}`].validate(valid => {
        if (!valid) {
          return false
        }
        this.submit()
      })

    }
  }

}
