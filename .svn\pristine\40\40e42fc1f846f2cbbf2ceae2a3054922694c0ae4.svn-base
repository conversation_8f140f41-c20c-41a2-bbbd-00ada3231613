<template>
  <div id="chart-container">
    <div id="chart-type">
      <div class="center-info" v-if="visible">
        <div class="info-total">{{ total }}</div>
        <div class="info-box"></div>
        <div class="info-title machinery-type-total">类型总数</div>
      </div>
    </div>
    <div class="chart-num">
      <span>比较上周</span>
      <span class="num">{{ " " + num }}</span>
    </div>
  </div>
</template>

<script>
import { pxToNum } from "@/utils/pxToVw";
export default {
  name: "chart",
  props: [],
  data() {
    return {
      chartData: [
        { value: 1048, name: "充电挖机" },
        { value: 735, name: "换电挖机" },
        { value: 580, name: "充电装载机" },
        { value: 484, name: "换电装载机" },
        { value: 300, name: "自卸车" },
        { value: 385, name: "混凝土罐车" },
      ],
      legend: [
        "充电挖机",
        "换电挖机",
        "充电装载机",
        "换电装载机",
        "自卸车",
        "混凝土罐车",
      ],
      total: 3682,
      visible: true,
      num: "+200",
    };
  },
  mounted() {
    this.setChart();
  },
  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        // this.getChartData();
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    async getChartData() {
      let res = await this.$http.get(
        `/equip/build/machineryType?sectionId=${this.$store.state.tree.sectionId}`
      );
      let chart = [];
      let lenData = [];
      let total = 0;
      res?.result.map((item) => {
        chart.push({
          value: Number(item.count),
          name: item.deviceName,
        });
        lenData.push(item.deviceName);
        total += Number(item.count);
      });
      this.total = total;
      this.chartData = chart;
      this.legend = lenData;
      this.visible = res?.result?.length > 0 ? true : false;
      this.setChart();
      console.log("chart", this.chartData, this.legend);
    },
    // 初始化
    setChart() {
      let _ = this;
      let myChart = _.$echarts.init(
        document.getElementById("chart-type"),
        null,
        {
          renderer: "svg",
        }
      );
      myChart.setOption({
        color: [
          "#1F95FF",
          "#3DC159",
          "#F9C918",
          "#ED4566",
          "#834BE0",
          "#2AC3C1",
          "#F4996D",
          "#76C1DE",
        ],
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)",
          padding: 10, // 提示框浮层内边距，
        },
        legend: {
          orient: "vertical",
          top: "3%", // 调整位置
          left: "48%", // 距离右侧位置
          icon: "circle", // 修改小图标为圆形
          itemHeight: 14, // 修改圆形小图标的大小
          data: _.legend,
          itemGap: 12, // 图例之间的间距
          textStyle: {
            fontSize: 12,
          },
          formatter: (name) => {
            // formatter格式化函数动态呈现数据
            var total = 0; // 用于计算总数
            var target; // 遍历拿到数据
            for (var i = 0; i < this.chartData.length; i++) {
              total += this.chartData[i]?.value
                ? this.chartData[i]?.value
                : 0.0;
              if (this.chartData[i].name == name) {
                target = this.chartData[i]?.value
                  ? this.chartData[i]?.value
                  : 0.0;
              }
            }
            var x =
              target == 0 && total == 0
                ? 0.0
                : Math.round((target / total) * 10000) / 100;
            return `${name}: ${x?.toFixed(_.fractNum)}%`;
          },
        },
        series: [
          {
            name: "机械类型",
            type: "pie",
            radius: ["40%", "65%"],
            center: ["24%", "50%"], //设置圆心
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2,
            },
            label: {
              show: false,
              position: "center",
            },
            data: _.chartData,
          },
        ],
      });
    },
  },
};
</script>

<style lang="scss" scoped>
#chart-container {
  width: 100%;
  height: 100%;

  #chart-type {
    width: 100%;
    height: 210px;
    position: relative;
    .center-info {
      position: absolute;
      left: 13%;
      top: 38%;
      width: 100px;
      height: 80px;

      .info-total {
        width: 100%;
        height: 30px;
        line-height: 30px;
        font-size: 20px;
        text-align: center;
        font-weight: bold;
      }

      .info-box {
        width: 100%;
        height: 4px;
        background: linear-gradient(45deg, #c2c2ca, #a5a9fa);
      }

      .info-title {
        width: 100%;
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        text-align: center;
      }
    }
  }

  .chart-num {
    width: 100%;
    height: 40px;
    border-top: 1px solid #e2e2e2;
    line-height: 40px;
    text-align: left;
    font-family: "思源黑体 CN", sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 14px;

    .num {
      color: #5aca72;
    }
  }
}
</style>
