/**
 * Cesium - https://github.com/CesiumGS/cesium
 *
 * Copyright 2011-2020 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/master/LICENSE.md for full licensing details.
 */
define(["./when-8d13db60","./Check-70bec281","./Math-61ede240","./Cartographic-f27b0939","./Cartesian2-09435a6c","./BoundingSphere-c409f092","./Cartesian4-5af5bb24","./RuntimeError-ba10bc3e","./WebGLConstants-4c11ee5f","./ComponentDatatype-5862616f","./FeatureDetection-7bd32c34","./Transforms-1509c877","./buildModuleUrl-392763e2","./AttributeCompression-75ce15eb","./IndexDatatype-9435b55f","./IntersectionTests-dbfba52c","./Plane-2bcb9154","./WebMercatorProjection-bc9aa7fe","./createTaskProcessorWorker","./EllipsoidTangentPlane-9c25b2da","./OrientedBoundingBox-7b25e901","./TerrainEncoding-3dab0ca0"],function(Te,f,pe,ve,Ee,ye,e,t,r,i,n,we,o,be,Ne,a,s,Me,d,Ce,xe,Se){function Ae(){f.DeveloperError.throwInstantiationError()}Object.defineProperties(Ae.prototype,{errorEvent:{get:f.DeveloperError.throwInstantiationError},credit:{get:f.DeveloperError.throwInstantiationError},tilingScheme:{get:f.DeveloperError.throwInstantiationError},ready:{get:f.DeveloperError.throwInstantiationError},readyPromise:{get:f.DeveloperError.throwInstantiationError},hasWaterMask:{get:f.DeveloperError.throwInstantiationError},hasVertexNormals:{get:f.DeveloperError.throwInstantiationError},availability:{get:f.DeveloperError.throwInstantiationError}});var h=[];Ae.getRegularGridIndices=function(e,t){if(e*t>=pe.CesiumMath.FOUR_GIGABYTES)throw new f.DeveloperError("The total number of vertices (width * height) must be less than 4,294,967,296.");var r=h[e];Te.defined(r)||(h[e]=r=[]);var i=r[t];return Te.defined(i)||v(e,t,i=e*t<pe.CesiumMath.SIXTY_FOUR_KILOBYTES?r[t]=new Uint16Array((e-1)*(t-1)*6+3*(e+t-2)):r[t]=new Uint32Array((e-1)*(t-1)*6+3*(e+t-2)),0),i},Ae.getRegularGridIndicesForReproject=function(e,t){if(e*t>=pe.CesiumMath.FOUR_GIGABYTES)throw new f.DeveloperError("The total number of vertices (width * height) must be less than 4,294,967,296.");var r=h[e];Te.defined(r)||(h[e]=r=[]);var i=r[t];return Te.defined(i)||v(e,t,i=e*t<pe.CesiumMath.SIXTY_FOUR_KILOBYTES?r[t]=new Uint16Array((e-1)*(t-1)*6):r[t]=new Uint32Array((e-1)*(t-1)*6),0),i};var c=[];Ae.getRegularGridIndicesAndEdgeIndices=function(e,t){if(e*t>=pe.CesiumMath.FOUR_GIGABYTES)throw new f.DeveloperError("The total number of vertices (width * height) must be less than 4,294,967,296.");var r=c[e];Te.defined(r)||(c[e]=r=[]);var i=r[t];if(!Te.defined(i)){var n=Ae.getRegularGridIndices(e,t),o=p(e,t),a=o.westIndicesSouthToNorth,s=o.southIndicesEastToWest,d=o.eastIndicesNorthToSouth,h=o.northIndicesWestToEast;i=r[t]={indices:n,westIndicesSouthToNorth:a,southIndicesEastToWest:s,eastIndicesNorthToSouth:d,northIndicesWestToEast:h}}return i};var T=[];function p(e,t){var r,i=new Array(t),n=new Array(e),o=new Array(t),a=new Array(e);for(r=0;r<e;++r)n[a[r]=r]=e*t-1-r;for(r=0;r<t;++r)o[r]=(r+1)*e-1,i[r]=(t-r-1)*e;return{westIndicesSouthToNorth:i,southIndicesEastToWest:n,eastIndicesNorthToSouth:o,northIndicesWestToEast:a}}function v(e,t,r,i){for(var n=0,o=0;o<t-1;++o){for(var a=0;a<e-1;++a){var s=n,d=s+e,h=d+1,c=s+1;r[i++]=s,r[i++]=d,r[i++]=c,r[i++]=c,r[i++]=d,r[i++]=h,++n}++n}var u=(t-1)/2,l=(e-1)/2;for(a=n=0;a<l;a++)r[i++]=n,r[i++]=n+1,r[i++]=n+2,n+=2;n=e*(t-1);for(a=0;a<l;a++)r[i++]=n+1,r[i++]=n,r[i++]=n+2,n+=2;for(a=n=0;a<u;a++)r[i++]=n+e,r[i++]=n,r[i++]=n+2*e,n+=2*e;n=e-1;for(a=0;a<u;a++)r[i++]=n,r[i++]=n+e,r[i++]=n+2*e,n+=2*e}function u(e,t,r,i,n){for(var o=Te.defined(n),a=e[0],s=e.length,d=1;d<s;++d){var h=e[d];!o||n[a+"_"+h]?(r[i++]=a,r[i++]=h,r[i++]=t,r[i++]=t,r[i++]=h,r[i++]=t+1,a=h,++t):(a=h,++t)}return i}Ae.getRegularGridAndSkirtIndicesAndEdgeIndices=function(e,t){if(e*t>=pe.CesiumMath.FOUR_GIGABYTES)throw new f.DeveloperError("The total number of vertices (width * height) must be less than 4,294,967,296.");var r=T[e];Te.defined(r)||(T[e]=r=[]);var i=r[t];if(!Te.defined(i)){var n=e*t,o=(e-1)*(t-1)*6,a=2*e+2*t,s=n+a,d=3*(e+t-2),h=o+6*Math.max(0,a-4)+d,c=p(e,t),u=c.westIndicesSouthToNorth,l=c.southIndicesEastToWest,I=c.eastIndicesNorthToSouth,m=c.northIndicesWestToEast,g=Ne.IndexDatatype.createTypedArray(s,h);v(e,t,g,0),Ae.addSkirtIndices(u,l,I,m,n,g,o+d),i=r[t]={indices:g,westIndicesSouthToNorth:u,southIndicesEastToWest:l,eastIndicesNorthToSouth:I,northIndicesWestToEast:m,indexCountWithoutSkirts:o}}return i},Ae.addSkirtIndices=function(e,t,r,i,n,o,a,s){var d=n;a=u(e,d,o,a,s),a=u(t,d+=e.length,o,a,s),a=u(r,d+=t.length,o,a,s),u(i,d+=r.length,o,a,s)},Ae.heightmapTerrainQuality=.25,Ae.getEstimatedLevelZeroGeometricErrorForAHeightmap=function(e,t,r){return 2*e.maximumRadius*Math.PI*Ae.heightmapTerrainQuality/(t*r)},Ae.prototype.requestTileGeometry=f.DeveloperError.throwInstantiationError,Ae.prototype.getLevelMaximumGeometricError=f.DeveloperError.throwInstantiationError,Ae.prototype.getTileDataAvailable=f.DeveloperError.throwInstantiationError,Ae.prototype.loadTileDataAvailability=f.DeveloperError.throwInstantiationError;var Pe=32767,_e=new ve.Cartesian3,Fe=new ve.Cartesian3,Be=new ve.Cartesian3,De=new ve.Cartographic,We=new Ee.Cartesian2,Ge=new ve.Cartesian3,Oe=new ye.Matrix4,Ye=new ye.Matrix4;function ke(e,t,r,i,n,o,a,s,d){var h=Number.POSITIVE_INFINITY,c=n.north,u=n.south,l=n.east,I=n.west;l<I&&(l+=pe.CesiumMath.TWO_PI);for(var m=e.length,g=0;g<m;++g){var f=e[g],T=r[f],p=i[f];De.longitude=pe.CesiumMath.lerp(I,l,p.x),De.latitude=pe.CesiumMath.lerp(u,c,p.y),De.height=T-t;var v=o.cartographicToCartesian(De,_e);ye.Matrix4.multiplyByPoint(a,v,v),ve.Cartesian3.minimumByComponent(v,s,s),ve.Cartesian3.maximumByComponent(v,d,d),h=Math.min(h,De.height)}return h}function Ve(e,t,r,i,n,o,a,s,d,h,c,u,l,I,m){var g=Te.defined(a),f=d.north,T=d.south,p=d.east,v=d.west;p<v&&(p+=pe.CesiumMath.TWO_PI);for(var E=r.length,y=0;y<E;++y){var w=r[y],b=n[w],N=o[w];De.longitude=pe.CesiumMath.lerp(v,p,N.x)+I,De.latitude=pe.CesiumMath.lerp(T,f,N.y)+m,De.height=b-h;var M,C=s.cartographicToCartesian(De,_e);if(g){var x=2*w;if(We.x=a[x],We.y=a[x+1],1!==c){var S=be.AttributeCompression.octDecode(We.x,We.y,Ge),A=we.Transforms.eastNorthUpToFixedFrame(_e,s,Ye),P=ye.Matrix4.inverseTransformation(A,Oe);ye.Matrix4.multiplyByPointAsVector(P,S,S),S.z*=c,ve.Cartesian3.normalize(S,S),ye.Matrix4.multiplyByPointAsVector(A,S,S),ve.Cartesian3.normalize(S,S),be.AttributeCompression.octEncode(S,We)}}i.hasWebMercatorT&&(M=(Me.WebMercatorProjection.geodeticLatitudeToMercatorAngle(De.latitude)-u)*l),t=i.encode(e,t,C,N,De.height,We,M)}}function Re(e,t){var r;return"function"==typeof e.slice&&"function"!=typeof(r=e.slice()).sort&&(r=void 0),Te.defined(r)||(r=Array.prototype.slice.call(e)),r.sort(t),r}return d(function(e,t){var r,i,n=e.quantizedVertices,o=n.length/3,a=e.octEncodedNormals,s=e.westIndices.length+e.eastIndices.length+e.southIndices.length+e.northIndices.length,d=e.includeWebMercatorT,h=Ee.Rectangle.clone(e.rectangle),c=h.west,u=h.south,l=h.east,I=h.north,m=Ee.Ellipsoid.clone(e.ellipsoid),g=e.exaggeration,f=e.minimumHeight*g,T=e.maximumHeight*g,p=e.relativeToCenter,v=we.Transforms.eastNorthUpToFixedFrame(p,m),E=ye.Matrix4.inverseTransformation(v,new ye.Matrix4);d&&(r=Me.WebMercatorProjection.geodeticLatitudeToMercatorAngle(u),i=1/(Me.WebMercatorProjection.geodeticLatitudeToMercatorAngle(I)-r));var y=n.subarray(0,o),w=n.subarray(o,2*o),b=n.subarray(2*o,3*o),N=Te.defined(a),M=new Array(o),C=new Array(o),x=new Array(o),S=d?new Array(o):[],A=Fe;A.x=Number.POSITIVE_INFINITY,A.y=Number.POSITIVE_INFINITY,A.z=Number.POSITIVE_INFINITY;var P=Be;P.x=Number.NEGATIVE_INFINITY,P.y=Number.NEGATIVE_INFINITY,P.z=Number.NEGATIVE_INFINITY;for(var _=Number.POSITIVE_INFINITY,F=Number.NEGATIVE_INFINITY,B=Number.POSITIVE_INFINITY,D=Number.NEGATIVE_INFINITY,W=0;W<o;++W){var G=y[W],O=w[W],Y=G/Pe,k=O/Pe,V=pe.CesiumMath.lerp(f,T,b[W]/Pe);De.longitude=pe.CesiumMath.lerp(c,l,Y),De.latitude=pe.CesiumMath.lerp(u,I,k),De.height=V,_=Math.min(De.longitude,_),F=Math.max(De.longitude,F),B=Math.min(De.latitude,B),D=Math.max(De.latitude,D);var R=m.cartographicToCartesian(De);M[W]=new Ee.Cartesian2(Y,k),C[W]=V,x[W]=R,d&&(S[W]=(Me.WebMercatorProjection.geodeticLatitudeToMercatorAngle(De.latitude)-r)*i),ye.Matrix4.multiplyByPoint(E,R,_e),ve.Cartesian3.minimumByComponent(_e,A,A),ve.Cartesian3.maximumByComponent(_e,P,P)}var U,H,z,L=Re(e.westIndices,function(e,t){return M[e].y-M[t].y}),j=Re(e.eastIndices,function(e,t){return M[t].y-M[e].y}),q=Re(e.southIndices,function(e,t){return M[t].x-M[e].x}),K=Re(e.northIndices,function(e,t){return M[e].x-M[t].x});H=ye.BoundingSphere.fromPoints(x),U=xe.OrientedBoundingBox.fromRectangle(h,f,T,m),(1!==g||f<0)&&(z=new Se.EllipsoidalOccluder(m).computeHorizonCullingPointPossiblyUnderEllipsoid(p,x,f));var Q=f;Q=Math.min(Q,ke(e.westIndices,e.westSkirtHeight,C,M,h,m,E,A,P)),Q=Math.min(Q,ke(e.southIndices,e.southSkirtHeight,C,M,h,m,E,A,P)),Q=Math.min(Q,ke(e.eastIndices,e.eastSkirtHeight,C,M,h,m,E,A,P)),Q=Math.min(Q,ke(e.northIndices,e.northSkirtHeight,C,M,h,m,E,A,P));for(var X=new Ce.AxisAlignedBoundingBox(A,P,p),Z=new Se.TerrainEncoding(X,Q,T,v,N,d),J=Z.getStride(),$=new Float32Array(o*J+s*J),ee=0,te=0;te<o;++te){if(N){var re=2*te;if(We.x=a[re],We.y=a[re+1],1!==g){var ie=be.AttributeCompression.octDecode(We.x,We.y,Ge),ne=we.Transforms.eastNorthUpToFixedFrame(x[te],m,Ye),oe=ye.Matrix4.inverseTransformation(ne,Oe);ye.Matrix4.multiplyByPointAsVector(oe,ie,ie),ie.z*=g,ve.Cartesian3.normalize(ie,ie),ye.Matrix4.multiplyByPointAsVector(ne,ie,ie),ve.Cartesian3.normalize(ie,ie),be.AttributeCompression.octEncode(ie,We)}}ee=Z.encode($,ee,x[te],M[te],C[te],We,S[te])}var ae=Math.max(0,2*(s-4)),se=e.indices.length+3*ae,de=Ne.IndexDatatype.createTypedArray(o+s,se);de.set(e.indices,0);var he=1e-4*(F-_),ce=1e-4*(D-B),ue=-he,le=he,Ie=ce,me=-ce,ge=o*J;Ve($,ge,L,Z,C,M,a,m,h,e.westSkirtHeight,g,r,i,ue,0),Ve($,ge+=e.westIndices.length*J,q,Z,C,M,a,m,h,e.southSkirtHeight,g,r,i,0,me),Ve($,ge+=e.southIndices.length*J,j,Z,C,M,a,m,h,e.eastSkirtHeight,g,r,i,le,0),Ve($,ge+=e.eastIndices.length*J,K,Z,C,M,a,m,h,e.northSkirtHeight,g,r,i,0,Ie);var fe=function(e,t,r,i){if(!(i<12)){for(var n={},o=e.length,a=0;a<o;a+=3){var s=e[a],d=e[a+1],h=e[a+2];(t[s]===Pe&&t[d]===Pe||0===t[s]&&0===t[d]||r[s]===Pe&&r[d]===Pe||0===r[s]&&0===r[d])&&(n[s+"_"+d]=1,n[d+"_"+s]=1),(t[d]===Pe&&t[h]===Pe||0===t[d]&&0===t[h]||r[d]===Pe&&r[h]===Pe||0===r[d]&&0===r[h])&&(n[d+"_"+h]=1,n[h+"_"+d]=1),(t[h]===Pe&&t[s]===Pe||0===t[h]&&0===t[s]||r[h]===Pe&&r[s]===Pe||0===r[h]&&0===r[s])&&(n[h+"_"+s]=1,n[s+"_"+h]=1)}return n}}(e.indices,y,w,e.level);return Ae.addSkirtIndices(L,q,j,K,o,de,e.indices.length,fe),t.push($.buffer,de.buffer),{vertices:$.buffer,indices:de.buffer,westIndicesSouthToNorth:L,southIndicesEastToWest:q,eastIndicesNorthToSouth:j,northIndicesWestToEast:K,vertexStride:J,center:p,minimumHeight:f,maximumHeight:T,boundingSphere:H,orientedBoundingBox:U,occludeePointInScaledSpace:z,encoding:Z,indexCountWithoutSkirts:e.indices.length}})});